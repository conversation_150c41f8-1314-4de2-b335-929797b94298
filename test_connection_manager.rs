use std::net::SocketAddr;
use std::time::Duration;
use tokio::net::TcpStream;
use tokio::time::sleep;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 测试TCP连接管理器");
    
    // 服务器地址 - 使用默认的TCP端口
    let server_addr: SocketAddr = "127.0.0.1:8081".parse()?;
    
    println!("📡 尝试连接到服务器: {}", server_addr);
    
    // 创建多个连接来测试连接管理器
    let mut connections = Vec::new();
    
    // 创建3个连接
    for i in 1..=3 {
        println!("🔗 创建连接 #{}", i);
        match TcpStream::connect(server_addr).await {
            Ok(stream) => {
                println!("✅ 连接 #{} 建立成功", i);
                connections.push(stream);
                // 稍微等待一下，让服务器处理连接
                sleep(Duration::from_millis(100)).await;
            }
            Err(e) => {
                println!("❌ 连接 #{} 失败: {}", i, e);
            }
        }
    }
    
    println!("⏳ 保持连接5秒钟...");
    sleep(Duration::from_secs(5)).await;
    
    // 关闭一些连接
    println!("🔌 关闭前两个连接...");
    for (i, mut stream) in connections.into_iter().take(2).enumerate() {
        println!("❌ 关闭连接 #{}", i + 1);
        let _ = stream.shutdown().await;
        sleep(Duration::from_millis(100)).await;
    }
    
    println!("⏳ 等待2秒钟观察剩余连接...");
    sleep(Duration::from_secs(2)).await;
    
    println!("🏁 测试完成");
    Ok(())
}

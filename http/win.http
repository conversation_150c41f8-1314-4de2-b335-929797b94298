POST http://{{SERVER_IP}}:10010
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "method": "query_related_data",
  "params": [
    {
      "sessionId": "123",
      "projectUrl": "D:\\tinghe-source\\rcqualitydataprod",
      "fileUrl": "app\\core\\service\\src\\main\\java\\com\\alipay\\rcqualitydataprod\\core\\service\\itac\\impl\\ItacAppServiceImpl.java",
      "language": "java",
      "intention": "CODE_GENERATE_TEST",
      "intentionType": "INTERFACE_TEST",
      "fileContent": "package com.alipay.rcqualitydataprod.core.service.itac.impl;\n\nimport com.alibaba.fastjson.JSONObject;\nimport com.alipay.archdatacenter.facade.model.dto.app.backendapp.BackendAppDTO;\nimport com.alipay.archdatacenter.facade.model.dto.common.UserDTO;\nimport com.alipay.archdatacenter.facade.model.request.app.backendapp.BackendAppPageRequest;\nimport com.alipay.archdatacenter.facade.model.request.app.backendapp.BackendAppSearchRequest;\nimport com.alipay.archdatacenter.facade.model.result.common.AdcResult;\nimport com.alipay.archdatacenter.facade.model.result.common.Page;\nimport com.alipay.archdatacenter.facade.service.app.backendapp.BackendAppQueryFacade;\nimport com.alipay.rcqualitydataprod.common.dal.dao.ItacSystemConfigDAO;\nimport com.alipay.rcqualitydataprod.common.dal.object.*;\nimport com.alipay.rcqualitydataprod.common.service.facade.quickapi.exception.CommonErrorEnum;\nimport com.alipay.rcqualitydataprod.common.service.facade.quickapi.exception.CommonException;\nimport com.alipay.rcqualitydataprod.common.service.facade.quickapi.exception.DataNotExistException;\nimport com.alipay.rcqualitydataprod.common.service.facade.quickapi.exception.NoTraceException;\nimport com.alipay.rcqualitydataprod.common.util.DAOUtils;\nimport com.alipay.rcqualitydataprod.common.util.JsonUtils;\nimport com.alipay.rcqualitydataprod.common.util.MsgUtils;\nimport com.alipay.rcqualitydataprod.common.util.ObjectUtils;\nimport com.alipay.rcqualitydataprod.common.util.consts.SeqGenerateType;\nimport com.alipay.rcqualitydataprod.common.util.git.GitUtils;\nimport com.alipay.rcqualitydataprod.common.util.itac.SequenceGenerator;\nimport com.alipay.rcqualitydataprod.common.util.matcher.AntStyleMatcher;\nimport com.alipay.rcqualitydataprod.core.model.service.itac.app.*;\nimport com.alipay.rcqualitydataprod.core.model.service.itac.config.SystemConfigConst;\nimport com.alipay.rcqualitydataprod.core.service.itac.ItacAppService;\nimport com.alipay.rcqualitydataprod.core.service.itac.ItacTagService;\nimport com.alipay.rcqualitydataprod.core.service.itac.daoclient.ItacAnalysisAppDAOClient;\nimport com.alipay.rcqualitydataprod.core.service.itac.daoclient.ItacBizInfoDAOClient;\nimport com.alipay.rcqualitydataprod.core.service.itac.daoclient.ItacSystemConfigDAOClient;\nimport com.alipay.rcqualitydataprod.core.service.utils.MapperUtils;\nimport com.alipay.sofa.runtime.api.annotation.SofaService;\nimport com.google.common.collect.Lists;\nimport lombok.Getter;\nimport lombok.Setter;\nimport lombok.extern.slf4j.Slf4j;\nimport org.apache.commons.lang3.StringUtils;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\nimport org.springframework.util.CollectionUtils;\n\nimport javax.annotation.Resource;\nimport java.util.*;\nimport java.util.stream.Collectors;\n\n/**\n * App服务\n *\n * <AUTHOR> * @date 2023/11/7 20:02\n */\n@Slf4j\n@Getter\n@Setter\n@Service\n@SofaService()\n@SuppressWarnings(\"all\")\npublic class ItacAppServiceImpl implements ItacAppService {\n    @Resource\n    BackendAppQueryFacade backendAppQueryFacade;\n    @Autowired\n    ItacAnalysisAppDAOClient itacAnalysisAppDAOClient;\n    @Autowired\n    SequenceGenerator sequenceGenerator;\n    @Autowired\n    ItacBizInfoDAOClient itacBizInfoDAOClient;\n    @Resource\n    protected ItacSystemConfigDAO itacSystemConfigDAO;\n    @Autowired\n    ItacSystemConfigDAOClient itacSystemConfigDAOClient;\n    @Autowired\n    ItacAntCodeChangeAnalysisHttpClient itacAntCodeChangeAnalysisHttpClient;\n    @Autowired\n    ItacTagService itacTagService;\n\n    /**\n     * 获取App\n     */\n    @Override\n    public AppVO getApp(AppReq req) {\n        ObjectUtils.valid(req, \"appName\");\n        ItacAnalysisAppDO appDO = itacAnalysisAppDAOClient.checkOneByAppName(req.getAppName());\n        secretForAppDO(appDO);\n\n        AppVO appVO = MapperUtils.map(appDO, AppVO.class);\n        // 设置业务域名称\n        ItacBusinessInformationDO bizInfoDO = itacBizInfoDAOClient.selectOne(appDO.getBusinessInformationCode());\n        if (Objects.nonNull(bizInfoDO)) {\n            appVO.setBusinessName(bizInfoDO.getBusinessName());\n        }\n        return appVO;\n    }\n\n    /**\n     * 添加App\n     */\n    @Override\n    public AppVO addApp(AppVO req) {\n        ObjectUtils.valid(req, \"appName\", \"businessInformationCode\", \"language\");\n        ItacAnalysisAppDO record = itacAnalysisAppDAOClient.selectOneByAppName(req.getAppName());\n        if (Objects.nonNull(record)) {\n            throw new CommonException(CommonErrorEnum.DATA_ALREADY_EXIST, MsgUtils.format(\"已存在应用, appName=[{}]\", req.getAppName()));\n        }\n        record = MapperUtils.map(req, ItacAnalysisAppDO.class);\n        record.setAppCode(sequenceGenerator.generateSequenceId(SeqGenerateType.ITAC_APP));\n        record.setGmtModified(new Date());\n        record.setGmtCreate(new Date());\n        itacAnalysisAppDAOClient.insert(record);\n        secretForAppDO(record);\n        return MapperUtils.map(record, AppVO.class);\n    }\n\n    /**\n     * 尝试启用ITA服务\n     */\n    @Override\n    public AppVO activeItaService(AppVO req) {\n        ObjectUtils.valid(req, \"appName\");\n        ItacAnalysisAppDO appDO = itacAnalysisAppDAOClient.checkOneByAppName(req.getAppName());\n        if (StringUtils.isBlank(appDO.getRepoUrl())) {\n            throw new CommonException(CommonErrorEnum.DATA_FORMAT_ERROR, \"App没有设置repoUrl\");\n        }\n        String projectId = GitUtils.getProjectIdByUrl(appDO.getRepoUrl());\n        JSONObject installResult = itacAntCodeChangeAnalysisHttpClient.installItaService(projectId);\n        appDO.setItaService(DAOUtils.toByte(itacAntCodeChangeAnalysisHttpClient.isActiveService(installResult)));\n        itacAnalysisAppDAOClient.update(appDO);\n        return MapperUtils.map(appDO, AppVO.class);\n    }\n\n    /**\n     * 更新App\n     */\n    @Override\n    public AppVO updateApp(AppVO req) {\n        ObjectUtils.valid(req, \"appName\");\n        ItacAnalysisAppDO updateDO = MapperUtils.map(req, ItacAnalysisAppDO.class);\n        itacAnalysisAppDAOClient.update(updateDO);\n        ItacAnalysisAppDO appDO = itacAnalysisAppDAOClient.checkOneByAppName(req.getAppName());\n        secretForAppDO(appDO);\n        return MapperUtils.map(appDO, AppVO.class);\n    }\n\n    /**\n     * 更新App trigger Source控制规则触发\n     */\n    @Override\n    public AppVO updateAppTriggerSource(AppReqWithTriggerSource appReqWithTriggerSource) {\n        TriggerSourceConf triggerSourceConf = appReqWithTriggerSource.getTriggerSourceConf();\n        ItacAnalysisAppDO appDO = itacAnalysisAppDAOClient.checkOneByAppName(appReqWithTriggerSource.getAppName());\n        AppExtInfo extInfo = JsonUtils.fromJsonNonNull(appDO.getExtInfo(), AppExtInfo.class);\n        List<TriggerSourceConf> triggerSourceConfs = extInfo.getTriggerSourceConf().stream().filter(conf -> triggerSourceConf.getTriggerSource().eq(conf.getTriggerSource()))\n                .collect(Collectors.toList());\n\n        if (triggerSourceConfs.isEmpty()) {\n            extInfo.getTriggerSourceConf().add(triggerSourceConf);\n        } else {\n            //            如果存在，则进行覆盖\n            List<TriggerSourceConf> triggerSourceConfsNew = extInfo.getTriggerSourceConf().stream().filter(conf -> !triggerSourceConf.getTriggerSource().eq(conf.getTriggerSource()))\n                    .collect(Collectors.toList());\n            triggerSourceConfsNew.add(triggerSourceConf);\n            extInfo.setTriggerSourceConf(triggerSourceConfsNew);\n        }\n\n        appDO.setExtInfo(JsonUtils.toJson(extInfo));\n        itacAnalysisAppDAOClient.update(appDO);\n        return MapperUtils.map(appDO, AppVO.class);\n    }\n\n    /**\n     * 更新App trigger Source控制规则触发\n     */\n    @Override\n    public AppVO updateAppDynamicTriggerSource(AppReqWithTriggerSource appReqWithTriggerSource) {\n        DynamicTriggerConf dynamicTriggerConf = appReqWithTriggerSource.getDynamicTriggerConf();\n        ItacAnalysisAppDO appDO = itacAnalysisAppDAOClient.checkOneByAppName(appReqWithTriggerSource.getAppName());\n        AppExtInfo extInfo = JsonUtils.fromJsonNonNull(appDO.getExtInfo(), AppExtInfo.class);\n        List<DynamicTriggerConf> triggerSourceConfs = extInfo.getDynamicTriggerConf().stream().filter(conf -> StringUtils.equals(conf.getDynamicSource(), dynamicTriggerConf.getDynamicSource()))\n                .collect(Collectors.toList());\n\n        if (triggerSourceConfs.isEmpty()) {\n            extInfo.getDynamicTriggerConf().add(dynamicTriggerConf);\n        } else {\n            //            如果存在，则进行覆盖\n            List<DynamicTriggerConf> triggerSourceConfsNew = extInfo.getDynamicTriggerConf().stream().filter(conf -> !StringUtils.equals(conf.getDynamicSource(), dynamicTriggerConf.getDynamicSource()))\n                    .collect(Collectors.toList());\n            triggerSourceConfsNew.add(dynamicTriggerConf);\n            extInfo.setDynamicTriggerConf(triggerSourceConfsNew);\n        }\n\n        appDO.setExtInfo(JsonUtils.toJson(extInfo));\n        itacAnalysisAppDAOClient.update(appDO);\n        return MapperUtils.map(appDO, AppVO.class);\n    }\n\n    /**\n     * 清理App字段\n     */\n    @Override\n    public AppVO cleanApp(CleanAppVO req) {\n        ObjectUtils.valid(req, \"appName\");\n        ItacAnalysisAppDO updateDO = MapperUtils.map(req, ItacAnalysisAppDO.class);\n        itacAnalysisAppDAOClient.update(updateDO);\n        ItacAnalysisAppDO appDO = itacAnalysisAppDAOClient.checkOneByAppName(req.getAppName());\n        secretForAppDO(appDO);\n        return MapperUtils.map(appDO, AppVO.class);\n    }\n\n    /**\n     * 获取应用详情\n     */\n    @Override\n    public BackendAppDTO getAppDetail(AppReq req) {\n        ObjectUtils.valid(req, \"appName\");\n        itacSystemConfigDAO.selectByPrimaryKey(1L);\n        AdcResult<BackendAppDTO> result = backendAppQueryFacade.getAppDetail(req.getAppName());\n        if (!result.isSuccess()) {\n            throw new CommonException(CommonErrorEnum.APP_ARCHDATACENTER_API_ERROR, result.getErrorMessage());\n        }\n        return result.getResult();\n    }\n\n    /**\n     * 查找应用名\n     */\n    @Override\n    public List<String> searchAppNames(AppReq req) {\n        BackendAppSearchRequest appReq = new BackendAppSearchRequest();\n        appReq.setFuzzyAppName(req.getAppName());\n        AdcResult<List<String>> result = backendAppQueryFacade.searchAppNames(appReq);\n        if (!result.isSuccess()) {\n            throw new CommonException(CommonErrorEnum.APP_ARCHDATACENTER_API_ERROR, result.getErrorMessage());\n        }\n        return result.getResult();\n    }\n\n    /**\n     * 同步应用信息\n     */\n    @Override\n    public AppVO syncAppInfo(AppReq req) {\n        ItacAnalysisAppParam param = new ItacAnalysisAppParam();\n        param.createCriteria().andAppNameEqualTo(req.getAppName());\n        ItacAnalysisAppDO appDO = itacAnalysisAppDAOClient.selectOne(param);\n        if (null == appDO) {\n            throw new DataNotExistException(MsgUtils.format(\"不存在应用, eventDO=[{}]\", req));\n        }\n\n        doSyncAppDO(appDO);\n        secretForAppDO(appDO);\n        return MapperUtils.map(appDO, AppVO.class);\n\n    }\n\n    /**\n     * 将应用详情信息同步到数据库\n     */\n    @Override\n    public void doSyncAppDO(ItacAnalysisAppDO appDO) {\n        AppReq req = new AppReq();\n        req.setAppName(appDO.getAppName());\n\n        BackendAppDTO appDTO = getAppDetail(req);\n        if (null == appDTO) {\n            throw new CommonException(CommonErrorEnum.DATA_NOT_EXIST, MsgUtils.format(\"没有查找到应用详情, appName=[{}]\", req.getAppName()));\n        }\n\n        List<UserDTO> owners = new ArrayList<>();\n        owners.add(appDTO.getOwner());\n        owners.addAll(appDTO.getBakOwners());\n\n        appDO.setQualityOwner(StringUtils.trimToNull(appDTO.getTestOwners().stream().map(UserDTO::getLoginAccount).collect(Collectors.joining(\",\"))));\n        appDO.setOwner(StringUtils.trimToNull(owners.stream().filter(Objects::nonNull).map(UserDTO::getLoginAccount).collect(Collectors.joining(\",\"))));\n        appDO.setAppDescription(StringUtils.defaultIfBlank(appDO.getAppDescription(), appDTO.getAppNameCn()));\n        if (Objects.nonNull(appDTO.getCodeRepository())) {\n            String repoUrl = StringUtils.defaultIfBlank(appDO.getRepoUrl(), appDTO.getCodeRepository().getCodeRepoUrl());\n            appDO.setRepoUrl(GitUtils.fmtRepoUrl(repoUrl, true));\n        }\n        appDO.setHaLevel(appDTO.getHaLevel());\n        appDO.setSecLevel(appDO.getSecLevel());\n        appDO.setBizLevel(appDTO.getBizLevel());\n        appDO.setSyncResult(\"SUCCESS\");\n        appDO.setGmtSync(new Date());\n        itacAnalysisAppDAOClient.update(appDO);\n    }\n\n    /**\n     * 批量同步应用信息\n     */\n    @Override\n    public int batchSyncAppInfo(BatchSyncAppReq req) {\n        int ret = 0;\n        ItacAnalysisAppParam param = new ItacAnalysisAppParam();\n        param.appendOrderByClause(ItacAnalysisAppParam.OrderCondition.GMTSYNC, ItacAnalysisAppParam.SortType.ASC);\n        if (CollectionUtils.isEmpty(req.getAppNameList())) {\n            param.setPagination(1, req.getBatchSize());\n        } else {\n            param.createCriteria().andAppNameIn(req.getAppNameList());\n        }\n        List<ItacAnalysisAppDO> appDOList = itacAnalysisAppDAOClient.selectAll(param);\n        for (ItacAnalysisAppDO appDO : appDOList) {\n            try {\n                doSyncAppDO(appDO);\n                ret++;\n            } catch (Throwable e) {\n                appDO.setGmtSync(new Date());\n                appDO.setSyncResult(\"FAIL\");\n                itacAnalysisAppDAOClient.update(appDO);\n            }\n        }\n        return ret;\n    }\n\n    /**\n     * 同步应用部署单元信息\n     * https://code.alipay.com/security_release/antkms.git -> security_release/antkms\n     */\n    @Override\n    public List<BackendAppDTO> searchAppByRepo(AppReq req) {\n        ObjectUtils.valid(req, \"repoUrl\");\n        String repoUrl = req.getRepoUrl();\n        BackendAppPageRequest appPageReq = new BackendAppPageRequest();\n        appPageReq.setSource(\"rcqualitydataprod\");\n        appPageReq.setPageNo(1);\n        appPageReq.setPageSize(100);\n        appPageReq.setCodeRepoGroupAndName(GitUtils.getProjectIdByUrl(repoUrl, \"/\"));\n        appPageReq.setStatuses(Arrays.asList(\"ONLINE\"));\n\n        log.info(\"[应用部署单元信息获取] appPageReq=[{}]\", appPageReq);\n        AdcResult<Page<BackendAppDTO>> result = backendAppQueryFacade.pageAppDetails(appPageReq);\n\n        if (!result.isSuccess()) {\n            log.info(\"[应用部署单元信息获取异常] appPageReq=[{}]\", appPageReq);\n            throw new CommonException(CommonErrorEnum.APP_ARCHDATACENTER_API_ERROR, result.getErrorMessage());\n        }\n\n        Page<BackendAppDTO> backendAppDTOPage = result.getResult();\n        return backendAppDTOPage.getData();\n    }\n\n    /**\n     * 是否接受AntCode事件\n     */\n    @Override\n    public ItacAnalysisAppDO acceptEventAntCode(ItacAntcodeEventDO eventDO) {\n        // AntCode事件的appName优先以group/appName格式为准\n        String appName = GitUtils.getProjectIdByUrl(eventDO.getRepoUrl(), \"/\");\n        ItacAnalysisAppDO appDO = itacAnalysisAppDAOClient.selectOneByAppName(appName);\n        if (Objects.isNull(appDO)) {\n            // 如果group/appName格式查找不到对应appName配置, 则直接通过appName进行查询\n            appName = eventDO.getRepoName();\n            appDO = itacAnalysisAppDAOClient.checkOneByAppNameNoTrace(appName);\n        }\n\n        // 判断应用创建来源是否允许事件触发\n        AppSourceEnum appSource = AppSourceEnum.getByCode(appDO.getSource());\n        if (!appSource.isEventTrigger()) {\n            throw new NoTraceException(CommonErrorEnum.CONFIG_STATUS_ERROR, MsgUtils.format(\"应用创建来源不允许通过事件触发, appName=[{}], appSource=[{}]\", appName, appSource));\n        }\n\n        // 获取触发配置\n        String eventTrigger = ObjectUtils.isBlankOrElse(appDO.getEventTrigger(), () -> itacSystemConfigDAOClient.getValue(SystemConfigConst.GLOBAL_EVENT_TRIGGER));\n        EventTriggerVO eventTriggerVO = JsonUtils.fromJson(eventTrigger, EventTriggerVO.class);\n        if (Objects.isNull(eventTriggerVO) || !eventTriggerVO.hasEvent(EventTypeEnum.ANTCODE)) {\n            throw new NoTraceException(CommonErrorEnum.CONFIG_NOT_EXIST, MsgUtils.format(\"应用没配置[ANTCODE]触发事件, appName=[{}]\", appName));\n        }\n\n        assertConfig(appName, eventTriggerVO.getConfig(), eventDO, Lists.newArrayList(\"eventType\", \"attrRef\"));\n\n        return appDO;\n    }\n\n    /**\n     * 配置匹配\n     *\n     * @param appName   应用名\n     * @param config    app.eventTrigger.config 触发配置\n     * @param eventJson 事件数据转换成JSON\n     * @param keyList   需要校验的key列表\n     */\n    protected static void assertConfig(String appName, JSONObject config, Object eventDO, List<String> keyList) {\n        if (Objects.isNull(config) || Objects.isNull(eventDO)) {\n            return;\n        }\n        JSONObject eventJson = JsonUtils.toJsonObject(eventDO);\n        for (String key : keyList) {\n            // 配置中不包含的话, 直接通过\n            if (config.containsKey(key)) {\n                if (!StringUtils.equals(config.getString(key), eventJson.getString(key))) {\n                    AntStyleMatcher antStyleMatcher = new AntStyleMatcher();\n                    antStyleMatcher.setIncludes(config.getString(key));\n                    if (!antStyleMatcher.match(eventJson.getString(key))) {\n                        throw new NoTraceException(CommonErrorEnum.CONFIG_NOT_MATCH, MsgUtils.format(\"应用配置不匹配, appName=[{}], key=[{}], expectValue=[{}], actualValue=[{}]\", appName, key, config.getString(key), eventJson.getString(key)));\n                    }\n                }\n            }\n        }\n    }\n\n    @Override\n    public ItacAnalysisAppDO acceptEventLinkE(ItacLinkeMsgEventDO eventDO) {\n        ItacAnalysisAppDO appDO = itacAnalysisAppDAOClient.checkOneByAppNameNoTrace(eventDO.getAppName());\n\n        // 判断应用创建来源是否允许事件触发\n        AppSourceEnum appSource = AppSourceEnum.getByCode(appDO.getSource());\n        if (!appSource.isEventTrigger()) {\n            throw new NoTraceException(CommonErrorEnum.CONFIG_STATUS_ERROR, MsgUtils.format(\"应用创建来源不允许通过事件触发, appName=[{}], appSource=[{}]\", appDO.getAppName(), appSource));\n        }\n\n        // 获取触发配置\n        String eventTrigger = ObjectUtils.isBlankOrElse(appDO.getEventTrigger(), () -> itacSystemConfigDAOClient.getValue(SystemConfigConst.GLOBAL_EVENT_TRIGGER));\n        EventTriggerVO eventTriggerVO = JsonUtils.fromJson(eventTrigger, EventTriggerVO.class);\n        if (Objects.isNull(eventTriggerVO) || !eventTriggerVO.hasEvent(EventTypeEnum.LINKE)) {\n            throw new NoTraceException(CommonErrorEnum.CONFIG_NOT_EXIST, MsgUtils.format(\"应用没配置[LINKE]触发事件, appName=[{}]\", eventDO.getAppName()));\n        }\n        return appDO;\n    }\n\n    /**\n     * 是否接受AciEvent事件\n     */\n    @Override\n    public ItacAnalysisAppDO acceptEventAci(ItacAciEventDO eventDO) {\n        // Aci事件的以appName为准\n        String appName = eventDO.getParamAppName();\n        ItacAnalysisAppDO appDO = itacAnalysisAppDAOClient.checkOneByAppNameNoTrace(appName);\n\n        // 判断应用创建来源是否允许事件触发\n        AppSourceEnum appSource = AppSourceEnum.getByCode(appDO.getSource());\n        if (!appSource.isEventTrigger()) {\n            throw new NoTraceException(CommonErrorEnum.CONFIG_STATUS_ERROR, MsgUtils.format(\"应用创建来源不允许通过事件触发, appName=[{}], appSource=[{}]\", appName, appSource));\n        }\n\n        // 获取触发配置\n        String eventTrigger = ObjectUtils.isBlankOrElse(appDO.getEventTrigger(), () -> itacSystemConfigDAOClient.getValue(SystemConfigConst.GLOBAL_EVENT_TRIGGER));\n        EventTriggerVO eventTriggerVO = JsonUtils.fromJson(eventTrigger, EventTriggerVO.class);\n        if (Objects.isNull(eventTriggerVO) || !eventTriggerVO.hasEvent(EventTypeEnum.ACI_EVENT)) {\n            throw new NoTraceException(CommonErrorEnum.CONFIG_NOT_EXIST, MsgUtils.format(\"应用没配置[ACI_EVENT]触发事件, appName=[{}]\", appName));\n        }\n\n        assertConfig(appName, eventTriggerVO.getConfig(), eventDO, Lists.newArrayList(\"eventType\", \"paramGitBranch\"));\n\n        return appDO;\n    }\n\n    /**\n     * 根据app配置的触发源配置获取规则集下的itemcode\n     */\n    public List<String> getRuleCodeBytriggerSourceConf(ItacAnalysisAppDO itacAnalysisAppDO) {\n        //    如果列表未传则取app配置的规则集\n        AppExtInfo appExtInfo = JsonUtils.fromJson(StringUtils.defaultIfBlank(itacAnalysisAppDO.getExtInfo(), \"{}\"), AppExtInfo.class);\n        List<TriggerSourceConf> triggerSourceConfList = appExtInfo.getTriggerSourceConf();\n        if (CollectionUtils.isEmpty(triggerSourceConfList)) {\n            //如果triggerSourceConf仍然没有查询出,则弹出异常\n            throw new NoTraceException(CommonErrorEnum.CONFIG_NOT_EXIST, MsgUtils.format(\"应用没配置规则集,无法根据规则集查询, appName=[{}]\", itacAnalysisAppDO.getAppName()));\n        }\n        List<String> tagItems = itacTagService.listItemByTriggerSourceConf(triggerSourceConfList);\n        return tagItems;\n\n    }\n\n    /**\n     * 重命名App\n     */\n    @Override\n    public AppVO renameApp(RenameAppReq req) {\n        if (StringUtils.equals(req.getFromAppName(), req.getToAppName())) {\n            throw new CommonException(CommonErrorEnum.INPUT_ERROR, \"参数[fromAppName]与[toAppName]不能相同\");\n        }\n        ItacAnalysisAppDO record = itacAnalysisAppDAOClient.selectOneByAppName(req.getToAppName());\n        if (Objects.nonNull(record)) {\n            throw new CommonException(CommonErrorEnum.DATA_ALREADY_EXIST, MsgUtils.format(\"已存在应用, appName=[{}]\", req.getToAppName()));\n        }\n\n        ItacAnalysisAppDO updateDO = itacAnalysisAppDAOClient.checkOneByAppName(req.getFromAppName());\n        updateDO.setAppName(req.getToAppName());\n        itacAnalysisAppDAOClient.update(updateDO);\n        return MapperUtils.map(updateDO, AppVO.class);\n    }\n\n    /**\n     * 设置\n     */\n    public void secretForAppDO(ItacAnalysisAppDO appDO) {\n        if (null == appDO) {\n            return;\n        }\n        appDO.setGitToken(\"******\");\n    }\n}",
      "selectedContent": "public BackendAppDTO getAppDetail(AppReq req) {"
    }
  ],
  "id": 1
}

###
POST http://{{SERVER_IP}}:10010
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "method": "query_related_data",
  "params": [
    {
      "sessionId": "123",
      "projectUrl": "D:\\tinghe-source\\rcqualitydataprod",
      "fileUrl": "app\\biz\\shared\\src\\main\\java\\com\\alipay\\rcqualitydataprod\\biz\\notify\\ItacNotifyServiceImpl.java",
      "language": "java",
      "intention": "CODE_GENERATE_TEST",
      "intentionType": "INTERFACE_TEST",
      "fileContent": "package com.alipay.rcqualitydataprod.biz.notify;\n\nimport com.alibaba.fastjson.JSONObject;\nimport com.alipay.lifemsgprod.common.service.facade.base.model.MsgSendResult;\nimport com.alipay.lifemsgprod.common.service.facade.channel.enums.MessageChannelEnum;\nimport com.alipay.lifemsgprod.common.service.facade.message.req.SingleChannelMsgSendReq;\nimport com.alipay.rcqualitydataprod.biz.pojo.query.ReportQuery;\nimport com.alipay.rcqualitydataprod.biz.pojo.vo.AutoReportCountVO;\nimport com.alipay.rcqualitydataprod.biz.pojo.vo.QueryResult;\nimport com.alipay.rcqualitydataprod.biz.pojo.vo.ReportResultVO;\nimport com.alipay.rcqualitydataprod.biz.service.ItacNotifyService;\nimport com.alipay.rcqualitydataprod.common.dal.object.*;\nimport com.alipay.rcqualitydataprod.common.util.*;\nimport com.alipay.rcqualitydataprod.common.util.compare.CompareUtils;\nimport com.alipay.rcqualitydataprod.common.util.date.DateUtils;\nimport com.alipay.rcqualitydataprod.common.util.vm.VelocityUtils;\nimport com.alipay.rcqualitydataprod.common.util.xingdou.XingDouUtils;\nimport com.alipay.rcqualitydataprod.core.model.itacenum.ItacNotifyStatusEnum;\nimport com.alipay.rcqualitydataprod.core.model.itacenum.ItacRiskEventsSourceEnum;\nimport com.alipay.rcqualitydataprod.core.model.itacenum.ItacRiskLevelEnum;\nimport com.alipay.rcqualitydataprod.core.model.service.itac.config.SystemConfigConst;\nimport com.alipay.rcqualitydataprod.core.model.service.itac.dingtalk.SampleActionCardMsg;\nimport com.alipay.rcqualitydataprod.core.model.service.itac.dingtalk.SendMsgResultVO;\nimport com.alipay.rcqualitydataprod.core.model.service.itac.notify.*;\nimport com.alipay.rcqualitydataprod.core.model.service.itac.project.CommitFromEnum;\nimport com.alipay.rcqualitydataprod.core.model.service.itac.tag.TagTypeEnum;\nimport com.alipay.sofa.runtime.api.annotation.SofaService;\nimport com.google.common.collect.Lists;\nimport com.google.common.collect.Maps;\nimport groovy.lang.Binding;\nimport lombok.Getter;\nimport lombok.Setter;\nimport lombok.extern.slf4j.Slf4j;\nimport org.apache.commons.lang.StringUtils;\nimport org.springframework.stereotype.Service;\nimport org.springframework.util.CollectionUtils;\n\nimport java.math.BigDecimal;\nimport java.util.*;\nimport java.util.stream.Collectors;\n\n/**\n * 通知服务\n *\n * <AUTHOR> * @date 2023/10/31 16:04\n */\n@Slf4j\n@Getter\n@Setter\n@Service\n@SofaService\npublic class ItacNotifyServiceImpl extends ItacNotifyServiceHelper implements ItacNotifyService {\n    /**\n     * 加载报告上下文\n     */\n    @Override\n    public Map<String, Object> loadReportContext(NotifyEmailReq req) {\n        ItacAnalysisProjectDO projectDO = itacAnalysisProjectDAOClient.checkOne(req.getProjectCode());\n        ItacAnalysisIterationInputDO inputDO = itacAnalysisIterationInputDAOClient.checkOne(req.getProjectCode());\n        inputDO.setIteration(XingDouUtils.getIterationId(inputDO.getIteration()));\n\n        ReportQuery reportQuery = new ReportQuery();\n        reportQuery.setProjectId(req.getProjectCode());\n        reportQuery.setRiskLevelList(Lists.newArrayList(ItacRiskLevelEnum.RiskHigh.getCode(), ItacRiskLevelEnum.RiskMedium.getCode()));\n        reportQuery.setTagType(TagTypeEnum.ITEM_RISK_LEVEL);\n        AutoReportCountVO countVO = itacAnalysisReportManager.queryAUTOReportCount(reportQuery);\n\n        List<ItacTagDO> tagRiskTypeList = itacTagService.listTag(TagTypeEnum.ITEM_RISK_TYPE);\n\n        QueryResult<List<ReportResultVO>> riskReportResult = itacAnalysisReportManager.queryReport(reportQuery);\n        if (CollectionUtils.isEmpty(riskReportResult.getData())) {\n            riskReportResult.setData(new ArrayList<>());\n        }\n\n        for (ReportResultVO report : riskReportResult.getData()) {\n            report.setRiskType(getTagName(report.getRiskType(), tagRiskTypeList));\n        }\n\n        ReportQuery lowRiskReportQuery = new ReportQuery();\n        lowRiskReportQuery.setPageNo(1);\n        lowRiskReportQuery.setPageSize(10);\n        lowRiskReportQuery.setProjectId(req.getProjectCode());\n        lowRiskReportQuery.setRiskLevelList(Lists.newArrayList(ItacRiskLevelEnum.RiskLow.getCode()));\n        lowRiskReportQuery.setCategoryCode(drmResource.getInterfaceCategoryCode());\n        QueryResult<List<ReportResultVO>> interfaceReportResult = itacAnalysisReportManager.queryLowRiskReport(lowRiskReportQuery);\n        if (CollectionUtils.isEmpty(interfaceReportResult.getData())) {\n            interfaceReportResult.setData(new ArrayList<>());\n        }\n\n        lowRiskReportQuery.setCategoryCode(drmResource.getDalCategoryCode());\n        QueryResult<List<ReportResultVO>> dalReportResult = itacAnalysisReportManager.queryLowRiskReport(lowRiskReportQuery);\n        if (CollectionUtils.isEmpty(dalReportResult.getData())) {\n            dalReportResult.setData(new ArrayList<>());\n        }\n\n        lowRiskReportQuery.setCategoryCode(drmResource.getOtherCategoryCode());\n        QueryResult<List<ReportResultVO>> otherReportResult = itacAnalysisReportManager.queryLowRiskReport(lowRiskReportQuery);\n        if (CollectionUtils.isEmpty(otherReportResult.getData())) {\n            otherReportResult.setData(new ArrayList<>());\n        }\n\n        lowRiskReportQuery.setCategoryCode(drmResource.getAiMethodCategoryCode());\n        QueryResult<List<ReportResultVO>> aiMethodReportResult = itacAnalysisReportManager.queryLowRiskReport(lowRiskReportQuery);\n        if (CollectionUtils.isEmpty(aiMethodReportResult.getData())) {\n            aiMethodReportResult.setData(new ArrayList<>());\n        }\n\n\n        Map<String, Object> ctx = new HashMap<>();\n        ctx.put(\"notifyId\", req.getNotifyId());\n        ctx.put(\"projectDO\", projectDO);\n        ctx.put(\"inputDO\", inputDO);\n        ctx.put(\"reportUrl\", getReportUrl(inputDO.getProjectCode(), SubscribeChannelConst.EMAIL, req.getNotifyId()));\n        ctx.put(\"gmtCreate\", DateUtils.yyyy_MM_dd_HHmmss(projectDO.getGmtCreate()));\n        ctx.put(\"countVO\", countVO);\n        ctx.put(\"riskReportList\", riskReportResult.getData());\n        ctx.put(\"interfaceReportList\", interfaceReportResult.getData());\n        ctx.put(\"dalReportList\", dalReportResult.getData());\n        ctx.put(\"otherReportList\", otherReportResult.getData());\n        ctx.put(\"aiMethodReportList\", aiMethodReportResult.getData());\n        ctx.put(\"EllipsisUtils\", EllipsisUtils.class);\n        return ctx;\n    }\n\n    /**\n     * 获取报告Url\n     */\n    public String getReportUrl(String projectCode, String channel, String notifyId) {\n        String reportUrl = MsgUtils.format(drmResource.getProjectReportUrlTemplate(), projectCode);\n        Map<String, String> query = new HashMap<>();\n        query.put(\"trackSource\", channel);\n        query.put(\"notifyId\", notifyId);\n        return URIUtils.addQuery(reportUrl, query);\n    }\n\n    /**\n     * 渲染邮件\n     */\n    @Override\n    public NotifyEmailVO renderEmail(NotifyEmailReq req) {\n        Map<String, Object> ctx = loadReportContext(req);\n        String body = VelocityUtils.rendVmFile(ctx, \"template/email/main_body.vm\");\n        NotifyEmailVO vo = new NotifyEmailVO();\n        vo.setBody(body);\n        return vo;\n    }\n\n    /**\n     * 渲染邮件页面\n     */\n    @Override\n    public String reportEmailWeb(NotifyEmailReq req) {\n        Map<String, Object> ctx = loadReportContext(req);\n        return VelocityUtils.rendVmFile(ctx, \"template/email/main_html.vm\");\n    }\n\n    /**\n     * 渲染并发送邮件\n     */\n    @Override\n    public NotifyEmailVO renderAndSendEmail(NotifyEmailReq req) {\n        ObjectUtils.valid(req, \"projectCode\", \"emailList\");\n        NotifyEmailVO emailVO = renderEmail(req);\n        req.setBody(emailVO.getBody());\n        return doSendEmail(req);\n    }\n\n    /**\n     * 发送邮件\n     */\n    @Override\n    public NotifyEmailVO doSendEmail(NotifyEmailReq req) {\n        ObjectUtils.valid(req, \"emailList\", \"body\");\n        NotifyEmailVO ret = new NotifyEmailVO();\n        ret.setEmailList(req.getEmailList());\n        if (req.isDebugMode()) {\n            ret.setSuccess(true);\n            ret.setMsgId(UUIDUtils.newId(\"mail_\"));\n            return ret;\n        }\n        SingleChannelMsgSendReq msgReq = new SingleChannelMsgSendReq();\n        msgReq.setAppId(messageAppId);\n        msgReq.setTemplateId(messageTemplateId);\n        msgReq.setChannel(MessageChannelEnum.EMAIL.getChannel());\n        msgReq.setToAccountNo(StringUtils.join(req.getEmailList(), \",\"));\n        log.info(\"[AliMail] 准备发送邮件, toAccountNo=[{}], toCcAccountNo=[{}]\", msgReq.getToAccountNo(), msgReq.getToCcAccountNo());\n\n        Map<String, Object> args = Maps.newHashMap();\n        args.put(\"body\", req.getBody());\n        msgReq.setTemplateArgs(args);\n        MsgSendResult result = channelMessageFacade.sendSingleChannelMsg(msgReq);\n        if (null != result && result.isSuccess()) {\n            ret.setSuccess(true);\n            ret.setMsgId(result.getMsgId());\n            log.info(\"[AliMail] 发送邮件成功, msgId=[{}]\", result.getMsgId());\n        } else {\n            ret.setSuccess(false);\n        }\n        return ret;\n    }\n\n\n    /**\n     * 决定报告是否需要透出\n     * 1. 存在未反馈的中高风险, 户已处理过/已查看过的不需要再通知了\n     * 2. 应用+迭代上次通知时间的间隔\n     * 分析数据: 迭代推进分析(15/天, 2%), PRE(400/天, 45%)/GRAY(20/天, 2%)阶段分析\n     */\n    @Override\n    public void needNotifyReport(NotifyContext notifyContext) {\n        ItacAnalysisProjectDO projectDO = notifyContext.getProjectDO();\n        ItacAnalysisIterationInputDO inputDO = notifyContext.getInputDO();\n        NotifyConfigVO notifyConfigVO = notifyContext.getNotifyConfigVO();\n\n        try {\n            // 首先统计项目命中情况,后续的逻辑处理都需要依赖统计数据\n            ReportQuery reportQuery = new ReportQuery();\n            reportQuery.setEnableCustomItemQuery(true);\n            reportQuery.setProjectId(projectDO.getProjectCode());\n            reportQuery.setRiskLevelList(Lists.newArrayList(ItacRiskLevelEnum.RiskHigh.getCode(), ItacRiskLevelEnum.RiskMedium.getCode()));\n            reportQuery.setTagType(TagTypeEnum.ITEM_RISK_LEVEL);\n            AutoReportCountVO countVO = itacAnalysisReportManager.queryAUTOReportCount(reportQuery);\n            notifyContext.setReportCountVO(countVO);\n\n            // [忽略] 人工反馈过\n            ItacRiskEventsParam riskEventParam = new ItacRiskEventsParam();\n            riskEventParam.createCriteria().andProjectCodeEqualTo(projectDO.getProjectCode()).andSourceNotEqualTo(ItacRiskEventsSourceEnum.AUTO.getCode());\n            List<ItacRiskEventsDO> riskEventsDOList = itacRiskEventsDAOClient.selectAll(riskEventParam);\n            if (!CollectionUtils.isEmpty(riskEventsDOList)) {\n                log.info(\"[通知决策] (拒绝) 忽略通知报告, 报告已经存在人工反馈, projectCode=[{}]\", projectDO.getProjectCode());\n                return;\n            }\n\n            JSONObject countJson = JsonUtils.toJsonObject(countVO);\n            int highNoProcessed = countVO.getHighHitcount() - countVO.getHighHitProcessed();\n            int mediumNoProcessed = countVO.getMediumHitcount() - countVO.getMediumHitProcessed();\n            int noProcessed = highNoProcessed + mediumNoProcessed;\n            if (noProcessed <= 0) {\n                log.info(\"[通知决策] (拒绝) 忽略通知报告, 报告没有待反馈中高风险, projectCode=[{}]\", projectDO.getProjectCode());\n                return;\n            }\n\n            countJson.put(\"highNoProcessed\", highNoProcessed);\n            countJson.put(\"mediumNoProcessed\", mediumNoProcessed);\n            countJson.put(\"noProcessed\", noProcessed);\n\n            // 已经满足通知主动订阅用户群的条件\n            notifyContext.setNotifySubscribePass(true);\n\n            Binding binding = new Binding();\n            binding.setProperty(\"projectDO\", projectDO);\n            binding.setProperty(\"inputDO\", inputDO);\n            // 支持字段: \"pr4dev2sit\", \"currentStage\", \"relativeUsers\", \"iteration\", \"platform\", \"ciTriggerScene\", \"tenant\"\n            binding.setProperty(\"extInfo\", notifyContext.getInputExtInfo());\n            // 风险统计\n            binding.setProperty(\"riskCount\", countJson);\n\n            if (StringUtils.equals(CommitFromEnum.ODC.getCode(), inputDO.getCommitFrom()) && notifyContext.getReportCountVO().getHitCustomizedItemCount() > 0) {\n                log.info(\"[通知决策] (通过) 满足ODC变更通知条件, projectCode=[{}], notifyConfigVO=[{}]\", projectDO.getProjectCode(), JsonUtils.toJson(notifyConfigVO));\n                notifyContext.setNotifyDbCommitterPass(true);\n                return;\n            }\n\n            // 报告没有配置通知规则，同时项目未命中定制规则（命中需通知）\n            if (StringUtils.isBlank(notifyConfigVO.getNotifyExpr()) && notifyContext.getReportCountVO().getHitCustomizedItemCount() <= 0) {\n                log.info(\"[通知决策] (拒绝) 忽略通知报告, 没有找到通知规则配置, projectCode=[{}], iteration=[{}]\", projectDO.getProjectCode(), projectDO.getAppName());\n                return;\n            }\n\n            Object result = scriptExecutor.executeExpr(notifyConfigVO.getNotifyExpr(), binding);\n            if (result instanceof Boolean) {\n                if ((boolean) result) {\n                    log.info(\"[通知决策] (通过) 满足通知条件, projectCode=[{}], notifyConfigVO=[{}]\", projectDO.getProjectCode(), JsonUtils.toJson(notifyConfigVO));\n                    notifyContext.setNotifyCommitterPass(true);\n                } else {\n                    log.info(\"[通知决策] (拒绝) 忽略通知报告, 通知规则不通过, projectCode=[{}], notifyConfigVO=[{}], context={}\", projectDO.getProjectCode(), JsonUtils.toJson(notifyConfigVO), JsonUtils.toJsonString(binding));\n                }\n            } else {\n                log.info(\"[通知决策] (拒绝) 忽略通知报告, 通知规则没有返回bool类型, projectCode=[{}], notifyConfigVO=[{}], context={}\", projectDO.getProjectCode(), JsonUtils.toJson(notifyConfigVO), JsonUtils.toJsonString(binding));\n            }\n\n            //报告评分>指定分数，或包含高风险规则,设置提交人被动通知\n            BigDecimal score = BigDecimal.valueOf(itacSystemConfigDAOClient.getLongValue(SystemConfigConst.GLOBAL_NOTIFY_PROJECT_SCORE));\n            boolean includeHigh = itacSystemConfigDAOClient.isTrue(SystemConfigConst.GLOBAL_NOTIFY_PROJECT_SCORE_INCLUDE_HIGH);\n            if (CompareUtils.compare(projectDO.getScore(), score) > 0 || (includeHigh && highNoProcessed > 0)) {\n                log.info(\"[通知决策] (通过) 满足评分通知条件, projectCode=[{}], notifyConfigVO=[{}]\", projectDO.getProjectCode(), JsonUtils.toJson(notifyConfigVO));\n                notifyContext.setNotifyCommitterPass(true);\n            } else {\n                log.info(\"[通知决策] (拒绝) 忽略评分通知报告, 通知规则不通过, projectCode=[{}], notifyConfigVO=[{}], context={}\", projectDO.getProjectCode(), JsonUtils.toJson(notifyConfigVO), JsonUtils.toJsonString(binding));\n            }\n\n            // 迭代报告命中定制规则，需要通知\n            if (notifyContext.getReportCountVO().getHitCustomizedItemCount() > 0 && !StringUtils.isBlank(projectDO.getIteration())) {\n                log.info(\"[通知决策] (通过) 满足定制规则通知条件, projectCode=[{}], notifyConfigVO=[{}]\", projectDO.getProjectCode(), JsonUtils.toJson(notifyConfigVO));\n                notifyContext.setNotifyCommitterPass(true);\n            }\n\n        } catch (Throwable e) {\n            log.info(\"[通知决策] (拒绝) 通知决策异常, projectCode=[{}], errorMsg=[{}]\", projectDO.getProjectCode(), e.getMessage(), e);\n        }\n    }\n\n    /**\n     * 决策是否需要通知报告, 如果需要通知则建立通知请求\n     * 通知对象:\n     * 1. 代码提交者 (默认订阅)\n     * 2. 订阅通知者\n     */\n    @Override\n    public void notifyReport(ItacAnalysisProjectDO projectDO) {\n        try {\n            log.info(\"[Notify] 通知报告结果, projectCode=[{}]\", projectDO.getProjectCode());\n\n            if (itacSystemConfigDAOClient.isFalse(SystemConfigConst.GLOBAL_NOTIFY_ENABLE)) {\n                log.info(\"[Notify] 全局关闭通知, projectCode=[{}]\", projectDO.getProjectCode());\n                return;\n            }\n            ItacNotifyInfoParam notifyParam = new ItacNotifyInfoParam();\n            notifyParam.createCriteria().andProjectCodeEqualTo(projectDO.getProjectCode());\n            ItacNotifyInfoDO notifyInfoDO = itacNotifyInfoDAOClient.selectOne(notifyParam);\n            if (null != notifyInfoDO) {\n                log.info(\"[Notify] 忽略通知报告, 报告不可重复发送, projectCode=[{}]\", projectDO.getProjectCode());\n                return;\n            }\n\n            ItacAnalysisIterationInputDO inputDO = itacAnalysisIterationInputDAOClient.checkOne(projectDO.getProjectCode());\n            JSONObject extInfo = JsonUtils.fromJsonNonNull(inputDO.getExtInfo());\n\n            ItacAnalysisAppDO appDO = itacAnalysisAppDAOClient.checkOneByAppName(inputDO.getAppName());\n            NotifyConfigVO notifyConfigVO = getNotifyConfig(appDO);\n\n            NotifyContext notifyContext = new NotifyContext();\n            notifyContext.setNotifyId(UUIDUtils.newId(\"nyi_\"));\n            notifyContext.setProjectDO(projectDO);\n            notifyContext.setAppDO(appDO);\n            notifyContext.setInputDO(inputDO);\n            notifyContext.setInputExtInfo(extInfo);\n            notifyContext.setNotifyConfigVO(notifyConfigVO);\n\n            List<SubscribeVO> findSubscribeVOList = loadReportSubscribeList(notifyContext);\n            if (CollectionUtils.isEmpty(findSubscribeVOList)) {\n                log.info(\"[Notify] 忽略通知报告, 没有找到订阅数据, projectCode=[{}], subscribeCount=[{}]\", projectDO.getProjectCode(), findSubscribeVOList.size());\n                return;\n            }\n\n            List<SubscribeVO> subscribeVOList = filterByNotifyMute(inputDO, findSubscribeVOList);\n            if (CollectionUtils.isEmpty(findSubscribeVOList)) {\n                log.info(\"[Notify] 忽略通知报告, 没有找到订阅数据 (静音过滤之后), projectCode=[{}], subscribeCount=[{}]\", projectDO.getProjectCode(), subscribeVOList.size());\n                return;\n            }\n\n            // 发送钉钉\n            List<SubscribeVO> dingTalkVOList = subscribeVOList.stream().filter(i -> i.getChannelList().contains(SubscribeChannelConst.DINGTALK)).collect(Collectors.toList());\n            NotifyDingTalkVO dingTalkResultVO = doNotifyReportDingtalk(notifyContext, dingTalkVOList);\n            // 发送邮件\n            List<SubscribeVO> emailVOList = subscribeVOList.stream().filter(i -> i.getChannelList().contains(SubscribeChannelConst.EMAIL)).collect(Collectors.toList());\n            NotifyEmailVO emailResultVO = doNotifyReportEmail(notifyContext, emailVOList);\n\n            // 统计通知人数\n            long cntAccount = subscribeVOList.stream().map(SubscribeVO::getUserId).distinct().count();\n\n            List<String> channelList = new ArrayList<>();\n            ObjectUtils.ifTrue(dingTalkResultVO.isSuccess(), () -> channelList.add(SubscribeChannelConst.DINGTALK));\n            ObjectUtils.ifTrue(emailResultVO.isSuccess(), () -> channelList.add(SubscribeChannelConst.EMAIL));\n\n            // 发送后直接入库\n            ItacNotifyInfoDO record = new ItacNotifyInfoDO();\n            record.setGmtCreate(new Date());\n            record.setGmtModified(new Date());\n            record.setNotifyId(notifyContext.getNotifyId());\n            record.setChannel(StringUtils.join(channelList, \",\"));\n            record.setProjectCode(projectDO.getProjectCode());\n            record.setGmtNotify(new Date());\n            record.setAppName(inputDO.getAppName());\n            record.setIteration(inputDO.getIteration());\n            record.setRepoUrl(inputDO.getRepoUrl());\n            record.setMsgId(emailResultVO.getMsgId());\n            record.setMailStatus(emailResultVO.isSuccess() ? ItacNotifyStatusEnum.SUCCESS.getCode() : ItacNotifyStatusEnum.FAIL.getCode());\n            record.setToAccount(StringUtils.join(emailResultVO.getEmailList(), \",\"));\n            record.setDingMsgId(dingTalkResultVO.getMsgId());\n            record.setDingStatus(dingTalkResultVO.isSuccess() ? ItacNotifyStatusEnum.SUCCESS.getCode() : ItacNotifyStatusEnum.FAIL.getCode());\n            record.setToDingAccount(StringUtils.join(dingTalkResultVO.getUserIdList(), \",\"));\n            record.setState((emailResultVO.isSuccess() || dingTalkResultVO.isSuccess()) ? ItacNotifyStatusEnum.SUCCESS.getCode() : ItacNotifyStatusEnum.FAIL.getCode());\n            record.setCntAccount((int) cntAccount);\n            itacNotifyInfoDAOClient.insert(record);\n            insertNotifyUser(record, subscribeVOList);\n        } catch (Throwable e) {\n            log.info(\"[Notify] 通知异常, projectCode=[{}], errorMsg=[{}]\", projectDO.getProjectCode(), e.getMessage(), e);\n        }\n    }\n\n    /**\n     * 获取钉钉通知内容\n     */\n    @Override\n    public DingTalkMsg renderDingText(NotifyContext notifyContext) {\n        ItacAnalysisProjectDO projectDO = notifyContext.getProjectDO();\n        ItacAnalysisIterationInputDO inputDO = notifyContext.getInputDO();\n        int riskHighMediumCount = notifyContext.getReportCountVO().getHighHitcount() + notifyContext.getReportCountVO().getMediumHitcount();\n        String dingTemplate = itacSystemConfigDAOClient.getValue(StringUtils.isBlank(inputDO.getIteration()) ? SystemConfigConst.GLOBAL_NOTIFY_DING_TEMPLATE : SystemConfigConst.GLOBAL_NOTIFY_DING_TEMPLATE_WITH_ITERATION);\n        DingTalkMsg msg = new DingTalkMsg();\n        msg.setTitle(MsgUtils.format(\"测试分析报告:{}\", inputDO.getAppName()));\n        msg.setText(MsgUtils.format(dingTemplate, riskHighMediumCount, projectDO.getProjectCode(), inputDO.getAppName(), inputDO.getIteration()));\n        return msg;\n    }\n\n    /**\n     * 发送钉钉通知\n     */\n    public NotifyDingTalkVO doNotifyReportDingtalk(NotifyContext notifyContext, List<SubscribeVO> dingTalkVOList) {\n        NotifyDingTalkVO ret = new NotifyDingTalkVO();\n\n        ItacAnalysisProjectDO projectDO = notifyContext.getProjectDO();\n\n        if (CollectionUtils.isEmpty(dingTalkVOList)) {\n            log.info(\"[Notify] (钉钉) 忽略通知报告, 没有找到订阅数据, projectCode=[{}]\", projectDO.getProjectCode());\n            return ret;\n        }\n\n        // 钉钉去重后通知\n        List<String> dingTalkList = dingTalkVOList.stream().map(SubscribeVO::getToDingtalk).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());\n        if (CollectionUtils.isEmpty(dingTalkList)) {\n            return ret;\n        }\n\n        DingTalkMsg msg = renderDingText(notifyContext);\n        SampleActionCardMsg params = new SampleActionCardMsg();\n        params.setTitle(msg.getTitle());\n        params.setText(msg.getText());\n        params.setSingleTitle(\"点击查看报告\");\n        params.setSingleURL(getReportUrl(projectDO.getProjectCode(), SubscribeChannelConst.DINGTALK, notifyContext.getNotifyId()));\n        params.setOpenBrowser(true);\n\n        ret.setUserIdList(dingTalkList);\n        ret.setBody(params.getText());\n        // 调试模式下不真实发送钉钉\n        if (itacSystemConfigDAOClient.isTrue(SystemConfigConst.GLOBAL_NOTIFY_DEBUG_MODE)) {\n            ret.setSuccess(true);\n            ret.setMsgId(UUIDUtils.newId(\"ding_\"));\n            return ret;\n        }\n        log.info(\"[Notify] (钉钉) 通知报告, projectCode=[{}]\", projectDO.getProjectCode());\n        SendMsgResultVO resultVO = aliDingTalkClient.sendSampleActionCardMsg(dingTalkList, params);\n        ret.setSuccess(resultVO.isSuccess());\n        ret.setMsgId(resultVO.getProcessQueryKey());\n        return ret;\n    }\n\n    /**\n     * 发送邮件通知\n     */\n    public NotifyEmailVO doNotifyReportEmail(NotifyContext notifyContext, List<SubscribeVO> emailVOList) {\n        if (CollectionUtils.isEmpty(emailVOList)) {\n            log.info(\"[Notify] (邮件) 忽略通知报告, 没有找到订阅数据, projectCode=[{}]\", notifyContext.getProjectDO().getProjectCode());\n            return new NotifyEmailVO();\n        }\n        // 邮件去重后通知\n        List<String> emailList = emailVOList.stream().map(SubscribeVO::getToEmail).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());\n        NotifyEmailReq req = new NotifyEmailReq();\n        req.setNotifyId(notifyContext.getNotifyId());\n        req.setProjectCode(notifyContext.getProjectDO().getProjectCode());\n        req.setEmailList(emailList);\n\n        // 调试模式下不真实发送邮件\n        req.setDebugMode(itacSystemConfigDAOClient.isTrue(SystemConfigConst.GLOBAL_NOTIFY_DEBUG_MODE));\n        log.info(\"[Notify] (邮件) 通知报告, projectCode=[{}]\", req.getProjectCode());\n        return renderAndSendEmail(req);\n    }\n}",
      "selectedContent": "public void notifyReport(ItacAnalysisProjectDO projectDO) {"
    }
  ],
  "id": 1
}


###
POST http://{{SERVER_IP}}:10010
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "method": "query_related_data",
  "params": [
    {
      "sessionId": "123",
      "projectUrl": "D:\\tinghe-source\\smart-server",
      "fileUrl": "D:\\tinghe-source\\smart-server\\app\\biz\\service\\impl\\src\\main\\java\\com\\alipay\\smart\\server\\biz\\impl\\AlgoTaskServiceImpl.java",
      "language": "java",
      "intention": "CODE_GENERATE_TEST",
      "intentionType": "AUTO",
      "fileContent": "package com.alipay.smart.server.biz.impl;\n\nimport com.alipay.smart.server.facade.AlgoTaskService;\nimport com.alipay.smart.server.facade.algotask.AlgoTaskQO;\nimport com.alipay.smart.server.facade.algotask.AlgoTaskVO;\nimport com.alipay.smart.server.facade.quickapi.model.CommonObjectResult;\nimport com.alipay.smart.server.service.algotask.AlgoTaskClient;\nimport com.alipay.smart.server.service.quickapi.template.CommonTemplate;\nimport com.alipay.sofa.runtime.api.annotation.SofaService;\nimport com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;\nimport lombok.extern.slf4j.Slf4j;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\n\nimport java.util.List;\n\n@Slf4j\n@Service\n@SofaService(bindings={@SofaServiceBinding(bindingType = \"tr\")})\npublic class AlgoTaskServiceImpl implements AlgoTaskService {\n    @Autowired\n    CommonTemplate commonTemplate;\n    @Autowired\n    AlgoTaskClient algoTaskClient;\n\n    @Override\n    public CommonObjectResult<AlgoTaskVO> createAlgoTask(AlgoTaskVO req) {\n        return commonTemplate.operate(req, () -> algoTaskClient.createAlgoTask(req));\n    }\n\n    @Override\n    public CommonObjectResult<List<AlgoTaskVO>> listAlgoTask(AlgoTaskQO req) {\n        return commonTemplate.operate(req, () -> algoTaskClient.listAlgoTask(req));\n    }\n\n    @Override\n    public CommonObjectResult<AlgoTaskVO> getAlgoTask(AlgoTaskQO req) {\n        return commonTemplate.operate(req, () -> algoTaskClient.getAlgoTask(req));\n    }\n\n    @Override\n    public CommonObjectResult<AlgoTaskVO> stopAlgoTask(AlgoTaskQO req) {\n        return commonTemplate.operate(req, () -> algoTaskClient.stopAlgoTask(req));\n    }\n}\n",
      "selectedContent": "public CommonObjectResult<AlgoTaskVO> createAlgoTask(AlgoTaskVO req) {"
    }
  ],
  "id": 1
}


###
POST http://{{SERVER_IP}}:10010
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "method": "scan_project",
  "params": [
    {
      "url": "D:\\tinghe-source\\rcqualitydataprod"
    }
  ],
  "id": 1
}


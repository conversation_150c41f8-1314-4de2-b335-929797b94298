java.lang.AbstractMethodError
java.lang.AbstractStringBuilder
java.lang.Appendable
java.lang.ApplicationShutdownHooks
java.lang.ArithmeticException
java.lang.ArrayIndexOutOfBoundsException
java.lang.ArrayStoreException
java.lang.AssertionError
java.lang.AssertionStatusDirectives
java.lang.AutoCloseable
java.lang.Boolean
java.lang.BootstrapMethodError
java.lang.Byte
java.lang.Character
java.lang.CharacterData
java.lang.CharacterData0E
java.lang.CharacterData00
java.lang.CharacterData01
java.lang.CharacterData02
java.lang.CharacterDataLatin1
java.lang.CharacterDataPrivateUse
java.lang.CharacterDataUndefined
java.lang.CharacterName
java.lang.CharSequence
java.lang.Class
java.lang.ClassCastException
java.lang.ClassCircularityError
java.lang.ClassFormatError
java.lang.ClassLoader
java.lang.ClassLoaderHelper
java.lang.ClassNotFoundException
java.lang.ClassValue
java.lang.Cloneable
java.lang.CloneNotSupportedException
java.lang.Comparable
java.lang.Compiler
java.lang.ConditionalSpecialCasing
java.lang.Deprecated
java.lang.Double
java.lang.Enum
java.lang.EnumConstantNotPresentException
java.lang.Error
java.lang.Exception
java.lang.ExceptionInInitializerError
java.lang.Float
java.lang.FunctionalInterface
java.lang.IllegalAccessError
java.lang.IllegalAccessException
java.lang.IllegalArgumentException
java.lang.IllegalMonitorStateException
java.lang.IllegalStateException
java.lang.IllegalThreadStateException
java.lang.IncompatibleClassChangeError
java.lang.IndexOutOfBoundsException
java.lang.InheritableThreadLocal
java.lang.InstantiationError
java.lang.InstantiationException
java.lang.Integer
java.lang.InternalError
java.lang.InterruptedException
java.lang.Iterable
java.lang.LinkageError
java.lang.Long
java.lang.Math
java.lang.NegativeArraySizeException
java.lang.NoClassDefFoundError
java.lang.NoSuchFieldError
java.lang.NoSuchFieldException
java.lang.NoSuchMethodError
java.lang.NoSuchMethodException
java.lang.NullPointerException
java.lang.Number
java.lang.NumberFormatException
java.lang.Object
java.lang.OutOfMemoryError
java.lang.Override
java.lang.Package
java.lang.Process
java.lang.ProcessBuilder
java.lang.ProcessEnvironment
java.lang.ProcessImpl
java.lang.Readable
java.lang.ReflectiveOperationException
java.lang.Runnable
java.lang.Runtime
java.lang.RuntimeException
java.lang.RuntimePermission
java.lang.SafeVarargs
java.lang.SecurityException
java.lang.SecurityManager
java.lang.Short
java.lang.Shutdown
java.lang.StackOverflowError
java.lang.StackTraceElement
java.lang.StrictMath
java.lang.String
java.lang.StringBuffer
java.lang.StringBuilder
java.lang.StringCoding
java.lang.StringIndexOutOfBoundsException
java.lang.SuppressWarnings
java.lang.System
java.lang.SystemClassLoaderAction
java.lang.Terminator
java.lang.Thread
java.lang.ThreadDeath
java.lang.ThreadGroup
java.lang.ThreadLocal
java.lang.Throwable
java.lang.TypeNotPresentException
java.lang.UNIXProcess
java.lang.UnknownError
java.lang.UnsatisfiedLinkError
java.lang.UnsupportedClassVersionError
java.lang.UnsupportedOperationException
java.lang.VerifyError
java.lang.VirtualMachineError
java.lang.Void
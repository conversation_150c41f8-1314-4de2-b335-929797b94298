[package]
name = "agent_common_service"
version = "0.2.0"
edition = "2021"

[dependencies]
anyhow = "1.0.86"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = { version = "1.0.140", features = [] }
once_cell = "1.19.0"
tokio = { version = "1.45.1", features = ["full"] }
log = "0.4.22"
tokenizers = "0.21.0"
rayon = "1.10.0"
quick-xml = {version = "0.36.1", features = ["serialize"]}
regex = "1.11.1"
uuid = { version = "1.0", features = ["v4"] }

#agent_db = { git = "ssh://*******************/code-generator-group/agent_db.git", tag = "v0.2.0" }
agent_db = { path = "../agent_db" }
git2 = "0.20.2"

[dev-dependencies]
env_logger = "0.10"


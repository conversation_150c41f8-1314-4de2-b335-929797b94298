[package]
name = "agent_android_service"
version = "0.0.1"
edition = "2021"

[dependencies]
agent_common_service = { path = "../agent_common_service" }
agent_db = { path = "../agent_db" }

tree-sitter = "0.22.6"
tree-sitter-java = "0.21.0"
tree-sitter-kotlin = "0.3.8"
once_cell = "1.19.0"
anyhow = "1.0.86"
log = "0.4.22"
serde_json = { version = "1.0.114", features = [] }
ignore = "0.4.22"
tokio = { version = "1.38.0", features = ["full"] }
rayon = "1.10.0"
serde = { version = "1.0.207", features = ["derive"] }


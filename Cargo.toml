[package]
name = "agent_gentests_service"
version = "0.1.0"
edition = "2021"

[dependencies]
log = "0.4.22"
log4rs = "1.3.0"
tree-sitter = "0.22.6"
tree-sitter-java = "0.21.0"
serde = { version = "1.0.219", features = ["derive", "rc"] }
serde_json = { version = "1.0.140", features = [] }
once_cell = "1.19.0"
ignore = "0.4.22"
indexmap = "2.2.6"
regex = "1.10.6"
anyhow = "1.0.86"
quick-xml = "0.36.1"
serde_yaml = "0.9.34"
reqwest = { version = "0.11.23", features = ["rustls-tls","blocking","json"] }
tokio = {version = "1.45.1", features = ["full"]}
pathdiff = "0.2"

#agent_common_service = { git = "ssh://*******************/code-generator-group/agent_common_service.git", tag = "v0.2.0" }
#agent_codefuse_service = { git = "ssh://*******************/code-generator-group/agent_codefuse_service.git", tag = "v0.3.0" }
#agent_db = { git = "ssh://*******************/code-generator-group/agent_db.git", tag = "v0.2.0" }

agent_db = { path = "../agent_db" }
agent_common_service = { path = "../agent_common_service" }
agent_codefuse_service = { path = "../agent_codefuse_service" }

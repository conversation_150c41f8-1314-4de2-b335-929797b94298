[package]
name = "agent_db"
version = "0.2.0"
edition = "2021"

[dependencies]
log4rs = "1.3.0"
lancedb = "0.21.3"
arrow-array = "55.2.0"
arrow-schema = "55.2.0"
arrow-buffer = "55.2.0"
rocksdb = {version = "0.22.0",features = []}
anyhow = "1.0.86"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = { version = "1.0.140", features = [] }
once_cell = "1.19.0"
dirs-next = "2.0.0"
log = "0.4.22"
clap = { version = "4.5.1", features = ["derive"] }
tantivy = "0.22.0"
reqwest = { version = "0.11.23", default-features = false, features = ["rustls-tls","json","blocking"] }
tokio = { version = "1.45.1", features = ["full"] }
futures = "0.3"
sys-info = "0.9.1"
difflib = "0.4.0"
tempfile = "3.8"
rand = "0.8"
urlencoding = "2.1.3"
chrono = "0.4.41"
env_logger = "0.11.8"


[dev-dependencies]
tokio = { version = "1.45.1", features = ["full"] }

[package]
name = "agent_codefuse_service"
version = "0.3.0"
edition = "2021"

[dependencies]
anyhow = "1.0.86"
once_cell = "1.19.0"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = { version = "1.0.140", features = [] }
log = "0.4.22"
ignore = "0.4.22"
tree-sitter = "0.22.6"
tree-sitter-java = "0.21.0"
tree-sitter-typescript = "0.21.2"
# tree-sitter-javascript = "0.21.4"
tree-sitter-javascript = { git = "ssh://*******************/winjo.gwj/tree-sitter-javascript.git", tag = "v0.21.5" } # fix cc version
oxc_resolver = "1.10.2"
tokio = { version = "1.45.1", features = ["full"] }
tokio-util = "0.7"
rayon = "1.10.0"

regex = "1.11.1"
tantivy = "0.22.0"
crunchy = "0.2.4"
lazy_static = "1.5.0"
blake3 = "1.5.5"
futures = "0.3.31"
num_cpus = "1.16.0"
rand = "0.9.1"

similar = "2.3"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"

#agent_db = { git = "ssh://*******************/code-generator-group/agent_db.git", tag = "v0.2.0" }
#agent_common_service = { git = "ssh://*******************/code-generator-group/agent_common_service.git", tag = "v0.2.0" }

agent_db = { path = "../agent_db" }
agent_common_service = { path = "../agent_common_service" }
agent_android_service = { path = "../agent_android_service" }
uuid = { version = "1.14.0", features = ["v4"] }
encoding_rs = "0.8"
chardet = "0.2"

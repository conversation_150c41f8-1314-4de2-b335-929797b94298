use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use agent_common_service::model::chat_model::{ChatFlowStatusEnum, SymbolAndStreamSearchResult};
use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;
use agent_db::remote::rpc_model::BaseResponse;
use log::info;
use rand::random_bool;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use tokio::sync::Mutex;
use crate::deepsearchV2::searchrouter_data::TypeEnum::THINK;

//问题拆分
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct SearchRouterRequestLLMData {
    pub model: String,
    pub messages: Vec<ChatMessageModel>,
    pub stream: bool,
    pub top_p: f64,
    pub top_k: usize,
    pub temperature: f64,
    pub chat_template_kwargs: ChatTemplateKwargs
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct ChatTemplateKwargs {
    pub enable_thinking: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchRouterFlowContext {
    //questionUid
    pub question_uid: String,
    //项目地址
    pub project_url: String,
    //分支
    pub branch: String,
    //会话Id
    pub sessionId: String,
    //用户原始query
    pub query: String,
    // query
    pub changedQuery: Option<String>,
    //explanation
    pub explanation: Option<String>,
    //subquery列表
    pub sub_query_list: Vec<QeuryAndRetrievalTool>,
    //new subquery列表
    pub new_sub_query_list: Vec<QeuryAndRetrievalTool>,
    // 响应query的文案
    pub think_response_content: VecDeque<Vec<DeepsearchThinkChildContent>>,
    // 给前端展示的数据，队列展示query result
    pub result_response_content: VecDeque<Vec<ChatRelatedCodeModel>>,
    //控制think 和 search_result交替出现
    pub is_think_type: bool,
    // 检索到的数据
    pub rag_segment_code_ist: Vec<Vec<ChatRelatedCodeModel>>,
    // 拼装后的rag数据，给deepsearch请求最后的答案
    pub combined_rag_code_data: Vec<ChatRelatedCodeModel>,
    // ap格式，供augment模式使用的格式
    // pub ap_formatted_code: Vec<ResultDataToAugment>,
    // ap格式，供augment模式使用的格式, 云辰要求的格式
    pub ap_formatted_code_v2: String,
    // 非web_search_result
    pub code_result_list:  Vec<Vec<ChatRelatedCodeModel>>,
    // web_search_result
    pub web_result_list:  Vec<Vec<ChatRelatedCodeModel>>,
    //是否是一个新query
    pub query_status: QeuryStatus,
    // 改造后的响应文案
    pub step_think_response_content: VecDeque<Vec<DeepsearchThinkChildContent>>,
    // deepsearch改造后result文案
    pub step_result_response_content: VecDeque<Vec<ChatRelatedCodeModel>>,
    // 新改造过渡文案
    pub step_transition_response_content: VecDeque<Vec<DeepsearchThinkChildContent>>,
    //新改造的状态,老的还要兼容比较恶心
    pub current_result_type: TypeEnum,
    //耗时统计
    pub deepsearch_time_counter: DeepsearchTimeCounter,
}

impl SearchRouterFlowContext {
    pub fn new() -> Self {
        SearchRouterFlowContext{
            question_uid: "".to_string(),
            project_url: "".to_string(),
            branch: "".to_string(),
            sessionId: "".to_string(),
            query: "".to_string(),
            changedQuery: None,
            explanation: None,
            sub_query_list: vec![],
            new_sub_query_list: vec![],
            think_response_content: Default::default(),
            result_response_content: Default::default(),
            is_think_type: true,
            rag_segment_code_ist: vec![],
            combined_rag_code_data: vec![],
            // ap_formatted_code: vec![],
            ap_formatted_code_v2: "".to_string(),
            code_result_list: vec![],
            web_result_list: vec![],
            query_status: QeuryStatus::FULL_NEW,
            step_think_response_content: Default::default(),
            step_result_response_content: Default::default(),
            step_transition_response_content: Default::default(),
            current_result_type: THINK,
            deepsearch_time_counter: DeepsearchTimeCounter::new(),
        }
    }
}

//拆分的sub query以及对应的retrieval_tool
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QeuryAndRetrievalTool {
    pub retrieval_tool: Option<String>,
    pub question: Option<String>,
    pub class: Option<String>,
    pub method: Option<String>,
    pub reasoning: Option<String>,
}

//websearch检索的单元数据结构
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebSearchResultItem {
    pub title: String,
    pub url: String,
    pub doc: String,
    pub searchDocAbstract: SearchDocAbstract,
    pub relScore: String,
    pub authorityScore: String,
    pub searchScoreInfo: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchDocAbstract{
    pub abstractOffline: String,
    pub abstractOnline: String,
    pub snippet: String,
}
//websearch的请求数据结构
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebSearchRequestBean {
    pub query: String,
    pub pluginVersion: String,
    pub productType: String,
    pub userToken: String,
    pub ideVersion: String,
    pub size: usize,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebSearchTransferData {
    pub id: String,
    pub title: String,
    pub url: String,
    pub segment: String,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebSearchFilterResItemData {
    pub id: String,
    pub title: String,
    pub url: String,
    pub reasoning: String,
}

//不同任务的类型统一
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum DispatcherTaskResult {
    Codebase_Search(Vec<ChatRelatedCodeModel>),
    Web_Search(Vec<WebSearchTransferData>),
    Symbol_Search(Vec<SymbolAndStreamSearchResult>),
    Upstream_Search(Vec<SymbolAndStreamSearchResult>)
}

//最后给到augment的数据
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ResultDataToAugment {
    pub query: Option<String>,
    pub tool: Option<String>,
    pub className: Option<String>,
    pub methodName: Option<String>,
    pub results: Vec<ChatRelatedCodeModel>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct ChatMessageModel {
    pub role: String,
    pub content: String,
}
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct BailingChatRequestModel {
    pub model: String,
    pub messages: Vec<ChatMessageModel>,
    pub stream: bool,
    pub maxTokens: usize,
    pub temperature: f64,
    pub topP: f64,
    pub topK: usize,
    pub traceId: String,
}

///deepsearch的返回
pub fn build_deepsearch_response<T>(data: T) -> BaseResponse<T> {
    BaseResponse {
        errorCode: 0,
        errorMsg: None,
        data: Some(data),
        totalCount: None,
    }
}
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct DeepsearchThinkChildContent{
    pub snippet: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct DeepsearchChatResponse<T> {
    pub r#type: Option<TypeEnum>,
    pub content: Option<T>,
    pub subquestions: Option<Vec<String>>,
    pub nlcontent: Option<String>,
    pub chatStatus: Option<ChatFlowStatusEnum>,
    pub necessary_index_percent: Option<u8>,
    pub questionUid: Option<String>,
    pub sessionId: Option<String>,
    pub step: Option<StepResponseResult>,
}

#[derive(Serialize, Deserialize, Hash, Eq, PartialEq, Debug, Clone)]
pub enum TypeEnum {
    //思考的问题
    THINK,
    //阶段的结果
    SEARCH_RESULT,
    //结束
    END,
    //过渡
    TRANSITION,
}

//耗时纪录埋点
#[derive(Serialize, Deserialize, Hash,  Debug, Clone)]
pub struct DeepsearchTimeCounter {
    //build
    pub buildTime: u128,
    //拆分
    pub understandQueryTime: u128,
    //改写query
    pub changeQueryTime: Vec<u128>,
    //codebase检索
    // codebaseSearchTime = queryChangeTime + bm25SearchTime + vectorSearchTime + filterTime
    pub codebaseSearchTime: Vec<u128>,
    // bm25检索
    pub bm25SearchTime: Vec<u128>,
    // 向量检索
    pub vectorSearchTime: Vec<u128>,
    // 过滤时间
    pub filterTime: Vec<u128>,
    //web检索
    pub webSearchTime: Vec<u128>,
    // symbol检索
    pub symbolSearchTime: Vec<u128>,
    // upstream检索
    pub upstreamSearchTime: Vec<u128>,
    //否要新一轮检索
    pub newQueryTime:Vec<u128>,
    //一轮检索耗时
    pub newStageTime:Vec<u128>,
    //全流程耗时
    pub totalCostTime: u128,
    // 新对话开始
    pub startTime: u128,
    // 对话结束
    pub endTime: u128,
    //web检索filter时间
    pub webSearchFilterTime: Vec<u128>,
}

impl DeepsearchTimeCounter {
    pub fn new() -> DeepsearchTimeCounter {
        Self{
            buildTime: 0,
            understandQueryTime: 0,
            changeQueryTime: vec![],
            codebaseSearchTime: vec![],
            bm25SearchTime: vec![],
            webSearchTime: vec![],
            symbolSearchTime: vec![],
            filterTime: vec![],
            newQueryTime: vec![],
            newStageTime: vec![],
            totalCostTime: 0,
            startTime: 0,
            endTime: 0,
            upstreamSearchTime: vec![],
            vectorSearchTime: vec![],
            webSearchFilterTime: vec![],
        }
    }
    pub fn clear(&mut self) {
        self.understandQueryTime = 0;
        self.changeQueryTime.clear();
        self.codebaseSearchTime.clear();
        self.webSearchTime.clear();
        self.symbolSearchTime.clear();
        self.upstreamSearchTime.clear();
        self.filterTime.clear();
        self.newQueryTime.clear();
        self.vectorSearchTime.clear();
        self.bm25SearchTime.clear();
        self.totalCostTime = 0;
        self.newStageTime.clear();
    }
}
pub async fn set_understand_time(understand_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.understandQueryTime = understand_time;
}
pub async fn set_total_cost_time(total_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.totalCostTime = total_time;
}
//增加query改写时间
pub async fn add_change_query_time(search_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.changeQueryTime.push(search_time);
}
//增加codebase检索耗时
pub async fn add_codebasesearch_time(search_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.codebaseSearchTime.push(search_time);
}
//queryChangeTime + bm25SearchTime + vectorSearchTime + filterTime
//bm25search
pub async fn add_bm25search_time(bm25search_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.bm25SearchTime.push(bm25search_time);
}
//vectorsearch
pub async fn add_vectorsearch_time(vectorsearch_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.vectorSearchTime.push(vectorsearch_time);
}
//增加web检索耗时
pub async fn add_websearch_time(search_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.webSearchTime.push(search_time);
}
//增加web检索耗时
pub async fn add_websearch_filter_time(web_search_filter_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.webSearchFilterTime.push(web_search_filter_time);
}
//增加symbol检索耗时
pub async fn add_symbolsearch_time(search_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.symbolSearchTime.push(search_time);
}
//增加upstream检索耗时
pub async fn add_upstreamsearch_time(search_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.upstreamSearchTime.push(search_time);
}
//增加过滤耗时
pub async fn add_filter_time(filter_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.filterTime.push(filter_time);
}
//增加是否启动新一轮检索的时间
pub async fn add_new_query_time(new_query_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.newQueryTime.push(new_query_time);
}
//会话的开始时间
pub async fn set_start_time(start_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.startTime = start_time;
}
//会话的结束时间
pub async fn set_end_time(end_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.endTime = end_time;
}
pub async fn add_onestage_time(one_stage_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.newStageTime.push(one_stage_time);
}
//计算一轮会话总耗时
pub async fn calculate_total_cost_time(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> HashMap<String, String>{
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.totalCostTime = flow_context.deepsearch_time_counter.endTime - flow_context.deepsearch_time_counter.startTime;
    let mut data = HashMap::new();
    data.insert("totalCostTime".to_string(), flow_context.deepsearch_time_counter.totalCostTime.to_string());
    data.insert("buildTime".to_string(), flow_context.deepsearch_time_counter.buildTime.to_string());
    data.insert("understandQueryTime".to_string(), flow_context.deepsearch_time_counter.understandQueryTime.to_string());
    data.insert("codebaseSearchTime".to_string(), calculate_average(flow_context.clone().deepsearch_time_counter.codebaseSearchTime));
    data.insert("webSearchTime".to_string(), calculate_average(flow_context.clone().deepsearch_time_counter.webSearchTime));
    data.insert("symbolSearchTime".to_string(), calculate_average(flow_context.clone().deepsearch_time_counter.symbolSearchTime));
    data.insert("upstreamSearchTime".to_string(), calculate_average(flow_context.clone().deepsearch_time_counter.upstreamSearchTime));
    data.insert("queryChangeTime".to_string(), calculate_average(flow_context.clone().deepsearch_time_counter.changeQueryTime));
    data.insert("bm25SearchTime".to_string(), calculate_average(flow_context.clone().deepsearch_time_counter.bm25SearchTime));
    data.insert("vectorSearchTime".to_string(), calculate_average(flow_context.clone().deepsearch_time_counter.vectorSearchTime));
    data.insert("filterTime".to_string(), calculate_average(flow_context.clone().deepsearch_time_counter.filterTime));
    data.insert("newQueryTime".to_string(), calculate_average(flow_context.clone().deepsearch_time_counter.newQueryTime));
    data.insert("oneStageTime".to_string(), calculate_average(flow_context.clone().deepsearch_time_counter.newStageTime));
    data.insert("webSearchFilterTime".to_string(), calculate_average(flow_context.clone().deepsearch_time_counter.webSearchFilterTime));
    data
}
//设置build的总耗时
pub async fn set_build_time(build_time: u128, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.deepsearch_time_counter.buildTime = build_time;
}
//计算平均值
fn calculate_average (input: Vec<u128>) -> String{
    if input.is_empty() {
        return "0".to_string();
    }
    let sum: u128 = input.iter().sum();
    let average = sum / input.len() as u128;
    average.to_string()
}
#[derive(Serialize, Deserialize, Hash,  Debug, Clone)]
pub enum QeuryStatus {
    //全新
    FULL_NEW,
    //部分更新
    PARTIAL_NEW,
    //和原来的问题一样
    FULL_OLD
}

//没有method name？？？
#[derive(Serialize, Deserialize, Hash,  Debug, Clone)]
pub struct SymbolTransferData {
    pub id: String,
    pub filePath: String,
    pub className: String,
    pub body: String,
}

#[derive(Serialize, Deserialize, Hash,  Debug, Clone)]
pub struct SymbolTransferResItem {
    pub id: String,
    pub reasoning: String,
}

#[derive(Serialize, Deserialize, Hash,  Debug, Clone)]
pub struct UpStreamTransferData {
    pub id: String,
    pub filePath: String,
    pub className: String,
    pub methodName: String,
    pub body: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct StepResponseResult {
    pub r#type: Option<TypeEnum>,
    pub content: Option<serde_json::Value>,
}

impl Default for StepResponseResult {
    fn default() -> Self {
        StepResponseResult{
            r#type: Some(THINK),
            content: Some(Value::Array(vec![])),
        }
    }
}
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use agent_common_service::model::chat_model::{ChatFlowStatusEnum, ChatRelatedRequestBean};
use log::info;
use once_cell::sync::Lazy;
use tokio::sync::Mutex;
use crate::function::chatflow::build_index_task::BuildTaskNode;
use crate::deepsearchV2::searchrouter_data::{set_build_time, set_start_time, DeepsearchTimeCounter, QeuryStatus, SearchRouterFlowContext, TypeEnum};
use crate::deepsearchV2::searchrouter_data::QeuryStatus::{FULL_NEW, PARTIAL_NEW};
use crate::deepsearchV2::searchrouter_data::TypeEnum::THINK;
use crate::deepsearchV2::searchrouter_query_task::SearchRouterAnswerTask;
use crate::deepsearchV2::searchrouter_understand_task::SearchRouterUnderstandTask;
use crate::deepsearchV2::searchrouter_util::{is_new_question, is_partial_new_question, is_searchrouter_same_question};
use crate::function::chat_strategy::{TaskNode, TaskNodeEnum, CHAT_TASK_MANAGER_V2, DEEPSEARCH_TASK_TYPE};
use crate::function::chat_strategy::TaskNodeEnum::END;

pub static DEEPSEARCH_TASK_MANAGER: Lazy<Arc<SearchRouterTaskManager>> =
    Lazy::new(|| Arc::new(SearchRouterTaskManager::new()));

pub struct SearchRouterTaskManager{
    searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>,
    task_status: Mutex<ChatFlowStatusEnum>,
    build_index_task: Arc<BuildTaskNode>,
    understand_task: Arc<SearchRouterUnderstandTask>,
    answer_task: Arc<SearchRouterAnswerTask>,
}

impl SearchRouterTaskManager{
    pub fn new() -> SearchRouterTaskManager{
        Self{
            searchrouter_flow_context: Arc::new(Mutex::new(SearchRouterFlowContext::new())),
            task_status: Mutex::new(ChatFlowStatusEnum::INIT),
            build_index_task: Arc::new(BuildTaskNode::new(DEEPSEARCH_TASK_TYPE)),
            understand_task: Arc::new(SearchRouterUnderstandTask::new()),
            answer_task: Arc::new(SearchRouterAnswerTask::new()),
        }
    }
    async fn update_task_info(&self, deepsearchRequestBean: &ChatRelatedRequestBean) {
        let mut deepsearch_flow_context = self.searchrouter_flow_context.lock().await;
        let deepsearch_request_bean = deepsearchRequestBean.clone();
        deepsearch_flow_context.question_uid = deepsearch_request_bean.questionUid.unwrap();
        deepsearch_flow_context.query = deepsearch_request_bean.query;
        deepsearch_flow_context.project_url = deepsearch_request_bean.projectUrl.unwrap();
        deepsearch_flow_context.branch = deepsearch_request_bean.branch.unwrap();
        deepsearch_flow_context.changedQuery = deepsearch_request_bean.changedQuery;
        deepsearch_flow_context.explanation = deepsearch_request_bean.explanation;
    }

    async fn reset_data(&self) {
        let mut deepsearch_flow_context = self.searchrouter_flow_context.lock().await;
        deepsearch_flow_context.sub_query_list.clear();
        deepsearch_flow_context.new_sub_query_list.clear();
        deepsearch_flow_context.think_response_content.clear();
        deepsearch_flow_context.result_response_content.clear();
        deepsearch_flow_context.rag_segment_code_ist.clear();
        deepsearch_flow_context.combined_rag_code_data.clear();
        deepsearch_flow_context.ap_formatted_code_v2.clear();
        deepsearch_flow_context.code_result_list.clear();
        deepsearch_flow_context.web_result_list.clear();
        deepsearch_flow_context.is_think_type = true;
        deepsearch_flow_context.query_status = FULL_NEW;
        deepsearch_flow_context.current_result_type = THINK;
        deepsearch_flow_context.step_think_response_content.clear();
        deepsearch_flow_context.step_result_response_content.clear();
        deepsearch_flow_context.step_transition_response_content.clear();
        deepsearch_flow_context.deepsearch_time_counter.clear();
    }

    async fn set_query_status(&self, query_status: QeuryStatus) {
        let mut flow_context = self.searchrouter_flow_context.lock().await;
        flow_context.query_status = query_status;
    }
    async fn chage_task_status(&self, status: ChatFlowStatusEnum) {
        let mut task_status = self.task_status.lock().await;
        *task_status = status;
    }
    async fn get_task_status(&self) -> ChatFlowStatusEnum {
        let mut task_status = self.task_status.lock().await;
        task_status.clone()
    }

    async fn pre_check(&self, searchrouter_chat_request_bean: &ChatRelatedRequestBean) {
        if !is_searchrouter_same_question(searchrouter_chat_request_bean, self.searchrouter_flow_context.clone()).await {
            if is_new_question(searchrouter_chat_request_bean, self.searchrouter_flow_context.clone()).await
            {
                //全新问题
                info!("deepsearch pre_check, re-init data since new question");
                self.update_task_info(searchrouter_chat_request_bean).await;
                self.reset_data().await;
                self.chage_task_status(ChatFlowStatusEnum::INIT).await;
                self.build_index_task.reset().await;
                self.answer_task.reset().await;
                self.understand_task.reset().await;
                self.set_query_status(FULL_NEW).await;
                let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                set_start_time(start_time, self.searchrouter_flow_context.clone()).await;
            } else if is_partial_new_question(searchrouter_chat_request_bean, self.searchrouter_flow_context.clone()).await {
                //部分更新
                info!("deepsearch pre_check, re-init data since partial_new question");
                let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                self.update_task_info(searchrouter_chat_request_bean).await;
                self.reset_data().await;
                self.chage_task_status(ChatFlowStatusEnum::INIT).await;
                self.build_index_task.reset().await;
                self.answer_task.reset().await;
                self.understand_task.reset().await;
                self.set_query_status(PARTIAL_NEW).await;
                let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                set_start_time(start_time, self.searchrouter_flow_context.clone()).await;
            }
        }else{
            info!("deepsearch pre_check, do nothong since same question");
        }
    }

    pub async fn process(&self, searchrouter_chat_request_bean: &ChatRelatedRequestBean) -> (ChatFlowStatusEnum, Arc<Mutex<SearchRouterFlowContext>>) {
        self.pre_check(&searchrouter_chat_request_bean.clone()).await;
        match self.get_task_status().await {
            ChatFlowStatusEnum::BUILD_INDEX | ChatFlowStatusEnum::INIT => {
                info!("deepsearch chat task status BUILD_INDEX or INIT");
                self.chage_task_status(ChatFlowStatusEnum::BUILD_INDEX).await;

                let build_index_taks_status = self.build_index_task.start(&searchrouter_chat_request_bean, &mut CHAT_TASK_MANAGER_V2.get_current_context().await).await;
                if build_index_taks_status == END {
                    set_build_time(CHAT_TASK_MANAGER_V2.get_current_context().await.build_consume_time_total, self.searchrouter_flow_context.clone()).await;
                    self.chage_task_status(ChatFlowStatusEnum::UNDERSTAND_QUERY).await;
                    self.understand_task.clone().start(searchrouter_chat_request_bean, self.searchrouter_flow_context.clone()).await;
                    return (self.get_task_status().await, self.searchrouter_flow_context.clone());
                } else {
                    return (self.get_task_status().await, self.searchrouter_flow_context.clone());
                }
            }
            ChatFlowStatusEnum::UNDERSTAND_QUERY => {
                info!("deepsearch chat task status UNDERSTAND_QUERY");
                let understand_task_status = self.understand_task.clone().start(searchrouter_chat_request_bean, self.searchrouter_flow_context.clone()).await;
                if understand_task_status == END {
                    self.chage_task_status(ChatFlowStatusEnum::ANSWER).await;
                    self.answer_task.clone().start(searchrouter_chat_request_bean, self.searchrouter_flow_context.clone()).await;
                    return (self.get_task_status().await, self.searchrouter_flow_context.clone());
                } else {
                    return (self.get_task_status().await, self.searchrouter_flow_context.clone());
                }
            }
            ChatFlowStatusEnum::ANSWER => {
                info!("deepsearch chat task status ANSWER，answer_task status {:?}", self.answer_task.get_status().await);
                let answer_task_status = self.answer_task.clone().start(searchrouter_chat_request_bean, self.searchrouter_flow_context.clone()).await;
            }

            _ => {
                info!("deepsearch chat task status UNKOWN {:?}", self.get_task_status().await);
            }
        }
        (self.get_task_status().await, self.searchrouter_flow_context.clone())
    }
    pub async fn get_current_type(&self) -> bool {
        let mut deepsearch_flow_context = self.searchrouter_flow_context.lock().await;
        deepsearch_flow_context.is_think_type.clone()
    }
    pub async fn set_current_type(&self, is_think: bool) {
        let mut deepsearch_flow_context = self.searchrouter_flow_context.lock().await;
        deepsearch_flow_context.is_think_type = is_think;
    }

    pub async fn get_response_result_type(&self) -> TypeEnum {
        let mut deepsearch_flow_context = self.searchrouter_flow_context.lock().await;
        deepsearch_flow_context.current_result_type.clone()
    }
    pub async fn set_response_result_type(&self, result_type: TypeEnum) {
        let mut deepsearch_flow_context = self.searchrouter_flow_context.lock().await;
        deepsearch_flow_context.current_result_type = result_type;
    }

    pub async fn get_answer_task_status(&self) -> TaskNodeEnum {
        self.answer_task.get_status().await
    }

    pub async fn is_current_project_and_branch(&self, project_url: &String, branch: &String) -> bool {
        //无论如何更新下questionUid，没有任何影响
        //chat_context.question_uid = question_uid.clone();
        let deepsearch_flow_context = self.searchrouter_flow_context.lock().await;
        if project_url.clone() == deepsearch_flow_context.project_url && branch.clone() == deepsearch_flow_context.branch
        {
            true
        } else {
            false
        }
    }

    pub async fn finish_sync_build_task(&self, project_url: &String, branch: &String) {
        if !self.is_current_project_and_branch(project_url, branch).await {
            //如果projectUrl和branch都已经变了，就没必要做其他处理了
            return;
        }
        self.build_index_task.change_sync_flag(true).await;
    }

    pub async fn is_build_process_timeout(&self,current_millis:u128)->bool{
        self.build_index_task.is_timeout(current_millis).await
    }
}


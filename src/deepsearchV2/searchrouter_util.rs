use std::collections::{HashMap, HashSet, VecDeque};
use std::future::Future;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use agent_common_service::model::chat_model::{AtomicSearchRequestBean, ChatFlowStatusEnum, ChatRelatedRequestBean, SymbolAndStreamSearchParams};
use agent_common_service::model::chat_model::ChatFlowStatusEnum::{ANSWER, UNDERSTAND_QUERY};
use agent_common_service::tools::common_tools::log_chat_step;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::remote_client::{chat_query_change, mquery_filter, mquery_rerank, IndexTypeEnum, QueryChangeDetail, QueryChangeRequestBean, TraceInfo};
use agent_db::domain::ap_data::{AtomicSearchTypeEnum, DeepsearchRelateCheckItem, DeepsearchRelateCheckResItem};
use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;
use agent_db::remote::rpc_model::{build_success_response, BaseResponse};
use futures::future::join_all;
use log::{error, info, warn};
use serde_json::Value;
use tokio::sync::Mutex;
use crate::dialogue::codefuse_index_repository::{query_index_entry, CHUNK_CLIENT};
use crate::dialogue::misc_util::{build_extra_data, doc_to_chunk, upload_panic_info};
use crate::deepsearchV2::searchrouter_data::{build_deepsearch_response, calculate_total_cost_time, set_end_time, DeepsearchChatResponse, DeepsearchThinkChildContent, DispatcherTaskResult, QeuryAndRetrievalTool, QeuryStatus, ResultDataToAugment, SearchRouterFlowContext, StepResponseResult, TypeEnum, WebSearchResultItem, WebSearchTransferData};
use crate::deepsearchV2::searchrouter_data::QeuryStatus::FULL_NEW;
use crate::deepsearchV2::searchrouter_data::TypeEnum::{SEARCH_RESULT, THINK, TRANSITION};
use crate::deepsearchV2::searchrouter_flow_task::DEEPSEARCH_TASK_MANAGER;
use crate::deepsearchV2::searchrouter_net::{common_web_search, filter_symbol_search_result, filter_upstream_search_result};
use crate::deepsearchV2::searchrouter_query_task::{convert_result, SEARCH_COUNT_FROM_LSP};
use crate::dialogue::data_struct::DataStatus;
use crate::function::chat_strategy::TaskNodeEnum::END;
use crate::function::chatflow::query_index_task_v2::{handler_search_chunk_data, merge_overlapping_chunks, sort_file_group_result};
use crate::service::ide_service::{symbol_search, usages_search};

pub const HEAD_TOKEN: &str = "ofGALCCf1AewBMLCifxlot4Xp3vmdxFl";    //调试用
pub const SEARCH_URL: &str = "https://antchat.alipay.com/v1/chat/completions";  //后面需要统一对接到codefuse服务端

//serchrouter流程里是否是同一个question的判断, todo
pub async fn is_searchrouter_same_question(deepsearchRequestBean: &ChatRelatedRequestBean, search_router_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> bool {
    let deepsearch_flow_context = search_router_flow_context.lock().await;
    if deepsearchRequestBean.questionUid == Some(deepsearch_flow_context.question_uid.clone()) &&
        deepsearchRequestBean.query == deepsearch_flow_context.query.clone() &&
        deepsearchRequestBean.changedQuery == deepsearch_flow_context.changedQuery &&
        deepsearchRequestBean.explanation == deepsearch_flow_context.explanation {
        true
    }else {
        false
    }
}
//全新question
pub async fn is_new_question(deepsearchRequestBean: &ChatRelatedRequestBean, search_router_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> bool {
    let deepsearch_flow_context = search_router_flow_context.lock().await;
    if deepsearchRequestBean.questionUid != Some(deepsearch_flow_context.question_uid.clone()) ||
        deepsearchRequestBean.query != deepsearch_flow_context.query.clone()  {
        true
    }else {
        false
    }
}
//partial-new query
pub async fn is_partial_new_question(deepsearchRequestBean: &ChatRelatedRequestBean, search_router_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> bool {
    let deepsearch_flow_context = search_router_flow_context.lock().await;
    if deepsearchRequestBean.questionUid == Some(deepsearch_flow_context.question_uid.clone()) &&
        deepsearchRequestBean.query == deepsearch_flow_context.query.clone() &&
        (deepsearchRequestBean.changedQuery.clone() != deepsearch_flow_context.changedQuery ||
        deepsearchRequestBean.explanation.clone() != deepsearch_flow_context.explanation){
        true
    }else {
        false
    }
}

//添加
pub async fn add_to_sub_query_list(sub_query_list: Vec<QeuryAndRetrievalTool>, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        let valid_sub_query_list: Vec<QeuryAndRetrievalTool> = sub_query_list.into_iter().filter(|tool| tool.question.is_some()).collect();
        flow_context.sub_query_list.extend(valid_sub_query_list);
    }
}
//清除
pub async fn clear_sub_query_list(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.sub_query_list.clear();
}
//获取
pub async fn get_sub_query_list(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Vec<QeuryAndRetrievalTool> {
    let flow_context = searchrouter_flow_context.lock().await;
    flow_context.sub_query_list.clone()
}
//new_sub_query
//添加
pub async fn add_to_new_sub_query_list(new_sub_query_list: Vec<QeuryAndRetrievalTool>, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.new_sub_query_list.extend(new_sub_query_list);
    }
}
//清除
pub async fn clear_new_sub_query_list(deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.new_sub_query_list.clear();
    }
}
//获取
pub async fn get_new_sub_query_list(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Vec<QeuryAndRetrievalTool> {
    let flow_context = searchrouter_flow_context.lock().await;
    flow_context.new_sub_query_list.clone()
}

//添加think_response
pub async fn add_to_end_of_think_response_content(content: String, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        let deepsearch_child_content = DeepsearchThinkChildContent {
            snippet: content,
        };
        let data = vec![deepsearch_child_content];
        flow_context.think_response_content.push_back(data);
    }
}
//清空think_response_content
pub async fn clear_think_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.think_response_content.clear();
}
//获取think_response
pub async fn get_first_from_think_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Option<Vec<DeepsearchThinkChildContent>>{
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.think_response_content.pop_front()
}
pub async fn get_think_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> VecDeque<Vec<DeepsearchThinkChildContent>>{
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.think_response_content.clone()
}

//添加think_response
pub async fn add_to_end_of_result_response_content(data: Vec<ChatRelatedCodeModel>, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.result_response_content.push_back(data);
    }
}
//清空think_response_content
pub async fn clear_result_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.result_response_content.clear();
}
//获取think_response
pub async fn get_first_from_result_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Option<Vec<ChatRelatedCodeModel>>{
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.result_response_content.pop_front()
}
pub async fn get_result_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> VecDeque<Vec<ChatRelatedCodeModel>>{
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.result_response_content.clone()
}

//进过filter+rerank后的数据， 顺序和sub_query_list是对应上的
pub async fn add_to_rag_segment_code_ist(data: Vec<ChatRelatedCodeModel>, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.rag_segment_code_ist.push(data);
    }
}
pub async fn clear_rag_segment_code_ist(deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>){
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.rag_segment_code_ist.clear();
    }
}
pub async fn get_rag_segment_code_ist( searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Vec<Vec<ChatRelatedCodeModel>> {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.rag_segment_code_ist.clone()
}
//combined_rag_code_data, 拼装后的数据
pub async fn add_to_combined_rag_code_data(data: Vec<ChatRelatedCodeModel>, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>){
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.combined_rag_code_data.extend(data);
    }
}
pub async fn clear_combined_rag_code_data(deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>){
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.combined_rag_code_data.clear();
    }
}
pub async fn get_combined_rag_code_data( searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Vec<ChatRelatedCodeModel> {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.combined_rag_code_data.clone()
}
//web_result和非web_result分开存储
pub async fn add_to_code_result_list(data: Vec<ChatRelatedCodeModel>, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>){
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.code_result_list.push(data);
    }
}
pub async fn clear_code_result_list(deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>){
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.code_result_list.clear();
    }
}
pub async fn get_code_result_list(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Vec<Vec<ChatRelatedCodeModel>> {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.code_result_list.clone()
}

//web_result_list
pub async fn add_to_web_result_list(data: Vec<ChatRelatedCodeModel>, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>){
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.web_result_list.push(data);
    }
}
pub async fn clear_web_result_list(deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>){
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.web_result_list.clear();
    }
}
pub async fn get_web_result_list(deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Vec<Vec<ChatRelatedCodeModel>> {
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        return flow_context.web_result_list.clone();
    }
    vec![]
}
//ap_formatted_code_v2 云辰要求的格式
pub async fn set_ap_formatted_code_v2(data: String, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>){
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.ap_formatted_code_v2 = data;
    }
}
pub async fn clear_ap_formatted_code_v2(deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>){
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.ap_formatted_code_v2 = "".to_string();
    }
}
pub async fn get_ap_formatted_code_v2(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> String {
    let mut flow_context = searchrouter_flow_context.lock().await;
    return flow_context.ap_formatted_code_v2.clone();
}
//改造后的think文案
//step_think_response_content
pub async fn add_to_end_of_step_think_response_content(content: String, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        let deepsearch_child_content = DeepsearchThinkChildContent {
            snippet: content,
        };
        let data = vec![deepsearch_child_content];
        flow_context.step_think_response_content.push_back(data);
    }
}

pub async fn clear_step_think_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.step_think_response_content.clear();
}

pub async fn get_first_from_step_think_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Option<Vec<DeepsearchThinkChildContent>>{
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.step_think_response_content.pop_front()
}
pub async fn get_step_think_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> VecDeque<Vec<DeepsearchThinkChildContent>>{
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.step_think_response_content.clone()
}
//改造后的response文案
//step_result_response_content
pub async fn add_to_end_of_step_result_response_content(data: Vec<ChatRelatedCodeModel>, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        flow_context.step_result_response_content.push_back(data);
    }
}
pub async fn clear_step_result_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.step_result_response_content.clear();
}
pub async fn get_first_from_step_result_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Option<Vec<ChatRelatedCodeModel>>{
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.step_result_response_content.pop_front()
}
pub async fn get_step_result_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> VecDeque<Vec<ChatRelatedCodeModel>>{
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.step_result_response_content.clone()
}
//改造后的response文案
//step_transition_response_content
pub async fn add_to_end_of_step_transition_response_content(content: String, deepsearchRequestBean: &ChatRelatedRequestBean, searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    if is_searchrouter_same_question(deepsearchRequestBean, searchrouter_flow_context.clone()).await {
        let mut flow_context = searchrouter_flow_context.lock().await;
        let deepsearch_child_content = DeepsearchThinkChildContent {
            snippet: content,
        };
        let data = vec![deepsearch_child_content];
        flow_context.step_transition_response_content.push_back(data);
    }
}

pub async fn clear_step_transition_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.step_transition_response_content.clear();
}

pub async fn get_first_from_step_transition_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Option<Vec<DeepsearchThinkChildContent>>{
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.step_transition_response_content.pop_front()
}
pub async fn get_step_transition_response_content(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> VecDeque<Vec<DeepsearchThinkChildContent>>{
    let mut flow_context = searchrouter_flow_context.lock().await;
    flow_context.step_transition_response_content.clone()
}


//augment流程的codebase search, 包含了检索，filter，rerank
//参考query_index_task_v2里的流程
pub async fn codebase_search_and_filter_V2(query: &String, projectUrl: &String, branch: &String, userToken: &String, productType: &String) -> (Vec<ChatRelatedCodeModel>,(u128, u128,u128,u128, u128)) {
    let mut query_change_time: u128 = 0;
    let mut bm25_search_time: u128 = 0;
    let mut vector_search_time: u128 = 0;
    let mut filter_time: u128 = 0;

    let query_change_request = QueryChangeRequestBean {
        userToken: userToken.to_string(),
        productType: productType.clone(),
        question: query.clone(),
    };
    info!("query change request:{}", serde_json::to_string(&query_change_request).unwrap());
    let timestamp_0 = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis();
    let query_change = chat_query_change(&query_change_request).await;
    let timestamp_1 = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis();
    query_change_time = timestamp_1 - timestamp_0;

    if let Some(query_change_detail) = query_change.data {
        let query_bm25_str = constract_bm25_query(query, &query_change_detail);
        info!("query_bm25_str content {}", query_bm25_str);

        let content_field = "content".to_string();
        //bm25检索
        let bm25_chunk_result = CHUNK_CLIENT.query_term_with_field(
            &content_field,
            &query_bm25_str,
            projectUrl,
            AGENT_CONFIG.index_search_top_num,
        );
        //query向量检索
        let query_vec = vec![query.clone()];
        let chunk_vector_result = query_index_entry(
            HashSet::new(),
            query_vec,
            HashSet::from([IndexTypeEnum::CHUNK_VECTOR]),
            userToken,
            projectUrl,
            productType,
            AGENT_CONFIG.index_search_top_num,
        );
        let query_search = tokio::join!(bm25_chunk_result, chunk_vector_result);
        //step 3: 处理检索结果
        let mut file_group_result: HashMap<String, Vec<ChatRelatedCodeModel>> =
            HashMap::new();
        //去重，key: file_url, value: content列表
        let mut repeat_data_map:HashMap<String,Vec<String>> = HashMap::new();

        //处理BM25结果
        match query_search.0 {
            Ok(chunk_result) => {
                for item in chunk_result.0 {
                    let chunk_data = doc_to_chunk(item.0, item.1).data;
                    if handler_search_chunk_data(&mut file_group_result, &mut repeat_data_map, chunk_data) { continue; }
                }
                bm25_search_time = chunk_result.1;
            }
            Err(e) => {
                error!("search chunk error:{}", e);
            }
        }
        //处理向量结果
        match query_search.1 {
            None => {
                warn!("query: {}, chunk vector result is none", query);
            }
            Some(query_related_data) => {
                vector_search_time = query_related_data.search_time.unwrap_or(0);
                let chunk_vector_data_opt = query_related_data.chunk_vector_data;
                match chunk_vector_data_opt {
                    None => {
                        warn!("query: {}, chunk vector result is none",query);
                    }
                    Some(chunk_vector_data_vec) => {
                        for query_chat_item_result in chunk_vector_data_vec {
                            let chunk_data = query_chat_item_result.search_result.data;
                            if handler_search_chunk_data(&mut file_group_result, &mut repeat_data_map, chunk_data) { continue; }
                        }
                    }
                }
            }
        }
        //排序，value值按从小到大排序
        sort_file_group_result(&mut file_group_result);
        //step 4: 过滤,每个文件一个线程，多文件同时过滤
        let all_value: Vec<ChatRelatedCodeModel> = file_group_result
            .values()
            .flat_map(|v| v.iter().cloned())
            .collect();
        let filter_group_value: Vec<Vec<ChatRelatedCodeModel>> =
            all_value.chunks(5).map(|chunk| chunk.to_vec()).collect();

        // 主处理逻辑
        let mut filter_item_result_vec = Vec::new();
        let BATCH_SIZE = 5;
        // 将 filter_group_value 切分成大小为 BATCH_SIZE 的批次
        for chunk in filter_group_value.chunks(BATCH_SIZE) {
            // 为每个 filter_item 创建异步任务
            let futures = chunk.iter().map(|filter_item| {
                let query = query.clone();
                let filter_item = filter_item.clone();
                async move {
                    let result = mquery_filter(query, filter_item.clone()).await;
                    if result.errorCode != 0 || result.data.is_none() {
                        warn!("filter result is null: {:?}", result);
                    }
                    (result, filter_item)
                }
            });

            let filter_stimestamp_0 = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis();
            // 并发执行所有任务
            let results = join_all(futures).await;
            let filter_stimestamp_1 = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis();
            filter_time = filter_stimestamp_1 - filter_stimestamp_0;


            // 处理结果
            for (result, filter_item) in results {
                filter_item_result_vec.extend(result.data.unwrap_or(filter_item));
            }
        }

        //过滤结果再次按文件级别聚合
        let mut filter_group_result: HashMap<String, Vec<ChatRelatedCodeModel>> =
            HashMap::new();

        for filter_item in filter_item_result_vec {
            let path = &filter_item.relativePath;
            let file_group_vec =
                filter_group_result.entry(path.clone()).or_insert(vec![]);
            file_group_vec.push(filter_item);
        }
        //step 5 : 文件级别rerank
        let mut rerank_param = vec![];
        for filter_result in filter_group_result {
            let mut filter_result_vec = filter_result.1;
            if filter_result_vec.len() == 0 {
                warn!("filter result is empty,file_url {:?}", filter_result.0)
            } else {
                // 按 startLine 排序
                filter_result_vec.sort_by(|a, b| a.startLine.cmp(&b.startLine));

                // 合并重叠的 chunks
                let merged_chunks = merge_overlapping_chunks(filter_result_vec);

                // 将合并后的 chunks 添加到 rerank_param
                rerank_param.extend(merged_chunks);
            }
        }
        //因为耗时问题，去掉rerank这个步骤，和codebase一致
        //let rerank = mquery_rerank(query.clone(), rerank_param).await;
        let end_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        //return rerank.data.unwrap_or(vec![]);
        return (rerank_param, (query_change_time, bm25_search_time, vector_search_time, filter_time, end_time - timestamp_0));
    }
    let end_time = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis();
    (vec![],  (query_change_time, bm25_search_time, vector_search_time, filter_time, end_time - timestamp_0))
}

//组合query及keyword_en
pub fn constract_bm25_query(query: &String, query_change_detail: &QueryChangeDetail) -> String{
    let mut combined_string = String::new();
    combined_string.push_str(query);
    if let Some(keyword_en_value) = &query_change_detail.keyword_en {
        if !combined_string.is_empty() {
            combined_string.push(' ');
        }
        combined_string.push_str(
            &keyword_en_value
                .iter()
                .cloned()
                .collect::<Vec<String>>()
                .join(" "),
        );
    }
    combined_string
}

pub async fn deepsearch_chat_related_info(chat_related_request: &ChatRelatedRequestBean) -> anyhow::Result<BaseResponse<DeepsearchChatResponse<serde_json::Value>>> {
    let (satus, context) = DEEPSEARCH_TASK_MANAGER.process(chat_related_request).await;
    match satus {
        ChatFlowStatusEnum::BUILD_INDEX => {
            let step_response_result = generate_new_response_result(context.clone()).await;
            let mut deepsearch_response_data = DeepsearchChatResponse  {
                r#type: None,
                content: None,
                subquestions: None,
                nlcontent: None,
                chatStatus: Some(UNDERSTAND_QUERY),
                necessary_index_percent: None,
                questionUid: chat_related_request.questionUid.clone(),
                sessionId: chat_related_request.sessionId.clone(),
                step: Some(step_response_result)
            };
            let base_response = build_success_response(deepsearch_response_data);
            Ok(base_response)
        }
        UNDERSTAND_QUERY => {
            let step_response_result = generate_new_response_result(context.clone()).await;
            let mut deepsearch_response_data = DeepsearchChatResponse  {
                r#type: None,
                content: None,
                subquestions: None,
                nlcontent: None,
                chatStatus: Some(UNDERSTAND_QUERY),
                necessary_index_percent: None,
                questionUid: chat_related_request.questionUid.clone(),
                sessionId: chat_related_request.sessionId.clone(),
                step: Some(step_response_result)
            };
            let base_response = build_success_response(deepsearch_response_data);
            Ok(base_response)
        }

        ANSWER => {
            //确保前面的流程走完
            if DEEPSEARCH_TASK_MANAGER.get_answer_task_status().await == END
            && get_think_response_content(context.clone()).await.len() == 0
            && get_result_response_content(context.clone()).await.len() == 0
            && get_step_think_response_content(context.clone()).await.len() == 0
            && get_step_result_response_content(context.clone()).await.len() == 0
            && get_step_transition_response_content(context.clone()).await.len() == 0{
                //end
                let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                set_end_time(end_time, context.clone()).await;
                //供H5请求问答
                let combined_rag_code = get_combined_rag_code_data(context.clone()).await;
                let subqueries = get_sub_query_list(context.clone()).await;
                let subquestions = subqueries.iter().map(|subquery| subquery.question
                    .as_ref()
                    .map(|s|s.as_str()).unwrap_or("").to_string()).collect();
                //供agent模式的格式
                // let ap_formatted_code = get_ap_formatted_code(context.clone()).await;
                // let ap_formatted_code_str = serde_json::to_string(&ap_formatted_code).unwrap();
                let ap_formatted_code = get_ap_formatted_code_v2(context.clone()).await;
                let mut step_response_result = generate_new_response_result(context.clone()).await;
                step_response_result.r#type = Some(TypeEnum::END);

                let mut deepsearch_response_data = DeepsearchChatResponse  {
                    r#type: Some(TypeEnum::END),
                    content: Some(serde_json::to_value(combined_rag_code).unwrap()),
                    subquestions: Some(subquestions),
                    nlcontent: Some(ap_formatted_code),
                    chatStatus: Some(ANSWER),
                    necessary_index_percent: None,
                    questionUid: chat_related_request.questionUid.clone(),
                    sessionId: chat_related_request.sessionId.clone(),
                    step: Some(step_response_result)
                };

                let time_record_data = calculate_total_cost_time(context.clone()).await;
                let trace_info = TraceInfo{
                    r#type: "DEEPSEARCH_SEARCH_DATA".to_string(),
                    data: serde_json::to_string(&time_record_data).unwrap(),
                    ideVersion: "".to_string(),
                    pluginVersion: "".to_string(),
                    userToken: chat_related_request.userToken.clone().unwrap_or("".to_string()),
                    deviceInfo: Default::default(),
                    occurTime: "".to_string(),
                };
                upload_panic_info(trace_info);
                info!("method: {}, question_id: {:?}, query: {}, project_url: {:?}, branch: {:?},value:{}",
                    "deepsearch_chat_related_info",
                    chat_related_request.questionUid,
                    chat_related_request.query,
                    chat_related_request.projectUrl,
                    chat_related_request.branch,
                    serde_json::to_string(&time_record_data).unwrap_or("".to_string())
                );
                let base_response = build_success_response(deepsearch_response_data);
                Ok(base_response)
            }else {
                //running
                //think
                if DEEPSEARCH_TASK_MANAGER.get_current_type().await {
                    let response_content_opt = get_first_from_think_response_content(context.clone()).await;
                    let step_response_result = generate_new_response_result(context.clone()).await;
                    match response_content_opt {
                        Some(response_content) => {
                            let res_data = build_ds_think_response_data(chat_related_request, THINK, ANSWER, response_content, step_response_result);
                            //下一步需要取对应的SEARCH_RESULT的数据
                            DEEPSEARCH_TASK_MANAGER.set_current_type(false).await;
                            return Ok(build_deepsearch_response(res_data.unwrap()));
                        }
                        None => {
                            // 如果还没有获取到think的数据， type不变， is_think_type不变, 前端如果看到连续的THINK， 可以忽略
                            let res_think_data = vec![];
                            let res_data = build_ds_think_response_data(chat_related_request, THINK, ANSWER, res_think_data, step_response_result);
                            return Ok(build_deepsearch_response(res_data.unwrap()));
                        }
                    }
                }else {
                    //获取第一个result
                    let response_content_opt = get_first_from_result_response_content(context.clone()).await;
                    let step_response_result = generate_new_response_result(context.clone()).await;
                    if let Some(response_content) = response_content_opt {
                        let res_data = build_ds_result_response_data(chat_related_request, SEARCH_RESULT, ANSWER, response_content, step_response_result);
                        //下一步需要取下个THINK的数据
                        DEEPSEARCH_TASK_MANAGER.set_current_type(true).await;
                       return Ok(build_deepsearch_response(res_data.unwrap()));
                    }else {
                        // 如果还没有获取到结果， type不变， is_think_type不变,
                        let res_data = build_ds_result_response_data(chat_related_request, SEARCH_RESULT, ANSWER, vec![], step_response_result);
                        return Ok(build_deepsearch_response(res_data.unwrap()));
                    }
                }
            }
        }
        _ => {
            info!("default DeepsearchChatResponse...");
            let res_think_data = vec![];
            let res_data = build_ds_think_response_data(chat_related_request,
                                                        THINK,
                                                        ANSWER,
                                                        res_think_data,StepResponseResult::default());
            return Ok(build_deepsearch_response(res_data.unwrap()));
        }
    }
}
fn build_ds_think_response_data(chat_related_request: &ChatRelatedRequestBean,
                          current_type: TypeEnum,
                          chat_status: ChatFlowStatusEnum,
                          content: Vec<DeepsearchThinkChildContent>, step_result: StepResponseResult) -> Option<DeepsearchChatResponse<serde_json::Value>> {
    let mut deepsearch_response_data = DeepsearchChatResponse  {
        r#type: Some(current_type),
        content: Some(serde_json::to_value(content).unwrap()),
        subquestions: None,
        nlcontent: None,
        chatStatus: Some(chat_status),
        necessary_index_percent: None,
        questionUid: chat_related_request.questionUid.clone(),
        sessionId: chat_related_request.sessionId.clone(),
        step: Some(step_result),
    };
    Some(deepsearch_response_data)
}
fn build_ds_result_response_data(chat_related_request: &ChatRelatedRequestBean,
                                current_type: TypeEnum,
                                chat_status: ChatFlowStatusEnum,
                                content: Vec<ChatRelatedCodeModel>,
                                 step_result: StepResponseResult)-> Option<DeepsearchChatResponse<serde_json::Value>> {
    let mut deepsearch_response_data = DeepsearchChatResponse  {
        r#type: Some(current_type),
        content: Some(serde_json::to_value(content).unwrap()),
        subquestions: None,
        nlcontent: None,
        chatStatus: Some(chat_status),
        necessary_index_percent: None,
        questionUid: chat_related_request.questionUid.clone(),
        sessionId: chat_related_request.sessionId.clone(),
        step: Some(step_result),
    };
    Some(deepsearch_response_data)
}

//获取当前的query,
// FULL_NEW返回flow_context.query
// PARTIAL_NEW返回explanation or changedQuery
// FULL_OLD返回 flow_context.query
pub async fn get_current_query(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> String {
    let flow_context = searchrouter_flow_context.lock().await;
    match flow_context.query_status {
        FULL_NEW => {
            flow_context.query.clone()
        }
        QeuryStatus::PARTIAL_NEW => {
            let explanation =  flow_context.explanation.clone().unwrap_or(String::new());
            if !explanation.is_empty() {
                return explanation;
            }
            let changedQuery = flow_context.changedQuery.clone().unwrap_or(String::new());
            if !changedQuery.is_empty() {
                return changedQuery;
            }
            String::new()
        }
        QeuryStatus::FULL_OLD => {
            flow_context.query.clone()
        }
    }
}

pub async fn generate_new_response_result(searchrouter_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> StepResponseResult{
    let response_result_type = DEEPSEARCH_TASK_MANAGER.get_response_result_type().await;
    match response_result_type {
        THINK => {
            let step_think_response_content_opt = get_first_from_step_think_response_content(searchrouter_flow_context.clone()).await;
            match step_think_response_content_opt {
                None => {
                    StepResponseResult {
                        r#type: Some(THINK),
                        content: Some(Value::Array(vec![])),
                    }
                }
                Some(step_think_response_content) => {
                    DEEPSEARCH_TASK_MANAGER.set_response_result_type(SEARCH_RESULT).await;
                    StepResponseResult {
                        r#type: Some(THINK),
                        content: Some(serde_json::to_value(step_think_response_content).unwrap()),
                    }
                }
            }
        }
        SEARCH_RESULT => {
            let step_result_response_content_opt = get_first_from_step_result_response_content(searchrouter_flow_context.clone()).await;
            match step_result_response_content_opt {
                None => {
                    StepResponseResult {
                        r#type: Some(SEARCH_RESULT),
                        content: Some(Value::Array(vec![])),
                    }
                }
                Some(step_result_response_content) => {
                    DEEPSEARCH_TASK_MANAGER.set_response_result_type(TRANSITION).await;
                    StepResponseResult {
                        r#type: Some(SEARCH_RESULT),
                        content: Some(serde_json::to_value(step_result_response_content).unwrap()),
                    }
                }
            }
        }
        TRANSITION => {
            let step_transition_response_content_opt = get_first_from_step_transition_response_content(searchrouter_flow_context.clone()).await;
            match step_transition_response_content_opt {
                None => {
                    StepResponseResult {
                        r#type: Some(TRANSITION),
                        content: Some(Value::Array(vec![])),
                    }
                }
                Some(step_transition_response_content) => {
                    DEEPSEARCH_TASK_MANAGER.set_response_result_type(THINK).await;
                    StepResponseResult {
                        r#type: Some(TRANSITION),
                        content: Some(serde_json::to_value(step_transition_response_content).unwrap()),
                    }
                }
            }
        }
        _ => {
            StepResponseResult::default()
        }
    }
}

pub async fn execute_atomic_search(atomic_search_request: AtomicSearchRequestBean) -> Vec<Vec<ChatRelatedCodeModel>>{
    let chat_request_bean = atomic_search_request.chatRequestBean.unwrap_or(Default::default());
    let param = SymbolAndStreamSearchParams {
        class_name: None,
        method_name: None,
        topN: None,
        project_url: None,
    };
    let symbol_search_param = atomic_search_request.symbolRequestBean.unwrap_or(param);

    let projectUrl_clone = chat_request_bean.projectUrl.unwrap_or(String::new());
    let branch_clone = chat_request_bean.branch.unwrap_or(String::new());
    let userToken_clone = chat_request_bean.userToken.unwrap_or(String::new());
    let productType_clone = chat_request_bean.productType.unwrap_or(String::new());
    let pluginVersion_clone = String::new();
    let ideVersion_clone = String::new();
    let tcpConnId_clone = chat_request_bean.tcpConnID.unwrap_or(String::new());;
    let query = chat_request_bean.query;
    let repo_git_url_clone = chat_request_bean.projectGitUrl.unwrap_or(String::new());

    let mut search_data_vec = Vec::new();
    let search_type_vec = atomic_search_request.searchTypeVec.unwrap_or(Vec::new());
    for search_type in search_type_vec {
        match search_type {
            AtomicSearchTypeEnum::Web_Search => {
                let result = common_web_search(&query,
                                               &userToken_clone,
                                               &productType_clone,
                                               5,
                                               &pluginVersion_clone,
                                               &ideVersion_clone,
                                               &repo_git_url_clone,
                                               Arc::new(Mutex::new(SearchRouterFlowContext::new()))).await.unwrap_or(Vec::new());
                search_data_vec.push(convert_result(&DispatcherTaskResult::Web_Search(result)).await);
            }
            AtomicSearchTypeEnum::Symbol_Search => {
                let symbol_result = symbol_search(&projectUrl_clone, &tcpConnId_clone, symbol_search_param.clone()).await.unwrap_or(Vec::new());
                let symbol_result_len = symbol_result.len();
                info!("symbol_result_len {}", symbol_result_len);
                //过滤返回的格式有问题，由于控制了个数，先注释掉
                // let symbol_filtered_data = filter_symbol_search_result(&query, symbol_result).await.unwrap_or(Vec::new());
                //info!("Symbol_Search search query {}, result count {}, filter count {}", &query, symbol_result_len, symbol_filtered_data.len());
                search_data_vec.push(convert_result(&DispatcherTaskResult::Symbol_Search(symbol_result)).await);
            }
            AtomicSearchTypeEnum::Codebase_Search => {
                let result = codebase_search_and_filter_V2(&query,
                                                           &projectUrl_clone,
                                                           &branch_clone,
                                                           &userToken_clone,
                                                           &productType_clone).await;
                info!("Codebase_Search search query {}, result count {}", &query, result.0.len());
                search_data_vec.push(convert_result(&DispatcherTaskResult::Codebase_Search(result.0)).await);
            }
            AtomicSearchTypeEnum::Upstream_Search => {
                let usages_result = usages_search(&projectUrl_clone, &tcpConnId_clone, symbol_search_param.clone()).await.unwrap_or(Vec::new());
                let usages_result_len = usages_result.len();
                // let upstream_filtered_data = filter_upstream_search_result(&query, usages_result).await.unwrap_or(Vec::new());
                // info!("Usage_Search search query {}, result count {}, filter count {}", &query, usages_result_len, upstream_filtered_data.len());
                info!("Usage_Search search query {}, result count {}", &query, usages_result_len);
                search_data_vec.push(convert_result(&DispatcherTaskResult::Upstream_Search(usages_result)).await);
            }
            _ => {}
        }
    }
    search_data_vec
}


#[cfg(test)]
mod tests {

    #[test]
    fn test_split_query() {
        // let query = "分析这个仓库的模块";
        // let runtime = tokio::runtime::Runtime::new().unwrap();
        // let query_result = runtime.block_on(searchrouter_split_query(&query));
        //
        // AGENT_CONFIG.deepsearch_count = 1;

    }
}
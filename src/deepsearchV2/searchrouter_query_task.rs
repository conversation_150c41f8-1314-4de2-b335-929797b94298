use crate::ast::js::constant::JS_VALID_EXTENSIONS;
use crate::ast::js::strategy::CodeFuseJsAstStrategy;
use crate::dialogue::codefuse_index_repository::{query_index_entry, FILE_CLIET};
use crate::dialogue::misc_util::{build_extra_data, doc_to_file, CLASS_REGEX};
use crate::utils::strategy_utils::get_chat_strategy_result;
use agent_common_service::model::chat_model::{ChatFlowStatusEnum, ChatRelatedRequestBean, SymbolAndStreamSearchParams};
use agent_db::config::runtime_config::{AgentConfig, AGENT_CONFIG};
use agent_db::dal::remote_client::{chat_query_change, deepsearch_filter_no_wiki, deepsearch_new_query, query_repo_wiki_entry, QueryChangeRequestBean};
use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;
use agent_db::tools::common_tools::LINE_ENDING;
use blake3::IncrementCounter::No;
use futures::future::join_all;
use log::{error, info};
use regex::Regex;
use serde_json::Value;
use std::cmp;
use std::collections::{HashMap, HashSet};
use std::fmt::format;
use std::path::Path;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use agent_db::domain::ap_data::{AtomicSearchTypeEnum, DeepsearchRelateCheckItem, DeepsearchRelateCheckResItem, GenerateNewQueryRequestBean, QueryResultFilterRequestBean, QueryWikiRequestBean, WikiTypeEnum};
use agent_db::remote::rpc_model::BaseResponse;
use tokio::sync::Mutex;
use uuid::Uuid;
use crate::ast_chunk::ast_chunk::AIKnowledgeRequestBean;
use crate::ast_chunk::ast_chunk_net::{format_git_repo_url_ai_knowledge, request_content_from_ai_knowledge};
use crate::deepsearchV2::combine_strategy::{CombineStrategy, JavaCombineStrategy};
use crate::deepsearchV2::searchrouter_data::{add_bm25search_time, add_change_query_time, add_codebasesearch_time, add_filter_time, add_new_query_time, add_onestage_time, add_symbolsearch_time, add_upstreamsearch_time, add_vectorsearch_time, add_websearch_time, DispatcherTaskResult, QeuryAndRetrievalTool, QeuryStatus, ResultDataToAugment, SearchRouterFlowContext, WebSearchTransferData};
use crate::deepsearchV2::searchrouter_net::{common_web_search, filter_symbol_search_result, filter_upstream_search_result, requestAisServer, searchrouter_new_query};
use crate::deepsearchV2::searchrouter_prompt::{generate_new_query};
use crate::deepsearchV2::searchrouter_util::{add_to_code_result_list, add_to_combined_rag_code_data, add_to_end_of_result_response_content, add_to_end_of_step_result_response_content, add_to_end_of_step_think_response_content, add_to_end_of_step_transition_response_content, add_to_end_of_think_response_content, add_to_new_sub_query_list, add_to_rag_segment_code_ist, add_to_sub_query_list, add_to_web_result_list, clear_new_sub_query_list, codebase_search_and_filter_V2, get_code_result_list, get_current_query, get_new_sub_query_list, get_rag_segment_code_ist, get_sub_query_list, get_web_result_list, is_searchrouter_same_question, set_ap_formatted_code_v2};
use crate::function::chat_strategy::TaskNodeEnum;
use crate::service::ide_service::{symbol_search, usages_search};

pub const QUERY_CHANGE_ORIGIN_FIELD: &str = "{original_query}";
pub const QUERY_CHANGE_REPO_FIELD: &str = "{repo_info}";
pub const ISRELATED_QUERY_FIELD: &str = "{query}";
pub const ISRELATED_QUERY_FIELD_V2: &str = "{question}";
pub const ISRELATED_CODE_SNIPPET_FIELD: &str = "{code_snippet}";
pub const ISRELATED_CODE_SNIPPET_FIELD_V2: &str = "{code_snippet_list}";

pub const NEED_NEW_QUERY_FIELD: &str = "{question}";
pub const MINI_QUESTIONS: &str = "{mini_questions}";
pub const NEED_NEW_CODE_SNIPPET_FIELD: &str = "{code_snippet}";
pub const REPOSITORY_INFO: &str = "{repository_info}";
pub const TEXT_SEGMENT: &str = "{text_segment}";
pub const DEEPSEARCH_COUNT_FOR_PARTIAL_NEW_QUESTION: usize = 1;
pub const SEARCH_COUNT_FROM_LSP: usize = 5;


pub struct SearchRouterAnswerTask {
    pub status: Mutex<TaskNodeEnum>,
}

impl SearchRouterAnswerTask {

    pub fn new() -> Self {
        SearchRouterAnswerTask { status : Mutex::new(TaskNodeEnum::READY) }
    }

    async fn change_status(&self, status: TaskNodeEnum) {
        let mut self_status = self.status.lock().await;
        *self_status = status;
    }

    pub async fn reset(&self) {
        self.change_status(TaskNodeEnum::READY).await;
    }

    pub async fn get_status(&self) -> TaskNodeEnum {
        let mut self_status = self.status.lock().await;
        (*self_status).clone()
    }

    pub async fn start(self: Arc<Self>, searchrouter_chat_request_bean: &ChatRelatedRequestBean, deepsearchFlowContext: Arc<Mutex<SearchRouterFlowContext>>) -> TaskNodeEnum {
        match self.get_status().await {
            TaskNodeEnum::READY => {
                let searchrouter_chat_request_bean_clone = searchrouter_chat_request_bean.clone();
                let self_clone = self.clone();
                tokio::spawn(async move {
                    self_clone.do_start(&searchrouter_chat_request_bean_clone, deepsearchFlowContext).await
                });
            }
            _ => {
                info!("answer_task already start and status : {:?}", self.get_status().await);
            }
        }
        self.get_status().await
    }

    pub async fn do_start(&self, searchrouter_chat_request_bean: &ChatRelatedRequestBean, search_router_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
        if !is_searchrouter_same_question(searchrouter_chat_request_bean, search_router_flow_context.clone()).await {
            info!("deepsearch answer_task exist since is_same_question is false");
            return;
        }
        self.change_status(TaskNodeEnum::RUNNING).await;
        let sub_query_list = get_sub_query_list(search_router_flow_context.clone()).await;
        info!("async answer_task started, sub_query_list.len {}", sub_query_list.len());
        if sub_query_list.is_empty() {
            info!("set answer_task end since sub_query_list is empty");
            if is_searchrouter_same_question(searchrouter_chat_request_bean, search_router_flow_context.clone()).await {
                self.change_status(TaskNodeEnum::END).await;
            }
        } else {
            self.par_think(sub_query_list, searchrouter_chat_request_bean, search_router_flow_context.clone(),  false).await;

            let mut contifinusFlag = true;
            let mut maxLoop = 1;
            while contifinusFlag {
                if maxLoop >= get_deepsearch_count(searchrouter_chat_request_bean, search_router_flow_context.clone()).await {
                    info!("searchrouter end since reach max query count {}", AGENT_CONFIG.deepsearch_count);
                    break;
                }
                if !is_searchrouter_same_question(searchrouter_chat_request_bean, search_router_flow_context.clone()).await {
                    info!("searchrouter answer_task exist since is_same_question is false");
                    break;
                }
                let new_query_start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                if is_searchrouter_same_question(searchrouter_chat_request_bean, search_router_flow_context.clone()).await {
                    self.is_need_new_query(searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
                }
                let new_query_end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                info!("deepsearch is_need_new_query cost  {}",  new_query_end_time - new_query_start_time);
                let new_sub_query_list = get_new_sub_query_list(search_router_flow_context.clone()).await;
                if new_sub_query_list.is_empty() {
                    info!("no sub new query, next step");
                    contifinusFlag = false;
                }else {
                    info!("get sub new query, continue search...");
                    maxLoop = maxLoop + 1;
                    //增加到sub_query_list
                    add_to_sub_query_list(new_sub_query_list.clone(), searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
                    self.par_think(new_sub_query_list, searchrouter_chat_request_bean, search_router_flow_context.clone(), true).await;
                }
            }
            info!("deepsearch answer_task end, to collent result");
            //生成最终的答案交给给H5做， todo
            //answer(deepsearchRequestBean, deepsearchFlowContext).await;

            //保存拼装后的数据， 留给H5请求大模型
            let combined_formatted_data = generate_combined_code_for_answer(searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
            add_to_combined_rag_code_data(combined_formatted_data, searchrouter_chat_request_bean, search_router_flow_context.clone()).await;

            info!("deepsearch answer_task, formatred_data");
            // //保存给ai partner的格式化数据
            // let ap_formatted_code = generete_combined_code_for_agent_v2(search_router_flow_context.clone()).await;
            let ap_formatted_code = generete_combined_code_for_agent_v1(search_router_flow_context.clone()).await.unwrap_or("".to_string());
            set_ap_formatted_code_v2(ap_formatted_code, searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
            info!("deepsearch answer_task, ap_formatted_code");

            info!("set answer_task end normally");
            if is_searchrouter_same_question(searchrouter_chat_request_bean, search_router_flow_context.clone()).await {
                info!("set answer_task status to end");
                self.change_status(TaskNodeEnum::END).await;
            }
        }
    }

    pub async fn par_think(&self, queries: Vec<QeuryAndRetrievalTool>, searchrouter_chat_request_bean: &ChatRelatedRequestBean, search_router_flow_context: Arc<Mutex<SearchRouterFlowContext>>,  need_transition: bool ) {
        let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        info!("par_think task started, queries {:?}", queries);
        if queries.is_empty() {
            return;
        }
        let deepsearchRequestBean_clone = searchrouter_chat_request_bean.clone();
        let projectUrl = deepsearchRequestBean_clone.projectUrl.unwrap_or(String::from(""));
        let branch = deepsearchRequestBean_clone.branch.unwrap_or(String::from(""));
        let userToken = deepsearchRequestBean_clone.userToken.unwrap_or(String::from(""));
        let productType = deepsearchRequestBean_clone.productType.unwrap_or(String::from(""));
        let pluginVersion = String::from("");
        let ideVersion = String::from("");
        let tcpConnId = deepsearchRequestBean_clone.tcpConnID.unwrap_or(String::from(""));
        let git_repo_url = deepsearchRequestBean_clone.projectGitUrl.unwrap_or("".to_string());
        let chat_intent = deepsearchRequestBean_clone.chatIntent;

        let mut task_container = Vec::new();

        for qeury_and_retrieval_tool in queries.clone() {
            let query = qeury_and_retrieval_tool.question.unwrap_or(String::from(""));
            if query.is_empty() {
                continue;
            }
            let projectUrl_clone = projectUrl.clone();
            let branch_clone = branch.clone();
            let userToken_clone = userToken.clone();
            let productType_clone = productType.clone();
            let pluginVersion_clone = pluginVersion.clone();
            let ideVersion_clone = ideVersion.clone();
            let tcpConnId_clone = tcpConnId.clone();
            let search_router_flow_context_inner = search_router_flow_context.clone();
            let repo_git_url_clone = git_repo_url.clone();
            let chat_intent_clone = chat_intent.clone();


            let future = async move {
                if qeury_and_retrieval_tool.retrieval_tool == Some(AtomicSearchTypeEnum::Codebase_Search.to_string()) {
                    let result = codebase_search_and_filter_V2(&query,
                                                               &projectUrl_clone,
                                                               &branch_clone,
                                                               &userToken_clone,
                                                               &productType_clone).await;
                    info!("Codebase_Search search query {}, result count {}", &query, result.0.len());
                    let time_recorder = result.1;
                    add_change_query_time(time_recorder.0, search_router_flow_context_inner.clone()).await;
                    add_bm25search_time(time_recorder.1, search_router_flow_context_inner.clone()).await;
                    add_vectorsearch_time(time_recorder.2, search_router_flow_context_inner.clone()).await;
                    add_filter_time(time_recorder.3, search_router_flow_context_inner.clone()).await;
                    add_codebasesearch_time(time_recorder.4, search_router_flow_context_inner.clone()).await;
                    DispatcherTaskResult::Codebase_Search(result.0)
                } else if qeury_and_retrieval_tool.retrieval_tool == Some(AtomicSearchTypeEnum::Web_Search.to_string()) {
                    if let Some(agent_common_service::model::chat_model::IntentEnum::EXPLAIN_CODE) = chat_intent_clone {
                        info!("skip Web_Search since chat_intent is EXPLAIN_CODE");
                        return DispatcherTaskResult::Web_Search(Vec::new());
                    }

                    let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                    let result = common_web_search(&query,
                                                   &userToken_clone,
                                                   &productType_clone,
                                                   5,
                                                   &pluginVersion_clone,
                                                   &ideVersion_clone,
                                                   &repo_git_url_clone,
                                                   search_router_flow_context_inner.clone()).await.unwrap_or(Vec::new());
                    let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                    add_websearch_time(end_time - start_time, search_router_flow_context_inner.clone()).await;
                    DispatcherTaskResult::Web_Search(result)
                } else if qeury_and_retrieval_tool.retrieval_tool == Some(AtomicSearchTypeEnum::Symbol_Search.to_string()) {
                    let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                    let symbol_class_name =  qeury_and_retrieval_tool.class;
                    let symbol_method_name =  qeury_and_retrieval_tool.method;
                    let symbol_search_param = SymbolAndStreamSearchParams {
                        class_name: symbol_class_name.clone(),
                        method_name: symbol_method_name.clone(),
                        topN: Some(SEARCH_COUNT_FROM_LSP),
                        project_url: Some(projectUrl_clone.clone()),
                    };
                    let symbol_result = symbol_search(&projectUrl_clone,&tcpConnId_clone, symbol_search_param).await.unwrap_or(Vec::new());
                    let symbol_result_len = symbol_result.len();
                    info!("symbol_result_len {}", symbol_result_len);
                    //过滤返回的格式有问题，由于控制了个数，先注释掉
                    // let symbol_filtered_data = filter_symbol_search_result(&query, symbol_result).await.unwrap_or(Vec::new());
                    // info!("Symbol_Search search query {}, class_name {:?}, method_name {:?}, result count {}, filter count {}",
                    //     &query, symbol_class_name, symbol_method_name, symbol_result_len, symbol_filtered_data.len());
                    let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                    add_symbolsearch_time(end_time - start_time, search_router_flow_context_inner.clone()).await;
                    DispatcherTaskResult::Symbol_Search(symbol_result)
                }else if qeury_and_retrieval_tool.retrieval_tool == Some(AtomicSearchTypeEnum::Upstream_Search.to_string()) {
                    let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                    let symbol_search_param = SymbolAndStreamSearchParams {
                        class_name: qeury_and_retrieval_tool.class,
                        method_name: qeury_and_retrieval_tool.method,
                        topN: Some(SEARCH_COUNT_FROM_LSP),
                        project_url: Some(projectUrl_clone.clone()),
                    };
                    let usages_result = usages_search(&projectUrl_clone,&tcpConnId_clone, symbol_search_param).await.unwrap_or(Vec::new());
                    let usages_result_len = usages_result.len();
                    info!("usages_result_len {}", usages_result_len);
                    // let upstream_filtered_data = filter_upstream_search_result(&query, usages_result).await.unwrap_or(Vec::new());
                    let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                    add_upstreamsearch_time(end_time - start_time, search_router_flow_context_inner.clone()).await;
                    info!("Usage_Search search query {}, result count {}, filter count {}", &query, usages_result_len, usages_result.len());
                    DispatcherTaskResult::Upstream_Search(usages_result)
                }else {
                    DispatcherTaskResult::Codebase_Search(Vec::new())
                }
            };
            task_container.push(Box::pin(future));
        }
        //先返回一个think给前端
        let first_sub_query = queries.get(0).unwrap().clone();
        add_to_end_of_think_response_content(first_sub_query.clone().question.unwrap(), searchrouter_chat_request_bean, search_router_flow_context.clone()).await;

        //ui改造， 第一轮没有过渡文案， 第二/三轮会有过滤文案
        if need_transition {
            let resoning = get_reasoning_from_query_list(queries.clone());
            info!("new_deepsearch_ui add resoning {}", resoning);
            add_to_end_of_step_transition_response_content(resoning, searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
        }
        //title
        let title = first_sub_query.question.unwrap().clone();
        info!("new_deepsearch_ui add title {}", title);
        add_to_end_of_step_think_response_content(title, searchrouter_chat_request_bean, search_router_flow_context.clone()).await;

        //等待执行完成
        info!("task_container.len {}", task_container.len());
        let results = join_all(task_container).await;

        //收集这一轮的结果
        let mut current_result_set = HashSet::new();
        let mut result_vec = Vec::new();
        for item in results.iter().enumerate() {
            let question = queries.get(item.0).unwrap().clone().question.unwrap_or(String::from(""));
            let covert_result = convert_result(item.1).await;
            current_result_set.extend(covert_result.clone());
            match item.1 {
                DispatcherTaskResult::Codebase_Search(res) => {
                    add_to_code_result_list(covert_result.clone(), searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
                }
                DispatcherTaskResult::Web_Search(res) => {
                    add_to_web_result_list(covert_result.clone(), searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
                }
                DispatcherTaskResult::Symbol_Search(res) => {
                    add_to_code_result_list(covert_result.clone(), searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
                }
                DispatcherTaskResult::Upstream_Search(res) => {
                    add_to_code_result_list(covert_result.clone(), searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
                }
            }
            result_vec.push((question, covert_result));
        }
        for rst in result_vec.iter().enumerate() {
            let content = rst.1.clone();
            if rst.0 == 0 {
                //前端展示
                add_to_end_of_result_response_content(content.1.clone(), searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
                //结果集
                add_to_rag_segment_code_ist(content.1.clone(), searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
            }else {
                //前端展示
                add_to_end_of_think_response_content(content.0, searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
                add_to_end_of_result_response_content(content.1.clone(), searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
                //结果集
                add_to_rag_segment_code_ist(content.1.clone(), searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
            }
        }

        //result
        let current_result_vec: Vec<ChatRelatedCodeModel> = current_result_set.into_iter().collect();
        info!("new_deepsearch_ui add title {}", current_result_vec.len());
        add_to_end_of_step_result_response_content(current_result_vec, searchrouter_chat_request_bean, search_router_flow_context.clone()).await;

        let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        add_onestage_time(end_time - start_time, search_router_flow_context.clone()).await;
        info!("par_think task finished, cost_time {}", end_time - start_time);
    }

    async fn is_need_new_query(&self, searchrouter_chat_request_bean: &ChatRelatedRequestBean, search_router_flow_context: Arc<Mutex<SearchRouterFlowContext>>) {
        let sub_query_list = get_sub_query_list(search_router_flow_context.clone()).await;

        //先清理已有的数据
        clear_new_sub_query_list(searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
        let original_query = get_current_query(search_router_flow_context.clone()).await;
        info!("original_query in is_need_new_query : {}", original_query);

        let sub_query_string = sub_query_list
            .iter()
            .map(|question| serde_json::to_string(question))
            .collect::<Result<Vec<String>, _>>().unwrap_or(Vec::new()).join("\n");

        //格式调整了
        let sub_query_list = get_sub_query_list(search_router_flow_context.clone()).await;
        let rag_segment_list = get_rag_segment_code_ist(search_router_flow_context.clone()).await;

        let mut code_result_str = String::new();
        let mut web_result_str = String::new();

        for sub_query in sub_query_list.iter().enumerate() {
            let query_tool_pair = sub_query.1.clone();
            let empty_vec = Vec::new();
            let matched_search_result = rag_segment_list.get(sub_query.0).unwrap_or(&empty_vec);
            if query_tool_pair.retrieval_tool == Some(AtomicSearchTypeEnum::Web_Search.to_string()) {
                //web_search
                web_result_str.push_str(&query_tool_pair.question.unwrap_or("".to_string()));
                web_result_str.push_str(LINE_ENDING);
                let web_snippets: Vec<String> = matched_search_result.iter().map(|item|item.snippet.clone()).collect();
                let web_snippet_str = web_snippets.join(LINE_ENDING);
                web_result_str.push_str(&web_snippet_str);
                web_result_str.push_str(LINE_ENDING);
                web_result_str.push_str(LINE_ENDING);
                web_result_str.push_str(LINE_ENDING);
            }else {
                //非web_search
                code_result_str.push_str(&query_tool_pair.question.unwrap_or("".to_string()));
                code_result_str.push_str(LINE_ENDING);
                let code_snippet: Vec<String> = matched_search_result.iter().map(|item|item.snippet.clone()).collect();
                let code_snippet_str = code_snippet.join("\n");
                code_result_str.push_str(&code_snippet_str);
                code_result_str.push_str(LINE_ENDING);
                code_result_str.push_str(LINE_ENDING);
                code_result_str.push_str(LINE_ENDING);
            }
        }

        let new_query_formatted_prompt = generate_new_query.replace(ISRELATED_QUERY_FIELD_V2, &original_query)
            .replace(MINI_QUESTIONS, &sub_query_string)
            .replace(ISRELATED_CODE_SNIPPET_FIELD, &code_result_str)
            .replace(TEXT_SEGMENT, &web_result_str);

        info!("new_query_formatted_prompt {}", new_query_formatted_prompt);
        let newquery_time_0 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let new_query_vec_opt = searchrouter_new_query(&new_query_formatted_prompt).await;
        let newquery_time_1 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        add_new_query_time(newquery_time_1 - newquery_time_0, search_router_flow_context.clone()).await;
        match new_query_vec_opt {
            Some(new_query_vec) => {
                add_to_new_sub_query_list(new_query_vec, searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
            }
            None => {
                info!("new_query_vec_opt is None");
            }
        }
    }
}

pub async fn convert_result(dispatcher_task_result: &DispatcherTaskResult) ->  Vec<ChatRelatedCodeModel>{
    match dispatcher_task_result {
        DispatcherTaskResult::Codebase_Search(res) => {
             res.clone()
        },
        DispatcherTaskResult::Web_Search(res) => {
            let mut chat_relate_code_model_vec = Vec::new();
            for item in res {
                let item_clone = item.clone();
                let  chat_relate_code_model = ChatRelatedCodeModel {
                    relativePath: item_clone.url,
                    snippet: item_clone.segment,
                    startLine: 0,
                    endLine: 0, //todo
                    source: Some(AtomicSearchTypeEnum::Web_Search.to_string()),
                    title: None,
                };
                chat_relate_code_model_vec.push(chat_relate_code_model);
            }
            chat_relate_code_model_vec
        },
        DispatcherTaskResult::Symbol_Search(res) => {
            let mut chat_relate_code_model_vec = Vec::new();
            for item in res {
                let item_clone = item.clone();
                let  chat_relate_code_model = ChatRelatedCodeModel {
                    relativePath: item_clone.relative_path,
                    snippet: item_clone.snippet,
                    startLine: item_clone.startLine as usize,
                    endLine: item_clone.endLine as usize,
                    source: Some(AtomicSearchTypeEnum::Symbol_Search.to_string()),
                    title: None,
                };
                chat_relate_code_model_vec.push(chat_relate_code_model);
            }
             chat_relate_code_model_vec
        },
        DispatcherTaskResult::Upstream_Search(res) => {
            let mut chat_relate_code_model_vec = Vec::new();
            for item in res {
                let item_clone = item.clone();
                let  chat_relate_code_model = ChatRelatedCodeModel {
                    relativePath: item_clone.relative_path,
                    snippet: item_clone.snippet,
                    startLine: item_clone.startLine as usize,
                    endLine: item_clone.endLine as usize,
                    source: Some(AtomicSearchTypeEnum::Upstream_Search.to_string()),
                    title: None,
                };
                chat_relate_code_model_vec.push(chat_relate_code_model);
            }
             chat_relate_code_model_vec
        },
        _ => {
            info!("result is not match any  DispatcherTaskResult : {:?}", dispatcher_task_result);
             Vec::new()
        }
    }
}
// 拼装deepsearch格式的数据
async fn generate_combined_code_for_answer(deepsearchRequestBean: &ChatRelatedRequestBean, deepsearchFlowContext: Arc<Mutex<SearchRouterFlowContext>>) -> Vec<ChatRelatedCodeModel> {
    let rag_segment_code_list = get_rag_segment_code_ist(deepsearchFlowContext.clone()).await;
    //去重
    let rag_segment_code_hashset: HashSet<ChatRelatedCodeModel> = rag_segment_code_list.into_iter().flatten().collect();
    let combine_code_result = combine_searched_code(deepsearchRequestBean, rag_segment_code_hashset).unwrap();
    combine_code_result
}

///对java， ts的function进行合并拼接，function拼接到class里
pub fn combine_searched_code(deepsearchRequestBean: &ChatRelatedRequestBean, data : HashSet<ChatRelatedCodeModel>) -> Option<Vec<ChatRelatedCodeModel>> {
    let project_url =  deepsearchRequestBean.clone().projectUrl.unwrap();
    let pathToChatRelatedCodeModel = convert_to_path_data_map(data);
    let mut combine_code_result = Vec::new();
    for (relativePath, model_list) in &pathToChatRelatedCodeModel {
        let file_url = Path::new(&project_url).join(relativePath);
        let file_suffix_opt = file_url.extension().and_then(|ext| ext.to_str());
        match file_suffix_opt {
            Some("java") => {
                let resut_into_file_rst = JavaCombineStrategy::combine_function_code(&file_url.display().to_string(), &model_list);
                if let Some(resut_into_file) = resut_into_file_rst {
                    combine_code_result.push(resut_into_file);
                }
            }
            Some(suffix) if JS_VALID_EXTENSIONS.contains(&suffix) => {
                // todo
                // CodeFuseJsAstStrategy::extra_method_chat_index(file_dir.path(), &content, project_url, branch).unwrap_or(vec![])
                for model_item in model_list {
                    combine_code_result.push(model_item.clone());
                }
            }
            _ => {
                for model_item in model_list {
                    combine_code_result.push(model_item.clone());
                }
            }
        }
    }
    Some(combine_code_result)
}

//拼装AI Partner格式的数据
async fn generete_combined_code_for_agent_v1(deepsearchFlowContext: Arc<Mutex<SearchRouterFlowContext>>) ->Option<String> {
    let rag_segment_code_list = get_rag_segment_code_ist(deepsearchFlowContext).await;
    //去重
    let rag_segment_code_hashset: HashSet<ChatRelatedCodeModel> = rag_segment_code_list.into_iter().flatten().collect();
    let path_to_ChatRelatedCodeModel = convert_to_path_data_map(rag_segment_code_hashset);

    let mut ap_formatted_code = String::new();
    ap_formatted_code.push_str("These are the semantic search results. Note that these may or may not include the full answer. It is up to you to decide if you need to call more tools to gather more information.");
    ap_formatted_code.push_str(LINE_ENDING);
    for (path, data) in  &path_to_ChatRelatedCodeModel {
        //一个文件开始
        let file_path = format!("```{}", path);
        ap_formatted_code.push_str(file_path.as_str());
        ap_formatted_code.push_str(LINE_ENDING);

        let mut data_clone = data.clone();
        data_clone.sort_by(|a, b| a.startLine.cmp(&b.startLine));
        for item in 0..data_clone.len() {
            let chat_relate_code_model = &data_clone[item];
            let start_line = chat_relate_code_model.startLine;
            let end_line = chat_relate_code_model.endLine;

            let mut lines: Vec<&str> = chat_relate_code_model.snippet.lines().collect();
            let mut vec_string: Vec<String> = lines.into_iter().map(|s| s.to_string()).collect();

            let last_line_number = vec_string.len() - 1;
            //更新第一行
            let first_line_opt = vec_string.get_mut(0);
            if let Some(first_line) = first_line_opt {
                *first_line = format!("{}|{}", start_line, first_line);
            }
            //更新最后一行
            let last_line_opt = vec_string.get_mut(last_line_number);
            if let Some(last_line) = last_line_opt {
                *last_line = format!("{}|{}", end_line, last_line);
            }
            let new_snippet = vec_string.join(LINE_ENDING);
            ap_formatted_code.push_str(new_snippet.as_str());
            ap_formatted_code.push_str(LINE_ENDING);
            ap_formatted_code.push_str("...");
            ap_formatted_code.push_str(LINE_ENDING);
        }

        //一个文件结束
        ap_formatted_code.push_str(LINE_ENDING);
        ap_formatted_code.push_str("```");
        ap_formatted_code.push_str(LINE_ENDING);
        ap_formatted_code.push_str(LINE_ENDING);
    }
    Some(ap_formatted_code)
}

///searchrouter给augmert的数据
async fn generete_combined_code_for_agent_v2(deepsearchFlowContext: Arc<Mutex<SearchRouterFlowContext>>) -> Vec<ResultDataToAugment> {
    let sub_query_list = get_sub_query_list(deepsearchFlowContext.clone()).await;
    let rag_segment_list = get_rag_segment_code_ist(deepsearchFlowContext).await;

    let mut result_data_to_augment_vec = Vec::new();
    for sub_query in sub_query_list.iter().enumerate() {
        let query_tool_pair = sub_query.1.clone();
        let empty_vec = Vec::new();
        let matched_search_result = rag_segment_list.get(sub_query.0).unwrap_or(&empty_vec);
        let result_data_to_augment = ResultDataToAugment {
            query: query_tool_pair.question,
            tool: query_tool_pair.retrieval_tool,
            className: query_tool_pair.class,
            methodName: query_tool_pair.method,
            results: matched_search_result.clone(),
        };
        result_data_to_augment_vec.push(result_data_to_augment);
    }
    result_data_to_augment_vec
}
//按文件路径进行k, v存储
fn convert_to_path_data_map(data : HashSet<ChatRelatedCodeModel>) -> HashMap<String, Vec<ChatRelatedCodeModel>> {
    let mut path_to_chatRelatedCodeModel = HashMap::new();
    for item in data {
        if path_to_chatRelatedCodeModel.contains_key(&item.relativePath) {
            let model_list: &mut Vec<ChatRelatedCodeModel> = path_to_chatRelatedCodeModel.get_mut(&item.relativePath).unwrap();
            model_list.push(item.clone());
        }else{
            let model_list = vec![item.clone()];
            path_to_chatRelatedCodeModel.insert(item.relativePath.clone(), model_list);
        }
    }
    //排序，按startLine从小到大进行排序
    for vec in path_to_chatRelatedCodeModel.values_mut() {
        vec.sort_by(|a, b| b.startLine.cmp(&a.startLine));
    }
    path_to_chatRelatedCodeModel
}

//获取deepsearch的检索的轮次
async fn get_deepsearch_count(searchrouter_chat_request_bean: &ChatRelatedRequestBean, deepsearchFlowContext: Arc<Mutex<SearchRouterFlowContext>>) -> usize{
    if let Some(agent_common_service::model::chat_model::IntentEnum::EXPLAIN_CODE) = searchrouter_chat_request_bean.chatIntent {
        info!("deepsearch count set to {} since chat_intent is EXPLAIN_CODE", AGENT_CONFIG.ds_count_explain_code);
        return AGENT_CONFIG.ds_count_explain_code as usize;
    }
    let flow_context = deepsearchFlowContext.lock().await;
    match flow_context.query_status{
        QeuryStatus::FULL_NEW => {
            info!("get_deepsearch_count, get count {} since FULL_NEW", AGENT_CONFIG.deepsearch_count);
            AGENT_CONFIG.deepsearch_count as usize
        }
        QeuryStatus::PARTIAL_NEW => {
            info!("get_deepsearch_count, get count {} since PARTIAL_NEW", DEEPSEARCH_COUNT_FOR_PARTIAL_NEW_QUESTION);
            DEEPSEARCH_COUNT_FOR_PARTIAL_NEW_QUESTION
        }
        QeuryStatus::FULL_OLD => {
            info!("get_deepsearch_count, get count {} since FULL_OLD", AGENT_CONFIG.deepsearch_count);
            AGENT_CONFIG.deepsearch_count as usize
        }
    }
}
//获取reasoning
fn get_reasoning_from_query_list(queries: Vec<QeuryAndRetrievalTool>) -> String {
    for sub_query in queries {
        if sub_query.reasoning.is_some(){
            return sub_query.reasoning.unwrap();
        }
    }
    "".to_string()
}
#[cfg(test)]
mod tests {
    use agent_common_service::model::chat_model::SymbolAndStreamSearchParams;
    use crate::service::ide_service::symbol_search;

    ///
    #[test]
    fn test_query_change(){
        let runtime = tokio::runtime::Runtime::new().unwrap();
        // let res = runtime.block_on(
        //     queryChange(deepsearch_chat_request_bean)
        // );

        // let res = runtime.block_on(
        //     multiSearch(&"".to_string(), &deepsearch_chat_request_bean)
        // );

        println!("==============================");

    }
    #[test]
    fn test_symbol_search(){

        let project_url = "/Users/<USER>/Downloads/alispace/caselike";
        let conn_id = "conn_1753864728_23033b8e7f5c45e88589cc5165d2053d";
        let symbol_param = SymbolAndStreamSearchParams {
            class_name: Some(String::from("")),
            method_name: Some(String::from("getMapSize")),
            topN: Some(10),
            project_url: None,
        };
        let runtime = tokio::runtime::Runtime::new().unwrap();

        let res = runtime.block_on(
            symbol_search(&project_url.to_string(), &conn_id.to_string(), symbol_param)
        );
        println!("res {}", serde_json::to_string_pretty(&res).unwrap());

        println!("=============test_symbol_search=================");

    }
}
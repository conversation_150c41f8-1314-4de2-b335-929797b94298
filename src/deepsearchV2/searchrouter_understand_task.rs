use crate::ast::js::constant::JS_VALID_EXTENSIONS;
use crate::ast::js::strategy::CodeFuseJsAstStrategy;
use crate::dialogue::codefuse_index_repository::FILE_CLIET;
use crate::dialogue::misc_util::{doc_to_file, CLASS_REGEX};
use crate::utils::strategy_utils::get_chat_strategy_result;
use agent_common_service::model::chat_model::{ChatFlowStatusEnum, ChatRelatedRequestBean};
use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;
use agent_db::tools::common_tools::LINE_ENDING;
use blake3::IncrementCounter::No;
use log::{error, info};
use regex::Regex;
use serde_json::{to_string, Value};
use std::cmp;
use std::collections::{HashMap, HashSet};
use std::fmt::format;
use std::path::Path;
use std::sync::Arc;
use std::thread::sleep;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use agent_db::dal::remote_client::deepsearch_query_split;
use agent_db::domain::ap_data::QeurySplitRequestBean;
use tokio::sync::Mutex;
use uuid::Uuid;
use crate::deepsearchV2::searchrouter_data::{set_understand_time, SearchRouterFlowContext};
use crate::deepsearchV2::searchrouter_net::searchrouter_split_query;
use crate::deepsearchV2::searchrouter_util::{add_to_sub_query_list, clear_sub_query_list, get_current_query, is_searchrouter_same_question};
use crate::function::chat_strategy::TaskNodeEnum;

pub struct SearchRouterUnderstandTask {
    pub status: Arc<Mutex<TaskNodeEnum>>,
}

impl SearchRouterUnderstandTask {

    pub fn new() -> Self {
        SearchRouterUnderstandTask { status : Arc::from(Mutex::new(TaskNodeEnum::READY)) }
    }

    async fn change_status(&self, status: TaskNodeEnum) {
        let mut self_status = self.status.lock().await;
        *self_status = status;
    }

    pub async fn reset(&self) {
        self.change_status(TaskNodeEnum::READY).await;
    }

    pub async fn get_status(&self) -> TaskNodeEnum {
        let mut self_status = self.status.lock().await;
        (*self_status).clone()
    }

    //问题拆分
    pub async fn start(self: Arc<Self>, deepsearch_chat_request_bean: &ChatRelatedRequestBean, deepsearchFlowContext: Arc<Mutex<SearchRouterFlowContext>>) -> TaskNodeEnum {
        match self.get_status().await {
            TaskNodeEnum::READY => {
                self.change_status(TaskNodeEnum::RUNNING).await;
                let self_clone = self.clone();
                let deepsearch_chat_request_bean_clone = deepsearch_chat_request_bean.clone();
                tokio::spawn(async move {
                    self_clone.do_start(&deepsearch_chat_request_bean_clone, deepsearchFlowContext).await
                });
            }
            _ => {}
        }
        self.get_status().await
    }

    pub async fn do_start(&self, searchrouter_chat_request_bean: &ChatRelatedRequestBean, search_router_flow_context:  Arc<Mutex<SearchRouterFlowContext>>) {
        if !is_searchrouter_same_question(searchrouter_chat_request_bean, search_router_flow_context.clone()).await {
            info!("deepsearch understand task exist since is_same_question is false");
            return;
        }

        //请求服务端获取拆分后的问题
        let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let mut sub_query_list = vec![];
        let sub_query_list_opt = searchrouter_split_query(searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
        let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let understand_consume_time = end_time - start_time;
        //耗时埋点
        set_understand_time(understand_consume_time, search_router_flow_context.clone()).await;
        info!("deepsearch understand query cost {}", understand_consume_time);
        if let Some(sub_query_list_v) = sub_query_list_opt {
            sub_query_list = sub_query_list_v;

        }

        if is_searchrouter_same_question(searchrouter_chat_request_bean, search_router_flow_context.clone()).await {
            clear_sub_query_list(search_router_flow_context.clone()).await;
            add_to_sub_query_list(sub_query_list, searchrouter_chat_request_bean, search_router_flow_context.clone()).await;
            self.change_status(TaskNodeEnum::END).await;
        }
    }

}

async fn is_ready(status: Arc<tokio::sync::Mutex<TaskNodeEnum>>) -> bool {
    for _ in 0..3 {
        // 获取锁并匹配枚举值
        let mut status_guard = status.lock().await;
        match *status_guard {
            TaskNodeEnum::READY => {
                *status_guard = TaskNodeEnum::RUNNING; // 修改状态
                return true; // 修改成功后返回 true
            }
            _ => sleep(Duration::from_millis(500)),
        }
    }
    false
}

#[cfg(test)]
mod tests {
    ///
    #[test]
    fn test_query_change(){
        println!("==============================");

    }
}
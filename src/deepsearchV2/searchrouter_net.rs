use std::collections::HashMap;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use agent_common_service::model::chat_model::{ChatRelatedRequestBean, SymbolAndStreamSearchResult};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::remote::http_client::{post_request, post_request_with_header, DEFAULT_TIMEOUT_MILLIS};
use agent_db::remote::rpc_model::BaseResponse;
use log::{error, info};
use serde_json::Value;
use tokio::join;
use tokio::sync::Mutex;
use uuid::Uuid;
use crate::ast_chunk::ast_chunk::{AIKnowledgeRequestBean, AIKnowledgeResponseBean};
use crate::ast_chunk::ast_chunk_net::{format_git_repo_url_ai_knowledge, request_content_from_ai_knowledge};
use crate::deepsearchV2::searchrouter_data::{add_websearch_filter_time, ChatMessageModel, ChatTemplateKwargs, QeuryAndRetrievalTool, SearchRouterFlowContext, SearchRouterRequestLLMData, SymbolTransferData, SymbolTransferResItem, UpStreamTransferData, WebSearchFilterResItemData, WebSearchRequestBean, WebSearchResultItem, WebSearchTransferData};
use crate::deepsearchV2::searchrouter_prompt::{query_split_with_wiki, query_split_wo_wiki, symbol_rank, text_rank, upstream_rank};
use crate::deepsearchV2::searchrouter_util::{get_current_query, HEAD_TOKEN, SEARCH_URL};

pub const ORIGINAL_QUERY: &str = "{original_query}";
pub const QUESTION: &str = "{question}";
pub const TEXT_SEGMENT_LIST: &str = "{text_segment_list}";
pub const SERVER_URL: &str = "https://antchat.alipay.com/v1/chat/completions";
pub const CASELIKE_SERVER_URL: &str =  "https://caselike.alipay.com/v1/codebase/search/web";
pub const SYMBOL_QUERY: &str =  "{symbol_query}";
pub const CODE_SNIPPET_LIST: &str =  "{code_snippet_list}";
pub const CALLER_QUERY: &str =  "{caller_query}";
pub const REPO_INFO: &str =  "{repo_info}";
pub const LLM_KIMI_K2: &str =  "Kimi-K2-Instruct";
pub const DEEPSEEK_V3: &str =  "DeepSeek-V3";
//5s超时
pub const MIDDLE_TIMEOUT_MILLIS: u64 = 5000;
//请求超时，默认取值的数据
pub const DEFAULT_COUNT_IF_TIMEOUT: usize = 3;

pub async fn requestAisServer(formatted_prompt: &String, llm_name: &String, timeout_millis: u64) -> Option<Value>{
    let mut messageList = Vec::new();
    let chatMessageModel = ChatMessageModel {
        role: "user".to_string(),
        content: formatted_prompt.clone(),
    };
    messageList.push(chatMessageModel);

    let params = SearchRouterRequestLLMData{
        model: llm_name.to_string(),
        messages:messageList,
        stream: false,
        top_p: 0.8,
        top_k: 20,
        temperature: 0.7,
        chat_template_kwargs: ChatTemplateKwargs { enable_thinking: false },
    };

    let trace_id =  Uuid::new_v4().simple().to_string();
    let mut headers = HashMap::new();
    headers.insert("Content-Type","application/json");
    headers.insert("Authorization",HEAD_TOKEN);
    headers.insert("SOFA-TraceId",&trace_id);
    headers.insert("SOFA-RpcId","0.1");

    let response_data = post_request_with_header::<serde_json::Value, _>(SEARCH_URL,
                                                                           &params,
                                                                           headers,
                                                                            timeout_millis,
                                                                           "searchrouter_split_query request fail").await;
    response_data
}

//query拆分
pub async fn searchrouter_split_query(searchrouter_request: &ChatRelatedRequestBean, search_router_flow_context:  Arc<Mutex<SearchRouterFlowContext>>) -> Option<Vec<QeuryAndRetrievalTool>>{
    info!("searchrouter_split_query...");
    let current_query = get_current_query(search_router_flow_context.clone()).await;
    info!("Current query: {}", current_query);
    let mut formatted_prompt = String::new();

    //根据开关来检索ai知识库, 数据展示， 建议获取仓库wiki的数据，todo
    // let query = searchrouter_request.query.clone();
    // let repo_git_url = searchrouter_request.projectGitUrl.clone().unwrap_or("".to_string());
    // if AGENT_CONFIG.use_repo_wiki == 1 {
    //     //启用ai知识库
    //     info!("use ai knowledge in searchrouter_split_query");
    //     let request_ai_knowledge_start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    //     let ai_knowledge_response_opt = request_content_from_ai_knowledge(&query, &repo_git_url, true).await;
    //     let request_ai_knowledge_end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    //     info!("request_ai_knowledge cost time {}", request_ai_knowledge_end_time - request_ai_knowledge_start_time);
    //     match ai_knowledge_response_opt {
    //         Some(ai_knowledge_response) => {
    //             formatted_prompt = query_split_with_wiki.replace(ORIGINAL_QUERY, &current_query).replace(REPO_INFO, &ai_knowledge_response.llmContent);
    //         }
    //         None => {
    //             //如果没有获取到ai知识库的数据，用wo_wiki的prompt
    //             formatted_prompt = query_split_wo_wiki.replace(ORIGINAL_QUERY, &current_query);
    //         }
    //     }
    // }else{
    //     //不启用ai知识库
    //     info!("no ai knowledge in searchrouter_split_query");
    //     formatted_prompt = query_split_wo_wiki.replace(ORIGINAL_QUERY, &current_query);
    // }

    //因为耗时，ai知识库的检索放到web_search的类型里
    formatted_prompt = query_split_wo_wiki.replace(ORIGINAL_QUERY, &current_query);
    let split_query_res = requestAisServer(&formatted_prompt, &LLM_KIMI_K2.to_string(), MIDDLE_TIMEOUT_MILLIS).await;
    match split_query_res {
        None => {
            info!("codefuse deepsearch check_result_relative , none res");
        }
        Some(query_res) => {
            info!("searchrooter split query : {}", query_res.to_string());
            if let Some(content_str) = query_res.get("choices")
                .and_then(|choices| choices.get(0))
                .and_then(|choice| choice.get("message"))
                .and_then(|message| message.get("content"))
                .and_then(Value::as_str) {
                info!("searchrooter split content_str: {}", content_str);
                let content_json = content_str.replace("```", "").replace("json\n", "");
                info!("content_json {}", content_json);
                let request_split_data: Vec<QeuryAndRetrievalTool> = match serde_json::from_str(&content_json) {
                    Ok(parsed_data) => parsed_data,
                    Err(e) => {
                        error!("content_str Failed to parse JSON: {}", e);
                        Vec::new()
                    }
                };
                info!("request_split_data {}", serde_json::to_string_pretty(&request_split_data).unwrap());
                return Some(request_split_data);
            }
        }
    }
    None
}

pub async fn common_web_search(sub_query: &String,
                               userToken: &String,
                               productType: &String,
                               size: usize,
                               pluginVersion: &String,
                               ideVersion: &String,
                               repo_git_url: &String,
                               search_router_flow_context: Arc<Mutex<SearchRouterFlowContext>>) -> Option<Vec<WebSearchTransferData>> {
    //web_search和ai知识库并发执行
    let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    let (web_search_result_opt, ai_knowledge_data_vec) = join!(
        search_web_remote(sub_query, userToken, productType, size, pluginVersion, ideVersion),
        request_ai_knowledge(sub_query, repo_git_url, false));
    let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    info!("ai_knowledge_data_vec len {}, cost {}", ai_knowledge_data_vec.len(), end_time - start_time);

    if let Some(web_search_result) = web_search_result_opt {
        if let Some(web_search_data) = web_search_result.data{
            let web_search_data_len = web_search_data.len();
            info!("web_search_data data len {}", web_search_data_len);
            let web_search_filter_start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
            let web_filter_data_opt = filter_web_search_data(sub_query, web_search_data, ai_knowledge_data_vec).await;
            let web_search_filter_end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
            let web_filter_data = web_filter_data_opt.unwrap_or(Vec::new());
            let web_filter_data_len = web_filter_data.len();
            info!("WEB_SEARCH search query {}, result count {}, filter count {}, filter cost {}", sub_query, web_search_data_len, web_filter_data_len, web_search_filter_end_time - web_search_filter_start_time);
            add_websearch_filter_time(web_search_filter_end_time - web_search_filter_start_time, search_router_flow_context).await;
            return Some(web_filter_data);
        }
    }
    None
}

//web search
pub async fn search_web_remote(sub_query: &String,
                               userToken: &String,
                               productType: &String,
                               size: usize,
                               pluginVersion: &String,
                               ideVersion: &String) -> Option<BaseResponse<Vec<WebSearchResultItem>>> {
    let web_seatch_request_bean = WebSearchRequestBean{
        query: sub_query.to_string(),
        pluginVersion: pluginVersion.to_string(),
        productType: productType.to_string(),
        userToken: userToken.to_string(),
        ideVersion: ideVersion.to_string(),
        size
    };
    let web_search_req_result = post_request::<Vec<WebSearchResultItem>, _>(CASELIKE_SERVER_URL,
                 &web_seatch_request_bean,
                 DEFAULT_TIMEOUT_MILLIS*2,
                 "searchrooter_split request fail",
    ).await;
    web_search_req_result
}

//过滤web_search的data
pub async fn filter_web_search_data(query: &String, web_search_data: Vec<WebSearchResultItem>, ext_data: Vec<WebSearchTransferData>) -> Option<Vec<WebSearchTransferData>>{
    //数据组织
    let mut web_filter_data = Vec::new();
    let mut web_data_v = Vec::new();
    let mut web_data_map = HashMap::new();
    let mut index = 0;
    for data in web_search_data {
        let web_id = format!("text_id_{}", index);
        let web_search_transfer_data = WebSearchTransferData {
            id: web_id.clone(),
            title: data.title,
            url: data.url,
            segment: data.doc,
        };
        index = index + 1;
        web_data_v.push(web_search_transfer_data.clone());
        web_data_map.insert(web_id, web_search_transfer_data);
    }
    //处理ext_data， 统一id的格式
    for data in ext_data.clone() {
        let web_id = format!("text_id_{}", index);
        let web_search_transfer_data = WebSearchTransferData {
            id: web_id.clone(),
            title: data.title,
            url: data.url,
            segment: data.segment,
        };
        index = index + 1;
        web_data_v.push(web_search_transfer_data.clone());
        web_data_map.insert(web_id, web_search_transfer_data);
    }
    //数据过滤
    let web_data_str = serde_json::to_string(&web_data_v).unwrap();
    let formatted_prompt = text_rank.replace(QUESTION, query).replace(TEXT_SEGMENT_LIST, &web_data_str);
    let web_filter_res_opt = requestAisServer(&formatted_prompt, &DEEPSEEK_V3.to_string(), MIDDLE_TIMEOUT_MILLIS).await;
    match web_filter_res_opt {
        None => {
            info!("codefuse filter_web_search_data , none res");
            //取前3的数据
            for i in 0..std::cmp::min(DEFAULT_COUNT_IF_TIMEOUT, ext_data.len()) {
                web_filter_data.push(ext_data[i].clone());
            }
            return Some(web_filter_data);
        }
        Some(web_filter_res) => {
            info!("filter_web_search_data : {}", web_filter_res.to_string());
            if let Some(content_str) = web_filter_res.get("choices")
                .and_then(|choices| choices.get(0))
                .and_then(|choice| choice.get("message"))
                .and_then(|message| message.get("content"))
                .and_then(Value::as_str) {
                info!("filter_web_search_data split content_str: {}", content_str);
                let content_json = content_str.replace("```", "").replace("json\n", "");
                info!("content_json {}", content_json);
                let web_res_data: Vec<WebSearchFilterResItemData> = match serde_json::from_str(&content_json) {
                    Ok(parsed_data) => parsed_data,
                    Err(e) => {
                        error!("content_str Failed to parse JSON: {}", e);
                        Vec::new()
                    }
                };
                info!("request_split_data {}", serde_json::to_string_pretty(&web_res_data).unwrap());
                for item in web_res_data {
                    web_filter_data.push(web_data_map.get(&item.id).unwrap().clone());
                }
                return Some(web_filter_data);
            }
        }
    }
    None
}

//是否触发新一轮的query
pub async fn searchrouter_new_query(formatted_prompt: &String) -> Option<Vec<QeuryAndRetrievalTool>>{
    let new_query_res_opt = requestAisServer(&formatted_prompt, &LLM_KIMI_K2.to_string(), MIDDLE_TIMEOUT_MILLIS).await;
    match new_query_res_opt {
        None => {
            info!("searchrouter_new_query , none res");
        }
        Some(new_query_resd) => {
            info!("searchrouter_new_query split query : {}", new_query_resd.to_string());
            if let Some(content_str) = new_query_resd.get("choices")
                .and_then(|choices| choices.get(0))
                .and_then(|choice| choice.get("message"))
                .and_then(|message| message.get("content"))
                .and_then(Value::as_str) {
                info!("searchrouter_new_query content_str: {}", content_str);
                let content_json = content_str.replace("```", "").replace("json\n", "");
                info!("content_json {}", content_json);
                let request_split_data: Vec<QeuryAndRetrievalTool> = match serde_json::from_str(&content_json) {
                    Ok(parsed_data) => parsed_data,
                    Err(e) => {
                        error!("content_str Failed to parse JSON: {}", e);
                        Vec::new()
                    }
                };
                info!("searchrouter_new_query {}", serde_json::to_string_pretty(&request_split_data).unwrap());
                return Some(request_split_data);
            }
        }
    }
    None
}

//过滤symbol检索到的数据
pub async fn filter_symbol_search_result(query: &String, data: Vec<SymbolAndStreamSearchResult>) -> Option<Vec<SymbolAndStreamSearchResult>> {
    if data.len() == 0 {
        info!("filter_symbol_search_result , none res since data.len = 0");
        return None;
    }
    let mut symbol_filter_data = Vec::new();
    let mut symbol_search_result_map = HashMap::new();
    let mut symbol_data_vec = Vec::new();
    let mut index = 0;
    for item in data.clone() {
        let item_clone = item.clone();
        let id = format!("code_id_{}", index);
        let symbol_transfer_data = SymbolTransferData {
            id: id.clone(),
            filePath: item.relative_path,
            className: item.class_name,
            body: item.snippet,
        };
        index = index +1;
        symbol_data_vec.push(symbol_transfer_data);
        symbol_search_result_map.insert(id, item_clone);
    }
    let symbol_data_vec_str = serde_json::to_string(&symbol_data_vec).unwrap();
    let symbol_formatted_str = symbol_rank.replace(SYMBOL_QUERY, query).replace(CODE_SNIPPET_LIST, &symbol_data_vec_str);
    let symbol_filter_res_opt = requestAisServer(&symbol_formatted_str, &DEEPSEEK_V3.to_string(),  MIDDLE_TIMEOUT_MILLIS).await;

    match symbol_filter_res_opt {
        None => {
            info!("searchrouter_new_query , none res");
            //取前3的数据
            for i in 0..std::cmp::min(DEFAULT_COUNT_IF_TIMEOUT, data.len()) {
                symbol_filter_data.push(data[i].clone());
            }
        }
        Some(new_query_resd) => {
            info!("filter_symbol_search_result split query : {}", new_query_resd.to_string());
            if let Some(content_str) = new_query_resd.get("choices")
                .and_then(|choices| choices.get(0))
                .and_then(|choice| choice.get("message"))
                .and_then(|message| message.get("content"))
                .and_then(Value::as_str) {
                info!("filter_symbol_search_result content_str: {}", content_str);
                let content_json = content_str.replace("```", "").replace("json\n", "");
                info!("content_json {}", content_json);
                let request_split_data: Vec<SymbolTransferResItem> = match serde_json::from_str(&content_json) {
                    Ok(parsed_data) => parsed_data,
                    Err(e) => {
                        error!("content_str Failed to parse JSON: {}", e);
                        Vec::new()
                    }
                };
                info!("filter_symbol_search_result {}", serde_json::to_string_pretty(&request_split_data).unwrap());
                request_split_data.iter().for_each(|item|symbol_filter_data.push(symbol_search_result_map.get(&item.id).unwrap().clone()));
            }
        }
    }
    Some(symbol_filter_data)
}

//过滤upstream的数据
pub async fn filter_upstream_search_result(query: &String, data: Vec<SymbolAndStreamSearchResult>) -> Option<Vec<SymbolAndStreamSearchResult>> {
    if data.len() == 0 {
        info!("filter_symbol_search_result , none res since data.len = 0");
        return None;
    }
    let mut symbol_filter_data = Vec::new();
    let mut symbol_search_result_map = HashMap::new();
    let mut symbol_data_vec = Vec::new();
    let mut index = 0;
    for item in data.clone() {
        let item_clone = item.clone();
        let id = format!("code_id_{}", index);
        let symbol_transfer_data = UpStreamTransferData {
            id: id.clone(),
            filePath: item.relative_path,
            className: item.class_name,
            methodName: item.method_name.unwrap_or("".to_string()),
            body: item.snippet,
        };
        index = index +1;
        symbol_data_vec.push(symbol_transfer_data);
        symbol_search_result_map.insert(id, item_clone);
    }
    let symbol_data_vec_str = serde_json::to_string(&symbol_data_vec).unwrap();
    let symbol_formatted_str = upstream_rank.replace(CALLER_QUERY, query).replace(CODE_SNIPPET_LIST, &symbol_data_vec_str);
    let symbol_filter_res_opt = requestAisServer(&symbol_formatted_str, &DEEPSEEK_V3.to_string(), MIDDLE_TIMEOUT_MILLIS).await;

    match symbol_filter_res_opt {
        None => {
            info!("searchrouter_new_query , none res");
            //取前3的数据
            for i in 0..std::cmp::min(DEFAULT_COUNT_IF_TIMEOUT, data.len()) {
                symbol_filter_data.push(data[i].clone());
            }
        }
        Some(new_query_resd) => {
            info!("filter_upstream_search_result split query : {}", new_query_resd.to_string());
            if let Some(content_str) = new_query_resd.get("choices")
                .and_then(|choices| choices.get(0))
                .and_then(|choice| choice.get("message"))
                .and_then(|message| message.get("content"))
                .and_then(Value::as_str) {
                info!("filter_upstream_search_result content_str: {}", content_str);
                let content_json = content_str.replace("```", "").replace("json\n", "");
                info!("content_json {}", content_json);
                let request_split_data: Vec<SymbolTransferResItem> = match serde_json::from_str(&content_json) {
                    Ok(parsed_data) => parsed_data,
                    Err(e) => {
                        error!("filter_upstream_search_result Failed to parse JSON: {}", e);
                        Vec::new()
                    }
                };
                info!("filter_upstream_search_result {}", serde_json::to_string_pretty(&request_split_data).unwrap());
                request_split_data.iter().for_each(|item|symbol_filter_data.push(symbol_search_result_map.get(&item.id).unwrap().clone()));
            }
        }
    }
    Some(symbol_filter_data)
}

//更方便定制， 定制prompt, 定制模型
//应用场景之一是内容的精简总结
pub async fn requestAisServer_v2(data: Vec<ChatMessageModel>, llm_name: &String) -> Option<Value> {
    let mut messageList = Vec::new();
    messageList.extend(data);

    let params = SearchRouterRequestLLMData{
        model: llm_name.clone(),
        messages:messageList,
        stream: false,
        top_p: 0.8,
        top_k: 20,
        temperature: 0.7,
        chat_template_kwargs: ChatTemplateKwargs { enable_thinking: false },
    };

    let trace_id =  Uuid::new_v4().simple().to_string();
    let mut headers = HashMap::new();
    headers.insert("Content-Type","application/json");
    headers.insert("Authorization",HEAD_TOKEN);
    headers.insert("SOFA-TraceId",&trace_id);
    headers.insert("SOFA-RpcId","0.1");

    let response_data = post_request_with_header::<serde_json::Value, _>(SEARCH_URL,
                                                                         &params,
                                                                         headers, DEFAULT_TIMEOUT_MILLIS*2,
                                                                         "requestAisServer_v2 request fail").await;
    response_data
}

pub async fn request_ai_knowledge(sub_query: &String, repo_git_url: &String, need_summary: bool) -> Vec<WebSearchTransferData>{
    //检索ai知识库
    let mut ai_knowledge_data_vec = Vec::new();
    //开关控制
    if AGENT_CONFIG.use_repo_wiki == 1 {
        info!("use ai knowledge in common_web_search");
        let request_ai_knowledge_start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let ai_knowledge_response_opt = request_content_from_ai_knowledge(sub_query, repo_git_url, need_summary).await;
        let request_ai_knowledge_end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        info!("request_ai_knowledge cost time {}", request_ai_knowledge_end_time - request_ai_knowledge_start_time);
        match ai_knowledge_response_opt {
            Some(ai_knowledge_response) => {
                let ai_knowledge_response_data = ai_knowledge_response.searchResults.unwrap_or(Vec::new());
                info!("ai_knowledge_response_data len {} ", ai_knowledge_response_data.len());
                for ai_knowledge_response_item in ai_knowledge_response_data {
                    let ai_web_item = WebSearchTransferData {
                        id: ai_knowledge_response_item.chunkId.unwrap_or("".to_string()),
                        title: ai_knowledge_response_item.title.unwrap_or("".to_string()),
                        url: ai_knowledge_response_item.url.unwrap_or("".to_string()),
                        segment: ai_knowledge_response_item.text.unwrap_or("".to_string()),
                    };
                    ai_knowledge_data_vec.push(ai_web_item);
                }
            }
            None => {
                info!("request_ai_knowledge result is None in Web_Search query ");
            }
        }
    }
    ai_knowledge_data_vec
}
use crate::dialogue::codefuse_index_repository::FILE_CLIET;
use crate::dialogue::misc_util::{doc_to_file, CLASS_REGEX};
use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;
use agent_db::tools::common_tools::LINE_ENDING;
use regex::Regex;
use std::collections::HashMap;

pub const SEPERATOR_CODE_HOLDER: &str = "...";

pub trait CombineStrategy {
    fn combine_function_code(file_url: &str, sorted_model_list: &Vec<ChatRelatedCodeModel>) -> Option<ChatRelatedCodeModel>;
}

pub struct JavaCombineStrategy;
pub struct JsCombineStrategy;

impl CombineStrategy for JavaCombineStrategy {

    fn combine_function_code(file_url: &str, model_list: &Vec<ChatRelatedCodeModel>) -> Option<ChatRelatedCodeModel> {
        let mut sorted_model_list = model_list.clone();

        sorted_model_list.sort_by(|a, b| a.startLine.cmp(&b.startLine));
        let first_item = sorted_model_list.get(0).unwrap();
        let last_item = sorted_model_list.get(sorted_model_list.len() - 1).unwrap();

        let mut result = String::new();
        sorted_model_list.iter().for_each(|item| {
            result.push_str(&item.snippet);
            result.push_str(LINE_ENDING);
        });

        let mut chat_relate_code_model = ChatRelatedCodeModel{
            relativePath: first_item.relativePath.clone(),
            snippet: result,
            startLine: first_item.startLine.clone(),
            endLine: last_item.endLine.clone(),
            source: None,
            title: None,
        };
        Some(chat_relate_code_model)
    }
}


impl CombineStrategy for JsCombineStrategy {
    fn combine_function_code(file_url: &str, model_list: &Vec<ChatRelatedCodeModel>) -> Option<ChatRelatedCodeModel> {
        None
    }
}

///获取数据行在文件里的行数
pub fn get_line_number_in_file(target_line: &str, file_content: &String) -> Option<usize> {
    let lines = file_content.lines();
    let mut line_mumber = 0;
    for line in lines {
        line_mumber = line_mumber + 1;
        if line.contains(target_line) {
            return Some(line_mumber);
        }
    }
    Some(0)
}
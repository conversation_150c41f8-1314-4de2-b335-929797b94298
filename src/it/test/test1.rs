#[cfg(test)]
mod test {
    use std::fs;
    use crate::api::req_model::{IntentionType, QueryRelatedRequestBean};
    use crate::it::loader::class_loader::build_repo;
    use crate::it::interface_parser::parse_interface;
    use crate::it::logger_init::init_logger;

    #[test]
    fn test1() {
        let _ = init_logger();

        let selected_content: &str = r#"
            public boolean process(ItacSubtaskDO subtaskDO)
    "#;

        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/rcqualitydataprod/app/biz/shared/src/main/java/com/alipay/rcqualitydataprod/biz/itacprocessor/impl/ItacSubtaskRunnerImpl.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/rcqualitydataprod".to_string(),
            fileUrl: "app/biz/shared/src/main/java/com/alipay/rcqualitydataprod/biz/itacprocessor/impl/ItacSubtaskRunnerImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::InterfaceTest,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };

        build_repo(query_related_request.projectUrl.as_str(), false);

        let interface_prompt_info = parse_interface(&query_related_request);
        println!("interface_prompt_info={}", serde_json::to_string(&interface_prompt_info).unwrap());
    }
}


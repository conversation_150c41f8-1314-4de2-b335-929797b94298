use serde::{Deserialize, Serialize};

use crate::it::java_model::class_info::ClassInfo;
use crate::it::java_model::method_info::MethodInfo;
use crate::it::java_parser::context::MockPoint;
use crate::it::project_model::project_info::ProjectInfo;

/// 接口信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct InterfacePromptData {
    // 接口方法签名, (ClassName.methodName)
    pub interface_method: Option<String>,
    // 实现方法签名, (ClassName.methodName)
    pub implement_method: String,
    // 方法信息
    pub method_info: MethodInfo,
    // 类信息, 目前不需要
    pub class_info: Option<ClassInfo>,
    // 项目信息
    pub project_info: ProjectInfo,
    // Mock点列表
    pub mock_point_list: Vec<MockPoint>,
    // 关键方法列表
    pub primary_method_list: Vec<MethodInfo>,
    // 直接显示错误信息
    pub error_code: ErrorCode,
    // 直接显示错误信息
    pub error_msg: String,
}

impl InterfacePromptData {
    pub fn new() -> Self {
        InterfacePromptData {
            interface_method: None,
            implement_method: "".to_string(),
            class_info: None,
            method_info: MethodInfo::new(),
            error_code: ErrorCode::Success,
            error_msg: "".to_string(),
            project_info: Default::default(),
            mock_point_list: vec![],
            primary_method_list: vec![],
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ErrorCode {
    Success,
    NoneSelectedMethod,
    ManySelectedMethod,
    NotFoundMethod,
}

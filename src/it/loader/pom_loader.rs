use std::str::from_utf8;

use indexmap::IndexMap;
use quick_xml::events::Event;
use quick_xml::Reader;
use serde::{Deserialize, Serialize};

use crate::it::loader::xml_loader::{XmlElement, XPath};
use crate::utils::file_utils::FileMetaInfo;
use crate::utils::index_map::deserialize_indexmap;
use crate::utils::index_map::serialize_indexmap;

/// Pom XML文件
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PomProject {
    // <parent/>
    pub parent: Option<PomGav>,
    // <groupId/>
    pub group_id: String,
    // <artifactId/>
    pub artifact_id: String,
    // <version/>
    pub version: String,
    // <packaging/>
    pub packaging: String,
    // <name/>
    pub name: String,
    // <modules/>
    pub modules: Vec<String>,
    // <properties/>
    #[serde(serialize_with = "serialize_indexmap", deserialize_with = "deserialize_indexmap")]
    pub properties: IndexMap<String, String>,
    // <dependencyManagement>/<dependencies>/<dependency>
    pub dependency_management: Vec<PomGav>,
    // <dependencies>/<dependency>
    pub dependencies: Vec<PomGav>,
    // 文件大小(字节)
    pub file_meta: FileMetaInfo,
}

impl PomProject {
    pub fn new() -> Self {
        PomProject {
            parent: None,
            group_id: String::new(),
            artifact_id: String::new(),
            version: String::new(),
            packaging: String::new(),
            name: String::new(),
            modules: Vec::new(),
            properties: IndexMap::new(),
            dependency_management: Vec::new(),
            dependencies: Vec::new(),
            file_meta: FileMetaInfo::new(),
        }
    }

    pub fn last_dep_as_mut(&mut self) -> Option<&mut PomGav> {
        self.dependencies.last_mut()
    }

    pub fn last_dep_mgr_as_mut(&mut self) -> Option<&mut PomGav> {
        self.dependency_management.last_mut()
    }
}

/// Pom GAV信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PomGav {
    pub group_id: String,
    pub artifact_id: String,
    pub version: String,
    pub type_: Option<String>,
}

impl PomGav {
    pub fn new() -> Self {
        PomGav {
            group_id: String::new(),
            artifact_id: String::new(),
            version: String::new(),
            type_: None,
        }
    }
}


// 解析PomXml文件
pub fn pom_xml_parser(xml_content: &str) -> PomProject {
    let mut ret = PomProject::new();
    let mut reader = Reader::from_str(xml_content);
    reader.config_mut().trim_text(true);

    let mut xpath = XPath::new();
    let mut buf = Vec::new();
    loop {
        let event_result = reader.read_event_into(&mut buf);
        match event_result {
            Ok(Event::Start(ref e) | Event::Empty(ref e)) => {
                if let Ok(tag_name) = from_utf8(e.name().as_ref()) {
                    if xpath.is_empty() && tag_name != "project" {
                        // root元素必须是<project>
                        return ret;
                    }
                    if let Ok(Event::Start(_)) = event_result {
                        xpath.push(XmlElement::build(tag_name));
                        let to_xpath = xpath.to_xpath();
                        match to_xpath.as_str() {
                            "project/parent" => {
                                ret.parent = Some(PomGav::new());
                            }
                            "project/dependencies/dependency" => {
                                ret.dependencies.push(PomGav::new());
                            }
                            "project/dependencyManagement/dependencies/dependency" => {
                                ret.dependency_management.push(PomGav::new());
                            }
                            _ => {}
                        }
                    }
                }
            }
            Ok(Event::End(ref e)) => {
                if let Ok(tag_name) = from_utf8(e.name().as_ref()) {
                    let to_xpath = xpath.to_xpath();
                    if let Some(last) = xpath.last() {
                        match to_xpath.as_str() {
                            "project/groupId" => {
                                ret.group_id = last.text.clone();
                            }
                            "project/artifactId" => {
                                ret.artifact_id = last.text.clone();
                            }
                            "project/version" => {
                                ret.version = last.text.clone();
                            }
                            "project/packaging" => {
                                ret.packaging = last.text.clone();
                            }
                            "project/name" => {
                                ret.name = last.text.clone();
                            }
                            "project/parent/groupId" => {
                                if let Some(parent) = ret.parent.as_mut() {
                                    parent.group_id = last.text.clone();
                                }
                            }
                            "project/parent/artifactId" => {
                                if let Some(parent) = ret.parent.as_mut() {
                                    parent.artifact_id = last.text.clone();
                                }
                            }
                            "project/parent/version" => {
                                if let Some(parent) = ret.parent.as_mut() {
                                    parent.version = last.text.clone();
                                }
                            }
                            "project/modules/module" => {
                                ret.modules.push(last.text.clone());
                            }
                            x if x.starts_with("project/properties/") => {
                                ret.properties.insert(tag_name.to_string(), last.text.clone());
                            }
                            "project/dependencies/dependency/groupId" => {
                                if let Some(dep) = ret.last_dep_as_mut() {
                                    dep.group_id = last.text.clone();
                                }
                            }
                            "project/dependencies/dependency/artifactId" => {
                                if let Some(dep) = ret.last_dep_as_mut() {
                                    dep.artifact_id = last.text.clone();
                                }
                            }
                            "project/dependencies/dependency/version" => {
                                if let Some(dep) = ret.last_dep_as_mut() {
                                    dep.version = last.text.clone();
                                }
                            }
                            "project/dependencies/dependency/type" => {
                                if let Some(dep) = ret.last_dep_as_mut() {
                                    dep.type_ = Some(last.text.clone());
                                }
                            }
                            "project/dependencyManagement/dependencies/dependency/groupId" => {
                                if let Some(dep) = ret.last_dep_mgr_as_mut() {
                                    dep.group_id = last.text.clone();
                                }
                            }
                            "project/dependencyManagement/dependencies/dependency/artifactId" => {
                                if let Some(dep) = ret.last_dep_mgr_as_mut() {
                                    dep.artifact_id = last.text.clone();
                                }
                            }
                            "project/dependencyManagement/dependencies/dependency/version" => {
                                if let Some(dep) = ret.last_dep_mgr_as_mut() {
                                    dep.version = last.text.clone();
                                }
                            }
                            "project/dependencyManagement/dependencies/dependency/type" => {
                                if let Some(dep) = ret.last_dep_mgr_as_mut() {
                                    dep.type_ = Some(last.text.clone());
                                }
                            }
                            _ => {}
                        }
                    }

                    xpath.pop_tag(tag_name);
                }
            }
            Ok(Event::Text(ref e)) => {
                if let Some(last_node) = xpath.last_mut() {
                    if let Ok(text) = e.unescape() {
                        last_node.text += text.as_ref();
                    }
                }
            }
            Err(e) => {
                print!("[XmlLoader] 解析XML错误, position=[{}], err={:?}", reader.error_position(), e);
                break;
            }
            Ok(Event::Eof) => break,
            _ => {}
        }
        buf.clear();
    }

    return ret;
}

#[cfg(test)]
mod test {
    use std::fs;

    use log::info;

    use crate::it::loader::pom_loader::pom_xml_parser;
    use crate::it::logger_init::init_logger;

    #[test]
    fn test1() {
        let _ = init_logger();
        let xml_content = fs::read_to_string("/Users/<USER>/tinghe-source/automated-interface-test-cases/pom.xml").unwrap();
        let ret = pom_xml_parser(xml_content.as_str());
        info!("ret: {}", serde_json::to_string(&ret).unwrap().to_string());
    }
}

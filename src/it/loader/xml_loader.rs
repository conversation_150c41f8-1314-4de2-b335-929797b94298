use std::any::Any;
use std::collections::HashMap;
use std::fmt::Debug;
use std::path::PathBuf;
use std::str::from_utf8;
use std::sync::Arc;

use indexmap::IndexMap;
use quick_xml::events::Event;
use quick_xml::Reader;
use serde::{Deserialize, Serialize};

use crate::utils::file_utils::{add_slash, file_path_to_dir, FileMetaInfo, path_buf_to_posix};
use crate::utils::string_utils::{multi_split, remove_prefix, substring_after, substring_after_last};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct XmlElement {
    // Tag名
    pub tag: String,
    // 属性
    pub attributes: HashMap<String, String>,
    // 文本内容
    pub text: String,
}

impl XmlElement {
    pub fn build(tag: &str) -> Self {
        XmlElement {
            tag: tag.to_string(),
            attributes: Default::default(),
            text: "".to_string(),
        }
    }

    pub fn attr(&self, name: &str) -> Option<&String> {
        self.attributes.get(name)
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SofaService {
    // 引用BeanId
    pub ref_id: String,
    // 接口
    pub interface: String,
    // 对应的Class类
    pub class: Option<String>,
    // UniqueId
    pub unique_id: Option<String>,
    // 是否是RPC
    pub is_rpc: bool,
    // 指定Bindings
    pub bindings: Vec<String>,
}

impl SofaService {
    pub fn is_rpc_service(&self, class: &str) -> bool {
        if !self.is_rpc {
            return false;
        }
        if let Some(class_name) = &self.class {
            return class_name == class;
        }
        return false;
    }

    pub fn is_service(&self, class: &str) -> bool {
        if let Some(class_name) = &self.class {
            return class_name == class;
        }
        return false;
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SofaReference {
    // BeanId
    pub id: String,
    // 接口
    pub interface: String,
    // UniqueId
    pub unique_id: Option<String>,
    // 是否是RPC
    pub is_rpc: bool,
    // 指定Bindings
    pub bindings: Vec<String>,
}

impl SofaReference {
    // 判断是否是RPC引用的Interface
    pub fn is_rpc_reference(&self, interface: &str) -> bool {
        if !self.is_rpc {
            return false;
        }
        return interface == self.interface;
    }

    // 判断是否是RPC引用的BeanId
    pub fn is_rpc_reference_id(&self, id: &str) -> bool {
        if !self.is_rpc {
            return false;
        }
        return id == self.id;
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SpringXmlBeanInfo {
    // Bundle
    pub bundle: String,
    // 文件大小(字节)
    pub file_meta: FileMetaInfo,
    // Bean映射: BeanId -> Class类
    pub bean_map: HashMap<String, String>,
    // SOFA服务 sofa:service -> binding.rpc
    pub sofa_service_list: Vec<SofaService>,
    // SOFA引用 sofa:reference -> binding.rpc
    pub sofa_reference_list: Vec<SofaReference>,
}

impl SpringXmlBeanInfo {
    pub fn new() -> Self {
        SpringXmlBeanInfo {
            bundle: "".to_string(),
            file_meta: FileMetaInfo::new(),
            bean_map: HashMap::new(),
            sofa_service_list: Vec::new(),
            sofa_reference_list: Vec::new(),
        }
    }

    // 判断是否Bundle匹配
    pub fn is_bundle(&self, bundle: &str) -> bool {
        self.bundle == bundle
    }

    // 判断是否RpcReference引用, 按BeanId或Interface类型匹配
    pub fn exist_rpc_reference_or(&self, bean_id: &str, interface: &str) -> bool {
        for sofa_reference in self.sofa_reference_list.iter() {
            if sofa_reference.is_rpc_reference_id(bean_id) || sofa_reference.is_rpc_reference(interface) {
                return true;
            }
        }
        return false;
    }

    // 判断是否存在RPC服务
    pub fn exist_rpc_service(&self, class: &str) -> bool {
        for sofa_service in self.sofa_service_list.iter() {
            if sofa_service.is_rpc_service(class) {
                return true;
            }
        }
        return false;
    }

    // 查找RPC服务
    pub fn find_rpc_service(&self, class: &str) -> Option<&SofaService> {
        for sofa_service in self.sofa_service_list.iter() {
            if sofa_service.is_rpc_service(class) {
                return Some(&sofa_service);
            }
        }
        None
    }

    // 更新当前Bean数据
    pub fn update(&mut self, other: SpringXmlBeanInfo) {
        self.bean_map = other.bean_map;
        self.sofa_service_list = other.sofa_service_list;
        self.sofa_reference_list = other.sofa_reference_list;
        self.file_meta.update();
    }
}

// 过滤maven工程中的 spring xml文件
pub fn filter_spring_xml(xml_file_list: &Vec<Arc<PathBuf>>, project_url: &str) -> Vec<Arc<PathBuf>> {
    let mut ret = Vec::new();
    let mut map_list = IndexMap::new();
    // Maven模式: src/main/resources/目录, 并且SpringXml文件有相同的相对路径
    // eg: META-INF/spring/xxx.xml, META-INF/app/xxx.xml, spring/xxx.xml
    let mut maven_mode = false;
    let project_url = add_slash(project_url);

    for xml_file in xml_file_list {
        if let Some(xml_path) = path_buf_to_posix(xml_file) {
            let mut relative_path = substring_after_last(xml_path.as_str(), "src/main/resources/");
            if relative_path.is_empty() {
                relative_path = substring_after(xml_path.as_str(), project_url.as_str());
                relative_path = remove_prefix(relative_path, "/");
                if relative_path.is_empty() {
                    continue;
                }
            } else {
                // 符合Maven路径规范
                maven_mode = true;
            }
            let mut relative_dir = file_path_to_dir(relative_path);
            if is_exclude_dir(relative_dir) {
                // 目录黑名单
                continue;
            }
            if is_exclude_file(xml_file) {
                // 文件黑名单
                continue;
            }
            // 按相对路径统计哪一个路径下的xml文件最多
            let mut list = map_list.entry(relative_dir.to_string()).or_insert(Vec::new());
            list.push(Arc::clone(xml_file));
        }
    }

    // 自动探测SpringXml文件: 相对路径最多的xml文件
    map_list.sort_by(|_, list1, _, list2| {
        list2.len().cmp(&list1.len())
    });

    let mut first = true;
    for (key, list) in map_list {
        // Maven模式: 相对路径文件数量最多的xml+符合spring路径的文件
        // 非Maven模式: 所有xml文件
        if !maven_mode || first || is_spring_xml(key.as_str()) {
            ret.extend(list);
        }
        first = false;
    }

    return ret;
}

// 目录黑名单
pub fn is_exclude_dir(relative_dir: &str) -> bool {
    if relative_dir.is_empty() {
        return false;
    }
    let relative_dir = add_slash(relative_dir);
    let exclude_dir_list = vec!["dalgen", "table", "sql", "map", "batis", "autoconf", "log4j", "logback", "logging", "template", "com/", "OSGI-INF/", "conf/", "config/"];
    for exclude_dir in exclude_dir_list {
        if relative_dir.contains(exclude_dir) {
            return true;
        }
    }
    return false;
}

// 文件黑名单
pub fn is_exclude_file(xml_file: &PathBuf) -> bool {
    if let Some(file_name) = xml_file.file_name() {
        if let Some(file_name_str) = file_name.to_str() {
            if ["log4j-spring.xml", "log4j2-spring.xml", "logback-spring.xml", "logging-spring.xml"].contains(&file_name_str) {
                return true;
            }
        }
    }
    return false;
}

// 判断是否为SpringXml文件
pub fn is_spring_xml(relative_dir: &str) -> bool {
    if relative_dir.is_empty() {
        return false;
    }
    let relative_dir = add_slash(relative_dir);
    if relative_dir.contains("spring/") {
        // 在spring/下的文件都算成SpringXml
        return true;
    }
    // WEB-INF/MERGE-INF下全都算成SpringXml
    return relative_dir.contains("WEB-INF/") || relative_dir.contains("MERGE-INF/");
}

pub struct XPath {
    pub xpath: Vec<XmlElement>,
}

impl XPath {
    pub fn new() -> Self {
        XPath {
            xpath: Vec::new(),
        }
    }

    pub fn to_xpath(&self) -> String {
        self.xpath.iter()
            .map(|node| node.tag.as_str())
            .collect::<Vec<_>>()
            .join("/")
    }

    pub fn eq_xpath(&self, xpath: &str) -> bool {
        let parts: Vec<&str> = xpath.split('/').collect();
        if parts.len() != self.xpath.len() {
            return false;
        }
        for (i, part) in parts.iter().enumerate() {
            if part != &self.xpath[i].tag {
                return false;
            }
        }
        true
    }

    pub fn match_xpath(&self, xpath: &str) -> bool {
        let parts: Vec<&str> = xpath.split('/').collect();
        let n = self.xpath.len();
        let m = parts.len();

        if m > n {
            return false;
        }

        for i in 0..=(n - m) {
            if self.xpath[i..i + m]
                .iter().zip(parts.iter())
                .all(|(node, part)| node.tag == *part) {
                return true;
            }
        }

        false
    }

    pub fn endswith_xpath(&self, xpath: &str) -> bool {
        let parts: Vec<&str> = xpath.split('/').collect();
        let n = self.xpath.len();
        let m = parts.len();

        if m > n {
            return false;
        }

        for (i, part) in parts.iter().enumerate() {
            if part != &self.xpath[n - m + i].tag {
                return false;
            }
        }

        true
    }

    pub fn push(&mut self, element: XmlElement) {
        self.xpath.push(element);
    }

    pub fn pop(&mut self) -> Option<XmlElement> {
        self.xpath.pop()
    }

    pub fn last_mut(&mut self) -> Option<&mut XmlElement> {
        self.xpath.last_mut()
    }

    pub fn last(&mut self) -> Option<&XmlElement> {
        self.xpath.last()
    }

    pub fn is_empty(&self) -> bool {
        self.xpath.is_empty()
    }

    pub fn pop_tag(&mut self, tag_name: &str) -> Option<XmlElement> {
        if let Some(last_node) = self.xpath.last_mut() {
            if last_node.tag == tag_name {
                return self.xpath.pop();
            }
        }
        None
    }

    pub fn last_tag(&self) -> Option<&str> {
        if let Some(last_node) = self.xpath.last() {
            return Some(last_node.tag.as_str());
        }
        return None;
    }
}

// 解析SpringXml文件
pub fn spring_xml_parser(xml_content: &str) -> SpringXmlBeanInfo {
    let mut ret = SpringXmlBeanInfo::new();
    let mut reader = Reader::from_str(xml_content);
    reader.config_mut().trim_text(true);

    let tag_with_attrs = vec!["bean", "sofa:service", "sofa:reference"];
    let save_attrs = vec!["id", "name", "class", "interface", "ref", "unique-id"];
    // 保存Tag间文本内容的标签
    let tag_with_text = vec![];

    let mut xpath = XPath::new();
    let mut buf = Vec::new();
    loop {
        let event_result = reader.read_event_into(&mut buf);
        match event_result {
            Ok(Event::Start(ref e) | Event::Empty(ref e)) => {
                if let Ok(tag_name) = from_utf8(e.name().as_ref()) {
                    if xpath.is_empty() && tag_name != "beans" {
                        // root元素必须是<beans>
                        return ret;
                    }
                    let mut element = XmlElement::build(tag_name);

                    if tag_with_attrs.contains(&tag_name) {
                        for attr in e.attributes() {
                            if let Ok(attr) = attr {
                                if let (Ok(key), Ok(value)) = (from_utf8(attr.key.as_ref()), from_utf8(attr.value.as_ref())) {
                                    if save_attrs.contains(&key) {
                                        element.attributes.insert(key.to_string(), value.to_string());
                                    }
                                }
                            }
                        }
                    }

                    match tag_name {
                        "bean" => {
                            if let Some(class) = element.attr("class") {
                                if let Some(id) = element.attr("id") {
                                    ret.bean_map.insert(id.to_string(), class.to_string());
                                }
                                if let Some(name) = element.attr("name") {
                                    // Spring3.2 <bean id="" name="" > 规范
                                    let bean_ids = multi_split(name, [',', ';', ' '].as_ref());
                                    for bean_id in bean_ids {
                                        ret.bean_map.insert(bean_id.to_string(), class.to_string());
                                    }
                                }
                            }
                        }
                        "sofa:service" => {
                            if let (Some(ref_id), Some(interface)) = (element.attr("ref"), element.attr("interface")) {
                                let service = SofaService {
                                    ref_id: ref_id.to_string(),
                                    interface: interface.to_string(),
                                    class: None,
                                    unique_id: element.attr("unique-id").cloned(),
                                    is_rpc: false,
                                    bindings: vec![],
                                };
                                ret.sofa_service_list.push(service);
                            }
                        }
                        "sofa:reference" => {
                            if let (Some(id), Some(interface)) = (element.attr("id"), element.attr("interface")) {
                                let reference = SofaReference {
                                    id: id.to_string(),
                                    interface: interface.to_string(),
                                    unique_id: element.attr("unique-id").cloned(),
                                    is_rpc: false,
                                    bindings: vec![],
                                };
                                ret.sofa_reference_list.push(reference);
                            }
                        }
                        "sofa:binding.tr" | "sofa:binding.bolt" | "sofa:binding.rest" | "sofa:binding.dubbo"
                        | "sofa:binding.h2c" | "sofa:binding.http" | "sofa:binding.tri" | "sofa:binding.ws" => {
                            match xpath.last_tag() {
                                Some("sofa:service") => {
                                    if let Some(mut data) = ret.sofa_service_list.last_mut() {
                                        data.is_rpc = true;
                                        data.bindings.push(tag_name.to_string());
                                    }
                                }
                                Some("sofa:reference") => {
                                    if let Some(mut data) = ret.sofa_reference_list.last_mut() {
                                        data.is_rpc = true;
                                        data.bindings.push(tag_name.to_string());
                                    }
                                }
                                _ => {}
                            }
                        }
                        _ => {}
                    }

                    if let Ok(Event::Start(_)) = event_result {
                        xpath.push(element);
                    }
                }
            }
            Ok(Event::End(ref e)) => {
                if let Ok(tag_name) = from_utf8(e.name().as_ref()) {
                    xpath.pop_tag(tag_name);
                }
            }
            Ok(Event::Text(ref e)) => {
                if let Some(last_node) = xpath.last_mut() {
                    if tag_with_text.contains(&last_node.tag.as_str()) {
                        if let Ok(text) = e.unescape() {
                            last_node.text += text.as_ref();
                        }
                    }
                }
            }
            Err(e) => {
                print!("[XmlLoader] 解析XML错误, position=[{}], err={:?}", reader.error_position(), e);
                break;
            }
            Ok(Event::Eof) => break,
            _ => {}
        }
        buf.clear();
    }

    for mut sofa_service in ret.sofa_service_list.iter_mut() {
        if let Some(class) = ret.bean_map.get(&sofa_service.ref_id) {
            sofa_service.class = Some(class.to_string());
        }
    }

    return ret;
}

#[cfg(test)]
mod test {
    use std::fs;

    use log::info;

    use crate::it::loader::xml_loader::{spring_xml_parser, XmlElement, XPath};
    use crate::it::logger_init::init_logger;

    #[test]
    fn test_xpath() {
        let _ = init_logger();
        let mut xpath = XPath::new();
        xpath.push(XmlElement::build("project"));
        xpath.push(XmlElement::build("tagA"));
        xpath.push(XmlElement::build("tagB"));
        xpath.push(XmlElement::build("tagC"));
        assert_eq!(xpath.eq_xpath("project/tagA/tagB/tagC"), true);
        assert_eq!(xpath.eq_xpath("project/tagA/tagB/tagD"), false);
        assert_eq!(xpath.eq_xpath("project/tagA/tagB"), false);
        assert_eq!(xpath.match_xpath("project/tagA"), true);
        assert_eq!(xpath.match_xpath("tagA/tagB/tagC"), true);
        assert_eq!(xpath.match_xpath("tagA/tagB"), true);
        assert_eq!(xpath.match_xpath("tagA/tagC"), false);
        assert_eq!(xpath.endswith_xpath("tagB/tagC"), true);
        assert_eq!(xpath.endswith_xpath("tagA/tagB"), false);
    }

    #[test]
    fn test1() {
        let _ = init_logger();
        let path = "/Users/<USER>/tinghe-source/rcqualitydataprod/app/biz/shared/src/main/resources/spring/biz-share.xml";
        let path = "/Users/<USER>/tinghe-source/rcqualitydataprod/app/biz/service/impl/src/main/resources/spring/biz-service-impl.xml";
        let path = "/Users/<USER>/tinghe-source/rcqualitydataprod/app/common/service/integration/src/main/resources/spring/common-service-integration.xml";
        let xml_content = fs::read_to_string(path).unwrap();
        let ret = spring_xml_parser(xml_content.as_str());
        info!("ret: {}", serde_json::to_string(&ret).unwrap().to_string());
    }
}
use std::fs;
use std::path::PathBuf;
use std::sync::Arc;
use log::info;
use serde::{Deserialize, Serialize};

use crate::utils::file_utils::FileMetaInfo;

/// CodeFuseItConfig YML文件
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CodeFuseItConfigFile {
    // 配置内容
    pub config: CodeFuseItConfig,
    // 文件内容
    pub content: String,
    // 文件大小(字节)
    pub file_meta: FileMetaInfo,
}

impl CodeFuseItConfigFile {
    pub fn build(path_buf: Arc<PathBuf>) -> Self {
        let mut ret = CodeFuseItConfigFile {
            config: CodeFuseItConfig::new(),
            content: "".to_string(),
            file_meta: FileMetaInfo::build(path_buf),
        };
        ret.reload(true);
        return ret;
    }

    pub fn reload(&mut self, force: bool) {
        if force || self.file_meta.is_modified() {
            if let Ok(file_content) = fs::read_to_string(&self.file_meta.file_path.as_ref()) {
                self.config = codefuse_yml_parser(file_content.as_str());
                self.content = file_content;
            }
        }
    }

    pub fn get_config(&mut self) -> &CodeFuseItConfig {
        self.reload(false);
        return &self.config;
    }

    pub fn get_content(&mut self) -> &String {
        self.reload(false);
        return &self.content;
    }

    pub fn get_file_path(&mut self) -> PathBuf {
        return self.file_meta.file_path.as_ref().clone();
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CodeFuseItConfig {
    // [Item] 测试框架
    #[serde(default)]
    pub test_framework: Option<String>,
    // [Item] 测试基类, 全类名
    #[serde(default)]
    pub test_base_class: Option<String>,
    // [Item] 关键方法列表
    #[serde(default)]
    pub primary_method_list: Vec<String>,
    // [Item] 约束条件
    #[serde(default)]
    pub constraint: Option<String>,
    // [Item] 模板
    #[serde(default)]
    pub template: Option<String>,
    // [Item] 示例
    #[serde(default)]
    pub example: Option<String>,
}

impl CodeFuseItConfig {
    pub fn new() -> Self {
        Self {
            test_framework: None,
            test_base_class: None,
            primary_method_list: vec![],
            constraint: None,
            template: None,
            example: None,
        }
    }
}

/// 解析codefuse-it.yml文件
pub fn codefuse_yml_parser(content: &str) -> CodeFuseItConfig {
    if let Ok(config) = serde_yaml::from_str::<CodeFuseItConfig>(content).map_err(|e| {
        info!("[YmlLoader] 解析codefuse-it.yml错误: {}", e);
        e
    }) {
        return config;
    } else {
        return CodeFuseItConfig::new();
    }
}


#[cfg(test)]
mod test {
    use std::fs;
    use std::path::PathBuf;
    use std::sync::Arc;
    use std::thread::sleep;
    use log::info;

    use crate::it::loader::xml_loader::{spring_xml_parser, XmlElement, XPath};
    use crate::it::loader::yml_loader::{codefuse_yml_parser, CodeFuseItConfigFile};
    use crate::it::logger_init::init_logger;

    #[test]
    fn test1() {
        let _ = init_logger();
        let path = "/Users/<USER>/tinghe-source/afworkflow/app/bootstrap/src/test/resources/config/codefuse-it.yaml";
        let mut x = CodeFuseItConfigFile::build(Arc::new(PathBuf::from(path)));
        info!("{}", serde_json::to_string(&x.get_config()).unwrap());
        sleep(std::time::Duration::from_secs(10));
        info!("{}", serde_json::to_string(&x.get_config()).unwrap());
    }
}
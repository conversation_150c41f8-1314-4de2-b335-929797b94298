use std::{fs, thread};
use std::cmp::max;
use std::collections::{HashMap, HashSet};
use std::path::PathBuf;
use std::sync::{Arc, RwLock};
use std::time::{Instant, SystemTime};

use anyhow::{anyhow, Result};
use indexmap::IndexMap;
use log::{debug, error, info, trace};
use once_cell::sync::Lazy;
use ignore::WalkBuilder;

use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{CodeInfo, ScanFileRecord};

use crate::it::java_model::class_info::ClassInfo;
use crate::it::java_parser::common::JDK_LANG;
use crate::it::java_parser::parser::parse_class_text;
use crate::it::loader::pom_loader::{pom_xml_parser, PomProject};
use crate::it::loader::xml_loader::{filter_spring_xml, is_spring_xml, spring_xml_parser, SpringXmlBeanInfo};
use crate::it::loader::yml_loader::CodeFuseItConfigFile;
use crate::utils::file_utils::{FileMetaInfo, path_buf_to_os, path_buf_to_posix, path_str_to_os, path_str_to_posix, path_to_posix};
use crate::utils::java_utils::{find_qualified_class_name, get_bundle_path, get_package_name, split_dimension, to_package, to_posix};
use crate::utils::string_utils::{count_camel_case_words, remove_suffix, split_at, substring_after, substring_after_last, substring_before};
use crate::utils::time_utils::timeout;

pub const FILE_PREFIX: &str = "F_";

pub const FILE_CODE_FUSE_IT0: &str = "src/test/resources/config/codefuse-it.yaml";
pub const FILE_CODE_FUSE_IT1: &str = "src/test/resources/config/codefuse-it.yml";

pub static REPO_CLASS_LOADER: Lazy<Arc<RwLock<RepoClassLoader>>> = Lazy::new(|| {
    Arc::new(RwLock::new(RepoClassLoader::new()))
});

pub struct ClassInfoCache {
    pub class_info: Arc<ClassInfo>,
    // 加载时间
    pub load_time: SystemTime,
    // 文件信息
    pub file_meta: FileMetaInfo,
}

/// 本地仓库类加载器
/// 1. 文件类(java/xml/pom): 按间隔时间1h(强制)/10min(缺失)索引
/// 2. 类结构缓存(class_info_map): 只缓存加载过的类, 如果文件有修改, 则重新解析更新缓存
/// 3. 接口-实现映射(interface_impl_map): 第一次load_implements时构建索引
pub struct RepoClassLoader {
    pub project_url: String,
    // codefuse-it.yml配置文件
    pub codefuse_it_config_list: Vec<CodeFuseItConfigFile>,
    // java文件列表
    pub java_file_list: Vec<Arc<PathBuf>>,
    // 启动文件列表
    pub starter_class_list: Vec<String>,
    // 测试基类
    pub test_base_class_list: Vec<String>,
    // xml文件列表
    pub xml_file_list: Vec<Arc<PathBuf>>,
    // yml文件列表
    pub yml_file_list: Vec<PathBuf>,
    // properties文件列表
    pub props_file_list: Vec<PathBuf>,
    // pom文件列表
    pub pom_file_list: Vec<PathBuf>,
    // 类文件映射
    pub class_file_map: HashMap<String, Arc<PathBuf>>,
    // 类信息Map, 按文件修改时间缓存, 如果文件有修改则重新加载
    pub class_info_map: IndexMap<String, ClassInfoCache>,
    // pom项目列表
    pub pom_project_map: IndexMap<String, PomProject>,
    // 存储Interface与Implement的映射关系, (全部以FullClassName方式存储)
    pub interface_impl_map: HashMap<String, Vec<String>>,
    // SpringXmlBean列表
    pub spring_xml_bean_list: Vec<SpringXmlBeanInfo>,
    // 创建时间
    pub build_time: SystemTime,
    // 创建codefuse_it_config_list的时间
    pub build_codefuse_it_config_time: SystemTime,
    // 创建interface_impl的时间
    pub build_interface_time: SystemTime,
    // 创建test_base_class的时间
    pub build_test_base_time: SystemTime,
    // 创建SpringXmlBean映射的时间
    pub build_spring_xml_bean_time: SystemTime,
}

impl RepoClassLoader {
    pub fn new() -> Self {
        RepoClassLoader {
            project_url: "".to_string(),
            codefuse_it_config_list: Vec::new(),
            java_file_list: Vec::new(),
            starter_class_list: Vec::new(),
            test_base_class_list: Vec::new(),
            xml_file_list: Vec::new(),
            yml_file_list: Vec::new(),
            props_file_list: Vec::new(),
            pom_file_list: Vec::new(),
            class_file_map: HashMap::new(),
            class_info_map: IndexMap::new(),
            pom_project_map: IndexMap::new(),
            interface_impl_map: HashMap::new(),
            spring_xml_bean_list: Vec::new(),
            build_time: SystemTime::UNIX_EPOCH,
            build_codefuse_it_config_time: SystemTime::UNIX_EPOCH,
            build_interface_time: SystemTime::UNIX_EPOCH,
            build_test_base_time: SystemTime::UNIX_EPOCH,
            build_spring_xml_bean_time: SystemTime::UNIX_EPOCH,
        }
    }

    pub fn set_project_url(&mut self, project_url: &str) {
        if project_url.is_empty() {
            return;
        }
        let fmt_project_url = path_str_to_posix(project_url);
        if self.project_url != fmt_project_url {
            self.clear_repo();
        }
        self.project_url = fmt_project_url;
    }

    pub fn clear_repo(&mut self) {
        self.codefuse_it_config_list.clear();
        self.java_file_list.clear();
        self.starter_class_list.clear();
        self.test_base_class_list.clear();
        self.xml_file_list.clear();
        self.yml_file_list.clear();
        self.props_file_list.clear();
        self.pom_file_list.clear();
        self.class_file_map.clear();
        self.class_info_map.clear();
        self.pom_project_map.clear();
        self.interface_impl_map.clear();
        self.build_time = SystemTime::UNIX_EPOCH;
        self.build_codefuse_it_config_time = SystemTime::UNIX_EPOCH;
        self.build_interface_time = SystemTime::UNIX_EPOCH;
        self.build_test_base_time = SystemTime::UNIX_EPOCH;
    }

    // 达到超时时间后进行重新构建
    pub fn rebuild_repo_timeout(&mut self, secs: u64) {
        if timeout(self.build_time, secs) {
            self.inner_build_repo();
        }
    }

    fn inner_build_repo(&mut self) {
        let start = Instant::now();

        self.codefuse_it_config_list.clear();
        self.java_file_list.clear();
        self.starter_class_list.clear();
        self.xml_file_list.clear();
        self.yml_file_list.clear();
        self.props_file_list.clear();
        self.pom_file_list.clear();
        self.class_file_map.clear();
        self.build_time = SystemTime::now();

        let project_url = self.project_url.as_str();
        let files: Vec<PathBuf> = list_repo_by_local(project_url);
        for file in files {
            if let Some(ext) = file.extension() {
                match ext.to_str() {
                    Some("yaml") | Some("yml") => {
                        // 其它yml文件
                        self.yml_file_list.push(file);
                    }
                    Some("properties") => {
                        self.props_file_list.push(file);
                    }
                    Some("xml") => {
                        if let Some(file_name) = file.file_name() {
                            if file_name == "pom.xml" {
                                // 处理 pom.xml 文件
                                self.pom_file_list.push(file);
                            } else {
                                // 其它xml文件
                                self.xml_file_list.push(Arc::new(file));
                            }
                        }
                    }
                    Some("java") => {
                        let arc_file = Arc::new(file.clone());
                        self.java_file_list.push(Arc::clone(&arc_file));
                        if let Some(java_file) = path_buf_to_posix(&file) {
                            let package = to_package(java_file.as_str());
                            if package != "" {
                                if package.ends_with("Application") {
                                    self.starter_class_list.push(package.clone());
                                }
                                self.class_file_map.insert(package, arc_file);
                            }
                        }
                    }
                    Some(_) | None => {}
                }
            }
        }
        self.inner_build_codefuse_it_config();
        info!( "[ClassLoader] 构建Repo文件索引: {}ms", start.elapsed().as_millis());
    }

    // 达到超时时间后进行重新构建
    pub fn rebuild_codefuse_it_config(&mut self, secs: u64) {
        // 新增配置文件10秒内不重复构建(10秒生效)
        if (self.codefuse_it_config_list.is_empty() && timeout(self.build_codefuse_it_config_time, 10))
            || timeout(self.build_codefuse_it_config_time, secs) {
            self.inner_build_codefuse_it_config();
        }
    }

    // 搜索codefuse-it.yaml/codefuse-it.yml配置文件
    // 路径限定: ${MAVEN_PROJECT_MODULE}/src/test/resources/config/codefuse-it.yaml
    fn inner_build_codefuse_it_config(&mut self) {
        self.codefuse_it_config_list.clear();
        self.build_codefuse_it_config_time = SystemTime::now();

        for pom_file in self.pom_file_list.iter() {
            let mut yaml_config_file = pom_file.clone();
            yaml_config_file.set_file_name(FILE_CODE_FUSE_IT0);
            if yaml_config_file.is_file() {
                self.codefuse_it_config_list.push(CodeFuseItConfigFile::build(Arc::new(yaml_config_file)));
            }
            let mut yml_config_file = pom_file.clone();
            yml_config_file.set_file_name(FILE_CODE_FUSE_IT1);
            if yml_config_file.is_file() {
                self.codefuse_it_config_list.push(CodeFuseItConfigFile::build(Arc::new(yml_config_file)));
            }
        }
    }

    pub fn rebuild_test_base_timeout(&mut self, secs: u64) {
        if timeout(self.build_test_base_time, secs) {
            self.inner_build_test_base();
        }
    }

    // 查找RootPom
    pub fn get_root_pom(&mut self) -> Option<PathBuf> {
        self.pom_file_list.iter().min_by_key(|path| {
            if let Some(path_str) = path_buf_to_posix(path) {
                path_str.len()
            } else {
                usize::MAX
            }
        }).cloned()
    }

    // 构建test_base数据
    fn inner_build_test_base(&mut self) {
        let start = Instant::now();

        self.test_base_class_list.clear();
        self.build_test_base_time = SystemTime::now();

        let mut src_test_java_dir = None;

        self.rebuild_repo_timeout(AGENT_CONFIG.it_rebuild_repo_timeout);
        self.rebuild_codefuse_it_config(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
        if self.codefuse_it_config_list.is_empty() {
            // 查找最可能的测试目录
            let mut max_score: usize = 0;
            if let Some(root_pom) = self.get_root_pom() {
                if let Some(root_path) = root_pom.parent() {
                    for pom_file in self.pom_file_list.iter() {
                        if let Ok(relative_path) = pom_file.strip_prefix(root_path) {
                            if let Some(score) = path_to_posix(&relative_path).map(|s| {
                                // test路径更重要一些, 其次考虑bootstrap路径
                                let cnt_test = s.matches("test").count();
                                let cnt_bootstrap = s.matches("bootstrap").count();
                                return cnt_test * 2 + cnt_bootstrap;
                            }) {
                                if score > max_score {
                                    let mut test_dir = pom_file.clone();
                                    test_dir.set_file_name("src/test/java");
                                    if test_dir.is_dir() {
                                        max_score = score;
                                        src_test_java_dir = Some(test_dir);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            if let Some(codefuse_it_config) = self.codefuse_it_config_list.first_mut() {
                let config_file = codefuse_it_config.get_file_path();
                let it_config = codefuse_it_config.get_config();
                if let Some(test_base_class) = &it_config.test_base_class {
                    if !test_base_class.trim().is_empty() {
                        if let Some(config_file_path) = path_buf_to_posix(&config_file) {
                            let java_file = format!("src/test/java/{}.java", to_posix(test_base_class.as_str()));
                            let full_path = config_file_path.replace(FILE_CODE_FUSE_IT0, java_file.as_str());
                            let full_path = full_path.replace(FILE_CODE_FUSE_IT1, java_file.as_str());
                            let arc_file = Arc::new(PathBuf::from(full_path));
                            if arc_file.is_file() {
                                self.class_file_map.insert(test_base_class.clone(), arc_file);
                                self.test_base_class_list.push(test_base_class.clone());
                                info!( "[ClassLoader] 构建Repo测试基类索引: {}ms", start.elapsed().as_millis());
                                return;
                            }
                        }
                    }
                }

                if let Some(codefuse_it_path) = path_buf_to_posix(&config_file) {
                    if codefuse_it_path.contains("src/test/resource") {
                        let test_pom_dir = substring_before(codefuse_it_path.as_str(), "src/test/resource");
                        if !test_pom_dir.is_empty() {
                            let test_dir = PathBuf::from(test_pom_dir).join("src/test/java");
                            if test_dir.is_dir() {
                                src_test_java_dir = Some(test_dir);
                            }
                        }
                    }
                }
            }
        }

        if let Some(src_test_java) = src_test_java_dir {
            if let Some(test_path) = path_buf_to_posix(&src_test_java) {
                let test_base_list = list_test_base_java(test_path.as_str());
                for test_base in test_base_list {
                    if let Some(java_file) = path_buf_to_posix(&test_base) {
                        let package = to_package(java_file.as_str());
                        if package != "" {
                            let arc_file = Arc::new(test_base.clone());
                            self.class_file_map.insert(package.clone(), arc_file);
                            self.test_base_class_list.push(package);
                        }
                    }
                }
            }
        }

        info!( "[ClassLoader] 构建Repo测试基类索引: {}ms", start.elapsed().as_millis());
    }

    pub fn rebuild_interface_timeout(&mut self, secs: u64) {
        if timeout(self.build_interface_time, secs) {
            self.inner_build_interface_impl_map();
        }
    }

    // 构建interface->implement数据
    fn inner_build_interface_impl_map(&mut self) {
        let start = Instant::now();

        self.interface_impl_map.clear();
        self.build_interface_time = SystemTime::now();

        for java_file in self.java_file_list.iter() {
            if let Some(scan_file_record) = get_file_record_by_db(java_file) {
                if let Some(code_info) = scan_file_record.code_info {
                    let interface_list = RepoClassLoader::inner_parse_interface_list(&code_info);
                    for interface in interface_list {
                        if let Some(full_qualified_name) = &code_info.full_qualified_name {
                            self.interface_impl_map.entry(interface).or_insert(Vec::new()).push(full_qualified_name.clone());
                            continue;
                        }
                        if let Some(class_name) = &code_info.class_name {
                            self.interface_impl_map.entry(interface).or_insert(Vec::new()).push(class_name.clone());
                            continue;
                        }
                    }
                }
            }
        }

        info!( "[ClassLoader] 构建Repo接口索引: {}ms", start.elapsed().as_millis());
    }

    // 解析类实现有哪些接口
    fn inner_parse_interface_list(code_info: &CodeInfo) -> Vec<String> {
        let mut ret = Vec::new();
        if let Some(implements_class_name_set) = &code_info.implements_class_name_set {
            for interface_class_name in implements_class_name_set.iter() {
                // JDK_LANG的接口数据不进行索引, 可能有特别多实现, eg: java.lang.Cloneable
                if JDK_LANG.contains_key(interface_class_name) {
                    continue;
                }

                if let Some(import_set) = &code_info.import_set {
                    let qualified_class_name = find_qualified_class_name(interface_class_name, import_set);
                    // java开头的接口不进行索引, eg: java.io.Serializable
                    if !qualified_class_name.is_empty() {
                        if qualified_class_name.starts_with("java.") {
                            continue;
                        }
                        ret.push(qualified_class_name.to_string());
                        continue;
                    }
                }

                // 排除JDK_LANG后, 只可能是同package下
                if let Some(full_qualified_name) = &code_info.full_qualified_name {
                    let package_name = get_package_name(full_qualified_name);
                    if !package_name.is_empty() {
                        ret.push(format!("{}.{}", package_name, interface_class_name));
                        continue;
                    }
                }
                // 无包名
                ret.push(interface_class_name.to_string());
            }
        }
        return ret;
    }

    // 重新构建bean数据
    pub fn rebuild_spring_xml_bean_timeout(&mut self, secs: u64) {
        if timeout(self.build_spring_xml_bean_time, secs) {
            self.inner_build_spring_xml_bean_list();
        } else {
            // 更新修改过的xml文件
            self.inner_update_spring_xml_bean_list();
        }
    }

    fn inner_update_spring_xml_bean_list(&mut self) {
        let start = Instant::now();

        let mut count = 0;
        for spring_xml_bean in self.spring_xml_bean_list.iter_mut() {
            if spring_xml_bean.file_meta.is_modified() {
                // 如果文件已变更过
                if let Some(xml_file) = path_buf_to_posix(&spring_xml_bean.file_meta.file_path) {
                    if let Ok(content) = fs::read_to_string(xml_file.as_str()) {
                        let xml_bean_info = spring_xml_parser(content.as_str());
                        spring_xml_bean.update(xml_bean_info);
                        count += 1;
                    }
                }
            }
        }
        if count > 0 {
            info!("[ClassLoader] 更新XmlBean索引: size=[{}], time=[{}]ms", count, start.elapsed().as_millis());
        }
    }
    // 构建bean数据
    fn inner_build_spring_xml_bean_list(&mut self) {
        let start = Instant::now();

        self.spring_xml_bean_list.clear();
        self.build_spring_xml_bean_time = SystemTime::now();

        let spring_xml_list = filter_spring_xml(&self.xml_file_list, self.project_url.as_str());
        for spring_xml in spring_xml_list.iter() {
            if let Some(xml_file) = path_buf_to_posix(spring_xml) {
                if let Ok(content) = fs::read_to_string(xml_file.as_str()) {
                    let mut xml_bean_info = spring_xml_parser(content.as_str());
                    xml_bean_info.bundle = get_bundle_path(xml_file.as_str(), self.project_url.as_str());
                    xml_bean_info.file_meta = FileMetaInfo::build(Arc::clone(spring_xml));
                    self.spring_xml_bean_list.push(xml_bean_info);
                }
            }
        }
        info!("[ClassLoader] 构建XmlBean索引: {}ms", start.elapsed().as_millis());
    }

    /// 查找Java源码
    pub fn find_java_source(&mut self, class_name: &str) -> Option<Arc<PathBuf>> {
        // 大于1小时, 强制刷新一次, 可能有删除文件的情况
        self.rebuild_repo_timeout(AGENT_CONFIG.it_rebuild_repo_timeout);
        if let Some(java_file) = self.class_file_map.get(class_name) {
            return Some(Arc::clone(java_file));
        } else {
            // 大于10分钟, 如果类缺失刷新一次, 可能有新增文件的情况
            self.rebuild_repo_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            match self.class_file_map.get(class_name) {
                Some(found) => Some(Arc::clone(found)),
                None => None
            }
        }
    }

    // 加载并缓存Pom信息
    pub fn load_pom(&mut self, pom_file: PathBuf) -> Option<PomProject> {
        let start = Instant::now();
        if let Some(pom_path) = path_buf_to_posix(&pom_file) {
            if let Some(pom_project) = self.pom_project_map.get(&pom_path) {
                if !pom_project.file_meta.is_modified() {
                    trace!("[PomLoader] 复用Pom: {}ms, pom_file={}", start.elapsed().as_millis(), pom_path);
                    return Some(pom_project.clone());
                }
            }

            if let Ok(content) = fs::read_to_string(pom_path.as_str()) {
                let mut pom_project = pom_xml_parser(content.as_str());
                pom_project.file_meta = FileMetaInfo::build(Arc::new(pom_file));
                self.pom_project_map.insert(pom_path.clone(), pom_project.clone());
                // 限制最大数据量
                while self.pom_project_map.len() > AGENT_CONFIG.it_class_loader_cached_size {
                    self.pom_project_map.shift_remove_index(0);
                }
                trace!("[PomLoader] 解析Pom: {}ms, pom_file={}", start.elapsed().as_millis(), pom_path);
                return Some(pom_project);
            }
        }

        None
    }

    // 加载并缓存仓库类信息
    pub fn load_class(&mut self, qualified_class_name: &str) -> Option<Arc<ClassInfo>> {
        // 如果存在缓存, 直接返回
        let start = Instant::now();
        if let Some(class_info_cache) = self.class_info_map.get(qualified_class_name) {
            if !class_info_cache.file_meta.is_modified() {
                trace!("[ClassLoader] 复用类: {}ms, class_name={}", start.elapsed().as_millis(), qualified_class_name);
                return Some(Arc::clone(&class_info_cache.class_info));
            }
        }

        // 如果没有缓存（超缓存超时）, 则尝试直接加载类信息然后缓存
        let class_file_opt = self.find_java_source(qualified_class_name);
        if let Some(class_file) = class_file_opt {
            let new_class_info_opt = inner_parse_class_file(Arc::clone(&class_file));
            if let Some(new_class_info) = new_class_info_opt {
                let class_info = Arc::new(new_class_info);
                self.class_info_map.insert(qualified_class_name.to_string(), ClassInfoCache {
                    class_info: Arc::clone(&class_info),
                    load_time: SystemTime::now(),
                    file_meta: FileMetaInfo::build(Arc::clone(&class_file)),
                });

                // 限制最大数据量
                while self.class_info_map.len() > AGENT_CONFIG.it_class_loader_cached_size {
                    self.class_info_map.shift_remove_index(0);
                }

                return Some(class_info);
            }
        }
        return None;
    }

    // 加载接口的实现类
    pub fn load_implements(&mut self, qualified_interface_name: &str, max_ret: usize) -> Vec<ClassInfo> {
        self.rebuild_interface_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);

        let mut find_impl_list = Vec::new();
        if let Some(qualified_impl_name_list) = self.interface_impl_map.get(qualified_interface_name) {
            for qualified_impl_name in qualified_impl_name_list {
                find_impl_list.push(qualified_impl_name.clone());
                if find_impl_list.len() >= max_ret {
                    break;
                }
            }
        }

        let mut ret = Vec::new();
        for class_info in find_impl_list {
            if let Some(class_info) = self.load_class(class_info.as_str()) {
                ret.push((*class_info).clone());
            }
        }

        return ret;
    }
}

// 从CodeFuse的DB索引中查找文件
pub fn get_file_record_by_db(file: &PathBuf) -> Option<ScanFileRecord> {
    let file_path = path_buf_to_os(&file)?;
    let file_key = format!("{}{}", FILE_PREFIX, file_path);
    return match KV_CLIENT.get(&file_key) {
        Ok(file_record_opt) => {
            let file_record_value = file_record_opt?;
            let scan_file_record_opt: serde_json::Result<ScanFileRecord> = serde_json::from_str(&file_record_value);
            match scan_file_record_opt {
                Ok(scan_file_record) => {
                    Some(scan_file_record)
                }
                Err(e) => {
                    error!("解析文件索引异常: {:?}", e);
                    None
                }
            }
        }
        Err(error) => {
            error!("获取文件索引异常: {:?}", error);
            None
        }
    };
}

// 通过CodeFuse的DB索引构建仓库数据
fn list_repo_by_db(dir_path: &str) -> Vec<PathBuf> {
    let mut ret = Vec::new();
    if dir_path.is_empty() {
        return ret;
    }
    let project_file_list_key = format!("{}{}", FILE_PREFIX, path_str_to_os(dir_path));
    let result = KV_CLIENT.get_from_prefix(&project_file_list_key);
    match result {
        Ok(map_opt) => {
            if let Some(map) = map_opt {
                for (key, _) in map {
                    let file_path = substring_after(key.as_str(), FILE_PREFIX);
                    if !file_path.is_empty() {
                        let path = PathBuf::from(file_path);
                        if is_repo_path(&path) {
                            ret.push(path);
                        }
                    }
                }
            }
        }
        Err(_) => {}
    }
    return ret;
}

// 通过本地文件索引构建仓库数据
fn list_repo_by_local(dir_path: &str) -> Vec<PathBuf> {
    let mut ret = Vec::new();
    if dir_path.is_empty() {
        return ret;
    }

    for entry in WalkBuilder::new(dir_path).filter_entry(|e| is_repo_path(&e.path().to_path_buf())).build() {
        if let Ok(entry) = entry {
            let entry_path = entry.path();
            if entry_path.is_file() {
                if let Some(extension) = entry_path.extension() {
                    if extension == "xml" || extension == "java" {
                        ret.push(entry_path.to_owned());
                    }
                }
            }
        }
    }
    return ret;
}

// 查找指定的TestBase数据
fn list_test_base_java(dir_path: &str) -> Vec<PathBuf> {
    let mut ret = Vec::new();
    if dir_path.is_empty() || !dir_path.contains("src/test/java") {
        return ret;
    }

    // 调研几个系统, AbstractTestBase.java等类都在6层以内的位置
    let mut map = IndexMap::new();
    for entry in WalkBuilder::new(dir_path).max_depth(Some(7)).filter_entry(|e| is_test_base_path(&e.path().to_path_buf())).build() {
        if let Ok(entry) = entry {
            let entry_path = entry.path();
            if entry_path.is_file() {
                if let Some(extension) = entry_path.extension() {
                    if extension == "java" {
                        let score = get_file_name_score(&entry_path.to_path_buf());
                        if score >= 0 {
                            map.insert(entry_path.to_owned(), score);
                        }
                    }
                }
            }
        }
    }

    map.sort_by(|_, a, _, b| a.cmp(b));
    ret = map.keys().cloned().collect();
    return ret;
}

// 为文件名打分
fn get_file_name_score(path: &PathBuf) -> isize {
    if let Some(file_name) = path.file_name() {
        if let Some(file_name_str) = file_name.to_str() {
            if file_name_str.ends_with("AbstractTestBase.java") {
                return 0;
            }
            if file_name_str.ends_with("TestBase.java") {
                return count_camel_case_words(file_name_str) + 100;
            }
            if file_name_str.ends_with("BaseTest.java") {
                return count_camel_case_words(file_name_str) + 200;
            }
        }
    }
    return -1;
}

// 是否默认的仓库加载数据
fn is_repo_path(path: &PathBuf) -> bool {
    let include_dir = vec![];
    // JAVA/NodeJS/Python/Go/Rust/PHP/Ruby/Kotlin等, 考虑多语言工程的可能性
    let exclude_dir = vec!["target/classes", "target/test-classes", "WEB-INF/classes", "WEB-INF/lib", "build/classes", "build/libs", "src/test/resources",
                           "node_modules/", "site-packages/", "pycache/", "bin/Debug/", "Carthage/", "CocoaPods/", "pkg/mod/", "pkg/sumdb/", "pkg/gocache/",
                           "target/debug/", "gems/gems/", "vendor/bundle/", "vendor/cache/", "vendor/gems/", "vendor/plugins/"];
    return is_include_path(path, include_dir, exclude_dir);
}


// 是否查找TestBase的路径
fn is_test_base_path(path: &PathBuf) -> bool {
    // 降低误杀可能性
    let include_dir = vec!["ats/base"];
    // 排除存放大量用例的路径
    let exclude_dir = vec!["smartunit", "acts/test", "ats"];

    return is_include_path(path, include_dir, exclude_dir);
}

// 过滤路径
fn is_include_path(path: &PathBuf, include_dir: Vec<&str>, exclude_dir: Vec<&str>) -> bool {
    let is_include = path_buf_to_posix(path).map(|s| match_dir(s.as_str(), include_dir)).unwrap_or(false);
    if is_include {
        return true;
    }

    let is_hidden = path.file_name().and_then(|name| name.to_str()).map(|s| s.starts_with(".")).unwrap_or(false);
    if is_hidden {
        return false;
    }

    let is_exclude = path_buf_to_posix(path).map(|s| match_dir(s.as_str(), exclude_dir)).unwrap_or(false);
    return !is_exclude;
}

// 匹配目录
fn match_dir(path: &str, dir_list: Vec<&str>) -> bool {
    for dir in dir_list {
        if path.contains(dir) {
            return true;
        }
    }
    return false;
}

/// 查找Repo下的类文件
pub fn load_repo_class_file(project_url: &str, qualified_class_name: &str) -> Option<Arc<PathBuf>> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            return repo_class_loader.find_java_source(qualified_class_name);
        }
        Err(_) => None
    }
}

/// 查找Repo下所有java类文件路径
pub fn load_repo_all_java_file(project_url: &str) -> Vec<Arc<PathBuf>> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);

            // 大于1小时, 强制刷新一次, 可能有删除文件的情况
            repo_class_loader.rebuild_repo_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            repo_class_loader.java_file_list.clone()
        }
        Err(_) => Vec::new()
    }
}

// 内部解析类
fn inner_parse_class_file(class_file: Arc<PathBuf>) -> Option<ClassInfo> {
    let start = Instant::now();
    match fs::read_to_string(&class_file.as_ref()) {
        Ok(file_content) => {
            let ret = parse_class_text(&file_content);
            info!("[ClassLoader] 解析类: {}ms, class_name=[{}]", start.elapsed().as_millis(), ret.qualified_class_name);
            return Some(ret);
        }
        Err(_) => None
    }
}

// 构建Repo索引
pub fn build_repo(project_url: &str, build_interface: bool) {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            repo_class_loader.inner_build_repo();
            if build_interface {
                repo_class_loader.inner_build_interface_impl_map();
            }
        }
        Err(_) => {}
    }
}

// 加载实现类
pub fn load_implements(qualified_interface_name: &str, max_ret: usize) -> Vec<ClassInfo> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            return repo_class_loader.load_implements(qualified_interface_name, max_ret);
        }
        Err(_) => Vec::new()
    }
}

/// 加载并解析Repo下的类
pub fn load_repo_class(project_url: &str, qualified_class_name: &str) -> Option<Arc<ClassInfo>> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            return repo_class_loader.load_class(qualified_class_name);
        }
        Err(_) => None
    }
}

/// 加载并解析Repo下的RootPom
pub fn load_repo_root_pom(project_url: &str) -> Option<PomProject> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            repo_class_loader.rebuild_repo_timeout(AGENT_CONFIG.it_rebuild_repo_timeout);
            if let Some(pom_xml) = repo_class_loader.get_root_pom() {
                return repo_class_loader.load_pom(pom_xml);
            }
            None
        }
        Err(_) => None
    }
}

/// 加载并解析Repo下的Pom
pub fn load_repo_pom(project_url: &str, pom_xml: PathBuf) -> Option<PomProject> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            repo_class_loader.rebuild_repo_timeout(AGENT_CONFIG.it_rebuild_repo_timeout);
            return repo_class_loader.load_pom(pom_xml);
        }
        Err(_) => None
    }
}

/// 加载类文件到ret内
/// Feature: 支持ClassName[][]
// TODO: 支持ClassName<K,V>
// TODO: 支持加载InnerClass, 也可能是InnerClass/Sibling
// TODO: InnerClass/Sibling做为Field/Argument/LocalVar/SuperClass
pub fn load_repo_class_to(project_url: &str, qualified_class_name: &str, ret: &mut ClassInfo) -> bool {
    let (class_name, dimension) = split_dimension(qualified_class_name);
    if let Some(loaded_class_info) = load_repo_class(project_url, class_name) {
        // 获取原始对象的引用
        ret.clone_from(&loaded_class_info);
        // 升维到之前的类型
        ret.increase_dimension(dimension);
        // 重新设置方法的class_ref
        ret.init_method_info();
        // 只要能找到类文件就退出, 这里不判断是否能解析成功
        return true;
    }
    return false;
}

/// 获取配置文件列表
pub fn load_codefuse_it_config_files(project_url: &str) -> Vec<CodeFuseItConfigFile> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            repo_class_loader.rebuild_repo_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            repo_class_loader.rebuild_codefuse_it_config(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            return repo_class_loader.codefuse_it_config_list.clone();
        }
        Err(_) => Vec::new()
    }
}

/// 获取第一个配置文件
pub fn load_codefuse_it_config_file_first(project_url: &str) -> Option<CodeFuseItConfigFile> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            repo_class_loader.rebuild_repo_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            repo_class_loader.rebuild_codefuse_it_config(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            return repo_class_loader.codefuse_it_config_list.first().cloned();
        }
        Err(_) => None
    }
}

/// 获取测试基类列表
pub fn load_test_base_class(project_url: &str) -> Vec<String> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            repo_class_loader.rebuild_test_base_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            return repo_class_loader.test_base_class_list.clone();
        }
        Err(_) => Vec::new()
    }
}

/// 获取启动类
pub fn load_starter_class(project_url: &str) -> Vec<String> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            // 对时间不敏感时, 使用更长的时间
            repo_class_loader.rebuild_repo_timeout(AGENT_CONFIG.it_rebuild_repo_timeout);
            return repo_class_loader.starter_class_list.clone();
        }
        Err(_) => Vec::new()
    }
}

/// 获取xml文件列表
pub fn load_xml_files(project_url: &str) -> Vec<Arc<PathBuf>> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            repo_class_loader.rebuild_repo_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            return repo_class_loader.xml_file_list.clone();
        }
        Err(_) => Vec::new()
    }
}

/// 获取yml文件列表
pub fn load_yml_files(project_url: &str) -> Vec<PathBuf> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            repo_class_loader.rebuild_repo_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            return repo_class_loader.yml_file_list.clone();
        }
        Err(_) => Vec::new()
    }
}

/// 获取props文件列表
pub fn load_props_files(project_url: &str) -> Vec<PathBuf> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            repo_class_loader.rebuild_repo_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            return repo_class_loader.props_file_list.clone();
        }
        Err(_) => Vec::new()
    }
}

/// 获取spring_xml_bean列表
pub fn load_spring_xml_bean_list(project_url: &str) -> Vec<SpringXmlBeanInfo> {
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            repo_class_loader.rebuild_repo_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            repo_class_loader.rebuild_spring_xml_bean_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            return repo_class_loader.spring_xml_bean_list.clone();
        }
        Err(_) => Vec::new()
    }
}

/// 处理spring_xml_bean列表
pub fn fn_mut_spring_xml_bean_list<F, R>(project_url: &str, mut callback: F) -> Result<R>
where
    F: FnMut(&Vec<SpringXmlBeanInfo>) -> R,
{
    match REPO_CLASS_LOADER.write() {
        Ok(mut repo_class_loader) => {
            repo_class_loader.set_project_url(project_url);
            repo_class_loader.rebuild_repo_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            repo_class_loader.rebuild_spring_xml_bean_timeout(AGENT_CONFIG.it_missing_rebuild_repo_timeout);
            Ok(callback(&repo_class_loader.spring_xml_bean_list))
        }
        Err(e) => Err(anyhow!("[ClassLoader] 读取数据(REPO_CLASS_LOADER)失败: {}", e))
    }
}

#[cfg(test)]
mod test {
    use std::thread::sleep;
    use std::time::{Duration, Instant};

    use log::info;

    use crate::it::loader::class_loader::{fn_mut_spring_xml_bean_list, list_repo_by_local, list_test_base_java, load_repo_root_pom, load_spring_xml_bean_list, REPO_CLASS_LOADER, RepoClassLoader};
    use crate::it::loader::pom_loader::PomProject;
    use crate::it::logger_init::init_logger;
    use crate::utils::java_utils::get_bundle_path;

    #[test]
    fn test1() {
        let list = list_repo_by_local("/Users/<USER>/tinghe-source/rcqualitydataprod");
        for item in list {
            println!("{:?}", item)
        }
    }

    #[test]
    fn test2() {
        let mut repo_class_loader = REPO_CLASS_LOADER.write().unwrap();
        repo_class_loader.set_project_url("/Users/<USER>/tinghe-source/rcqualitydataprod");
        let a = repo_class_loader.find_java_source("com.alipay.rcqualitydataprod.core.model.convert.instemc.InstemConverter");
        println!("{:?}", a)
    }

    #[test]
    fn test_pom() {
        let _ = init_logger();
        let pom_project = load_repo_root_pom("/Users/<USER>/tinghe-source/rcqualitydataprod");
        info!("{}", serde_json::to_string(&pom_project).unwrap());
    }


    #[test]
    fn test3() {
        let list = list_test_base_java("/Users/<USER>/tinghe-source/rcqualitydataprod/app/test/src/test/java");
        for item in list {
            println!("{:?}", item)
        }
    }

    #[test]
    fn test4() {
        let mut class_loader = RepoClassLoader::new();
        class_loader.set_project_url("/Users/<USER>/tinghe-source/afworkflow");
        class_loader.inner_build_repo();
        class_loader.inner_build_test_base();
        println!("{}", serde_json::to_string(&class_loader.test_base_class_list).unwrap());
    }

    #[test]
    fn test5() {
        let _ = init_logger();
        let mut class_loader = RepoClassLoader::new();
        class_loader.set_project_url("/Users/<USER>/tinghe-source/rcqualitydataprod");
        class_loader.inner_build_repo();
        let start = Instant::now();
        class_loader.inner_build_spring_xml_bean_list();

        println!("{}ms", start.elapsed().as_millis());
        println!("{}", serde_json::to_string(&class_loader.java_file_list).unwrap());
    }

    #[test]
    fn test6() {
        let _ = init_logger();
        let start = Instant::now();
        loop {
            let bundle_bean_map = load_spring_xml_bean_list("/Users/<USER>/tinghe-source/rcqualitydataprod");
            info!("{}ms", start.elapsed().as_millis());
            info!("{}", serde_json::to_string(&bundle_bean_map).unwrap());
            sleep(Duration::from_secs(5));
        }
    }


    #[test]
    fn test8() {
        let _ = init_logger();
        let start = Instant::now();

        let qualified_class_name = "com.alipay.rcqualitydataprod.biz.service.mobilegw.impl.RcqualitydataGwFacadeImpl";
        let file_url = "app/biz/service/impl/src/main/java/com/alipay/rcqualitydataprod/biz/service/mobilegw/impl/RcqualitydataGwFacadeImpl.java";
        let project_url = "/Users/<USER>/tinghe-source/rcqualitydataprod";
        let bundle = get_bundle_path(&file_url, project_url);

        loop {
            let result = fn_mut_spring_xml_bean_list(project_url, |spring_xml_bean_list| {
                for spring_xml_bean in spring_xml_bean_list {
                    if spring_xml_bean.exist_rpc_service(qualified_class_name) {
                        return true;
                    }
                }
                return false;
            });

            if let Ok(result) = result {
                info!("{}ms", start.elapsed().as_millis());
                info!("{}", serde_json::to_string(&result).unwrap());
            }
            sleep(Duration::from_secs(1));
        }
    }
}
use std::cmp::PartialEq;

use indexmap::IndexMap;
use serde::{Deserialize, Serialize};

use crate::it::java_model::class_info::ClassInfo;
use crate::it::java_model::func_call::ArgumentType::{Any, Later, Type};
use crate::it::java_model::method_info::MethodInfo;
use crate::it::java_parser::context::{AccessRecorder, ExpandContext};
use crate::utils::java_utils::{match_java_class, METHOD_NAME_LOG};

// 函数调用信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FuncCallInfo {
    // 【Debug】方法调用深度
    pub call_depth: isize,
    // [扩展] 调用类型
    pub call_type: FuncCallType,
    // 目标对象
    pub target_object: String,
    //  [扩展] 目标类, 有可能分析不出来
    pub target_type: Option<String>,
    // 方法名
    pub method_name: String,
    // 字段完整定义
    pub code_body: String,
    // 调用方法参数
    #[serde(skip, serialize_with = "serialize_indexmap", deserialize_with = "deserialize_indexmap")]
    pub args_map: IndexMap<String, ArgumentType<ClassInfo>>,
    //  [扩展] 方法信息, 解析后才有具体数据
    pub callee_method_info: Option<MethodInfo>,
}

#[derive(Debug, Serialize, Deserialize, PartialEq, Eq, Clone)]
pub enum FuncCallType {
    // [初始化] 未知类型, 以下所有类型都是已确认类型, Unknown用于初始化数据
    Unknown,
    // 类内调用(包括类内静态方法), eg: func()
    Inner,
    // 静态方法调用, eg: String.valueOf("");
    Static,
    // 参数调用, param.func()
    ParamCall,
    // 属性调用, field.func()
    FieldCall,
    // 属性调用-Bean调用, bean.func()
    BeanCall,
    // 变量调用, variable.func()
    VarCall,
    // [不支持] 链式调用, eg: obj.obj.func(), obj.func().func(), func().func(), 例外:this.field.func()
    ChainCall,
    // [不支持] 其它调用, eg: new Thread(() -> {}).start()
    Other,
}

/// 调用入参数类型
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ArgumentType<T> {
    /// 任意类型. 无法确定具体类型
    Any,
    /// [后置处理] 参数类型, 类加载后再处理引用数据
    /// 0: 节点kind, 1: 后置处理数据
    Later(String, LaterData),
    /// 已确定具体类型 `T`.
    Type(T),
}

/// [后置处理] 调用入参数类型后置处理数据
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LaterData {
    // 节点数据
    pub text: String,
    // 维度信息
    pub dimensions: String,
}

impl LaterData {
    pub fn build(text: &str) -> Self {
        LaterData {
            text: text.to_string(),
            dimensions: "".to_string(),
        }
    }
}

impl FuncCallInfo {
    pub fn new(target_object: String, method_name: String) -> Self {
        FuncCallInfo {
            call_depth: -1,
            call_type: FuncCallType::Unknown,
            target_object,
            target_type: None,
            method_name,
            code_body: "".to_string(),
            callee_method_info: None,
            args_map: IndexMap::new(),
        }
    }

    pub fn get_qualified_signature(&self) -> String {
        let args = self.args_map.values().map(|e| {
            match e {
                Type(e) => e.get_correct_class_name(),
                _ => "?".to_string(),
            }
        }).collect::<Vec<_>>().join(", ");
        return if let Some(target_type) = &self.target_type {
            format!("{}#{}({})", target_type, self.method_name, args)
        } else {
            format!("{}#{}({})", self.target_object, self.method_name, args)
        };
    }

    // 是否日志方法
    pub fn is_log_call(&self) -> bool {
        self.target_object.to_lowercase().contains("log") && METHOD_NAME_LOG.contains(&self.method_name.as_str())
    }

    // 判断是否需要展开
    pub fn is_skip_expand(&self) -> bool {
        if self.is_log_call() {
            return true;
        }
        return false;
    }

    pub fn init_callee_method_info(&mut self, ref_class: &mut ClassInfo, ref_method: &mut MethodInfo, ref_func_call: &Vec<&mut FuncCallInfo>, class_info: &mut ClassInfo, expand_context: &mut ExpandContext) {
        self.target_type = Some(class_info.get_correct_class_name());
        if !expand_context.is_expand_func_call(self) {
            // 限制该func_call进行扩展
            return;
        }

        if self.is_skip_expand() {
            // 加载类之前判断, 使用猜测类名
            return;
        }

        class_info.load_class(expand_context);
        self.target_type = Some(class_info.qualified_class_name.clone());

        if self.is_skip_expand() {
            // 加载类之后判断, 已确定下正确类名
            return;
        }

        // 在查找调用方法之前完成args_type的设置
        self.expand_args_type(ref_class, ref_method, ref_func_call, expand_context);
        if let Some(callee_method_info) = class_info.find_callee_method(self, expand_context) {
            self.callee_method_info = Some(callee_method_info.clone());
        }
    }

    // 确定call_type
    pub fn init_func_call(&mut self, ref_class: &mut ClassInfo, ref_method: &mut MethodInfo, ref_func_call: &Vec<&mut FuncCallInfo>, expand_context: &mut ExpandContext, access_recorder: &mut AccessRecorder) {
        if self.target_object == "" || self.target_object == "this" || self.target_object == "super" {
            self.call_type = FuncCallType::Inner;
            let mut class_info = ref_class.clone();
            self.init_callee_method_info(ref_class, ref_method, ref_func_call, &mut class_info, expand_context);
            return;
        }

        if self.target_object.contains("(") {
            self.call_type = FuncCallType::ChainCall;
            if let Some(return_type) = FuncCallInfo::get_last_call_return_type(ref_func_call, self.target_object.as_str()).as_mut() {
                self.init_callee_method_info(ref_class, ref_method, ref_func_call, return_type, expand_context);
            }
            return;
        }

        // 简化this.field.func()或super.field.func()
        if self.target_object.contains(".") {
            let parts: Vec<&str> = self.target_object.split(".").collect();
            if parts.len() == 2 && (parts[0] == "this" || parts[0] == "super") {
                self.target_object = parts[1].to_string();
                self.call_type = FuncCallType::FieldCall;
                // 继续向后处理
            } else {
                self.call_type = FuncCallType::ChainCall;
                if let Some(return_type) = FuncCallInfo::get_last_call_return_type(ref_func_call, self.target_object.as_str()).as_mut() {
                    self.init_callee_method_info(ref_class, ref_method, ref_func_call, return_type, expand_context);
                }
                return;
            }
        }

        if let Some(field) = ref_class.field_map.get_mut(self.target_object.as_str()) {
            self.call_type = FuncCallType::FieldCall;
            if field.is_bean() {
                self.call_type = FuncCallType::BeanCall;
            }
            let bean_type = field.init_bean_type(expand_context.project_url.as_str());
            access_recorder.record_field(field);
            let mut field_type = field.field_type.clone();
            self.init_callee_method_info(ref_class, ref_method, ref_func_call, &mut field_type, expand_context);
            // 记录调用方法
            access_recorder.record_func_call(&self, &field_type, ref_class, &bean_type, expand_context);
            return;
        }

        if let Some(var) = ref_method.local_var_map.get(self.target_object.as_str()) {
            self.call_type = FuncCallType::VarCall;
            let mut var_type = var.clone();
            self.init_callee_method_info(ref_class, ref_method, ref_func_call, &mut var_type, expand_context);
            return;
        }

        if let Some(param) = ref_method.param_map.get(self.target_object.as_str()) {
            self.call_type = FuncCallType::ParamCall;
            let mut param_type = param.clone();
            self.init_callee_method_info(ref_class, ref_method, ref_func_call, &mut param_type, expand_context);
            return;
        }

        if match_java_class(self.target_object.as_str()) {
            self.call_type = FuncCallType::Static;
            let mut target_type = ref_class.new_class(self.target_object.as_str());
            self.init_callee_method_info(ref_class, ref_method, ref_func_call, &mut target_type, &mut expand_context.clone_with(|x| x.match_method_modifier = Some("static".to_string())));
            return;
        }

        self.call_type = FuncCallType::Other;
    }

    // 扩展参数类型
    pub fn expand_args_type(&mut self, ref_class: &mut ClassInfo, ref_method: &mut MethodInfo, ref_func_call: &Vec<&mut FuncCallInfo>, expand_context: &mut ExpandContext) {
        for (key, value) in self.args_map.iter_mut() {
            match value {
                Any => {}
                Later(kind, later_data) => {
                    match kind.as_str() {
                        "field_access" => {
                            let field_list: Vec<&str> = later_data.text.split(".").collect();
                            // eg: value | value[0].field[0].a
                            if let Some(mut field_info) = ref_method.find_local_var_by_path(field_list.clone(), expand_context) {
                                *value = Type(field_info.for_arg_type(later_data, expand_context));
                                continue;
                            }
                            if let Some(mut field_info) = ref_method.find_param_by_path(field_list.clone(), expand_context) {
                                *value = Type(field_info.for_arg_type(later_data, expand_context));
                                continue;
                            }
                            // eg: super.field | super.field[0][0].a[0][0] | super.field[].length
                            if let Some(mut field_info) = ref_class.find_field_by_path(field_list, expand_context) {
                                *value = Type(field_info.field_type.for_arg_type(later_data, expand_context));
                                continue;
                            }
                        }
                        "identifier" => {
                            // field
                            if let Some(mut field_info) = ref_class.find_field_by_name(later_data.text.as_str(), expand_context) {
                                *value = Type(field_info.field_type.for_arg_type(later_data, expand_context));
                                continue;
                            }
                        }
                        "method_invocation" => {
                            // 函数调用
                            if let Some(return_type) = FuncCallInfo::get_last_call_return_type(ref_func_call, key.as_str()) {
                                *value = Type(return_type);
                                continue;
                            }
                        }
                        _ => {}
                    }
                    *value = Any;
                }
                Type(ref mut arg_type) => {
                    arg_type.load_class(expand_context);
                    arg_type.prune_type_info();
                }
            }
        }
    }

    pub fn expand_one_func_call(&mut self, ref_class: &mut ClassInfo, ref_method: &mut MethodInfo, ref_func_call: &Vec<&mut FuncCallInfo>, expand_context: &mut ExpandContext, access_recorder: &mut AccessRecorder) {
        if expand_context.max_func_call_depth.is_limit() || expand_context.time_limit.is_limit() {
            return;
        }

        self.call_depth = ref_method.call_depth;
        self.init_func_call(ref_class, ref_method, ref_func_call, expand_context, access_recorder);

        if let Some(method) = self.callee_method_info.as_mut() {
            method.call_depth = ref_method.call_depth + 1;
            method.expand_function_call(&mut expand_context.clone_with(|x| x.max_func_call_depth.inc()), access_recorder);
        }
    }

    pub fn get_last_call_return_type(ref_func_call: &Vec<&mut FuncCallInfo>, expr: &str) -> Option<ClassInfo> {
        if let Some(last_call) = ref_func_call.last() {
            if let Some(last_call_return) = &last_call.callee_method_info {
                if last_call.code_body == expr {
                    return Some(last_call_return.return_type.clone());
                }
            }
        }
        return None;
    }
}


use serde::{Deserialize, Serialize};

use crate::it::java_model::class_info::ClassInfo;
use crate::it::loader::class_loader::fn_mut_spring_xml_bean_list;
use crate::utils::java_utils::{has_rpc_reference_annotation, is_dao_class};

/// 字段信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FieldInfo {
    // 字段Bean类型
    pub bean_type: Option<BeanType>,
    // 标识符
    pub modifiers: String,
    // 字段名称
    pub field_name: String,
    // 字段类型
    pub field_type: ClassInfo,
    // 注释
    pub comment: Option<String>,
    // 字段完整定义
    pub code_body: String,
}

/// 枚举字段信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EnumConstantInfo {
    // 字段名称
    pub field_name: String,
    // 代码实现定义
    pub code_body: String,
}

/// Bean类型
#[derive(Debug, Serialize, Deserialize, <PERSON>lone, PartialEq, Eq)]
pub enum BeanType {
    Field,
    Bean,
    Rpc,
    Dao,
}

impl FieldInfo {
    // 初始化bean类型
    pub fn init_bean_type(&mut self, project_url: &str) -> BeanType {
        match &self.bean_type {
            None => {
                self.bean_type = Some(BeanType::Field);
                if self.is_bean() {
                    self.bean_type = Some(BeanType::Bean);
                    if self.is_dao() {
                        self.bean_type = Some(BeanType::Dao);
                    } else if self.is_rpc_reference(project_url) {
                        self.bean_type = Some(BeanType::Rpc);
                    }
                }
                return self.bean_type.clone().unwrap();
            }
            Some(bean_type) => bean_type.clone()
        }
    }

    pub fn is_dao(&self) -> bool {
        return is_dao_class(self.field_type.modifiers.as_str(), self.field_type.qualified_class_name.as_str());
    }

    pub fn is_bean(&self) -> bool {
        return self.modifiers.contains("@Resource")
            || self.modifiers.contains("@Autowired")
            || self.modifiers.contains("@SofaReference")
            || self.modifiers.contains("@RpcConsumer")
            || self.modifiers.contains("@Qualifier")
            || self.modifiers.contains("@Named")
            || self.modifiers.contains("@Inject");
    }

    // 判断是否是RPC引用
    pub fn is_rpc_reference(&self, project_url: &str) -> bool {
        if has_rpc_reference_annotation(self.modifiers.as_str(), true) {
            return true;
        }

        // 查找SofaRpcReference的Xml声明
        let result = fn_mut_spring_xml_bean_list(project_url, |spring_xml_bean_list| {
            for spring_xml_bean in spring_xml_bean_list.iter() {
                if spring_xml_bean.exist_rpc_reference_or(self.field_name.as_str(), self.field_type.qualified_class_name.as_str()) {
                    return true;
                }
            }
            return false;
        });

        return result.unwrap_or(false);
    }

    pub fn get_qualified_field_signature(&self) -> String {
        return format!("{}#{}", self.field_type.qualified_class_name, self.field_name);
    }

    pub fn prune_basic_info(&mut self) {
        self.code_body = "".to_string();
        self.field_type.super_class = None;
        self.field_type.super_interface_list.clear();
        self.field_type.prune_basic_info(false, false);
    }
}
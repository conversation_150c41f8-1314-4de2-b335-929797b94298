use indexmap::IndexMap;
use log::info;
use serde::{Deserialize, Serialize};

use crate::it::java_model::class_type::ClassType;
use crate::it::java_model::common::DEBUG_CLASS;
use crate::it::java_model::field_info::{BeanType, EnumConstantInfo, FieldInfo};
use crate::it::java_model::func_call::{FuncCallInfo, LaterData};
use crate::it::java_model::method_info::{MethodInfo, MethodMatchLevel};
use crate::it::java_parser::common::{JDK_LANG, JDK_PRIMARY, JDK_PRIMARY_ALIAS, JDK_UTIL, JDK_WIDENING_CONVERSION_1, JDK_WIDENING_CONVERSION_2};
use crate::it::java_parser::context::{AccessRecorder, ExpandContext};
use crate::it::loader::class_loader::{fn_mut_spring_xml_bean_list, load_implements, load_repo_class_to};
use crate::utils::index_map::deserialize_indexmap;
use crate::utils::index_map::serialize_indexmap;
use crate::utils::java_utils::{has_http_service_annotation, has_rpc_service_annotation, is_dal_param, list_type_parameter_name, split_dimension, with_annotation};
use crate::utils::string_utils::{remove_suffix, split_at, substring_before, trim_whitespaces};
use crate::utils::vec_utils::index_less_equal;

/// 类结构
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ClassInfo {
    // 候选列表, 如果package是猜测出来的, 则将候选列表放在这里, 最终在加载类文件的时候最终确认
    #[serde(skip)]
    pub search_qualified_class_name: Vec<String>,
    // 完整类名(package.name)
    pub qualified_class_name: String,
    // 父类
    pub super_class: Option<Box<ClassInfo>>,
    // 类型
    pub class_type: ClassType,
    // 类名, (SimpleClassName)
    pub class_name: String,
    // 注释
    pub comment: Option<String>,
    // 包名
    pub package_name: String,
    // ImportSet信息, 常规import, 不包括*/static
    #[serde(skip)]
    pub import_list: Vec<String>,
    // ImportStar信息: import a.b.*
    #[serde(skip)]
    pub import_star: Vec<String>,
    // ImportStatic信息: import static
    #[serde(skip)]
    pub import_static: Vec<String>,
    // ImportMap信息: SimpleName -> QualifiedName
    #[serde(skip, serialize_with = "serialize_indexmap", deserialize_with = "deserialize_indexmap")]
    pub import_map: IndexMap<String, String>,
    // 父类
    pub super_interface_list: Vec<ClassInfo>,
    // 属性->类型映射
    #[serde(serialize_with = "serialize_indexmap", deserialize_with = "deserialize_indexmap")]
    pub field_map: IndexMap<String, FieldInfo>,
    // 枚举->映射
    #[serde(serialize_with = "serialize_indexmap", deserialize_with = "deserialize_indexmap")]
    pub enum_constant_list: IndexMap<String, EnumConstantInfo>,
    // 方法列表
    pub method_list: Vec<MethodInfo>,
    // 描述
    pub modifiers: String,
    // 模板类参数名
    #[serde(skip)]
    pub generic_name_list: Vec<String>,
    // 模板类参数类型
    pub generic_type_list: Vec<ClassInfo>,
    // 已加载, 当尝试加载过后就是已加载了
    pub loaded: bool,
    // 是否属于Project/src
    pub project_src: bool,
    // 兄弟类, 与outer类并列在同一个Java文件中
    #[serde(skip)]
    pub sibling_class_list: Vec<ClassInfo>,
    // 内部类, 在当前类文件内部实现
    #[serde(skip)]
    pub inner_class_list: Vec<ClassInfo>,
    // 实现类, 如果当前是接口, 需要查找到对应的实现类
    #[serde(skip)]
    pub implements_class_list: Vec<ClassInfo>,
}

impl ClassInfo {
    pub fn new() -> Self {
        ClassInfo {
            class_type: ClassType::Class,
            class_name: String::new(),
            comment: None,
            package_name: String::new(),
            search_qualified_class_name: Vec::new(),
            qualified_class_name: String::new(),
            import_list: Vec::new(),
            import_star: Vec::new(),
            import_static: Vec::new(),
            import_map: IndexMap::new(),
            super_class: None,
            super_interface_list: Vec::new(),
            field_map: IndexMap::new(),
            enum_constant_list: IndexMap::new(),
            method_list: Vec::new(),
            modifiers: String::new(),
            generic_name_list: Vec::new(),
            generic_type_list: Vec::new(),
            loaded: false,
            project_src: false,
            sibling_class_list: Vec::new(),
            inner_class_list: Vec::new(),
            implements_class_list: Vec::new(),
        }
    }

    pub fn void() -> Self {
        let mut ret = Self::new();
        ret.set_class_name("void");
        ret.set_class_type(ClassType::Primary);
        ret.qualified_class_name = "void".to_string();
        ret.loaded = true;
        return ret;
    }

    pub fn debug_info(&self) {
        if DEBUG_CLASS {
            info!("ClassInfo({})={}", self.class_name, serde_json::to_string(self).unwrap());
        }
    }

    pub fn new_class(&mut self, class_name: &str) -> ClassInfo {
        let mut ret = Self::new();
        if self.class_type == ClassType::PojoClass {
            // Pojo类型传递
            ret.set_class_type(self.class_type.clone());
        }
        // 先设置class_type再设置class_name(会根据情况更新class_type)
        ret.set_class_name(class_name);
        // 推断候选class_name
        let search_class_list = self.search_qualified_class_name(ret.class_name.as_str());
        ret.set_search_qualified_class_name(search_class_list);

        // 处理解析中的模板类, eg: Result<Long, String> / Map<String,T>
        let type_parameters = list_type_parameter_name(class_name);
        for type_parameter in type_parameters {
            ret.generic_type_list.push(self.new_class(type_parameter));
        }
        return ret;
    }

    pub fn new_method(&self) -> MethodInfo {
        let mut method_info = MethodInfo::new();
        method_info.init_class_info(self.qualified_class_name.clone());
        return method_info;
    }

    pub fn is_public(&self) -> bool {
        self.modifiers.contains("public")
    }

    pub fn is_array(&self) -> bool {
        self.qualified_class_name.ends_with("[]")
    }

    pub fn is_class_type(&self, class_type: ClassType) -> bool {
        self.class_type == class_type
    }

    pub fn is_http_service(&self) -> bool {
        return has_http_service_annotation(self.modifiers.as_str());
    }

    pub fn is_rpc_service(&self, project_url: &str) -> bool {
        if has_rpc_service_annotation(self.modifiers.as_str(), true) {
            return true;
        }

        // 查找SofaRpcService的Xml声明
        let result = fn_mut_spring_xml_bean_list(project_url, |spring_xml_bean_list| {
            for spring_xml_bean in spring_xml_bean_list.iter() {
                if spring_xml_bean.exist_rpc_service(self.qualified_class_name.as_str()) {
                    // 默认只在本Bundle中搜索
                    return true;
                }
            }
            return false;
        });

        return result.unwrap_or(false);
    }

    // 是否import了一部分包名
    pub fn contains_import_package(&self, partial_package_name: &str) -> bool {
        return self.import_list.iter().any(|x| x.contains(partial_package_name))
            || self.import_star.iter().any(|x| x.contains(partial_package_name))
            || self.import_static.iter().any(|x| x.contains(partial_package_name));
    }

    // 决定下一个class_name是什么
    pub fn next_class_name(&mut self) -> bool {
        if self.search_qualified_class_name.is_empty() {
            return false;
        }
        let first = self.search_qualified_class_name.remove(0);
        self.set_qualified_class_name(first.as_str());
        return true;
    }

    pub fn init_primary_class_type(&mut self) -> &Self {
        if JDK_PRIMARY.contains_key(self.qualified_class_name.as_str()) {
            self.class_type = ClassType::Primary;
        }
        if JDK_LANG.contains_key(self.qualified_class_name.as_str()) {
            self.loaded = true;
        }
        if self.qualified_class_name.starts_with("java.") {
            self.class_type = ClassType::JdkClass;
        }
        return self;
    }

    pub fn set_class_type(&mut self, value: ClassType) -> &Self {
        self.class_type = value;
        return self;
    }

    // 尝试转为Pojo
    pub fn try_to_pojo(mut self) -> Self {
        self.try_set_pojo();
        return self;
    }

    // 尝试转为Pojo
    pub fn try_set_pojo(&mut self) {
        if self.class_type == ClassType::Class {
            // 只有普通Class类型可转为Pojo, 其它已知类型不能再修改
            self.class_type = ClassType::PojoClass;
        }
    }

    pub fn for_arg_type(&mut self, later_data: &mut LaterData, expand_context: &mut ExpandContext) -> ClassInfo {
        let mut arg_type = self.clone();
        arg_type.decrease_dimension(later_data.dimensions.as_str());
        arg_type.load_class(expand_context);
        arg_type.prune_type_info();
        return arg_type;
    }

    /// 只保留基础类型信息, 其它数据都不需要
    pub fn prune_type_info(&mut self) {
        self.import_list.clear();
        self.import_star.clear();
        self.import_static.clear();
        self.import_map.clear();
        self.field_map.clear();
        self.enum_constant_list.clear();
        self.method_list.clear();
        self.sibling_class_list.clear();
        self.inner_class_list.clear();
        if let Some(superclass) = self.super_class.as_mut() {
            superclass.prune_type_info();
        }
    }

    /// 设置成Pojo
    pub fn prune_pojo(&mut self) {
        if self.class_type == ClassType::PojoClass {
            // Pojo类型需要清空不需要的数据
            // 清空方法列表
            self.method_list.clear();
            // 字段按Pojo处理
            for (_, field_item) in self.field_map.iter_mut() {
                field_item.field_type.try_set_pojo();
                field_item.field_type.prune_pojo();
            }
            // 模板类按Pojo处理
            for generic_type in self.generic_type_list.iter_mut() {
                generic_type.try_set_pojo();
                generic_type.prune_pojo();
            }
            // 父类按Pojo处理
            if let Some(superclass) = self.super_class.as_mut() {
                superclass.try_set_pojo();
                superclass.prune_pojo();
            }

            return;
        } else if self.class_type == ClassType::Enum {
            self.method_list.clear();
            return;
        }
    }

    // 最终统一剪枝, 最后剪枝时统一再处理prune, 因为param/return也可能被方法func_call调用, 需要分析
    pub fn prune_finally(&mut self) {
        self.prune_pojo();
        self.implements_class_list.clear();
        self.inner_class_list.clear();
        self.sibling_class_list.clear();
        for method_info in self.method_list.iter_mut() {
            method_info.prune_finally();
        }
    }

    // 按访问数据剪枝
    pub fn prune_access_recorder(&mut self, access_recorder: &AccessRecorder) {
        self.prune_finally();
        self.field_map.retain(|_, v| access_recorder.field_counter.contains_key(v.get_qualified_field_signature().as_str()));
        self.method_list.retain(|m| access_recorder.method_counter.contains_key(m.get_qualified_method_signature().as_str()));
    }

    // 只保留最基本的数据信息, 只保留一级关联数据
    pub fn prune_basic_info(&mut self, keep_field: bool, keep_method: bool) {
        self.import_list.clear();
        self.import_star.clear();
        self.import_static.clear();
        self.import_map.clear();
        self.generic_name_list.clear();
        self.generic_type_list.clear();
        self.implements_class_list.clear();
        self.inner_class_list.clear();
        self.sibling_class_list.clear();

        for super_interface in self.super_interface_list.iter_mut() {
            super_interface.super_interface_list.clear();
            super_interface.prune_basic_info(false, false);
        }
        for super_class in self.super_class.iter_mut() {
            super_class.super_class = None;
            super_class.prune_basic_info(false, false);
        }

        if keep_field {
            for (_, field_info) in self.field_map.iter_mut() {
                field_info.prune_basic_info();
            }
        } else {
            self.field_map.clear();
        }

        if keep_method {
            for method_info in self.method_list.iter_mut() {
                method_info.prune_basic_info();
            }
        } else {
            self.method_list.clear();
        }
    }

    pub fn set_class_name(&mut self, value: &str) -> &Self {
        self.class_name = trim_whitespaces(value);
        self.init_qualified_class_name();
        return self;
    }

    pub fn set_package_name(&mut self, value: &str) -> &Self {
        let package_name = value.trim_end_matches(';').split_whitespace().last().unwrap_or("");
        self.package_name = package_name.to_string();
        self.init_qualified_class_name();
        return self;
    }

    pub fn set_search_qualified_class_name(&mut self, value: Vec<String>) -> &Self {
        self.search_qualified_class_name = value;
        self.next_class_name();
        return self;
    }

    pub fn set_qualified_class_name(&mut self, value: &str) -> &Self {
        self.qualified_class_name = value.to_string();
        let parts: Vec<&str> = value.rsplitn(2, '.').collect();
        self.class_name = parts[0].to_string();
        if let Some(package_name) = parts.get(1) {
            self.package_name = package_name.to_string();
        }
        self.init_primary_class_type();
        return self;
    }

    // 非常确认完整类名时使用此方法
    pub fn ensure_qualified_class_name(&mut self, value: &str) -> &Self {
        self.set_qualified_class_name(value);
        self.search_qualified_class_name.clear();
        return self;
    }

    // 获取当前正确的ClassName, 如果还没有load要么返回唯一的qualified_class_name要么返回simple_class_name
    pub fn get_correct_class_name(&self) -> String {
        if self.loaded || self.search_qualified_class_name.is_empty() {
            return self.qualified_class_name.clone();
        }
        return self.class_name.clone();
    }

    pub fn add_import(&mut self, value: &str) -> &Self {
        let parts: Vec<&str> = value.trim_end_matches(';').split_whitespace().collect();
        let value = parts.last().unwrap_or(&"");
        if value.ends_with(".*") {
            self.import_star.push(value.to_string());
            return self;
        }

        if let Some(&"static") = parts.get(1) {
            self.import_static.push(value.to_string());
            return self;
        }

        self.import_list.push(value.to_string());

        if let Some(last_name) = value.split(".").last() {
            self.import_map.insert(last_name.to_string(), value.to_string());
        }
        return self;
    }

    pub fn push_field(&mut self, field_info: FieldInfo) {
        self.field_map.insert(field_info.field_name.clone(), field_info);
    }

    pub fn push_method(&mut self, mut method: MethodInfo) {
        method.init_method_signature();
        method.init_complexity_score();
        self.method_list.push(method);
    }

    // 通过class_name/package_name计算全类名
    pub fn init_qualified_class_name(&mut self) {
        if self.class_name.is_empty() {
            return;
        }
        if self.package_name == "" {
            self.qualified_class_name = self.class_name.to_string();
        } else {
            self.qualified_class_name = format!("{}.{}", self.package_name, self.class_name);
        }
        self.init_primary_class_type();
    }

    // 查找全类名, eg: com.alipay.ClassName
    pub fn search_qualified_class_name(&self, class_name: &str) -> Vec<String> {
        let (raw_class_name, dimension) = split_dimension(class_name);

        // 优先从import中精确查询
        if let Some(import) = self.import_map.get(raw_class_name) {
            return vec![import.to_string() + dimension];
        }
        // 其次从JDK中查询
        if let Some(import) = JDK_LANG.get(raw_class_name) {
            return vec![import.to_string() + dimension];
        }

        let mut ret = Vec::new();

        // 与目前class有相同的package
        if !self.package_name.is_empty() {
            ret.push(format!("{}.{}", self.package_name, class_name));
        }

        // 从import_star中候选
        for import in self.import_star.iter() {
            ret.push(import.replace("*", class_name));
        }

        // 要把自己添加上来, 1.没有任何package的情况, 2. 直接指定类全名, 3. InnerClass
        ret.push(class_name.to_string());

        if self.import_star.contains(&String::from("java.util.*")) {
            // 从java.util中查询是否存在, 如果存在则最后选取java.util
            if let Some(import) = JDK_UTIL.get(raw_class_name) {
                let jdk_util_class = import.to_string() + dimension;
                if let Some(pos) = ret.iter().position(|x| *x == jdk_util_class) {
                    let data = ret.remove(pos);
                    ret.push(data);
                }
            }
        }
        return ret;
    }

    // 提升public类定义
    pub fn promote_public_sibling(&mut self) {
        if self.is_public() {
            return;
        }

        if let Some(idx) = self.sibling_class_list.iter().position(|x| x.is_public()) {
            let mut sibling_class_list = self.sibling_class_list.clone();
            let mut public_info = sibling_class_list[idx].clone();
            sibling_class_list[idx] = self.clone();
            public_info.sibling_class_list = sibling_class_list;
            self.clone_from(&public_info);
        }
    }

    pub fn find_method_signature(&self, method_signature: &str) -> Option<&MethodInfo> {
        for method_info in self.method_list.iter() {
            if method_info.method_signature == method_signature {
                return Some(&method_info);
            }
        }
        None
    }

    // 查找接口中匹配的方法
    pub fn find_interface_method(&mut self, find_method_info: &MethodInfo, expand_context: &mut ExpandContext) -> Option<(&mut MethodInfo, MethodMatchLevel)> {
        let mut best_match = None;
        let mut best_match_level = MethodMatchLevel::None;

        for super_interface in self.super_interface_list.iter_mut() {
            if let Some((found_method, match_level)) = super_interface.find_method(find_method_info, expand_context) {
                if match_level.gt(&best_match_level) {
                    best_match_level = match_level;
                    best_match = Some((found_method, best_match_level.clone()));
                }
            }
        }

        return best_match;
    }

    // 查找匹配方法
    pub fn find_method(&mut self, find_method_info: &MethodInfo, expand_context: &mut ExpandContext) -> Option<(&mut MethodInfo, MethodMatchLevel)> {
        let mut best_match = None;
        let mut best_match_level = MethodMatchLevel::None;

        self.load_class(expand_context);

        for method_info in self.method_list.iter_mut() {
            if method_info.skip_construct(expand_context) {
                continue;
            }

            let match_level = method_info.match_method(find_method_info);

            if match_level == MethodMatchLevel::ParamAll {
                // 全匹配直接返回
                return Some((method_info, MethodMatchLevel::ParamAll));
            }

            if match_level.gt(&best_match_level) {
                best_match_level = match_level;
                best_match = Some((method_info, best_match_level.clone()));
            }
        }

        best_match
    }

    // 查找func_call调用的目标方法
    pub fn find_callee_method(&mut self, func_call: &mut FuncCallInfo, expand_context: &mut ExpandContext) -> Option<&MethodInfo> {
        self.inner_find_callee_method(func_call, expand_context).map(|(info, _)| info)
    }

    fn inner_find_callee_method(&mut self, func_call: &FuncCallInfo, expand_context: &mut ExpandContext) -> Option<(&MethodInfo, MethodMatchLevel)> {
        let mut best_match = None;
        let mut best_match_level = MethodMatchLevel::MethodName; // 从MethodName起步

        self.load_class(expand_context);

        if self.is_class_type(ClassType::Interface) {
            // 如果当前是对接口方法的调用, 需要映射到具体实现上, 加载具体实现
            self.load_implements(expand_context);
            if !self.implements_class_list.is_empty() {
                // 查找复杂度最高的那个实现
                let mut max_complexity_score = -1;
                for mut implements_class in self.implements_class_list.iter_mut() {
                    if let Some((impl_method, impl_level)) = implements_class.inner_find_callee_method(func_call, expand_context) {
                        if impl_method.complexity_score > max_complexity_score {
                            max_complexity_score = impl_method.complexity_score;
                            best_match = Some((impl_method, impl_level.clone()));
                        }
                    }
                }
                return best_match;
            }
            // 如果没有找到任何实现, 继续使用Interface的方法进行处理
        }

        for method_info in self.method_list.iter() {
            if method_info.skip_construct(expand_context) {
                continue;
            }

            // 必须为指定标识, eg: public/static
            if let Some(match_modifier) = &expand_context.match_method_modifier {
                if !method_info.modifiers.contains(match_modifier) {
                    continue;
                }
            }

            let match_level = method_info.match_func_call(func_call);

            if match_level == MethodMatchLevel::ParamAll {
                // 全匹配直接返回
                return Some((method_info, MethodMatchLevel::ParamAll));
            }

            if match_level.gt(&best_match_level) {
                best_match_level = match_level;
                best_match = Some((method_info, best_match_level.clone()));
            }
        }

        if let Some(superclass) = self.super_class.as_mut() {
            if let Some((super_method, super_level)) = superclass.inner_find_callee_method(func_call, expand_context) {
                if super_level.gt(&best_match_level) {
                    best_match_level = super_level;
                    best_match = Some((super_method, best_match_level.clone()));
                }
            }
        }

        best_match
    }

    // 路径查找, 例如: a.b.c / super.x.a.b.c
    pub fn find_field_by_path(&mut self, mut field_path: Vec<&str>, expand_context: &mut ExpandContext) -> Option<FieldInfo> {
        if field_path.is_empty() {
            return None;
        }

        let field_name = field_path.remove(0).trim();
        if field_path.is_empty() {
            // 标识符
            return self.find_field_by_name(field_name, expand_context);
        }

        if field_name == "this" {
            return self.find_field_by_path(field_path, expand_context);
        }
        if field_name == "super" {
            return match self.super_class.as_mut() {
                None => None,
                Some(superclass) => {
                    return superclass.find_field_by_path(field_path, expand_context);
                }
            };
        }

        match self.find_field_by_name(field_name, expand_context) {
            None => None,
            Some(mut field_info) => {
                return field_info.field_type.find_field_by_path(field_path, expand_context);
            }
        }
    }

    pub fn find_field_by_name(&mut self, field_name: &str, expand_context: &mut ExpandContext) -> Option<FieldInfo> {
        if expand_context.find_expand {
            self.load_class(expand_context);
        }
        if self.is_array() && field_name == "length" {
            return Some(FieldInfo {
                bean_type: None,
                modifiers: "".to_string(),
                field_name: "length".to_string(),
                field_type: self.new_class("int"),
                comment: None,
                code_body: "".to_string(),
            });
        }

        let mut the_field_name = field_name;

        let mut dimension = "".to_string();
        let dimension_count = field_name.matches("[").count();
        if dimension_count > 0 {
            the_field_name = substring_before(field_name, "[");
            dimension = "[]".repeat(dimension_count);
        }

        if let Some(field_info) = self.field_map.get_mut(the_field_name.trim()) {
            let mut clone = field_info.clone();
            clone.field_type.decrease_dimension(dimension.as_str());
            return Some(clone);
        }

        if let Some(superclass) = self.super_class.as_mut() {
            return superclass.find_field_by_name(field_name, expand_context);
        }
        return None;
    }

    // 初始化MethodInfo, 补充信息
    pub fn init_method_info(&mut self) {
        for method_info in self.method_list.iter_mut() {
            method_info.init_class_info(self.qualified_class_name.clone());
        }
    }

    // 扩展加载Field实现
    pub fn init_field_info(&mut self, project_url: &str) {
        for (_, field_item) in self.field_map.iter_mut() {
            field_item.init_bean_type(project_url);
        }
    }

    // 定义哪些类是忽略加载的
    pub fn is_skip_load(&self, expand_context: &ExpandContext) -> bool {
        // 是否为dal.Param类型(AliGenerator生成的Param加载慢, 无必要解析)
        if is_dal_param(self.qualified_class_name.as_str()) {
            return true;
        }
        return false;
    }

    // 加载类
    pub fn load_class(&mut self, expand_context: &ExpandContext) {
        if self.loaded {
            return;
        }

        let class_type = self.class_type.clone();
        let generic_type_list = self.generic_type_list.clone();
        loop {
            if !self.is_skip_load(expand_context) {
                let project_url = expand_context.project_url.as_str();
                let qualified_class_name = self.qualified_class_name.clone();
                if load_repo_class_to(project_url, qualified_class_name.as_str(), self) {
                    if class_type == ClassType::PojoClass {
                        // 类加载时不会识别PojoClass等类型, 重新设置应对的ClassType
                        // 只有Pojo不是类加载时能确定的, 其它的类型都由类加载时设定
                        self.set_class_type(class_type);
                    }
                    self.generic_type_list = generic_type_list;
                    return;
                }
            }

            if !self.next_class_name() {
                // 没有候选类名时退出, 可能是Library
                self.class_type = ClassType::LibClass;
                self.loaded = true;
                return;
            }
        }
    }

    // 加载接口对应的实现类
    pub fn load_implements(&mut self, expand_context: &ExpandContext) {
        if self.is_class_type(ClassType::Interface) {
            // 如果当前是对接口方法的调用, 需要映射到具体实现上
            let mut implements_list = load_implements(self.qualified_class_name.as_str(), expand_context.max_implements_limit);
            for implements in implements_list {
                if implements.is_skip_load(expand_context) {
                    continue;
                }
                self.implements_class_list.push(implements);
            }
        }
    }

    // 扩展加载实现
    pub fn expand_class_info(&mut self, expand_context: &mut ExpandContext) {
        self.load_class(expand_context);
        self.expand_superclass(expand_context);
        self.expand_field_map(expand_context);
        self.expand_generic_type(expand_context);
    }

    // 扩展加载Field实现
    pub fn expand_superclass(&mut self, expand_context: &mut ExpandContext) {
        if expand_context.max_super_depth.is_limit() || expand_context.time_limit.is_limit() {
            return;
        }
        if let Some(superclass_ref) = self.super_class.as_mut() {
            superclass_ref.expand_class_info(&mut expand_context.clone_with(|x| x.max_super_depth.inc()));
        }
    }

    // 扩展加载模板实现
    pub fn expand_generic_type(&mut self, expand_context: &mut ExpandContext) {
        if expand_context.max_field_depth.is_limit() || expand_context.time_limit.is_limit() {
            return;
        }

        for generic_type in self.generic_type_list.iter_mut() {
            generic_type.expand_class_info(&mut expand_context.clone_with(|x| x.max_field_depth.inc()));
        }
    }

    // 扩展加载Field实现
    pub fn expand_field_map(&mut self, expand_context: &mut ExpandContext) {
        if expand_context.max_field_depth.is_limit() || expand_context.time_limit.is_limit() {
            return;
        }

        for (_, field_item) in self.field_map.iter_mut() {
            field_item.field_type.expand_class_info(&mut expand_context.clone_with(|x| x.max_field_depth.inc()));
        }
    }

    // 扩展加载Method实现
    pub fn expand_all_method_info(&mut self, expand_context: &mut ExpandContext, access_recorder: &mut AccessRecorder) {
        self.load_class(expand_context);
        for method in self.method_list.iter_mut() {
            method.expand_return_type(expand_context);
            method.expand_param_type(expand_context);
            method.expand_function_call(expand_context, access_recorder);
        }
    }

    // 降维, eg: dimension=[][]
    pub fn decrease_dimension(&mut self, dimension: &str) {
        if dimension.is_empty() {
            return;
        }
        self.class_name = remove_suffix(self.class_name.as_str(), dimension).to_string();
        self.qualified_class_name = remove_suffix(self.qualified_class_name.as_str(), dimension).to_string();
        self.search_qualified_class_name.iter_mut().for_each(|class_name| {
            *class_name = remove_suffix(class_name, dimension).to_string();
        });
    }

    // 升维, eg: dimension=[][]
    pub fn increase_dimension(&mut self, dimension: &str) {
        if dimension.is_empty() {
            return;
        }
        self.class_name += dimension;
        self.qualified_class_name += dimension;
        self.search_qualified_class_name.iter_mut().for_each(|class_name| {
            *class_name += dimension;
        });
    }

    // 是否全等
    pub fn is_equal(&self, arg_type: &Self) -> bool {
        self.qualified_class_name == arg_type.qualified_class_name
    }

    // 获取别类名(eg: java.lang.Integer -> int)
    fn get_alias_class_name(&self) -> String {
        if self.is_class_type(ClassType::Primary) {
            return self.qualified_class_name.clone();
        }
        if let Some(alias_type) = JDK_PRIMARY_ALIAS.get(self.qualified_class_name.as_str()) {
            return alias_type.clone();
        } else {
            return self.qualified_class_name.clone();
        }
    }

    // 是否可从入参拓宽
    fn can_widening_conversion(&self, arg_type: &Self) -> bool {
        let to_type = self.get_alias_class_name();
        let from_type = arg_type.get_alias_class_name();

        if index_less_equal(&JDK_WIDENING_CONVERSION_1, from_type.as_str(), to_type.as_str()) {
            return true;
        }

        if index_less_equal(&JDK_WIDENING_CONVERSION_2, from_type.as_str(), to_type.as_str()) {
            return true;
        }

        return false;
    }


    // self做为param时，是否能接受arg_type做为入参
    pub fn is_acceptable(&self, arg_type: &Self) -> bool {
        // 全等判断
        if self.is_equal(&arg_type) {
            return true;
        }

        // extends
        if let Some(superclass) = &arg_type.super_class {
            if self.is_acceptable(superclass) {
                return true;
            }
        }

        // implements
        for super_interface in arg_type.super_interface_list.iter() {
            if self.is_acceptable(super_interface) {
                return true;
            }
        }

        // 自动拓宽转换
        if self.can_widening_conversion(arg_type) {
            return true;
        }
        // object接受一切
        return self.qualified_class_name == "java.lang.Object";
    }
}

/// 模板类
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct GenericParam {
    pub name: Option<String>,
    pub class_info: Option<ClassInfo>,
}
use serde::{Deserialize, Serialize};

// Java类的分类
#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize, Clone)]
pub enum ClassType {
    // 默认为Class, 如果是Class类型则需要进一步判断
    Class,
    // 主要存储数据, 忽略方法实现列表, 加载时不加载方法列表, 主要用于param/return
    PojoClass,
    // Jdk中的类型: java.*
    JdkClass,
    // library中的类
    LibClass,
    // 基本类型
    Primary,
    // 枚举
    Enum,
    // 接口
    Interface,
    // 注解
    Annotation,
}

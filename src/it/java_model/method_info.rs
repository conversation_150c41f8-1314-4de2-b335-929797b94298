use std::cmp::Ordering;
use std::collections::HashSet;
use std::sync::Arc;

use indexmap::IndexMap;
use serde::{Deserialize, Serialize};

use crate::it::java_model::class_info::ClassInfo;
use crate::it::java_model::func_call::ArgumentType::Type;
use crate::it::java_model::func_call::FuncCallInfo;
use crate::it::java_parser::context::{AccessRecorder, ExpandContext};
use crate::it::loader::class_loader::load_repo_class;
use crate::utils::index_map::deserialize_indexmap;
use crate::utils::index_map::serialize_indexmap;
use crate::utils::java_utils::{get_simple_class_name, has_http_method_annotation};
use crate::utils::string_utils::substring_before;

/// 方法信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MethodInfo {
    // 【Debug】方法调用深度
    pub call_depth: isize,
    // 所属类信息
    pub class_info_ref: Option<String>,
    // 方法名
    pub method_name: String,
    // 注释
    pub comment: Option<String>,
    // 方法modifiers
    pub modifiers: String,
    // 方法签名(method(Class))
    pub method_signature: String,
    // 方法签名(ClassName#methodName(ArgClass))
    #[serde(skip)]
    pub qualified_method_signature: String,
    // 方法体
    pub code_body: Arc<String>,
    // 入参
    #[serde(serialize_with = "serialize_indexmap", deserialize_with = "deserialize_indexmap")]
    pub param_map: IndexMap<String, ClassInfo>,
    // 出参
    pub return_type: ClassInfo,
    // 方法调用信息
    pub function_call_list: Vec<FuncCallInfo>,
    // 本地变量定义
    #[serde(skip, serialize_with = "serialize_indexmap", deserialize_with = "deserialize_indexmap")]
    pub local_var_map: IndexMap<String, ClassInfo>,
    // 是否构造函数
    #[serde(skip)]
    pub is_constructor: bool,
    // return计数
    #[serde(skip)]
    pub count_return: isize,
    // throw计数
    #[serde(skip)]
    pub count_throw: isize,
    // if计数
    #[serde(skip)]
    pub count_if: isize,
    // switch计数
    #[serde(skip)]
    pub count_switch: isize,
    // for计数
    #[serde(skip)]
    pub count_for: isize,
    // while计数
    #[serde(skip)]
    pub count_while: isize,
    // lambda计数
    #[serde(skip)]
    pub count_lambda: isize,
    // 函数复杂度
    pub complexity_score: isize,
    // 函数起止位置，rust中tree-sitter解析出来是从第一行开始，但是python/插件前端传过来的是从第0行开始，需要注意
    pub start_end_position: (usize, usize),
}

impl MethodInfo {
    pub fn new() -> Self {
        MethodInfo {
            call_depth: 0,
            class_info_ref: None,
            method_signature: "".to_string(),
            qualified_method_signature: "".to_string(),
            method_name: "".to_string(),
            comment: None,
            modifiers: "".to_string(),
            code_body: Arc::new("".to_string()),
            param_map: IndexMap::new(),
            return_type: ClassInfo::void(),
            function_call_list: Vec::new(),
            local_var_map: IndexMap::new(),
            is_constructor: false,
            count_return: 0,
            count_throw: 0,
            count_if: 0,
            count_switch: 0,
            count_for: 0,
            count_while: 0,
            count_lambda: 0,
            complexity_score: 0,
            start_end_position: (0, 0), // 0 为初始化值，需要判断
        }
    }

    pub fn get_qualified_method_signature(&self) -> String {
        return self.qualified_method_signature.clone();
    }

    pub fn set_code_body(&mut self, text: &str) {
        self.code_body = Arc::new(text.to_string());
    }
    // 关联所属类
    pub fn init_class_info(&mut self, qualified_class_name: String) {
        self.class_info_ref = Some(qualified_class_name);
    }

    // 生成签名
    pub fn init_method_signature(&mut self) {
        if let Some(ref_class) = &self.class_info_ref {
            self.method_signature = format!("{}#{}({})", get_simple_class_name(ref_class), self.method_name, self.param_map.values().map(|e| e.class_name.clone()).collect::<Vec<_>>().join(", "));
            self.qualified_method_signature = format!("{}#{}({})", ref_class, self.method_name, self.param_map.values().map(|e| e.qualified_class_name.clone()).collect::<Vec<_>>().join(", "));
        } else {
            self.method_signature = format!("{}({})", self.method_name, self.param_map.values().map(|e| e.class_name.clone()).collect::<Vec<_>>().join(", "));
            self.qualified_method_signature = format!("{}({})", self.method_name, self.param_map.values().map(|e| e.qualified_class_name.clone()).collect::<Vec<_>>().join(", "));
        }
    }

    pub fn is_http_method(&self) -> bool {
        return has_http_method_annotation(self.modifiers.as_str());
    }

    // 跳过构造函数
    pub fn skip_construct(&self, expand_context: &ExpandContext) -> bool {
        expand_context.skip_constructor && self.is_constructor
    }

    // 方法匹配度, 匹配: 方法名/参数个数/参数类型
    pub fn match_method(&self, other: &MethodInfo) -> MethodMatchLevel {
        // 名称相同
        if self.method_name != other.method_name {
            return MethodMatchLevel::None;
        }

        // 参数个数相同
        if self.param_map.len() != other.param_map.len() {
            return MethodMatchLevel::MethodName;
        }


        // (全等,转换,Any)
        let mut param_num = (0, 0, 0);
        for (idx, (_, param_type)) in self.param_map.iter().enumerate() {
            if let Some((_, arg_type)) = other.param_map.get_index(idx) {
                if param_type.is_equal(arg_type) {
                    param_num.0 += 1;
                } else if param_type.is_acceptable(arg_type) {
                    param_num.1 += 1;
                } else {
                    // 参数类型无法匹配
                    return MethodMatchLevel::ParamCount;
                }
            }
        }

        if self.param_map.len() == param_num.0 {
            return MethodMatchLevel::ParamAll;
        } else {
            return MethodMatchLevel::ParamNum(param_num.0, param_num.1, param_num.2);
        }
    }

    // 方法实现与方法调用匹配度
    pub fn match_func_call(&self, func_call: &FuncCallInfo) -> MethodMatchLevel {
        // 名称相同
        if self.method_name != func_call.method_name {
            return MethodMatchLevel::None;
        }

        // 参数个数相同
        if self.param_map.len() != func_call.args_map.len() {
            return MethodMatchLevel::MethodName;
        }

        // (全等,转换,Any)
        let mut param_num = (0, 0, 0);
        for (idx, (_, param_type)) in self.param_map.iter().enumerate() {
            if let Some((_, argument_type)) = func_call.args_map.get_index(idx) {
                if let Type(arg_type) = argument_type {
                    if param_type.is_equal(arg_type) {
                        param_num.0 += 1;
                    } else if param_type.is_acceptable(arg_type) {
                        param_num.1 += 1;
                    } else {
                        // 参数类型无法匹配
                        return MethodMatchLevel::ParamCount;
                    }
                } else {
                    // Later/Any都按Any计算
                    param_num.2 += 1;
                }
            }
        }

        if self.param_map.len() == param_num.0 {
            return MethodMatchLevel::ParamAll;
        } else {
            return MethodMatchLevel::ParamNum(param_num.0, param_num.1, param_num.2);
        }
    }

    pub fn push_func_call(&mut self, func_call: FuncCallInfo) {
        self.function_call_list.push(func_call);
    }

    // 扩展加载返回值类型
    pub fn expand_return_type(&mut self, expand_context: &mut ExpandContext) {
        self.return_type.expand_class_info(expand_context);
    }

    // 扩展加载参数类型
    pub fn expand_param_type(&mut self, expand_context: &mut ExpandContext) {
        for (_, param_type) in self.param_map.iter_mut() {
            param_type.expand_class_info(expand_context);
        }
        self.init_method_signature();
    }

    // 扩展加载方法调用
    pub fn expand_function_call(&mut self, expand_context: &mut ExpandContext, access_recorder: &mut AccessRecorder) {
        if expand_context.max_func_call_depth.is_limit() || expand_context.time_limit.is_limit() {
            return;
        }
        access_recorder.record_method(self);
        let ref_method = &mut self.clone_as_ref();
        if let Some(ref_class) = self.ref_class(expand_context).as_mut() {
            // 如果能加载到类, 再扩展, 否则就没有扩展的必要了(信息不全, 扩展不出内容)
            let mut ref_func_call = Vec::new();
            for func_call in self.function_call_list.iter_mut().rev() {
                func_call.expand_one_func_call(ref_class, ref_method, &ref_func_call, expand_context, access_recorder);
                ref_func_call.push(func_call);
            }
        }
        self.prune_func_call(&mut HashSet::new());
    }

    // 未扩展的方法剪枝掉
    pub fn prune_func_call(&mut self, his_callee_method: &mut HashSet<String>) {
        self.function_call_list.retain(|x| x.callee_method_info.is_some());
        for func_call in self.function_call_list.iter_mut() {
            if let Some(method_info) = &mut func_call.callee_method_info {
                if his_callee_method.contains(method_info.qualified_method_signature.as_str()) {
                    // 在之前已调用过了, 属于同一个method反复调用的情况
                    func_call.callee_method_info = None;
                } else {
                    his_callee_method.insert(method_info.qualified_method_signature.clone());
                    method_info.prune_func_call(his_callee_method);
                    // 将callee_method中的调用参数清空, 没办法将所有数据放在prompt中, 需要进行数据精简
                    // 数据优先级: code_body > return_type > param_map
                    method_info.param_map.clear();
                }
            }
        }
    }

    /// 参数与返回值进行Pojo剪枝
    pub fn prune_pojo(&mut self) {
        for (_, field_item) in self.param_map.iter_mut() {
            field_item.try_set_pojo();
            field_item.prune_pojo();
        }

        self.return_type.try_set_pojo();
        self.return_type.prune_pojo();
    }

    // 最终剪枝
    pub fn prune_finally(&mut self) {
        self.prune_pojo();
        self.prune_func_call(&mut HashSet::new());
        self.local_var_map.clear();
    }

    // 只保留基本信息
    pub fn prune_basic_info(&mut self) {
        self.function_call_list.clear();
        self.code_body = Arc::new("".to_string());
        self.local_var_map.clear();
        self.return_type.prune_basic_info(false, false);
        self.param_map.clear();
    }

    // 在ref中引用哪些数据分别需要clone
    pub fn clone_as_ref(&mut self) -> Self {
        let mut ret = MethodInfo::new();
        ret.local_var_map = self.local_var_map.clone();
        ret.param_map = self.param_map.clone();
        ret.call_depth = self.call_depth;
        return ret;
    }

    // 引用类
    pub fn ref_class(&mut self, expand_context: &mut ExpandContext) -> Option<ClassInfo> {
        if let Some(class_info_ref) = &self.class_info_ref {
            let project_url = expand_context.project_url.as_str();
            if let Some(loaded_class_info) = load_repo_class(project_url, class_info_ref) {
                return Some((*loaded_class_info).clone());
            }
        }
        None
    }

    // 从本地变量开始查找
    pub fn find_local_var_by_path(&mut self, mut field_path: Vec<&str>, expand_context: &mut ExpandContext) -> Option<ClassInfo> {
        if field_path.is_empty() {
            return None;
        }

        let field_name = field_path.remove(0).trim();
        if field_path.is_empty() {
            // 标识符
            return self.find_local_var_by_name(field_name, expand_context);
        }

        match self.find_local_var_by_name(field_name, expand_context) {
            None => None,
            Some(mut class_info) => {
                match class_info.find_field_by_path(field_path, expand_context) {
                    None => None,
                    Some(field_info) => {
                        return Some(field_info.field_type.clone());
                    }
                }
            }
        }
    }

    pub fn find_local_var_by_name(&mut self, var_name: &str, expand_context: &mut ExpandContext) -> Option<ClassInfo> {
        let mut the_var_name = var_name;

        let mut dimension = "".to_string();
        let dimension_count = var_name.matches("[").count();
        if dimension_count > 0 {
            the_var_name = substring_before(var_name, "[");
            dimension = "[]".repeat(dimension_count);
        }

        if let Some(class_info) = self.local_var_map.get_mut(the_var_name.trim()) {
            let mut clone = class_info.clone();
            clone.decrease_dimension(dimension.as_str());
            return Some(clone);
        }

        return None;
    }

    // 从入参变量开始查找
    pub fn find_param_by_path(&mut self, mut field_path: Vec<&str>, expand_context: &mut ExpandContext) -> Option<ClassInfo> {
        if field_path.is_empty() {
            return None;
        }

        let field_name = field_path.remove(0).trim();
        if field_path.is_empty() {
            // 标识符
            return self.find_param_by_name(field_name, expand_context);
        }

        match self.find_param_by_name(field_name, expand_context) {
            None => None,
            Some(mut class_info) => {
                match class_info.find_field_by_path(field_path, expand_context) {
                    None => None,
                    Some(field_info) => {
                        return Some(field_info.field_type.clone());
                    }
                }
            }
        }
    }

    pub fn find_param_by_name(&mut self, var_name: &str, expand_context: &mut ExpandContext) -> Option<ClassInfo> {
        let mut the_var_name = var_name;

        let mut dimension = "".to_string();
        let dimension_count = var_name.matches("[").count();
        if dimension_count > 0 {
            the_var_name = substring_before(var_name, "[");
            dimension = "[]".repeat(dimension_count);
        }

        if let Some(class_info) = self.param_map.get_mut(the_var_name.trim()) {
            let mut clone = class_info.clone();
            clone.decrease_dimension(dimension.as_str());
            return Some(clone);
        }

        return None;
    }

    // 计算方法复杂度
    pub fn init_complexity_score(&mut self) -> isize {
        self.complexity_score = self.count_return + self.count_throw + self.count_if + self.count_switch + self.count_for + self.count_while + self.count_lambda;
        self.complexity_score
    }
}

/// 方法匹配等级
#[repr(u8)]
#[derive(Debug, PartialEq, Eq, PartialOrd, Clone)]
pub enum MethodMatchLevel {
    // 无匹配
    None = 0,
    // 方法名匹配
    MethodName = 1,
    // 参数个数匹配
    ParamCount = 2,
    // 部分匹配, 给出匹配个数(全等/兼容/Any)
    ParamNum(usize, usize, usize) = 3,
    // 所有参数匹配
    ParamAll = 4,
}

impl Ord for MethodMatchLevel {
    fn cmp(&self, other: &Self) -> Ordering {
        match (self, other) {
            // 如果是两个ParamNum类型
            (MethodMatchLevel::ParamNum(self_eq, self_cast, self_any), MethodMatchLevel::ParamNum(other_eq, other_cast, other_any)) => {
                // 判断any, 越小越好
                if self_any < other_any {
                    return Ordering::Greater;
                }
                if self_any > other_any {
                    return Ordering::Less;
                }
                // 判断cast, 越小越好
                if self_cast < other_cast {
                    return Ordering::Greater;
                }
                if self_cast > other_cast {
                    return Ordering::Less;
                }
                // 判断eq, 越大越好
                if self_eq > other_eq {
                    return Ordering::Greater;
                }
                if self_eq < other_eq {
                    return Ordering::Less;
                }
                // 全部相等
                return Ordering::Equal;
            }
            _ => {
                if self > other {
                    return Ordering::Greater;
                }
                if self < other {
                    return Ordering::Less;
                }
                return Ordering::Equal;
            }
        }
    }
}

#[cfg(test)]
mod tests {
    #[test]
    fn test_cmp() {}
}
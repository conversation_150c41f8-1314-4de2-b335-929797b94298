use log::info;

use crate::api::req_model::QueryRelatedRequestBean;
use crate::it::java_model::class_info::ClassInfo;
use crate::it::java_model::method_info::MethodInfo;
use crate::it::java_parser::context::ExpandContext;
use crate::it::loader::class_loader::{load_codefuse_it_config_file_first, load_repo_class};
use crate::utils::string_utils::{rsplit_at, split_at, substring_after, substring_before, substring_before_last};

/// 解析codefuse-it配置文件
pub fn parser_codefuse_config(query_related_request: &QueryRelatedRequestBean) -> Option<String> {
    let mut config_file = load_codefuse_it_config_file_first(query_related_request.projectUrl.as_str());
    return config_file.map(|mut f| f.get_content().clone());
}

pub fn load_primary_method_list(mut expand_context: &mut ExpandContext) -> Vec<MethodInfo> {
    let old_skip_constructor = expand_context.skip_constructor;
    expand_context.skip_constructor = false;
    let mut config_file = load_codefuse_it_config_file_first(expand_context.project_url.as_str());
    let ret = config_file.map(|mut f| {
        let mut find_method_list = vec![];
        let primary_method_list = &f.get_config().primary_method_list;
        for primary_method in primary_method_list {
            if let Some(mut method_info) = to_method_info(primary_method.as_str()) {
                if let Some(mut class_info) = method_info.ref_class(&mut expand_context) {
                    if let Some(find_method) = class_info.find_method(&method_info, &mut expand_context) {
                        let find_method_info = find_method.0;
                        find_method_info.prune_finally();
                        find_method_list.push(find_method_info.clone());
                    }
                }
            }
        }
        return find_method_list;
    }).unwrap_or(vec![]);
    expand_context.skip_constructor = old_skip_constructor;
    return ret;
}

pub fn to_method_info(method_signature: &str) -> Option<MethodInfo> {
    let class_method = substring_before(method_signature, "(");
    let (class_name, mut method_name) = rsplit_at(class_method, &['#', '.']);
    if class_name.is_empty() || method_name.is_empty() {
        return None;
    }
    method_name = &method_name[1..];
    let mut method_info = MethodInfo::new();
    method_info.class_info_ref = Some(class_name.to_string());
    method_info.method_name = method_name.to_string();
    let argument = substring_after(method_signature, "(");
    let argument = substring_before_last(argument, ")");
    if !argument.is_empty() {
        for (idx, argument) in argument.split(",").into_iter().enumerate() {
            if argument.is_empty() {
                continue;
            }
            let mut param_type = ClassInfo::new();
            param_type.set_qualified_class_name(argument);
            method_info.param_map.insert(format!("req{}", idx), param_type);
        }
    }
    return Some(method_info);
}

#[cfg(test)]
mod tests {
    use crate::api::req_model::{IntentionType, QueryRelatedRequestBean};
    use crate::it::config::codefuse_it_config::{load_primary_method_list, parser_codefuse_config, to_method_info};
    use crate::it::java_parser::context::ExpandContext;
    use crate::it::logger_init::init_logger;

    #[test]
    fn test1() {
        let _ = init_logger();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/rcqualitydataprod".to_string(),
            fileUrl: "app/biz/shared/src/main/java/com/alipay/rcqualitydataprod/biz/itacprocessor/impl/ItacSubtaskRunnerImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: "".to_string(),
            selectedContent: "".to_string(),
            ..default_req_bean
        };
        let a = parser_codefuse_config(&query_related_request);
        print!("{:?}", a);
    }

    #[test]
    fn test2() {
        let _ = init_logger();
        to_method_info("com.alipay.aiforce.afworkflow.domain.template.service.ServiceTemplate#process");
        to_method_info("com.alipay.aiforce.afworkflow.facade.svat.CommonApiResult.CommonApiResult(java.lang.String, java.lang.String)");
        to_method_info("com.alipay.aiforce.afworkflow.facade.svat.service.OperationMeasureFacade.record");
        to_method_info("com.alipay.rcqualitydataprod.biz.itacprocessor.impl.ItacSubtaskRunnerImpl.run()");
        to_method_info("com.alipay.rcqualitydataprod.biz.itacprocessor.impl.ItacSubtaskRunnerImpl#run()");
    }


    #[test]
    fn test3() {
        let _ = init_logger();
        let mut expand_context = ExpandContext::build("/Users/<USER>/tinghe-source/afworkflow");
        let a = load_primary_method_list(&mut expand_context);
        print!("{}", serde_json::to_string(&a).unwrap_or("".to_string()));
    }
}
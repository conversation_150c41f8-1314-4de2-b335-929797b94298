use std::ops::Deref;
use std::time::Instant;
use anyhow::Result;
use log::{info, warn};

// 位置修改
// use agent_common_service::model::rpc_model::{BaseResponse, build_success_response};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::remote::rpc_model::{build_success_response, BaseResponse};
use crate::api::req_model::{IntentionType, QueryRelatedRequestBean};
use crate::api::res_model::QueryRelatedResult;
use crate::it::config::codefuse_it_config::{load_primary_method_list, parser_codefuse_config};
use crate::it::interface_model::{ErrorCode, InterfacePromptData};
use crate::it::java_parser::context::{AccessRecorder, ExpandContext};
use crate::it::java_parser::parser::parse_class_text;
use crate::it::project_model::project_parser::parser_project_info;

pub fn query_related_data_it(query_related_request: &QueryRelatedRequestBean) -> Result<BaseResponse<QueryRelatedResult>> {
    let interface_prompt_data = parse_interface(query_related_request);
    let interface_prompt_config = parser_codefuse_config(query_related_request);
    return Ok(build_success_response(QueryRelatedResult {
        agent_version: AGENT_CONFIG.agent_version.clone(),
        related_data_vec: Vec::new(),
        interface_prompt_data: Some(interface_prompt_data),
        interface_prompt_config,
        intention_type: IntentionType::InterfaceTest,
    }));
}

// 解析接口
pub fn parse_interface(query_related_request: &QueryRelatedRequestBean) -> InterfacePromptData {
    info!("[接口测试] 接口测试检索, request=[{:?}]", query_related_request.simplify());

    let mut ret = InterfacePromptData::new();
    // 代码片段
    let selected_class_info = parse_class_text(&query_related_request.selectedContent);
    let selected_method_list = selected_class_info.method_list;
    if selected_method_list.is_empty() {
        warn!("[接口测试] 没有选中任何方法, query_related_request");
        ret.error_code = ErrorCode::NoneSelectedMethod;
        ret.error_msg = "很抱歉，您没有选中方法或选中方法不完整，请先在代码文件中选中一个完整方法".to_string();
        return ret;
    }
    if selected_method_list.len() > 1 {
        warn!("[接口测试] 选中了多个方法, query_related_request");
        ret.error_code = ErrorCode::ManySelectedMethod;
        ret.error_msg = "很抱歉，您选中了多个方法，请先在代码文件中选中一个完整方法".to_string();
        return ret;
    }
    let selected_method_info = &selected_method_list[0];

    let mut expand_context = ExpandContext::build(&query_related_request.projectUrl);
    let mut access_recorder = AccessRecorder::new();

    // 整个类解析出来
    let class_info = &mut parse_class_text(&query_related_request.fileContent);
    // 查找接口方法
    if let Some((find_interface_method_info, _)) = class_info.find_interface_method(selected_method_info, &mut expand_context) {
        ret.interface_method = Some(find_interface_method_info.method_signature.clone());
    }
    // 从整个类中获取指定的方法, 避免用户选择不完全的问题
    let find_method_info_opt = class_info.find_method(selected_method_info, &mut expand_context);
    if let Some((find_method_info, _)) = find_method_info_opt {
        info!("[接口测试] 开始检索方法关联数据, session_id=[{}], method=[{}]", query_related_request.sessionId, find_method_info.method_signature);
        find_method_info.expand_return_type(&mut expand_context);
        find_method_info.expand_param_type(&mut expand_context);
        find_method_info.expand_function_call(&mut expand_context, &mut access_recorder);
        find_method_info.prune_finally();
        ret.implement_method = find_method_info.method_signature.clone();
        ret.method_info = find_method_info.clone();
        class_info.init_field_info(expand_context.project_url.as_str());
        class_info.prune_access_recorder(&access_recorder);
        class_info.prune_basic_info(true, true);
        ret.class_info = Some(class_info.clone());
        ret.project_info = parser_project_info(query_related_request);
        ret.mock_point_list = access_recorder.mock_recorder.values().cloned().collect();
        ret.primary_method_list = load_primary_method_list(&mut expand_context);
        info!("[接口测试] 方法RAG数据检索完成, session_id=[{}], method=[{}], time=[{}ms]", query_related_request.sessionId, ret.implement_method, expand_context.time_limit.elapsed_millis());
        return ret;
    } else {
        warn!("[接口测试] 在当前文件中没有检索到选中方法, query_related_request={:?}", query_related_request);
        ret.error_code = ErrorCode::NotFoundMethod;
        ret.error_msg = "很抱歉，在当前文件中没有检索到选中方法，请重新选中一个完整方法".to_string();
        return ret;
    }
}

pub fn parse_interface_for_debug(query_related_request: &QueryRelatedRequestBean, interface_test_info: &InterfacePromptData) {
    let mut class_info = parse_class_text(&query_related_request.fileContent);
    let mut ext_info = ExpandContext::build(&query_related_request.projectUrl);
    let mut access_recorder = AccessRecorder::new();
    class_info.expand_class_info(&mut ext_info);
    class_info.expand_all_method_info(&mut ext_info, &mut access_recorder);
    for field_info in class_info.field_map.values() {
        info!("field_name=[{}], rpc_ref=[{}]", field_info.field_name, field_info.is_rpc_reference(ext_info.project_url.as_str()));
    }
    println!("{}", serde_json::to_string(&class_info).unwrap());
}

#[cfg(test)]
mod test {
    use std::fs;

    use log::info;

    use crate::api::req_model::{IntentionType, QueryRelatedRequestBean};
    use crate::it::interface_model::InterfacePromptData;
    use crate::it::interface_parser::{parse_interface, parse_interface_for_debug};
    use crate::it::java_model::method_info::MethodMatchLevel;
    use crate::it::logger_init::init_logger;
    use crate::utils::java_utils::JAVA_IDENTIFIER_REGEX;

    #[test]
    fn test1() {
        let value = "";
        let parts: Vec<&str> = value.rsplitn(2, '.').collect();
        let class_name = parts[0];
        let package_name = parts.get(1);
        println!("{:?}", package_name);
    }
    #[test]
    fn test2() {
        let mut match_levels = vec![(MethodMatchLevel::ParamCount, "A"), (MethodMatchLevel::ParamAll, "B")];
        match_levels.sort_by(|a, b| a.0.cmp(&b.0).reverse());
        // match_levels.reverse();
        println!("Sorted Match Levels: {:?}", match_levels);
    }
    #[test]
    fn test_parse_interface() {
        let _ = init_logger();
        let selected_content: &str = r#"
            public AppVO getApp(AppReq req) {
    "#;

        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/rcqualitydataprod/app/core/service/src/main/java/com/alipay/rcqualitydataprod/core/service/itac/impl/ItacAppServiceImpl.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/rcqualitydataprod".to_string(),
            fileUrl: "app/core/service/src/main/java/com/alipay/rcqualitydataprod/core/service/itac/impl/ItacAppServiceImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };

        let interface_prompt_info = parse_interface(&query_related_request);
        info!("interface_prompt_info={}", serde_json::to_string(&interface_prompt_info).unwrap());
    }

    #[test]
    fn test_parse_interface_1() {
        let _ = init_logger();
        let selected_content: &str = r#"
            public boolean process(ItacSubtaskDO subtaskDO)
    "#;

        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/rcqualitydataprod/app/biz/shared/src/main/java/com/alipay/rcqualitydataprod/biz/itacprocessor/impl/ItacSubtaskRunnerImpl.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/rcqualitydataprod".to_string(),
            fileUrl: "app/biz/shared/src/main/java/com/alipay/rcqualitydataprod/biz/itacprocessor/impl/ItacSubtaskRunnerImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };

        let interface_prompt_info = parse_interface(&query_related_request);
        info!("interface_prompt_info={}", serde_json::to_string(&interface_prompt_info).unwrap());
    }

    #[test]
    fn test_parse_interface_2() {
        let _ = init_logger();
        let selected_content: &str = r#"
            public BaseResult<AppVO> test(@RequestBody BaseResult<AppReq> req) {
    "#;

        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/rcqualitydataprod/app/web/src/main/java/com/alipay/rcqualitydataprod/web/itac/ItacAppTestController.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/rcqualitydataprod".to_string(),
            fileUrl: "app/biz/shared/src/main/java/com/alipay/rcqualitydataprod/biz/itacprocessor/impl/ItacSubtaskRunnerImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };

        let interface_prompt_info = parse_interface(&query_related_request);
        info!("interface_prompt_info={}", serde_json::to_string(&interface_prompt_info).unwrap());
    }

    #[test]
    fn test_parse_interface_example1() {
        let _ = init_logger();
        let selected_content: &str = r#"
public void args() {}
    "#;

        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/tree-sitter-java-parser-test/src/main/java/com/alipay/example/human/generic/BaseResult.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/tree-sitter-java-parser-test".to_string(),
            fileUrl: "src/main/java/com/alipay/example/human/args/Example1.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };

        let interface_prompt_info = parse_interface(&query_related_request);
        info!("interface_prompt_info={}", serde_json::to_string(&interface_prompt_info).unwrap());
    }

    #[test]
    fn test_parse_interface_for_class_file() {
        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/tree-sitter-java-parser-test/src/main/java/com/alipay/example/human/generic/BaseResult.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/tree-sitter-java-parser-test".to_string(),
            fileUrl: "src/main/java/com/alipay/example/human/generic/BaseResult.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: class_content.to_string(),
            selectedContent: "".to_string(),
            ..default_req_bean
        };

        let interface_test_info = InterfacePromptData::new();
        parse_interface_for_debug(&query_related_request, &interface_test_info);
    }

    #[test]
    fn test_api_sofa_ref() {
        let _ = init_logger();
        let selected_content: &str = r#"
            public RpcClientResult<Boolean> sendDingDing(SingleChannelMsgSendReq message) {
    "#;

        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/xcode/app/common/service/integration/src/main/java/com/alipay/xcodes/common/service/integration/gotone/impl/GotoneClientImpl.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/xcode".to_string(),
            fileUrl: "app/common/service/integration/src/main/java/com/alipay/xcodes/common/service/integration/gotone/impl/GotoneClientImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };

        let interface_test_info = InterfacePromptData::new();
        parse_interface_for_debug(&query_related_request, &interface_test_info);
    }

    #[test]
    fn test_api_sofa_ref_xml() {
        let _ = init_logger();
        let selected_content: &str = r#"
            public OperatorDTO queryOperatorDTOByName(String name) {
    "#;

        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/rcqualitydataprod/app/common/service/integration/src/main/java/com/alipay/rcqualitydataprod/common/service/integration/antbuservice/impl/AntbuserviceClientImpl.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/rcqualitydataprod".to_string(),
            fileUrl: "app/common/service/integration/src/main/java/com/alipay/rcqualitydataprod/common/service/integration/antbuservice/impl/AntbuserviceClientImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };

        let interface_test_info = InterfacePromptData::new();
        parse_interface_for_debug(&query_related_request, &interface_test_info);
    }

    #[test]
    fn test_api_sofa_ref_none() {
        let _ = init_logger();
        let selected_content: &str = r#"
            public AppVO getApp(AppReq req) {
    "#;

        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/rcqualitydataprod/app/core/service/src/main/java/com/alipay/rcqualitydataprod/core/service/itac/impl/ItacAppServiceImpl.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/rcqualitydataprod".to_string(),
            fileUrl: "app/core/service/src/main/java/com/alipay/rcqualitydataprod/core/service/itac/impl/ItacAppServiceImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };

        let interface_test_info = InterfacePromptData::new();
        parse_interface_for_debug(&query_related_request, &interface_test_info);
    }

    #[test]
    fn test3() {
        // 要匹配的字符串
        let input = r#"abcd"#;

        // 进行匹配
        if JAVA_IDENTIFIER_REGEX.is_match(input) {
            println!("Matched!");
        } else {
            println!("Not matched!");
        }
    }
}
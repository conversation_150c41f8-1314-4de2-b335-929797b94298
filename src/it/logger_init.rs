use std::fs;

use log4rs::append::console::ConsoleAppender;
use log4rs::append::rolling_file::policy::compound::CompoundPolicy;
use log4rs::append::rolling_file::policy::compound::roll::fixed_window::FixedWindowRoller;
use log4rs::append::rolling_file::policy::compound::trigger::size::SizeTrigger;
use log4rs::append::rolling_file::RollingFileAppender;
use log4rs::config::{Appender, Logger, Root};
use log4rs::Config;
use log4rs::encode::pattern::PatternEncoder;
use log::LevelFilter;

use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::tools::common_tools::expand_user_home;

const MAX_LOG_NUM: u32 = 3;
const MAX_LOG_SIZE: u64 = 10 * 1024 * 1024;

///初始化日志组件.两个appender: 一个输出到控制台，一个输出到文件
pub fn init_logger() -> Result<(), log::SetLoggerError> {
    //如果没有日志目录则新建
    let log_url = expand_user_home(&AGENT_CONFIG.base_data_url)
        .unwrap()
        .join("logs");
    if !log_url.exists() {
        let _ = fs::create_dir_all(&log_url);
    }
    // 控制台Appender
    let stdout = ConsoleAppender::builder()
        .encoder(Box::new(PatternEncoder::new(
            "{d(%Y-%m-%d %H:%M:%S)} {M} {l} {L}:{m}{n}",
        )))
        .build();

    // 滚动文件Appender
    let size_trigger = SizeTrigger::new(MAX_LOG_SIZE);
    // let pattern = "/Users/<USER>/.codefuse/log/test.{}.log";
    let pattern = log_url.join("codefuse_agent.{}.log");
    let fixed_window_roller = FixedWindowRoller::builder()
        .base(1)
        .build(pattern.to_str().unwrap().trim(), MAX_LOG_NUM)
        .unwrap();

    let compound_policy =
        CompoundPolicy::new(Box::new(size_trigger), Box::new(fixed_window_roller));
    //此处由于rs机制问题,join单独声明,否则有所有权移动问题
    let current_log = log_url.join("codefuse_agent.log");
    let current_log = current_log.to_str().unwrap().trim();

    let rolling_file = RollingFileAppender::builder()
        .encoder(Box::new(PatternEncoder::new(
            "{d(%Y-%m-%d %H:%M:%S)} {M} {l} {L}:{m}{n}",
        )))
        .build(current_log, Box::new(compound_policy))
        .unwrap();

    // 配置

    let mut log_filter: LevelFilter;
    match AGENT_CONFIG.log_level.trim() {
        "DEBUG" => log_filter = LevelFilter::Debug,
        "WARN" => log_filter = LevelFilter::Warn,
        "ERROR" => log_filter = LevelFilter::Error,
        _ => log_filter = LevelFilter::Info,
    }

    let config = Config::builder()
        .appender(Appender::builder().build("stdout", Box::new(stdout)))
        .appender(Appender::builder().build("rolling_file", Box::new(rolling_file)))
        // Add specific logger for lance_encoding::decoder to suppress excessive logging
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lance_encoding::decoder", LevelFilter::Error)
        )
        // Add specific logger for lance_encoding to suppress excessive logging
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lance_encoding", LevelFilter::Error)
        )
        // Add specific logger for lancedb to reduce verbosity
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lancedb", LevelFilter::Error)
        )
        // Add specific logger for lance to suppress all lance-related logs
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lance", LevelFilter::Error)
        )
        // Add specific logger for lance_core to suppress core lance logs
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lance_core", LevelFilter::Error)
        )
        // Add specific logger for lance_io to suppress IO-related lance logs
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lance_io", LevelFilter::Error)
        )
        // Add specific logger for lance_file to suppress file-related lance logs
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lance_file", LevelFilter::Error)
        )
        // Add specific logger for lance_index to suppress index-related lance logs
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lance_index", LevelFilter::Error)
        )
        // Add specific logger for lance_table to suppress table-related lance logs
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lance_table", LevelFilter::Error)
        )
        // Add specific logger for lance_datafusion to suppress datafusion-related lance logs
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lance_datafusion", LevelFilter::Error)
        )
        // Add specific logger for lance_arrow to suppress arrow-related lance logs
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lance_arrow", LevelFilter::Error)
        )
        // Add specific logger for lance_linalg to suppress linear algebra-related lance logs
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("lance_linalg", LevelFilter::Error)
        )
        // Add specific logger for tantivy::directory::managed_directory to suppress file deletion logs
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("tantivy::directory::managed_directory", LevelFilter::Warn)
        )
        // Add specific logger for tantivy to reduce verbosity
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)  // Don't inherit from root logger
                .build("tantivy", LevelFilter::Warn)
        )
        .build(
            Root::builder()
                .appender("stdout")
                .appender("rolling_file")
                .build(log_filter),
        )
        .unwrap();
    // 日志热重载
    log4rs::init_config(config)?;
    Ok(())
}

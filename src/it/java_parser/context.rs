use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, SystemTimeError};

use indexmap::IndexMap;
use log::info;
use serde::{Deserialize, Serialize};
use tree_sitter::Node;

use crate::it::java_model::class_info::ClassInfo;
use crate::it::java_model::common::{DEBUG_KIND_STACK, DEBUG_PARSER};
use crate::it::java_model::field_info::{BeanType, FieldInfo};
use crate::it::java_model::func_call::{FuncCallInfo, FuncCallType};
use crate::it::java_model::method_info::MethodInfo;
use crate::it::loader::class_loader::load_repo_class;
use crate::utils::file_utils::path_str_to_posix;
use crate::utils::index_map::deserialize_indexmap;
use crate::utils::index_map::serialize_indexmap;
use crate::utils::java_utils::is_do_class;
use crate::utils::string_utils::truncate_string;

// 解析时上下文
pub struct ParserContext<'a> {
    pub start_time: SystemTime,
    pub content: &'a str,
    pub comment: Option<String>,
    pub line: &'a str,
    pub kind: &'a str,
    pub kind_stack: Vec<String>,
    pub kind_prev: Option<String>,
    pub skip_field: bool,
    pub skip_method: bool,
    // 跳过解析method详情, 只保留method_name和modifier
    pub skip_method_detail: bool,
    pub skip_method_variable: bool,
    pub skip_super_class: bool,
    pub skip_super_interfaces: bool,
}

impl<'a> ParserContext<'a> {
    pub fn new(content: &'a str) -> Self {
        ParserContext {
            start_time: SystemTime::now(),
            content,
            comment: None,
            line: "",
            kind: "",
            kind_stack: Vec::new(),
            kind_prev: None,
            skip_field: false,
            skip_method: false,
            skip_method_detail: false,
            skip_method_variable: false,
            skip_super_class: false,
            skip_super_interfaces: false,
        }
    }

    pub fn basic_class_info(content: &'a str) -> Self {
        ParserContext {
            start_time: SystemTime::now(),
            content,
            comment: None,
            line: "",
            kind: "",
            kind_stack: Vec::new(),
            kind_prev: None,
            skip_field: true,
            skip_method: true,
            skip_method_detail: true,
            skip_method_variable: true,
            skip_super_class: true,
            skip_super_interfaces: true,
        }
    }

    pub fn text(&self, node: &Node) -> &str {
        return &self.content[node.byte_range()];
    }

    pub fn make_comment(&mut self, node: &Node) {
        let mut comment = truncate_string(self.text(node), 128);
        if node.kind() == "block_comment" && comment.ends_with("...") {
            comment += " */"
        }
        self.comment = Some(comment);
    }

    pub fn take_comment(&mut self) -> Option<String> {
        self.comment.take()
    }

    // 如果stack中有多个class_declaration
    pub fn is_inner_class(&self) -> bool {
        let type_declaration = vec!["class_declaration", "enum_declaration", "interface_declaration", "annotation_type_declaration"];
        let count = self.kind_stack.iter().filter(|&item| type_declaration.contains(&item.as_str())).count();
        return count > 0;
    }

    pub fn debug_info(&mut self, node: &Node) {
        if DEBUG_PARSER {
            self.kind = node.kind();
            self.line = &self.content[node.byte_range()];
        }
        if DEBUG_KIND_STACK {
            info!("kind={:?}", self.kind_stack);
        }
    }
}

// 扩展是上下文
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ExpandContext {
    pub project_url: String,
    // 在查找数据时自动扩展
    // 主要用于: find_field / find_method 查找字段或方法时, 需要自动扩展查找superclass
    pub find_expand: bool,
    // 在查找方法时跳过构造函数
    pub skip_constructor: bool,
    // 查找接口实现类的数量进行限制, 接口现实现的关系比较弱, 可能存在多态实现的情况, 需要限制从多少实现类中查找最佳方法
    pub max_implements_limit: usize,
    // 在扩展func_call时限制哪些函数调用类型
    pub call_type_expand: Vec<FuncCallType>,
    // superclass最大层次限制:
    // 0, 类本身, 例如return_type/param_type
    // n, 延伸n层, 例如n层superclass
    pub max_super_depth: DepthLimit,
    // 字段最大嵌套深度
    // 0, 类本身字段
    // n, 延伸n层, 例如n层superclass
    pub max_field_depth: DepthLimit,
    // 方法最大调用深度
    // 0, 类本身字段
    // n, 延伸n层, 例如n层superclass
    pub max_func_call_depth: DepthLimit,
    // 运行时间限制
    pub time_limit: TimeLimit,
    // 限制匹配方法描述
    pub match_method_modifier: Option<String>,
}

impl ExpandContext {
    pub fn build(project_url: &str) -> Self {
        ExpandContext {
            project_url: path_str_to_posix(project_url),
            find_expand: true,
            skip_constructor: true,
            max_implements_limit: 3,
            call_type_expand: vec![FuncCallType::Inner, FuncCallType::BeanCall],
            max_super_depth: DepthLimit {
                depth: 0,
                max_depth: 2,
            },
            max_field_depth: DepthLimit {
                depth: 0,
                max_depth: 2,
            },
            max_func_call_depth: DepthLimit {
                depth: 0,
                max_depth: 5,
            },
            time_limit: TimeLimit {
                start_time: SystemTime::now(),
                max_millis: 1000,
            },
            match_method_modifier: None,
        }
    }

    pub fn is_expand_func_call(&self, func_call: &FuncCallInfo) -> bool {
        if self.call_type_expand.is_empty() {
            // 不指定就是无限制
            return true;
        }
        return self.call_type_expand.contains(&func_call.call_type);
    }
    pub fn is_and_limit(&self) -> bool {
        self.max_super_depth.is_limit() && self.max_field_depth.is_limit()
    }

    pub fn is_or_limit(&self) -> bool {
        self.max_super_depth.is_limit() || self.max_field_depth.is_limit()
    }

    // Clone的时候调用函数
    pub fn clone_with(&self, func: fn(c: &mut Self)) -> Self {
        let mut clone = self.clone();
        func(&mut clone);
        clone
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DepthLimit {
    pub depth: usize,
    pub max_depth: usize,
}

impl DepthLimit {
    pub fn is_limit(&self) -> bool {
        self.depth >= self.max_depth
    }

    pub fn inc(&mut self) {
        self.depth += 1;
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TimeLimit {
    // 记录开始时间
    pub start_time: SystemTime,
    // 限制最大执行时间
    pub max_millis: u128,
}

impl TimeLimit {
    pub fn is_limit(&self) -> bool {
        if self.max_millis <= 0 {
            return false;
        }
        match self.start_time.elapsed() {
            Ok(duration) => { duration.as_millis() > self.max_millis }
            Err(_) => false,
        }
    }

    pub fn elapsed_millis(&self) -> u128 {
        match self.start_time.elapsed() {
            Ok(duration) => { duration.as_millis() }
            Err(_) => 0,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MockPoint {
    pub bean_type: BeanType,
    pub bean_name: String,
    pub bean_class_name: String,
    pub bean_qualified_class_name: String,
    pub method_name: String,
    pub method_signature: String,
    pub container_class_name: String,
    pub container_qualified_class_name: String,
    pub do_class_list: Vec<ClassInfo>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AccessRecorder {
    #[serde(serialize_with = "serialize_indexmap", deserialize_with = "deserialize_indexmap")]
    pub field_counter: IndexMap<String, usize>,
    #[serde(serialize_with = "serialize_indexmap", deserialize_with = "deserialize_indexmap")]
    pub method_counter: IndexMap<String, usize>,
    #[serde(serialize_with = "serialize_indexmap", deserialize_with = "deserialize_indexmap")]
    pub mock_recorder: IndexMap<String, MockPoint>,
}

impl AccessRecorder {
    pub fn new() -> Self {
        AccessRecorder {
            field_counter: IndexMap::new(),
            method_counter: IndexMap::new(),
            mock_recorder: IndexMap::new(),
        }
    }

    pub fn record_func_call(&mut self, func_call: &FuncCallInfo, target_type: &ClassInfo, ref_class: &ClassInfo, bean_type: &BeanType, expand_context: &mut ExpandContext) {
        match bean_type {
            BeanType::Dao | BeanType::Rpc => {
                let signature = func_call.get_qualified_signature();
                let mut mock_bean = MockPoint {
                    bean_type: bean_type.clone(),
                    bean_name: func_call.target_object.clone(),
                    bean_class_name: target_type.class_name.clone(),
                    bean_qualified_class_name: target_type.qualified_class_name.clone(),
                    method_name: func_call.method_name.clone(),
                    method_signature: signature.clone(),
                    container_class_name: ref_class.class_name.clone(),
                    container_qualified_class_name: ref_class.qualified_class_name.clone(),
                    do_class_list: Vec::new(),
                };

                if *bean_type == BeanType::Dao {
                    // DAO类型的Mock可能需要对应的DO对象
                    for import in target_type.import_list.iter() {
                        if is_do_class(import) {
                            if let Some(do_class_info) = load_repo_class(expand_context.project_url.as_str(), import.as_str()).as_deref() {
                                let mut do_class_info = do_class_info.clone();
                                do_class_info = do_class_info.try_to_pojo();
                                do_class_info.prune_pojo();
                                mock_bean.do_class_list.push(do_class_info);
                            }
                        }
                    }
                }

                self.mock_recorder.entry(signature).or_insert(mock_bean);
            }
            _ => {}
        }
    }

    pub fn record_field(&mut self, field_info: &FieldInfo) {
        let entry = self.field_counter.entry(field_info.get_qualified_field_signature()).or_insert(0);
        *entry += 1;
    }

    pub fn record_method(&mut self, method_info: &MethodInfo) {
        let entry = self.method_counter.entry(method_info.get_qualified_method_signature()).or_insert(0);
        *entry += 1;
    }
}

#[cfg(test)]
mod tests {
    use crate::it::java_parser::context::ExpandContext;

    #[test]
    fn test1() {
        let x = ExpandContext::build("");
        let c = x.clone_with(|n| n.max_super_depth.inc());
        println!("{:?}, {:?}", x, c);
    }
}
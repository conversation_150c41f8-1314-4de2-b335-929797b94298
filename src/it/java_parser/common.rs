use std::collections::HashMap;

use once_cell::sync::Lazy;

pub static CLASS_NAME_NOT_FOUND: &str = "#NotFound#";

pub static JDK_PRIMARY: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut class_map = HashMap::new();
    class_map.insert("void".to_string(), "void".to_string());
    class_map.insert("boolean".to_string(), "boolean".to_string());
    class_map.insert("byte".to_string(), "byte".to_string());
    class_map.insert("short".to_string(), "short".to_string());
    class_map.insert("int".to_string(), "int".to_string());
    class_map.insert("long".to_string(), "long".to_string());
    class_map.insert("float".to_string(), "float".to_string());
    class_map.insert("double".to_string(), "double".to_string());
    class_map.insert("char".to_string(), "char".to_string());
    return class_map;
});

pub static JDK_LANG: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut class_map = HashMap::new();
    parse_jdk_class(&mut class_map, include_str!("../../../resources/jdk-lang.txt"));
    class_map.extend(JDK_PRIMARY.iter().map(|(k, v)| (k.to_string(), v.to_string())));
    return class_map;
});

pub static JDK_UTIL: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut class_map = HashMap::new();
    parse_jdk_class(&mut class_map, include_str!("../../../resources/jdk-util.txt"));
    return class_map;
});

fn parse_jdk_class(class_map: &mut HashMap<String, String>, content: &str) {
    for line in content.lines() {
        let parts: Vec<&str> = line.trim().split(".").collect();
        let simple_class_name = parts.last().unwrap().to_string();
        let qualified_class_name = line.to_string();
        class_map.insert(qualified_class_name.clone(), qualified_class_name.clone());
        class_map.insert(simple_class_name, qualified_class_name);
    }
}

// 原子类型与对象类型的映射
pub static JDK_PRIMARY_ALIAS: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut alias_map = HashMap::new();
    alias_map.insert("java.lang.Boolean".to_string(), "boolean".to_string());
    alias_map.insert("java.lang.Byte".to_string(), "byte".to_string());
    alias_map.insert("java.lang.Short".to_string(), "short".to_string());
    alias_map.insert("java.lang.Integer".to_string(), "int".to_string());
    alias_map.insert("java.lang.Long".to_string(), "long".to_string());
    alias_map.insert("java.lang.Float".to_string(), "float".to_string());
    alias_map.insert("java.lang.Double".to_string(), "double".to_string());
    alias_map.insert("java.lang.Character".to_string(), "char".to_string());
    return alias_map;
});

// 类型拓宽转换链路1: 可从前向后拓宽
pub static JDK_WIDENING_CONVERSION_1: Lazy<Vec<&str>> = Lazy::new(|| {
    vec!["byte", "short", "int", "long", "float", "double", "java.lang.Number"]
});

// 类型拓宽转换链路2: 可从前向后拓宽
pub static JDK_WIDENING_CONVERSION_2: Lazy<Vec<&str>> = Lazy::new(|| {
    vec!["char", "int", "long", "float", "double"]
});

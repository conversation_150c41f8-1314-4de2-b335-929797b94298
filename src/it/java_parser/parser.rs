use tree_sitter::{<PERSON><PERSON>, Parser};

use crate::it::java_model::class_info::ClassInfo;
use crate::it::java_model::class_type::ClassType;
use crate::it::java_model::field_info;
use crate::it::java_model::field_info::{BeanType, EnumConstantInfo, FieldInfo};
use crate::it::java_model::func_call::{ArgumentType, FuncCallInfo, LaterData};
use crate::it::java_model::func_call::ArgumentType::{Any, Later, Type};
use crate::it::java_model::method_info::MethodInfo;
use crate::it::java_parser::common::CLASS_NAME_NOT_FOUND;
use crate::it::java_parser::context::ParserContext;
use crate::utils::string_utils::{remove_suffix, substring_before, trim_whitespaces, truncate_string};

/// 直接使用text解析类(也支持片段)
pub fn parse_class_text(text: &str) -> ClassInfo {
    let mut method_context = ParserContext::new(&text);
    let mut parser = Parser::new();
    parser.set_language(&tree_sitter_java::language()).unwrap();
    let tree = parser.parse(method_context.content, None).unwrap();
    let cursor = tree.walk();
    let node = cursor.node();
    let mut class_info = ClassInfo::new();
    parse_class(&node, &mut method_context, &mut class_info);
    return class_info;
}

/// 使用ParserContext解析类
pub fn parse_class_context(mut parser_context: &mut ParserContext) -> ClassInfo {
    let mut parser = Parser::new();
    parser.set_language(&tree_sitter_java::language()).unwrap();
    let tree = parser.parse(parser_context.content, None).unwrap();
    let cursor = tree.walk();
    let node = cursor.node();
    let mut class_info = ClassInfo::new();
    parse_class(&node, &mut parser_context, &mut class_info);
    return class_info;
}

/// 解析类
/// 文档: https://github.com/tree-sitter/tree-sitter-java/blob/master/grammar.js
pub fn parse_class(node: &Node, parser_context: &mut ParserContext, class_info: &mut ClassInfo) {
    match node.kind() {
        "package_declaration" => {
            class_info.set_package_name(parser_context.text(node));
            return;
        }
        "import_declaration" => {
            class_info.add_import(parser_context.text(node));
            return;
        }
        "block_comment" | "line_comment" => {
            parser_context.make_comment(node);
        }
        "class_declaration" | "enum_declaration" | "interface_declaration" | "annotation_type_declaration" => {
            let class_name_list = list_node_text(node, vec!["identifier"], parser_context, 1, 1);
            let class_name = class_name_list.first().unwrap_or(&CLASS_NAME_NOT_FOUND.to_string()).to_string();

            if class_info.loaded {
                // 2. 第二次解析到class定义时, 当前类已加载, 说明遇到了一个新类 (brother/inner)
                let mut new_class_info = class_info.new_class(class_name.as_str());
                // 进入parse_class前,先将当前type_declaration去除掉,避免重复push
                parse_class(node, parser_context, &mut new_class_info);
                if parser_context.is_inner_class() {
                    class_info.inner_class_list.push(new_class_info);
                } else {
                    // 判断谁是public谁是default
                    class_info.sibling_class_list.push(new_class_info);
                    // 如果brother中有public的类, 将public的设置为主类
                    class_info.promote_public_sibling();
                }
                return;
            }

            // 1. 第一次解析到class定义时, 先处理数据信息
            let modifiers = list_node_text(node, vec!["modifiers"], parser_context, 1, 1);
            if let Some(modifier) = modifiers.first() {
                class_info.modifiers = modifier.to_string();
            }
            // 类名必须存在，使用#NotFound#记录错误
            class_info.set_class_name(class_name.as_str());
            class_info.comment = parser_context.take_comment();
            // 模型类需要解析参数映射关系
            if let Some(type_parameters_node) = node.child_by_field_name("type_parameters") {
                parse_type_parameters(&type_parameters_node, parser_context, class_info);
            }

            class_info.loaded = true;
            class_info.project_src = true;
            // 不可以return, 需要继续解析
        }
        "enum" => {
            class_info.set_class_type(ClassType::Enum);
        }
        "interface" => {
            class_info.set_class_type(ClassType::Interface);
        }
        "@interface" => {
            class_info.set_class_type(ClassType::Annotation);
        }
        "enum_constant" => {
            if let Some(field_name_node) = node.child_by_field_name("name") {
                let field_name = parser_context.text(&field_name_node);
                let field_info = EnumConstantInfo {
                    field_name: field_name.to_string(),
                    code_body: parser_context.text(node).to_string(),
                };

                class_info.enum_constant_list.insert(field_name.to_string(), field_info);
            }
            return;
        }
        "superclass" => {
            if parser_context.skip_super_class {
                return;
            }
            if let Some(class_name) = list_node_text(node, vec!["scoped_type_identifier", "type_identifier"], parser_context, 2, 1).first() {
                // 使用全类名时: eg: com.alipay.pkg.Base
                // 使用简类名时: eg: String
                let superclass = class_info.new_class(class_name);
                class_info.super_class = Some(Box::new(superclass));
            }
            return;
        }
        "field_declaration" => {
            if !parser_context.skip_field {
                // 字段的完整定义
                for field_info in parse_var_declaration(node, parser_context, class_info) {
                    class_info.push_field(field_info);
                }
            }
            return;
        }
        "method_declaration" | "constructor_declaration" => {
            if parser_context.skip_method || class_info.is_class_type(ClassType::PojoClass) {
                return;
            }

            let mut method_info = class_info.new_method();
            parse_method_declaration(node, parser_context, &mut method_info, class_info);
            if !method_info.method_name.is_empty() {
                class_info.push_method(method_info);
            }
            return;
        }
        "super_interfaces" => {
            if parser_context.skip_super_interfaces {
                return;
            }
            let interfaces = list_node_text(node, vec!["scoped_type_identifier", "type_identifier"], parser_context, 3, -1);
            for interface in interfaces {
                let mut super_interface_info = class_info.new_class(&interface);
                super_interface_info.set_class_type(ClassType::Interface);
                class_info.super_interface_list.push(super_interface_info);
            }
            return;
        }
        _ => {}
    }

    let mut child_cursor = node.walk();
    for variable_node in node.children(&mut child_cursor) {
        parser_context.kind_stack.push(variable_node.kind().to_string());
        parser_context.debug_info(&variable_node);
        parse_class(&variable_node, parser_context, class_info);
        parser_context.kind_stack.pop();
        parser_context.kind_prev = Some(variable_node.kind().to_string());
    }
}

/// 解析模板类参数
pub fn parse_type_parameters(node: &Node, parser_context: &mut ParserContext, class_info: &mut ClassInfo) {
    if "type_parameters" != node.kind() {
        return;
    }

    let mut child_cursor = node.walk();
    for variable_node in node.children(&mut child_cursor) {
        match variable_node.kind() {
            "<" | "," | ">" => { continue }
            _ => {
                let mut type_param = parser_context.text(&variable_node);
                type_param = substring_before(type_param, "extends");
                class_info.generic_name_list.push(type_param.trim().to_string());
                continue;
            }
        }
    }
    return;
}

/// 解析method_declaration
pub fn parse_method_declaration(node: &Node, parser_context: &mut ParserContext, method_info: &mut MethodInfo, class_info: &mut ClassInfo) {
    if "method_declaration" != node.kind() && "constructor_declaration" != node.kind() {
        return;
    }

    // 解析方法名
    if let Some(name_node) = node.child_by_field_name("name") {
        method_info.method_name = parser_context.text(&name_node).to_string();
    }
    let modifiers: Vec<String> = list_node_text(node, vec!["modifiers"], parser_context, 1, 1);
    method_info.modifiers = modifiers.first().unwrap_or(&"".to_string()).to_string();

    if parser_context.skip_method_detail {
        // 跳过方法详情解析
        return;
    }

    method_info.comment = parser_context.take_comment();
    // 提取body
    method_info.set_code_body(parser_context.text(node));
    method_info.is_constructor = "constructor_declaration" == node.kind();


    // 解析返回值
    if let Some(type_node) = node.child_by_field_name("type") {
        method_info.return_type = class_info.new_class(parser_context.text(&type_node));
    }

    // 解析参数
    if let Some(parameters_node) = node.child_by_field_name("parameters") {
        let mut param_cursor = parameters_node.walk();
        for param_node in parameters_node.children(&mut param_cursor) {
            match param_node.kind() {
                "formal_parameter" => {
                    if let Some(param_name_node) = param_node.child_by_field_name("name") {
                        if let Some(param_type_node) = param_node.child_by_field_name("type") {
                            let param_name = parser_context.text(&param_name_node);
                            let param_type = parser_context.text(&param_type_node);
                            method_info.param_map.insert(param_name.to_string(), class_info.new_class(&param_type).try_to_pojo());
                        }
                    }
                }
                _ => {}
            }
        }
    }

    // 解析方法体
    if let Some(body_node) = node.child_by_field_name("body") {
        parse_method_block(&body_node, parser_context, method_info, class_info);
    }

    // 解析起止行号
    let start_line = node.start_position().row;
    let end_line = node.end_position().row;
    method_info.start_end_position = (start_line, end_line);
}

/// 解析方法块
pub fn parse_method_block(node: &Node, parser_context: &mut ParserContext, method_info: &mut MethodInfo, class_info: &mut ClassInfo) {
    match node.kind() {
        "local_variable_declaration" => {
            if !parser_context.skip_method_variable {
                for field_info in parse_var_declaration(node, parser_context, class_info) {
                    method_info.local_var_map.insert(field_info.field_name, field_info.field_type);
                }
            }
        }
        "if_statement" => {
            method_info.count_if += 1;
        }
        "return_statement" => {
            method_info.count_return += 1;
        }
        "throw_statement" => {
            method_info.count_throw += 1;
        }
        "switch_expression" => {
            method_info.count_switch += 1;
        }
        "while_statement" | "do_statement" => {
            method_info.count_while += 1;
        }
        "for_statement" | "enhanced_for_statement" => {
            method_info.count_for += 1;
        }
        "lambda_expression" => {
            method_info.count_lambda += 1;
        }
        "method_invocation" => {
            // 函数调用
            // 分析:类内调用/静态方法调用/参数调用/属性调用/属性调用-Bean调用/对象调用
            // 分析:内部类,implements/abstract class/lambda, 确认模板函数
            if let Some(func_call) = parse_method_invocation(node, parser_context, method_info, class_info) {
                method_info.push_func_call(func_call);
            }
            // 这里不能return, 有可能参数是lambda，需要继续分析调用
        }
        _ => {}
    }

    let mut child_cursor = node.walk();
    for variable_node in node.children(&mut child_cursor) {
        parser_context.kind_stack.push(variable_node.kind().to_string());
        parser_context.debug_info(&variable_node);
        parse_method_block(&variable_node, parser_context, method_info, class_info);
        parser_context.kind_stack.pop();
    }
}

/// 解析函数调用
pub fn parse_method_invocation(node: &Node, parser_context: &mut ParserContext, method_info: &mut MethodInfo, class_info: &mut ClassInfo) -> Option<FuncCallInfo> {
    if "method_invocation" != node.kind() {
        return None;
    }

    if let Some(method_name_node) = node.child_by_field_name("name") {
        let method_name = parser_context.text(&method_name_node).to_string();

        // 默认是this
        let mut target_object = "".to_string();
        if let Some(object_node) = node.child_by_field_name("object") {
            target_object = parser_context.text(&object_node).to_string();
        }

        let mut func_call = FuncCallInfo::new(target_object, method_name);
        func_call.code_body = truncate_string(parser_context.text(node), 128);
        if let Some(args_node) = node.child_by_field_name("arguments") {
            parser_context.debug_info(&args_node);
            parse_arguments(&args_node, parser_context, &mut func_call, method_info, class_info);
        }
        return Some(func_call);
    }

    return None;
}

/// 解析参数列表
pub fn parse_arguments(node: &Node, parser_context: &mut ParserContext, func_call: &mut FuncCallInfo, method_info: &mut MethodInfo, class_info: &mut ClassInfo) {
    if node.kind() != "argument_list" {
        return;
    }

    let mut child_cursor = node.walk();
    for variable_node in node.children(&mut child_cursor) {
        match variable_node.kind() {
            "(" | "," | ")" => { continue }
            _ => {
                // expression
                let mut arg_name = parser_context.text(&variable_node).to_string();
                let arg_type = parser_expression_for_args(&variable_node, parser_context, func_call, method_info, class_info);
                if arg_name.len() > 64 || func_call.args_map.contains_key(arg_name.as_str()) {
                    // 限制变量最大长度, 节约内存
                    arg_name = format!("{}_{}", variable_node.kind(), func_call.args_map.len());
                }
                func_call.args_map.insert(arg_name, arg_type);
            }
        }
    }
}

/// 处理表达式
fn parser_expression_for_args(node: &Node, parser_context: &mut ParserContext, func_call: &mut FuncCallInfo, method_info: &mut MethodInfo, class_info: &mut ClassInfo) -> ArgumentType<ClassInfo> {
    parser_context.debug_info(&node);
    match node.kind() {
        x if x.ends_with("_integer_literal") => {
            // int/long
            return Type(class_info.new_class("int"));
        }
        x if x.ends_with("_floating_point_literal") => {
            // float/double
            return Type(class_info.new_class("float"));
        }
        "null_literal" => {
            // null
            return Any;
        }
        "this" => {
            // this
            return Type(class_info.clone());
        }
        "true" | "false" | "binary_expression" | "instanceof_expression" => {
            // true / false
            // binary_expression: x == 1
            // v instanceof Integer
            return Type(class_info.new_class("boolean"));
        }
        "character_literal" => {
            // 'c'
            return Type(class_info.new_class("char"));
        }
        "string_literal" => {
            // "str"
            return Type(class_info.new_class("String"));
        }
        "class_literal" => {
            // Object.class
            return Type(class_info.new_class("Class"));
        }
        "method_reference" => {
            // StringUtils::isBlank
            return Type(class_info.new_class("Function"));
        }
        "object_creation_expression" | "array_creation_expression" => {
            // new Object(); new Object[];
            let list = list_node_text(&node, vec!["type_identifier"], parser_context, 2, 1);
            if let Some(type_identifier) = list.first() {
                return Type(class_info.new_class(type_identifier));
            }
            return Any;
        }
        "assignment_expression" => {
            // x = 1, 取左侧变量
            if let Some(left_node) = node.child_by_field_name("left") {
                let identifier = parser_context.text(&left_node).to_string();
                return find_identifier_arg_type(method_info, class_info, identifier.as_str());
            } else {
                return Any;
            }
        }
        "update_expression" | "unary_expression" | "parenthesized_expression" => {
            // update_expression: ++/--
            // unary_expression: +/-/!/~
            // parenthesized_expression: (v)
            let mut default_ret = Any;
            if node.kind() == "update_expression" || node.kind() == "unary_expression" {
                default_ret = Type(class_info.new_class("int"));
            }
            let mut child_cursor = node.walk();
            for variable_node in node.children(&mut child_cursor) {
                match variable_node.kind() {
                    "++" | "--" => { continue }
                    "+" | "-" | "~" => { continue }
                    "(" | ")" => { continue }
                    "!" => {
                        return Type(class_info.new_class("boolean"));
                    }
                    _ => {
                        // 尝试解析expression
                        return parser_expression_for_args(&variable_node, parser_context, func_call, method_info, class_info);
                    }
                }
            }
            // 大概率是int类型, int可转换为long/float/double/char, 具有适配性
            return default_ret;
        }
        "cast_expression" => {
            // (int) v
            if let Some(arg_type_node) = node.child_by_field_name("type") {
                let arg_type = parser_context.text(&arg_type_node);
                return Type(class_info.new_class(arg_type));
            }
            return Any;
        }
        "lambda_expression" => {
            // 函数式接口, 目标是只有一个方法的接口, 我们统一叫@FunctionalInterface, 以下规范:
            // 1. Target type of a lambda conversion must be an interface
            // 2. Multiple non-overriding abstract methods found in interface
            return Type(class_info.new_class("FunctionalInterface"));
        }
        "ternary_expression" => {
            // 三元表达式[?:]
            if let Some(consequence_node) = node.child_by_field_name("consequence") {
                // 判断前返回值
                let arg_type = parser_expression_for_args(&consequence_node, parser_context, func_call, method_info, class_info);
                if let Later(_, _) = arg_type {
                    // 如果当前arg_type判断是Later, 深度判断后返回值
                    if let Some(alternative_node) = node.child_by_field_name("alternative") {
                        return parser_expression_for_args(&alternative_node, parser_context, func_call, method_info, class_info);
                    }
                }
                return arg_type;
            }
            // 无法判断类型
            return Any;
        }
        "identifier" => {
            // v
            let arg_name = parser_context.text(&node).to_string();
            return find_identifier_arg_type(method_info, class_info, arg_name.as_str());
        }
        "array_access" => {
            // a.b.c[1]
            if let Some(array_node) = node.child_by_field_name("array") {
                // 判断array返回
                let arg_type = parser_expression_for_args(&array_node, parser_context, func_call, method_info, class_info);
                return match arg_type {
                    Any => Any,
                    Later(kind, mut data) => {
                        data.dimensions += "[]";
                        return Later(kind, data);
                    }
                    Type(v) => {
                        // 如果识别出类需要解维度
                        let class_name = trim_whitespaces(v.class_name.as_str());
                        if class_name.ends_with("[]") {
                            let array_class_name = remove_suffix(&class_name, "[]");
                            return Type(class_info.new_class(array_class_name));
                        }
                        return Type(v);
                    }
                };
            }
            return Any;
        }
        _ => {
            // field_access: 对象field访问, eg: req.name => [req, name]
            return Later(node.kind().to_string(), LaterData::build(parser_context.text(&node)));
        }
    }
}

/// 查找方法调用入参的数据类型
/// 查找顺序: local | params | fields | super.fields
fn find_identifier_arg_type(method_info: &mut MethodInfo, class_info: &mut ClassInfo, arg_name: &str) -> ArgumentType<ClassInfo> {
    // local | params | fields | super.fields
    if let Some(var) = method_info.local_var_map.get(arg_name) {
        return Type(var.clone());
    } else if let Some(var) = method_info.param_map.get(arg_name) {
        return Type(var.clone());
    } else if let Some(var) = class_info.field_map.get(arg_name) {
        return Type(var.field_type.clone());
    } else {
        return Later("identifier".to_string(), LaterData::build(arg_name));
    }
}

/// 解析变量定义: field_declaration||local_variable_declaration
pub fn parse_var_declaration(node: &Node, parser_context: &mut ParserContext, class_info: &mut ClassInfo) -> Vec<FieldInfo> {
    let mut ret = Vec::new();
    let kind = node.kind();
    if "field_declaration" != kind && "local_variable_declaration" != kind {
        return ret;
    }
    if let Some(field_type_node) = node.child_by_field_name("type") {
        // 单行定义多个变量
        let mut cursor = node.walk();
        for declarator_node in node.children_by_field_name("declarator", &mut cursor) {
            let modifiers: Vec<String> = list_node_text(node, vec!["modifiers"], parser_context, 1, 1);
            if let Some(name_node) = declarator_node.child_by_field_name("name") {
                let field_name = parser_context.text(&name_node);
                let mut field_type = parser_context.text(&field_type_node).to_string();

                if let Some(dimensions_node) = declarator_node.child_by_field_name("dimensions") {
                    let dimensions = trim_whitespaces(parser_context.text(&dimensions_node));
                    // 维度, eg: [][], 需要附加到类型上
                    field_type = field_type + &dimensions;
                }

                ret.push(FieldInfo {
                    bean_type: None,
                    modifiers: modifiers.first().unwrap_or(&String::new()).to_string(),
                    field_name: field_name.to_string(),
                    field_type: class_info.new_class(field_type.as_str()),
                    comment: parser_context.take_comment(),
                    code_body: parser_context.text(node).to_string(),
                });
            }
        }
    }
    return ret;
}

/// 获取节点下所有指定kind类型的文本
/// # Arguments
/// * `node` - 查找节点下的内容
/// * `kind` - 查找指定类型
/// * `parser_context` - 解析上下文
/// * `max_level` - 搜索最大深度, 一般是1, type_identifier如果考虑<T>则max_level=2
/// * `max_ret` - 限制最大返回值, <=0时表示不限制
pub fn list_node_text(node: &Node, find_kind: Vec<&str>, parser_context: &mut ParserContext, max_level: isize, max_ret: isize) -> Vec<String> {
    let mut ret = Vec::new();
    parser_context.debug_info(&node);
    inner_list_node_text(node, &find_kind, parser_context, &mut ret, 0, max_level, max_ret);
    return ret;
}

fn inner_list_node_text(node: &Node, find_kind: &Vec<&str>, parser_context: &mut ParserContext, ret: &mut Vec<String>, level: isize, max_level: isize, max_ret: isize) {
    if max_ret > 0 && ret.len() >= max_ret as usize {
        return;
    }
    if find_kind.contains(&node.kind()) {
        ret.push(parser_context.text(node).to_string());
        return;
    }

    // 限制最大查询深度
    if level >= max_level {
        return;
    }

    let mut child_cursor = node.walk();
    for variable_node in node.children(&mut child_cursor) {
        parser_context.kind_stack.push(variable_node.kind().to_string());
        parser_context.debug_info(&variable_node);
        inner_list_node_text(&variable_node, find_kind, parser_context, ret, level + 1, max_level, max_ret);
        parser_context.kind_stack.pop();
    }
}

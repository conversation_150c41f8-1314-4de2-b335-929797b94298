use std::collections::HashMap;

use log::info;
use once_cell::sync::Lazy;

use crate::api::req_model::QueryRelatedRequestBean;
use crate::it::java_model::class_info::ClassInfo;
use crate::it::loader::class_loader::{load_repo_class, load_repo_root_pom, load_starter_class, load_test_base_class};
use crate::it::project_model::project_info::{Framework, ProjectInfo};

// 测试基类与框架的关系
pub static TEST_BASE_FRAMEWORK_MAP: Lazy<HashMap<&str, Framework>> = Lazy::new(|| {
    let mut alias_map = HashMap::new();
    // acts
    alias_map.insert("ActsTestBase", Framework::Acts);
    // ats
    alias_map.insert("Sofa3DataDriver", Framework::Ats);
    // sofa-test junit
    alias_map.insert("JunitEclipseSofaTest", Framework::JUnit);
    alias_map.insert("JunitOsgiTests", Framework::JUnit);
    alias_map.insert("JunitOsgiInjectionTest", Framework::JUnit);
    alias_map.insert("JunitAceBundleCreatorTest", Framework::JUnit);
    // sofa-test junit4
    alias_map.insert("SofaJUnit4ClassRunner", Framework::JUnit4);
    alias_map.insert("JUnit4SofaOSGiRunner", Framework::JUnit4);
    // sofa-test testng
    alias_map.insert("TestNGEclipseSofaTest", Framework::TestNG);
    alias_map.insert("TestNGOsgiTest", Framework::TestNG);

    // spring-test junit4
    alias_map.insert("AbstractJUnit4SpringContextTests", Framework::JUnit4);
    alias_map.insert("AbstractTransactionalJUnit4SpringContextTests", Framework::JUnit4);

    // spring-test testng
    alias_map.insert("AbstractTestNGSpringContextTests", Framework::TestNG);
    alias_map.insert("AbstractTransactionalTestNGSpringContextTests", Framework::TestNG);

    return alias_map;
});


pub fn parser_project_info(query_related_request: &QueryRelatedRequestBean) -> ProjectInfo {
    let mut ret = ProjectInfo::default();
    let project_url = query_related_request.projectUrl.as_str();
    info!("[接口测试] 检索Project信息, project_url=[{}]", project_url);

    let test_base_class_name = load_test_base_class(project_url);
    let mut test_framework: Option<Framework> = None;
    if let Some(test_base_file) = test_base_class_name.first() {
        if let Some(test_base_class_arc) = load_repo_class(project_url, test_base_file) {
            let mut test_base_class = (*test_base_class_arc).clone();
            if test_base_class.loaded {
                test_framework = detect_test_framework(&test_base_class);

                test_base_class.prune_basic_info(false, false);
                ret.test_base_class.push(test_base_class);
            }
        }
    }

    let starter_class_name = load_starter_class(project_url);
    if let Some(starter_class_file) = starter_class_name.first() {
        if let Some(starter_class_arc) = load_repo_class(project_url, starter_class_file) {
            let mut starter_class = (*starter_class_arc).clone();
            if starter_class.loaded {
                if starter_class.modifiers.contains("@SpringBootApplication") {
                    ret.add_framework(Framework::SpringBoot);
                }
                starter_class.prune_basic_info(false, false);
                ret.starter_class.push(starter_class);
            }
        }
    }

    if let Some(root_pom) = load_repo_root_pom(project_url) {
        if let Some(parent) = root_pom.parent {
            match parent.artifact_id.as_str() {
                "sofaboot-alipay-dependencies" | "sofaboot-enterprise-dependencies" => {
                    ret.add_framework(Framework::SOFABoot);
                    if parent.version.starts_with("3.") {
                        ret.add_framework(Framework::SOFABoot3);
                    }
                    if parent.version.starts_with("4.") {
                        ret.add_framework(Framework::SOFABoot4);
                    }
                }
                "spring-boot-starter-parent" => {
                    ret.add_framework(Framework::SpringBoot);
                    if parent.version.starts_with("2.") {
                        ret.add_framework(Framework::SpringBoot2);
                    }
                    if parent.version.starts_with("3.") {
                        ret.add_framework(Framework::SpringBoot3);
                    }
                }
                _ => {}
            }
        }

        let mut test_gav = Vec::new();
        for gav in root_pom.dependency_management.iter() {
            match gav.artifact_id.as_str() {
                "cloudengine-bom" => {
                    ret.add_framework(Framework::SOFA);
                    if gav.version.starts_with("3.") {
                        ret.add_framework(Framework::SOFA3);
                    }
                    if gav.version.starts_with("4.") {
                        ret.add_framework(Framework::SOFA4);
                    }
                }
                "pandora-boot-starter-bom" => {
                    ret.add_framework(Framework::Pandora);
                }
                _ => {}
            }

            if test_framework.is_none() {
                match gav.group_id.as_str() {
                    "junit" | "org.junit" => {
                        test_gav.push((Framework::JUnit, 4));
                        if gav.version.starts_with("4.") {
                            test_gav.push((Framework::JUnit4, 2));
                        }
                    }
                    "org.junit.jupiter" => {
                        test_gav.push((Framework::JUnit5, 3));
                    }
                    "org.testng" => {
                        test_gav.push((Framework::TestNG, 1));
                    }
                    _ => {}
                }
            }
        }

        if !test_gav.is_empty() {
            test_gav.sort_by_key(|&(_, priority)| priority);
            test_framework = test_gav.first().map(|(framework, _)| framework.clone());
        }
    }

    // JUnit4做兜底
    ret.add_framework(test_framework.unwrap_or(Framework::JUnit4));
    return ret;
}

pub fn detect_test_framework(test_base_class: &ClassInfo) -> Option<Framework> {
    // 通过extends判断
    if let Some(super_class) = &test_base_class.super_class {
        if let Some(find_framework) = TEST_BASE_FRAMEWORK_MAP.get(super_class.class_name.as_str()) {
            return Some(find_framework.clone());
        }
    }

    // 通过annotation判断
    if test_base_class.modifiers.contains("@Listeners") {
        return Some(Framework::TestNG);
    }
    if test_base_class.modifiers.contains("@RunWith") {
        return Some(Framework::JUnit4);
    }
    if test_base_class.modifiers.contains("@SpringBootTest") {
        return Some(Framework::JUnit5);
    }

    // 通过import判断
    if test_base_class.contains_import_package("org.testng.") {
        return Some(Framework::TestNG);
    }
    if test_base_class.contains_import_package("org.junit.jupiter.") {
        return Some(Framework::JUnit5);
    }
    if test_base_class.contains_import_package("org.junit.") {
        return Some(Framework::JUnit4);
    }

    return None;
}


#[cfg(test)]
mod test {
    use crate::api::req_model::QueryRelatedRequestBean;
    use crate::it::logger_init::init_logger;
    use crate::it::project_model::project_parser::parser_project_info;

    #[test]
    fn test1() {
        let _ = init_logger();
        let default_req_bean = QueryRelatedRequestBean::default();
        let request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/bizsettle".to_string(),
            fileUrl: "".to_string(),
            language: "".to_string(),
            intention: "".to_string(),
            intentionType: Default::default(),
            fileContent: "".to_string(),
            selectedContent: "".to_string(),
            ..default_req_bean
        };
        let project_info = parser_project_info(&request);
        println!("{}", serde_json::to_string(&project_info).unwrap());
    }
}
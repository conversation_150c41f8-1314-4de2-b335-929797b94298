use serde::{Deserialize, Serialize};

use crate::it::java_model::class_info::ClassInfo;

// 项目信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ProjectInfo {
    // 识别出的框架
    pub framework_list: Vec<Framework>,
    // 启动类
    pub starter_class: Vec<ClassInfo>,
    // 测试基类
    pub test_base_class: Vec<ClassInfo>,
}

impl Default for ProjectInfo {
    fn default() -> Self {
        ProjectInfo {
            framework_list: vec![],
            starter_class: vec![],
            test_base_class: vec![],
        }
    }
}

impl ProjectInfo {
    pub fn add_framework(&mut self, framework: Framework) {
        if self.framework_list.contains(&framework) {
            return;
        }
        self.framework_list.push(framework);
    }
}

// 框架信息
// 版本文档: https://yuque.antfin-inc.com/middleware/sofaboot/releasenote
// 不明确指明版本一般是主流版本, 两者是基本兼容的
#[derive(Debug, Serialize, Deserialize, PartialEq, Eq, Partial<PERSON>rd, Clone)]
pub enum Framework {
    SOFA,
    SOFA3,
    SOFA4,
    SOFABoot,
    SOFABoot3,
    SOFABoot4,
    SpringBoot,
    SpringBoot2,
    SpringBoot3,
    Pandora,
    TestNG,
    JUnit,
    JUnit4,
    JUnit5,
    Ats,
    Acts,
}

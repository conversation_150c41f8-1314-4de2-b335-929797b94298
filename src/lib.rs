pub mod dal {
    pub mod kv_client;

    pub mod index_client;

    pub mod vector_client;

    pub mod remote_client;
}
pub mod tools {
    pub mod common_tools;
    pub mod counter;
}
pub mod domain {
    pub mod code_kv_index_domain;

    pub mod code_chat_domain;
    pub mod ap_data;
}

pub mod remote {
    pub mod http_client;
    pub mod rpc_model;
}

///agent配置
pub mod config {
    pub mod runtime_config;

    pub mod agent_logger;
}


#[cfg(test)]
mod tests {
    use std::fs;

    use rocksdb::{IteratorMode, Options, ReadOptions, DB};

    #[test]
    fn it_works() {
        let path = "/tmp/r_data_temp";
        let mut opts = Options::default();
        opts.create_if_missing(true);
        let db = DB::open(&opts, path).unwrap();
        db.put("hello", "world");
        db.put("hello_1", "world11");

        let result = db.get("hello").unwrap();
        match result {
            Some(value) => println!("Retrieved value: {:?}", String::from_utf8(value).unwrap()),
            None => println!("Value not found"),
        }
        //
        // let result = db.get("hello11").unwrap();
        // match result {
        //     Some(value) => println!("Retrieved value: {:?}", String::from_utf8(value).unwrap()),
        //     None => println!("Value not found"),
        // }
        // db.delete("hello");
        //
        // let result = db.get("hello").unwrap();
        // match result {
        //     Some(value) => println!("Retrieved value: {:?}", String::from_utf8(value).unwrap()),
        //     None => println!("Value not found"),
        // }
    }

    #[test]
    fn it_works1() {
        let path = "/tmp/r_data_temp";
        let mut opts = Options::default();
        opts.create_if_missing(true);
        let db = DB::open(&opts, path).unwrap();
        // 使用前缀搜索 "abc"
        let mut read_opts = ReadOptions::default();
        // 设置迭代器的前缀模式
        read_opts.set_prefix_same_as_start(true);
        let iter = db.iterator_opt(IteratorMode::From(b"hello_", rocksdb::Direction::Forward), read_opts);

        for item in iter {
            let (key, value) = item.unwrap();
            let key_str = std::str::from_utf8(&key).unwrap();
            if !key_str.starts_with("hello") {
                println!("hhhh");
                break; // 当键不再以 "abc" 开头时，停止迭代
            }
            println!("Found key: {}, value: {}", key_str, std::str::from_utf8(&value).unwrap());
        }
    }


    #[test]
    fn it_works2() {
        let path = "/tmp/r_data_temp";
        let mut opts = Options::default();
        opts.set_max_total_wal_size(1 << 29);
        let db = DB::open(&opts, path).unwrap();
        for index in 0..1000000 {
            let key = format!("hello_{}", index);
            let r = db.put(key, "world");

            if index == 500000 {
                let result = fs::remove_dir_all(path);
                match result {
                    Ok(_) => {
                        println!("删除成功");
                    }
                    Err(e) => {
                        println!("删除失败: {:?}", e);
                    }
                }
            };
            match r {
                Ok(_) => {}
                Err(e) => {
                    println!("put error: {:?}", e);
                }
            }
        }


        let db_size = db.property_value("rocksdb.estimate-table-readers-mem").unwrap();
        let db_size1 = db.property_value("rocksdb.estimate-num-keys").unwrap();
        let db_size2 = db.property_value("rocksdb.total-sst-files-size").unwrap();
        let db_size3 = db.property_value("rocksdb.dbstats").unwrap();
        println!("Database size: {:?} bytes", db_size);
        println!("Database size: {:?} bytes", db_size1);
        println!("Database size: {:?} bytes", db_size2);
        println!("Database size: {:?} bytes", db_size3);


        let value_opt = db.get("hello_500010").unwrap();
        let r = String::from_utf8(value_opt.unwrap()).unwrap();
        println!("值: {}", r)
    }

    #[test]
    fn test() {
        let path = "/tmp/r_data_temp";
        let mut opts = Options::default();
        opts.set_max_total_wal_size(1 << 29);
        let db = DB::open(&opts, path).unwrap();

        let value_opt = db.get("hello_0").unwrap();
        let r = String::from_utf8(value_opt.unwrap()).unwrap();
        println!("值: {}", r)
    }

    #[test]
    fn test11() {
        let metadata = fs::metadata("/Users/<USER>/RustroverProjects/codefuse_local_agent/agent_db/src/lib.rs").unwrap();
        println!("文件大小: {:?}", metadata.len())
    }
}

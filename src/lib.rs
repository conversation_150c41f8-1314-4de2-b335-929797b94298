///agent模型
pub mod model;

///agent工具
pub mod tools {
    pub mod code_tokenizer;
    pub mod common_tools;
    pub mod jaccard_similarity;
    pub mod related_module_score;
    pub mod git_utils;

    pub mod xml_tools;

}


///agent服务接口定义
pub mod service {
    pub mod code_scan;

    pub mod code_complete;

    pub mod code_complete_rag_framework;

    pub mod code_ast_analysis;
    pub mod code_complete_rag_executor;

    pub mod abstract_similarity;

    pub mod code_complete_cache;
    pub mod project_chat;
    pub mod code_edit;

    pub mod similarity {
        pub mod current_package_handler;
        pub mod similarity_import_handler;
        pub mod similarity_name_handler;
    }
}



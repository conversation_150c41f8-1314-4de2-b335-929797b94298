pub mod it {
    pub mod test {
        pub mod test1;
    }

    pub mod config {
        pub mod codefuse_it_config;
    }
    pub mod logger_init;

    pub mod loader {
        pub mod class_loader;
        pub mod xml_loader;
        pub mod pom_loader;
        pub mod yml_loader;
    }

    pub mod java_parser {
        pub mod common;
        pub mod context;
        pub mod parser;
    }

    pub mod java_model {
        pub mod common;
        pub mod class_type;
        pub mod class_info;
        pub mod field_info;
        pub mod method_info;
        pub mod func_call;
    }

    pub mod project_model {
        pub mod project_info;
        pub mod project_parser;
    }

    pub mod interface_model;
    pub mod interface_parser;
}

pub mod utils {
    pub mod time_utils;
    pub mod string_utils;
    pub mod file_utils;
    pub mod java_utils;
    pub mod vec_utils;
    pub mod index_map;
    pub mod path_cmp_utils;
}

pub mod ut {
    pub mod project_info;
    pub mod agent_ut;
}

pub mod api {
    pub mod req_model;

    pub mod res_model;
    pub mod api;

    pub mod ut_agent_prompt_req_model;
}

pub mod intent {
    pub mod test_intent;
}

pub mod scan {
    pub mod common_scan;
}

pub mod java_ast {
    pub mod java_file_parser;
    pub mod java_ast_handle;
}

pub mod kotlin_ast {
    pub mod kotlin_file_parser;
    pub mod kotlin_ast_handle;
}

pub mod android_strategy {
    pub mod code_complete_scan_strategy_android;
    pub mod code_complete_rag_strategy_android;
    pub mod code_complete_cache_strategy_android;
    pub mod language_file_tyle_adapter;
    pub mod android_ideclient_request_handle;
}

pub mod model {
    pub mod lib_class_cache_model;
}

#[cfg(test)]
mod tests {
    #[test]
    fn it_works() {}
}

use crate::config::runtime_config::AGENT_CONFIG;
use crate::dal::remote_client::QueryInProject;
use crate::tools::common_tools::expand_user_home;
use log::{error, info};
use std::collections::{HashMap, HashSet};
use std::error::Error;
use std::marker::PhantomData;
use std::path::Path;
use std::sync::mpsc::{channel, Receiver, Sender};
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::{SystemTime, UNIX_EPOCH};
use tantivy::collector::{Count, TopDocs};
use tantivy::query::{
    AllQuery, BooleanQuery, FuzzyTermQuery, Occur, PhrasePrefixQuery, Query, QueryParser,
    RegexQuery, TermQuery,
};
use tantivy::schema::{Document, Field, IndexRecordOption, Schema, Value};
use tantivy::tokenizer::{
    LowerCaser, NgramTokenizer, SimpleTokenizer, TextAnalyzer, TextAnalyzerBuilder,
};
use tantivy::{query, Index, IndexWriter, Opstamp, Score, TantivyDocument, Term};
use Occur::{Must, Should};

/// 定义一个 trait，使得不同类型的对象可以转换为 Tantivy 的 Document
pub trait Indexable {
    /// 将对象转换为 Tantivy 的 Document
    fn to_document(&self, schema: &Schema) -> TantivyDocument;

    fn to_schema() -> Schema;
}

enum IndexCommand<T> {
    Save(T),
    Commit(Sender<tantivy::Result<Opstamp>>),
    Shutdown,
    Update(T),
    Delete(T),
}

/// 泛型的索引客户端，绑定到具体类型 T
pub struct IndexClient<T> {
    index: Arc<Index>,
    sender: Sender<IndexCommand<T>>,
    schema: Schema,
    _marker: PhantomData<T>,
}

impl<T: Indexable> IndexClient<T>
where
    T: Indexable + Send + Clone + 'static,
{
    /// 创建一个新的索引客户端
    ///
    /// # 参数
    ///
    /// - `schema`: 索引的 schema
    /// - `index_path`: 索引的存储路径
    /// - `threads`: 索引的线程数
    ///
    /// # 返回
    ///
    /// 返回一个 `IndexClient` 实例
    pub fn create(schema: Schema, index_path: &Path, threads: usize) -> tantivy::Result<Self> {
        info!("create index client,index_path: {:?}", index_path);
        // 创建索引存储路径，如果不存在则创建
        let data_cache_url = expand_user_home(&AGENT_CONFIG.base_data_url)
            .unwrap()
            .join("chat")
            .join(index_path);
        if !data_cache_url.exists() {
            let _ = std::fs::create_dir_all(&data_cache_url);
        }
        let tokenizer = TextAnalyzer::builder(SimpleTokenizer::default())
            .filter(LowerCaser)
            .build();
        // 创建索引
        let mut index = Index::open_or_create(
            tantivy::directory::MmapDirectory::open(data_cache_url)?,
            schema.clone(),
        )?;
        index.set_multithread_executor(threads)?;
        index.tokenizers().register("default", tokenizer);
        // 创建写入器，设置缓冲区大小为 50MB
        let writer = index.writer(50_000_000)?;

        let (sender, receiver): (Sender<IndexCommand<T>>, Receiver<IndexCommand<T>>) = channel();
        let schema_clone = schema.clone();
        thread::spawn(move || {
            let mut writer = writer;
            for command in receiver {
                match command {
                    IndexCommand::Save(obj) => {
                        let doc = obj.to_document(&schema_clone);
                        if let Err(e) = writer.add_document(doc) {
                            error!("fail to add . {:?}", e)
                        }
                    }
                    IndexCommand::Commit(resp_sender) => {
                        let result = writer.commit();
                        if let Err(e) = resp_sender.send(result) {
                            error!("fail to commit {:?}", e)
                        }
                    }
                    IndexCommand::Shutdown => {
                        break;
                    }
                    IndexCommand::Update(obj) => {
                        let field_id = schema_clone.get_field("id").unwrap();
                        let doc = obj.to_document(&schema_clone);
                        let id_value = doc
                            .get_first(field_id)
                            .unwrap()
                            .as_str()
                            .unwrap()
                            .to_string();
                        let delete_term = Term::from_field_text(field_id, &id_value);
                        //删除文档
                        let _ = writer.delete_term(delete_term);
                        //再次保存
                        if let Err(e) = writer.add_document(doc) {
                            error!("fail to update . {:?}", e)
                        }
                    }
                    IndexCommand::Delete(obj) => {
                        let field_id = schema_clone.get_field("id").unwrap();
                        let doc = obj.to_document(&schema_clone);
                        let id_value = doc
                            .get_first(field_id)
                            .unwrap()
                            .as_str()
                            .unwrap()
                            .to_string();
                        let delete_term = Term::from_field_text(field_id, &id_value);
                        //删除文档
                        let _ = writer.delete_term(delete_term);
                    }
                }
            }
        });

        Ok(IndexClient {
            index: Arc::new(index),
            sender,
            schema,
            _marker: PhantomData,
        })
    }

    /// 提交写入
    pub fn commit(&self) -> tantivy::Result<Opstamp> {
        let (resp_sender, resp_receiver) = channel();
        self.sender.send(IndexCommand::Commit(resp_sender)).unwrap();
        resp_receiver.recv().unwrap()
    }

    pub fn save(&self, obj: &T) -> tantivy::Result<()> {
        self.sender.send(IndexCommand::Save(obj.clone())).unwrap();
        Ok(())
    }

    pub fn save_batch(&self, obj_vec: &[T]) -> tantivy::Result<()> {
        for obj in obj_vec {
            let r = self.sender.send(IndexCommand::Save(obj.clone()));

            match r {
                Ok(data) => {}
                Err(e) => {
                    error!("fail to save_batch {:?}", e);
                }
            }
        }
        Ok(())
    }

    pub fn update(&self, obj: &T) -> tantivy::Result<()> {
        self.sender.send(IndexCommand::Update(obj.clone())).unwrap();
        Ok(())
    }

    pub fn update_batch(&self, obj_vec: &[T]) -> tantivy::Result<()> {
        for obj in obj_vec {
            let r = self.sender.send(IndexCommand::Update(obj.clone()));

            match r {
                Ok(data) => {}
                Err(e) => {
                    error!("fail to save_batch {:?}", e);
                }
            }
        }
        Ok(())
    }

    pub fn delete(&self, obj: &T) -> tantivy::Result<()> {
        self.sender.send(IndexCommand::Delete(obj.clone())).unwrap();
        Ok(())
    }

    pub fn delete_batch(&self, obj_vec: &[T]) -> tantivy::Result<()> {
        for obj in obj_vec {
            let r = self.sender.send(IndexCommand::Delete(obj.clone()));

            match r {
                Ok(data) => {}
                Err(e) => {
                    error!("fail to save_batch {:?}", e);
                }
            }
        }
        Ok(())
    }

    /// 保存或更新 CodefuseChunk 对象
    /// 如果索引库中存在相同 id 的文档，则更新；否则新增
    ///
    /// # 参数
    ///
    /// - `obj`: 要保存或更新的对象
    ///
    /// # 返回
    ///
    /// 返回操作结果
    pub fn save_or_update(&self, obj: &T) -> tantivy::Result<()> {
        // 首先检查是否存在相同 id 的文档
        let doc = obj.to_document(&self.schema);
        let field_id = self.schema.get_field("id").unwrap();
        let id_value = doc
            .get_first(field_id)
            .unwrap()
            .as_str()
            .unwrap()
            .to_string();

        // 查询是否存在相同 id 的文档
        let exists = self.check_document_exists(&id_value)?;

        if exists {
            // 如果存在，使用 update 方法
            self.update(obj)
        } else {
            // 如果不存在，使用 save 方法
            self.save(obj)
        }
    }

    /// 批量保存或更新对象
    /// 对每个对象检查是否存在相同 id，存在则更新，否则新增
    pub fn save_or_update_batch(&self, obj_vec: &[T]) -> tantivy::Result<()> {
        for obj in obj_vec {
            let r = self.save_or_update(obj);
            match r {
                Ok(_) => {}
                Err(e) => {
                    error!("fail to save_or_update_batch {:?}", e);
                }
            }
        }
        Ok(())
    }

    /// 检查文档是否存在
    ///
    /// # 参数
    ///
    /// - `id`: 要检查的文档 id
    ///
    /// # 返回
    ///
    /// 返回文档是否存在
    fn check_document_exists(&self, id: &str) -> tantivy::Result<bool> {
        let index_reader = self.index.reader()?;
        let searcher = index_reader.searcher();
        let field_id = self.schema.get_field("id").unwrap();
        let term = Term::from_field_text(field_id, id);
        let term_query = TermQuery::new(term, IndexRecordOption::Basic);

        let top_docs = searcher.search(&term_query, &TopDocs::with_limit(1))?;
        Ok(!top_docs.is_empty())
    }

    ///按照ids， 批量查询匹配id的文档
    pub async fn query_by_id(
        &self,
        ids: &HashSet<String>,
        project_url: &str,
        top_num: usize,
    ) -> tantivy::Result<Vec<(Score, TantivyDocument)>> {
        let mut query_result = Vec::new();

        for id in ids {
            let once_query_result_rst = self.query_by_strict_param(project_url, id).await;
            match once_query_result_rst {
                Ok(once_query_result) => {
                    if once_query_result.len() > 0 {
                        query_result.extend(once_query_result);
                    }
                }
                Err(e) => {
                    info!("fail to query_by_id id is {},e is {:?}", id, e);
                }
            }
        }

        Ok(query_result)
    }

    ///按project_url, id严格匹配
    pub async fn query_by_strict_param(
        &self,
        project_url: &str,
        id: &str,
    ) -> tantivy::Result<Vec<(Score, TantivyDocument)>> {
        if project_url.is_empty() && id.is_empty() {
            return Ok(Vec::new());
        }

        let mut queries = Vec::new();
        if !project_url.is_empty() {
            let field_project_url = self.schema.get_field("project_url")?;
            let term = Term::from_field_text(field_project_url, project_url);
            queries.push((
                Occur::Must,
                Box::new(TermQuery::new(term, IndexRecordOption::Basic)) as Box<dyn Query>,
            ));
        }
        if !id.is_empty() {
            let id_field = self.schema.get_field("id").unwrap();
            let term = Term::from_field_text(id_field, id);
            queries.push((
                Occur::Must,
                Box::new(TermQuery::new(term, IndexRecordOption::Basic)) as Box<dyn Query>,
            ));
        }

        let searcher = self.index.reader()?.searcher();
        let mut query_result = Vec::new();
        let mut boolean_query = BooleanQuery::new(queries);
        let top_docs = searcher.search(&boolean_query, &TopDocs::with_limit(1));

        match top_docs {
            Ok(docs) => {
                for (score, doc_address) in docs {
                    let retrieved_doc_opt = searcher.doc(doc_address);
                    if let Ok(retrieved_doc) = retrieved_doc_opt {
                        query_result.push((score, retrieved_doc));
                    } else {
                        error!("fail to retrieve doc {:?}", doc_address);
                    }
                }
            }
            Err(e) => {
                error!("fail to search top docs {}", e);
            }
        }

        Ok(query_result)
    }
    //指定字段的term查询
    pub async fn query_term_with_field(
        &self,
        file_name: &String,
        value: &String,
        project_url: &String,
        top_num: usize,
    ) -> tantivy::Result<(Vec<(Score, TantivyDocument)>, u128)> {
        let start_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        let reader_rst = self.index.reader()?;
        let query_field = self.schema.get_field(file_name)?;
        let project_field = self.schema.get_field("project_url")?;
        let searcher = reader_rst.searcher();
        // 创建 URL 精确匹配查询
        let url_query = TermQuery::new(
            Term::from_field_text(project_field, project_url),
            IndexRecordOption::Basic,
        );
        let query_parser = QueryParser::for_index(&self.index, vec![query_field]);
        let content_query = query_parser.parse_query(value)?;
        //组合查询条件
        let query = BooleanQuery::new(vec![
            (Occur::Must, Box::new(url_query)),
            (Occur::Must, Box::new(content_query)),
        ]);
        // 执行查询
        let top_docs = searcher.search(&query, &TopDocs::with_limit(top_num))?;
        let mut query_result = Vec::new();
        for (score, doc_address) in top_docs {
            let retrieved_doc = searcher.doc(doc_address)?;
            query_result.push((score, retrieved_doc));
        }
        let consume_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis()
            - start_time;
        (Ok((query_result, consume_time)))
    }

    pub async fn query_multi_key(
        &self,
        field_to_search: &HashSet<String>,
        word_to_search: &HashSet<String>,
        project_url: &str,
        top_num: usize,
        is_execute: bool,
    ) -> tantivy::Result<Vec<(Score, TantivyDocument)>> {
        if !is_execute {
            return Ok(Vec::new());
        }
        if field_to_search.is_empty() {
            info!("field_to_search is empty in query_multi_key");
            return Ok(Vec::new());
        }
        let mut query_result = Vec::new();

        let mut fields: Vec<Field> = Vec::new();
        let reader_rst = self.index.reader();
        if let Ok(index_reader) = reader_rst {
            for key in field_to_search {
                let field_rst = self.schema.get_field(key);
                match field_rst {
                    Ok(field) => {
                        fields.push(field);
                    }
                    Err(e) => {
                        error!("fail to get field , field name {} {:?}", key, e);
                    }
                }
            }

            let mut queries = Vec::new();
            if !project_url.is_empty() {
                let field_project_url_rst = self.schema.get_field("project_url");
                match field_project_url_rst {
                    Ok(field_project_url) => {
                        let query_parser_project_url =
                            QueryParser::for_index(&*self.index, vec![field_project_url]);
                        let query_project_url_rst =
                            query_parser_project_url.parse_query(project_url);
                        if let Ok(query_project_url) = query_project_url_rst {
                            queries.push((Must, query_project_url));
                        }
                    }
                    Err(e) => {
                        error!("fail to search project_url field {:?}", e);
                    }
                }
            }
            if !word_to_search.is_empty() {
                let query_parser_content = QueryParser::for_index(&*self.index, fields);
                // 过滤掉empty的数据
                let mut elements: Vec<String> = word_to_search
                    .iter()
                    .filter(|s| !s.is_empty())
                    .cloned()
                    .collect();
                let query_content_combine = elements.join(" OR ");
                let query_content_rst = query_parser_content.parse_query(&query_content_combine);
                match query_content_rst {
                    Ok(query_content) => {
                        queries.push((Must, query_content));
                    }
                    Err(e) => {
                        error!("fail to search word_to_search {:?}", e);
                    }
                }
            }
            let mut boolean_query = BooleanQuery::new(queries);
            let searcher = index_reader.searcher();
            let top_docs_rst =
                searcher.search(&boolean_query, &TopDocs::with_limit(top_num as usize));
            match top_docs_rst {
                Ok(top_docs) => {
                    for (score, doc_address) in top_docs {
                        let retrieved_doc = searcher.doc(doc_address)?;
                        query_result.push((score, retrieved_doc));
                    }
                }
                Err(e) => {
                    error!("fail to search top_docs {:?}", e);
                }
            }
        }

        Ok(query_result)
    }

    pub fn query(
        &self,
        query_param: &HashMap<String, String>,
        top_num: usize,
    ) -> tantivy::Result<Vec<(Score, TantivyDocument)>> {
        let mut query_result = Vec::new();
        let index_reader = self.index.reader();
        let mut queries: Vec<(Occur, Box<dyn Query>)> = Vec::new();
        match index_reader {
            Ok(index_reader) => {
                let searcher = index_reader.searcher();
                let mut fields: Vec<Field> = Vec::new();
                for (key, value) in query_param.iter() {
                    let field_res = self.schema.get_field(key);
                    match field_res {
                        Ok(field) => {
                            //Ok(field) => fields.push(field),
                            let term_item = Term::from_field_text(field, value);
                            queries.push((
                                Occur::Must,
                                Box::new(TermQuery::new(term_item, IndexRecordOption::Basic))
                                    as Box<dyn Query>,
                            ))
                        }
                        Err(e) => {
                            error!("fail to get field , field name {} {:?}", key, e);
                        }
                    }
                }

                // let query_parser = QueryParser::for_index(&*self.index, fields);
                // let mut queries: Vec<(Occur, Box<dyn Query>)> = Vec::new();
                // for (key, value) in query_param.iter() {
                //     let query = query_parser.parse_query(value)?;
                //     queries.push((Must, query));
                // }

                let boolean_query = BooleanQuery::new(queries);
                let top_docs_rst = searcher.search(&boolean_query, &TopDocs::with_limit(top_num));
                match top_docs_rst {
                    Ok(top_docs) => {
                        for (score, doc_address) in top_docs {
                            let retrieved_doc_rst = searcher.doc(doc_address);
                            if let Ok(retrieved_doc) = retrieved_doc_rst {
                                query_result.push((score, retrieved_doc));
                            } else {
                                error!("fail to search top_docs {:?}", retrieved_doc_rst);
                            }
                        }
                    }
                    Err(e) => {
                        error!("fail to search top_docs {:?}", e);
                    }
                }
            }
            Err(e) => {
                error!("fail to search index {:?}", e);
            }
        }
        Ok(query_result)
    }

    //不支持查询字段为空的情况
    pub async fn query_in_project(
        &self,
        query_in_project: QueryInProject,
        is_execute: bool,
    ) -> tantivy::Result<Vec<(Score, TantivyDocument)>> {
        if !is_execute {
            return Ok(Vec::new());
        }

        if query_in_project.project_url.is_empty() && query_in_project.word_to_search.is_empty() {
            return Ok(Vec::new());
        }
        let mut queries = Vec::new();
        let mut query_result = Vec::new();
        let index_reader_rst = self.index.reader();
        match index_reader_rst {
            Ok(index_reader) => {
                let mut fields: Vec<Field> = Vec::new();
                let searcher = index_reader.searcher();

                for key in query_in_project.field_to_search {
                    let field_res = self.schema.get_field(&key);
                    match field_res {
                        Ok(field) => fields.push(field),
                        Err(e) => {
                            error!("fail to get field , field name {} {:?}", key, e);
                        }
                    }
                }
                if !&query_in_project.project_url.is_empty() {
                    let field_project_url_rst = self.schema.get_field("project_url");
                    match field_project_url_rst {
                        Ok(field_project_url) => {
                            let query_parser_project_url =
                                QueryParser::for_index(&*self.index, vec![field_project_url]);
                            let query_project_url_rst =
                                query_parser_project_url.parse_query(&query_in_project.project_url);
                            if let Ok(query_project_url) = query_project_url_rst {
                                queries.push((Must, query_project_url));
                            } else {
                                error!(
                                    "fail to search project_url field {:?}",
                                    query_in_project.project_url
                                );
                            }
                        }
                        Err(e) => {
                            error!("fail to search project_url field {:?}", e);
                        }
                    }
                }
                if !query_in_project.word_to_search.is_empty() {
                    let query_parser_content = QueryParser::for_index(&*self.index, fields);
                    let mut elements: Vec<String> =
                        query_in_project.word_to_search.iter().cloned().collect();
                    let query_content_combine = elements.join(" OR ");
                    let query_content_rst =
                        query_parser_content.parse_query(&query_content_combine);
                    match query_content_rst {
                        Ok(query_content) => {
                            queries.push((Must, query_content));
                        }
                        Err(e) => {
                            error!("fail to search word_to_search {:?}", e);
                        }
                    }
                }
                let mut boolean_query = BooleanQuery::new(queries);
                let top_docs_rst = searcher.search(
                    &boolean_query,
                    &TopDocs::with_limit(query_in_project.top_num as usize),
                );
                match top_docs_rst {
                    Ok(top_docs) => {
                        for (score, doc_address) in top_docs {
                            let retrieved_doc = searcher.doc(doc_address)?;
                            query_result.push((score, retrieved_doc));
                        }
                    }
                    Err(e) => {
                        error!("fail to search top_docs {:?}", e);
                    }
                }
            }
            Err(e) => {
                error!("fail to search index {:?}", e);
            }
        }
        Ok(query_result)
    }

    //兼容windows平台
    pub async fn query_count_in_project(
        &self,
        query_in_project: QueryInProject,
    ) -> tantivy::Result<usize> {
        if query_in_project.project_url.is_empty() {
            return Ok(0);
        }
        // let mut container = Vec::new();
        let index_reader_rst = self.index.reader();
        match index_reader_rst {
            Ok(index_reader) => {
                let field_project_url_rst = self.schema.get_field("project_url");
                match field_project_url_rst {
                    Ok(field) => {
                        let project_url_term =
                            Term::from_field_text(field, &query_in_project.project_url);
                        let term_query = TermQuery::new(project_url_term, IndexRecordOption::Basic);

                        let searcher = index_reader.searcher();
                        let count_rst = searcher.search(&term_query, &Count);
                        match count_rst {
                            Ok(count) => {
                                return Ok(count);
                            }
                            Err(err) => {
                                println!("fail to search count_rst {:?}", err);
                                error!("fail to search count_rst {:?}", err);
                            }
                        }
                    }
                    Err(e) => {
                        println!("fail to search index {:?}", e);
                        error!("fail to search project_url field {:?}", e);
                    }
                }
            }
            Err(e) => {
                error!("fail to search index {:?}", e);
            }
        }
        Ok(0)
    }

    ///前缀搜索, project_url是预留字段，id_prefix是id的regex，一般就是文件的绝对路径
    pub async fn query_by_id_regex(
        &self,
        project_url: &str,
        id_prefix: &str,
    ) -> tantivy::Result<Vec<(Score, TantivyDocument)>> {
        let index_reader_rst = self.index.reader();
        match index_reader_rst {
            Ok(index_reader) => {
                let id_field_rst = self.schema.get_field("id");
                match id_field_rst {
                    Ok(id_field) => {
                        let prefix_term = Term::from_field_text(id_field, id_prefix);
                        let mut term_query = Vec::new();
                        term_query.push(prefix_term);
                        let phrase_query = PhrasePrefixQuery::new(term_query);
                        let searcher = index_reader.searcher();
                        let count_rst = searcher.search(&phrase_query, &Count);

                        // let searcher = index_reader.searcher();
                        // let regex_query = RegexQuery::from_pattern(id_prefix, id_field).unwrap();
                        // // let top_docs = searcher.search(&regex_query, &TopDocs::with_limit(limit))?;
                        // let count_rst = searcher.search(&regex_query, &Count);
                        match count_rst {
                            Ok(count) => {
                                if count > 0 {
                                    let mut query_result = Vec::new();
                                    let top_docs = searcher
                                        .search(&phrase_query, &TopDocs::with_limit(count))?;
                                    // let top_docs = searcher.search(&regex_query, &TopDocs::with_limit(count))?;
                                    for (score, doc_address) in top_docs {
                                        let retrieved_doc = searcher.doc(doc_address)?;
                                        query_result.push((score, retrieved_doc));
                                    }
                                    return Ok(query_result);
                                }
                            }
                            Err(e) => {}
                        }
                    }
                    Err(e) => {
                        error!("fail to get field for id");
                    }
                }
            }
            Err(e) => {
                error!("fail to search index {:?}", e);
            }
        }
        Ok(Vec::new())
    }

    ///分页查询数据，避免内存风险
    pub async fn query_data_by_page(
        &self,
        query_in_project: QueryInProject,
    ) -> tantivy::Result<Vec<(Score, TantivyDocument)>> {
        let mut query_result = Vec::new();
        if query_in_project.project_url.is_empty() {
            return Ok(query_result);
        }

        let mut container = Vec::new();
        let index_reader_rst = self.index.reader();
        match index_reader_rst {
            Ok(index_reader) => {
                let field_project_url_rst = self.schema.get_field("project_url");
                match field_project_url_rst {
                    Ok(field) => {
                        let query_parser_project_url =
                            QueryParser::for_index(&*self.index, vec![field]);
                        let query_project_url_rst =
                            query_parser_project_url.parse_query(&query_in_project.project_url);
                        if let Ok(query_project_url) = query_project_url_rst {
                            container.push((Must, query_project_url));
                        } else {
                            error!(
                                "fail to search project_url field {:?}",
                                query_in_project.project_url
                            );
                        }
                    }
                    Err(e) => {
                        error!("fail to search project_url field {:?}", e);
                    }
                }
                let searcher = index_reader.searcher();
                let boolean_query = BooleanQuery::new(container);

                let limit_num = query_in_project.page_size as usize;
                let offset_num =
                    ((query_in_project.page_number - 1) * query_in_project.page_size) as usize;
                let top_docs_rst = searcher.search(
                    &boolean_query,
                    &TopDocs::with_limit(limit_num).and_offset(offset_num),
                );
                match top_docs_rst {
                    Ok(top_docs) => {
                        for (score, doc_address) in top_docs {
                            let retrieved_doc = searcher.doc(doc_address)?;
                            query_result.push((score, retrieved_doc));
                        }
                    }
                    Err(e) => {}
                }
            }
            Err(e) => {
                error!("fail to search index {:?}", e);
            }
        }

        Ok(query_result)
    }

    ///查询一个仓库满足某个条件的数据count
    pub async fn query_count_by_position_in_project(
        &self,
        project_url: &str,
        field: &str,
        value: usize,
    ) -> tantivy::Result<usize> {
        if project_url.is_empty() {
            return Ok(0);
        }
        let mut container = Vec::new();
        let index_reader_rst = self.index.reader();
        match index_reader_rst {
            Ok(index_reader) => {
                let field_project_url_rst = self.schema.get_field("project_url");
                match field_project_url_rst {
                    Ok(field) => {
                        let term_1 = Term::from_field_text(field, project_url);
                        container.push((
                            Occur::Must,
                            Box::new(TermQuery::new(term_1, IndexRecordOption::Basic))
                                as Box<dyn Query>,
                        ));
                    }
                    Err(e) => {
                        error!("fail to search project_url field {:?}", e);
                    }
                }
                let field_has_content_vector_rst = self.schema.get_field(field);
                match field_has_content_vector_rst {
                    Ok(field) => {
                        let term_2 = Term::from_field_u64(field, value as u64);
                        container.push((
                            Occur::Must,
                            Box::new(TermQuery::new(term_2, IndexRecordOption::Basic))
                                as Box<dyn Query>,
                        ));
                    }
                    Err(e) => {
                        error!("fail to search project_url field {:?}", e);
                    }
                }
                let searcher = index_reader.searcher();
                let boolean_query = BooleanQuery::new(container);
                let count_rst = searcher.search(&boolean_query, &Count);
                match count_rst {
                    Ok(count) => return Ok(count),
                    Err(e) => {
                        error!("fail to search count_rst {:?}", e);
                    }
                }
            }
            Err(e) => {
                error!("fail to search index {:?}", e);
            }
        }
        Ok(0)
    }

    ///查询没有content vector的数量,需要知道匹配总数而不关心具体内容时非常高效
    pub async fn query_no_content_vector_count_in_project(
        &self,
        project_url: &str,
    ) -> tantivy::Result<usize> {
        self.query_count_by_position_in_project(project_url, "has_content_vector", 0)
            .await
    }

    ///按满足某条件的数据，进行分页查询
    pub async fn query_data_by_position_in_project(
        &self,
        query_in_project: &QueryInProject,
        field: &str,
        value: usize,
    ) -> tantivy::Result<Vec<(Score, TantivyDocument)>> {
        if query_in_project.project_url.is_empty() {
            return Ok(Vec::new());
        }

        let mut query_result = Vec::new();
        let mut queries = Vec::new();
        let index_reader = self.index.reader();
        match index_reader {
            Ok(index_reader) => {
                let searcher = index_reader.searcher();
                let field_project_url_rst = self.schema.get_field("project_url");
                match field_project_url_rst {
                    Ok(field_project_url) => {
                        let term_1 =
                            Term::from_field_text(field_project_url, &query_in_project.project_url);
                        queries.push((
                            Occur::Must,
                            Box::new(TermQuery::new(term_1, IndexRecordOption::Basic))
                                as Box<dyn Query>,
                        ));
                    }
                    Err(e) => {
                        error!("fail to search project_url field {:?}", e);
                    }
                }
                let field_summary_rst = self.schema.get_field(field);
                match field_summary_rst {
                    Ok(field) => {
                        let term_2 = Term::from_field_u64(field, value as u64);
                        queries.push((
                            Occur::Must,
                            Box::new(TermQuery::new(term_2, IndexRecordOption::Basic))
                                as Box<dyn Query>,
                        ));
                    }
                    Err(e) => {
                        error!("fail to search project_url field {:?}", e);
                    }
                }
                let boolean_query = BooleanQuery::new(queries);
                let mut top_docs = Vec::new();
                ///优先查询top
                if query_in_project.top_num != 0 {
                    top_docs = searcher.search(
                        &boolean_query,
                        &TopDocs::with_limit(query_in_project.top_num as usize),
                    )?;
                    let top_docs_rst = searcher.search(
                        &boolean_query,
                        &TopDocs::with_limit(query_in_project.top_num as usize),
                    );
                    match top_docs_rst {
                        Ok(top_docs_d) => {
                            top_docs = top_docs_d;
                        }
                        Err(e) => {
                            error!("fail to search top_docs {:?}", e);
                        }
                    }
                } else if query_in_project.page_number != 0 && query_in_project.page_size != 0 {
                    ///其次进行分页查询
                    let limit_num = query_in_project.page_size as usize;
                    let offset_num =
                        ((query_in_project.page_number - 1) * query_in_project.page_size) as usize;
                    let top_docs_rst = searcher.search(
                        &boolean_query,
                        &TopDocs::with_limit(limit_num).and_offset(offset_num),
                    );
                    match top_docs_rst {
                        Ok(top_docs_d) => {
                            top_docs = top_docs_d;
                        }
                        Err(e) => {
                            error!("fail to search top_docs {:?}", e);
                        }
                    }
                }
                for (score, doc_address) in top_docs {
                    let retrieved_doc = searcher.doc(doc_address)?;
                    query_result.push((score, retrieved_doc));
                }
            }

            Err(e) => {
                error!("fail to search index {:?}", e);
            }
        }
        Ok(query_result)
    }

    ///分页查询没有content vector的数据
    pub async fn query_data_no_content_vector_in_project(
        &self,
        query_in_project: &QueryInProject,
    ) -> tantivy::Result<Vec<(Score, TantivyDocument)>> {
        self.query_data_by_position_in_project(query_in_project, "has_content_vector", 0)
            .await
    }
    ///分页查询没有summary vector的数据
    pub async fn query_data_no_summary_vector_in_project(
        &self,
        query_in_project: &QueryInProject,
    ) -> tantivy::Result<Vec<(Score, TantivyDocument)>> {
        self.query_data_by_position_in_project(query_in_project, "has_summary_vector", 0)
            .await
    }
    //db中文档的总数量
    pub fn total_count(&self) -> u64 {
        let index_reader = self.index.reader();
        match index_reader {
            Ok(index_reader) => {
                let searcher = index_reader.searcher();
                let doc_count = searcher.num_docs();
                doc_count
            }
            Err(_) => 0,
        }
    }

    /// 快速获取到db里的所有project和branch， 主要用于数据的恢复
    ///为了加快查询速度， 每隔10条数据检索一次，每次检索5条数据，
    /// 推荐在FILE的db里使用， 速度最快
    pub fn get_distinct_project(&self) -> tantivy::Result<HashSet<(String, String)>> {
        //每页数量
        let page_size = 5;
        //间隔数量
        let skip_num = 20;
        let total_count = self.total_count();
        let times = total_count as usize / (page_size + skip_num);

        let mut distinct_data = HashSet::new();

        let index_reader_rst = self.index.reader();
        match index_reader_rst {
            Ok(index_reader) => {
                let searcher = index_reader.searcher();
                //匹配所有文档的查询
                let query = AllQuery;
                let mut current_time = 1;
                while current_time <= times {
                    //使用 TopDocs 收集器，设置限制和偏移
                    let top_docs_rst = searcher.search(
                        &query,
                        &TopDocs::with_limit(page_size)
                            .and_offset((current_time - 1) * (page_size + skip_num)),
                    );
                    current_time = current_time + 1;
                    if let Ok(top_docs) = top_docs_rst {
                        for (score, doc_address) in top_docs {
                            let retrieved_doc = searcher.doc::<TantivyDocument>(doc_address);
                            if let Ok(doc) = retrieved_doc {
                                //获取 project_url 和 branch 字段的值
                                let project_url = doc
                                    .get_first(self.schema.get_field("project_url").unwrap())
                                    .unwrap()
                                    .as_str()
                                    .unwrap_or("");
                                let branch = doc
                                    .get_first(self.schema.get_field("branch").unwrap())
                                    .unwrap()
                                    .as_str()
                                    .unwrap_or("");

                                // 将对应的值添加到 HashSet 中，自动去重
                                distinct_data.insert((project_url.to_string(), branch.to_string()));
                            }
                        }
                    }
                }
            }
            Err(e) => {}
        }
        Ok(distinct_data)
    }
    ///在整个db里，分页获取has_content_vector=1的数据
    pub fn get_has_content_vector_data_by_page(
        &self,
        query_in_project: QueryInProject,
    ) -> tantivy::Result<Vec<(Score, TantivyDocument)>> {
        let index_reader_rst = self.index.reader();
        match index_reader_rst {
            Ok(index_reader) => {
                let searcher = index_reader.searcher();
                let has_content_vector_field =
                    self.index.schema().get_field("has_content_vector").unwrap();
                // 创建查询：has_content_vector = 1
                let query = TermQuery::new(
                    Term::from_field_u64(has_content_vector_field, 1),
                    IndexRecordOption::Basic,
                );
                // 执行查询
                let offset =
                    (query_in_project.page_number - 1) * query_in_project.page_size as usize;
                let top_docs = searcher.search(
                    &query,
                    &TopDocs::with_limit(query_in_project.page_size).and_offset(offset),
                )?;
                let mut result_container = Vec::new();
                for (score, doc_address) in top_docs {
                    let retrieved_doc = searcher.doc(doc_address)?;
                    result_container.push((score, retrieved_doc));
                }
                return Ok(result_container);
            }
            Err(e) => {}
        }
        Ok(Vec::new())
    }

    ///获取整个db库里，has_content_vector=1的文档数量
    pub fn get_has_content_vector_data_count(&self) -> tantivy::Result<usize> {
        let index_reader_rst = self.index.reader();
        match index_reader_rst {
            Ok(index_reader) => {
                let searcher = index_reader.searcher();
                let has_content_vector_field =
                    self.index.schema().get_field("has_content_vector").unwrap();
                // 创建查询：has_content_vector = 1
                let query = TermQuery::new(
                    Term::from_field_u64(has_content_vector_field, 1),
                    IndexRecordOption::Basic,
                );
                // 执行查询
                let count = searcher.search(&query, &Count)?;
                return Ok(count);
            }
            Err(e) => {}
        }
        Ok(0)
    }

    /// 按指定project_url查询tantivy里面所有file_url集合
    ///
    /// # 参数
    ///
    /// - `project_url`: 项目URL，用于过滤特定项目的数据
    ///
    /// # 返回
    ///
    /// 返回该项目下所有不重复的file_url集合
    pub async fn query_file_vec(&self, project_url: &str) -> tantivy::Result<Vec<String>> {
        if project_url.is_empty() {
            return Ok(Vec::new());
        }

        let index_reader_rst = self.index.reader();
        match index_reader_rst {
            Ok(index_reader) => {
                let searcher = index_reader.searcher();
                let field_project_url = self.schema.get_field("project_url")?;
                let field_file_url = self.schema.get_field("file_url")?;

                // 创建project_url精确匹配查询
                let term = Term::from_field_text(field_project_url, project_url);
                let query = TermQuery::new(term, IndexRecordOption::Basic);

                // 首先获取总文档数量来动态设置限制
                let count_result = searcher.search(&query, &Count);
                let total_docs = match count_result {
                    Ok(count) => count,
                    Err(e) => {
                        error!(
                            "fail to count documents for project_url: {} {:?}",
                            project_url, e
                        );
                        return Err(e);
                    }
                };

                info!(
                    "Found {} total documents for project_url: {}",
                    total_docs, project_url
                );

                // 使用实际文档数量作为限制，确保获取所有结果
                let limit = if total_docs > 0 { total_docs } else { 1000 };
                let top_docs_rst = searcher.search(&query, &TopDocs::with_limit(limit));

                match top_docs_rst {
                    Ok(top_docs) => {
                        let mut file_urls = std::collections::HashSet::new();

                        // 处理所有文档，不设置任何硬编码限制
                        for (_score, doc_address) in top_docs {
                            if let Ok(doc) = searcher.doc::<TantivyDocument>(doc_address) {
                                if let Some(file_url_value) = doc.get_first(field_file_url) {
                                    if let Some(file_url_str) = file_url_value.as_str() {
                                        file_urls.insert(file_url_str.to_string());
                                    }
                                }
                            }
                        }

                        info!(
                            "Collected {} unique file URLs for project_url: {}",
                            file_urls.len(),
                            project_url
                        );

                        // 将HashSet转换为Vec并返回，自动去重
                        Ok(file_urls.into_iter().collect())
                    }
                    Err(e) => {
                        error!("fail to search project_url: {} {:?}", project_url, e);
                        Err(e)
                    }
                }
            }
            Err(e) => {
                error!("fail to get index reader {:?}", e);
                Err(e)
            }
        }
    }

    /// 按指定project_url查询tantivy里面所有数据的id集合
    /// 使用分页查询确保能获取所有符合条件的数据，不受数量限制
    ///
    /// # 参数
    ///
    /// - `project_url`: 项目URL，用于过滤特定项目的数据
    ///
    /// # 返回
    ///
    /// 返回该项目下所有数据的id集合
    pub async fn query_all_ids_by_project(
        &self,
        project_url: &str,
    ) -> tantivy::Result<Vec<String>> {
        info!(
            "Starting query_all_ids_by_project for project_url: {}",
            project_url
        );

        if project_url.is_empty() {
            return Ok(Vec::new());
        }

        // 首先获取该项目的总记录数
        let mut query_in_project = QueryInProject::default();
        query_in_project.project_url = project_url.to_string();
        let total_count = self
            .query_count_in_project(query_in_project.clone())
            .await?;
        info!(
            "Total records for project_url '{}': {}",
            project_url, total_count
        );

        if total_count == 0 {
            return Ok(Vec::new());
        }

        // 设置分页参数
        let page_size = 1000; // 每页1000条记录，平衡内存使用和查询效率
        let total_pages = (total_count + page_size - 1) / page_size; // 向上取整

        let mut all_ids = Vec::with_capacity(total_count);

        // 分页查询所有数据
        for page in 0..total_pages {
            info!(
                "Querying page {} of {} for project_url: {}",
                page + 1,
                total_pages,
                project_url
            );

            // 设置分页查询参数
            query_in_project.page_size = page_size;
            query_in_project.page_number = page + 1; // page_number 从1开始

            // 执行分页查询
            let page_results = self.query_data_by_page(query_in_project.clone()).await?;

            // 提取ID字段
            let index_reader_rst = self.index.reader();
            match index_reader_rst {
                Ok(index_reader) => {
                    let field_id = self.schema.get_field("id")?;
                    let mut page_ids = Vec::new();

                    for (_score, doc) in page_results {
                        if let Some(id_value) = doc.get_first(field_id) {
                            if let Some(id_str) = id_value.as_str() {
                                page_ids.push(id_str.to_string());
                            }
                        }
                    }

                    info!("Page {} completed, found {} ids", page + 1, page_ids.len());
                    all_ids.extend(page_ids);

                    // 如果当前页的数据少于page_size，说明已经是最后一页
                    if all_ids.len() >= total_count {
                        break;
                    }
                }
                Err(e) => {
                    error!("fail to get index reader for page {}: {:?}", page + 1, e);
                    return Err(e);
                }
            }
        }

        info!(
            "Query completed, found {} ids for project_url: {} (expected: {})",
            all_ids.len(),
            project_url,
            total_count
        );
        Ok(all_ids)
    }

    /// 根据id集合查询对应的file_url集合（去重）
    ///
    /// # 参数
    ///
    /// - `ids`: id集合
    /// - `project_url`: 项目URL，用于过滤特定项目的数据
    ///
    /// # 返回
    ///
    /// 返回这些id对应的不重复file_url集合
    pub async fn query_file_urls_by_ids(
        &self,
        ids: &[String],
        project_url: &str,
    ) -> tantivy::Result<Vec<String>> {
        if ids.is_empty() {
            return Ok(Vec::new());
        }

        let index_reader_rst = self.index.reader();
        match index_reader_rst {
            Ok(index_reader) => {
                let searcher = index_reader.searcher();
                let field_project_url = self.schema.get_field("project_url")?;
                let field_id = self.schema.get_field("id")?;
                let field_file_url = self.schema.get_field("file_url")?;

                let mut file_urls = std::collections::HashSet::new();

                // 为每个id创建查询
                for id in ids {
                    let mut queries = Vec::new();

                    // 添加project_url条件
                    if !project_url.is_empty() {
                        let term = Term::from_field_text(field_project_url, project_url);
                        queries.push((
                            Occur::Must,
                            Box::new(TermQuery::new(term, IndexRecordOption::Basic))
                                as Box<dyn Query>,
                        ));
                    }

                    // 添加id条件
                    let term = Term::from_field_text(field_id, id);
                    queries.push((
                        Occur::Must,
                        Box::new(TermQuery::new(term, IndexRecordOption::Basic)) as Box<dyn Query>,
                    ));

                    let boolean_query = BooleanQuery::new(queries);

                    // 搜索匹配的文档
                    let top_docs_rst = searcher.search(&boolean_query, &TopDocs::with_limit(10));
                    match top_docs_rst {
                        Ok(top_docs) => {
                            for (_score, doc_address) in top_docs {
                                if let Ok(doc) = searcher.doc::<TantivyDocument>(doc_address) {
                                    if let Some(file_url_value) = doc.get_first(field_file_url) {
                                        if let Some(file_url_str) = file_url_value.as_str() {
                                            file_urls.insert(file_url_str.to_string());
                                        }
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            error!("fail to search id: {} {:?}", id, e);
                        }
                    }
                }

                // 将HashSet转换为Vec并返回
                Ok(file_urls.into_iter().collect())
            }
            Err(e) => {
                error!("fail to get index reader {:?}", e);
                Err(e)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::dal::index_client::Indexable;
    use crate::domain::code_chat_domain::CodefuseChunk;
    use rand::Rng;
    use std::time::Instant;
    use tantivy::schema::Schema;
    use tantivy::{doc, TantivyDocument};
    use tempfile::TempDir;

    /// 示例结构体: User
    struct User {
        id: u64,
        name: String,
        email: String,
    }

    /// 实现 `Indexable` trait 以便 User 可以被索引
    impl Indexable for User {
        fn to_document(&self, schema: &Schema) -> TantivyDocument {
            doc!(
                schema.get_field("id").unwrap() => self.id,
                schema.get_field("name").unwrap() => &*self.name,
                schema.get_field("email").unwrap() => &*self.email,
            )
        }

        fn to_schema() -> Schema {
            // 定义 User 的 schema
            let mut user_schema_builder = Schema::builder();
            let user_id = user_schema_builder.add_u64_field("id", tantivy::schema::STORED);
            let user_name = user_schema_builder
                .add_text_field("name", tantivy::schema::TEXT | tantivy::schema::STORED);
            let user_email = user_schema_builder
                .add_text_field("email", tantivy::schema::TEXT | tantivy::schema::STORED);
            user_schema_builder.build()
        }
    }

    /// 示例结构体: Person
    struct Person {
        id: u64,
        first_name: String,
        last_name: String,
        age: u64,
    }

    /// 实现 `Indexable` trait 以便 Person 可以被索引
    impl Indexable for Person {
        fn to_document(&self, schema: &Schema) -> TantivyDocument {
            doc!(
                schema.get_field("id").unwrap() => self.id,
                schema.get_field("first_name").unwrap() => &*self.first_name,
                schema.get_field("last_name").unwrap() => &*self.last_name,
                schema.get_field("age").unwrap() => self.age,
            )
        }

        fn to_schema() -> Schema {
            // 定义 Person 的 schema
            let mut person_schema_builder = Schema::builder();
            let person_id = person_schema_builder.add_u64_field("id", tantivy::schema::STORED);
            let first_name = person_schema_builder.add_text_field(
                "first_name",
                tantivy::schema::TEXT | tantivy::schema::STORED,
            );
            let last_name = person_schema_builder
                .add_text_field("last_name", tantivy::schema::TEXT | tantivy::schema::STORED);
            let age = person_schema_builder.add_u64_field("age", tantivy::schema::STORED);
            person_schema_builder.build()
        }
    }

    /// 生成随机的 CodefuseChunk 数据
    fn generate_random_chunk(id: usize) -> CodefuseChunk {
        use rand::Rng;
        let mut rng = rand::thread_rng();

        CodefuseChunk {
            id: format!("chunk_{}", id),
            file_url: format!("/path/to/file_{}.rs", rng.gen_range(1..=100)),
            index: id as u64,
            content: format!("fn test_function_{}() {{\n    // Some test code\n    println!(\"Hello, world!\");\n}}", id),
            start_line: rng.gen_range(1..=1000),
            end_line: rng.gen_range(1001..=2000),
            summary: format!("Summary for chunk {}", id),
            project_url: format!("/project/path_{}", rng.gen_range(1..=10)),
            branch: "main".to_string(),
            hash: format!("hash_{:x}", rng.gen::<u64>()),
            feature: "test".to_string(),
            summary_keyword: vec![format!("keyword_{}", id), "test".to_string()],
            has_summary: rng.gen_range(0..=1),
            has_content_vector: rng.gen_range(0..=1),
            has_summary_vector: rng.gen_range(0..=1),
        }
    }

    #[test]
    fn test_save_or_update_functionality() {
        use std::time::Instant;
        use tempfile::TempDir;

        // 创建临时目录用于测试
        let temp_dir = TempDir::new().unwrap();
        let index_path = temp_dir.path().join("test_index");

        // 生成少量测试数据
        println!("Generating test CodefuseChunk objects...");
        let chunks: Vec<CodefuseChunk> = (0..10).map(|i| generate_random_chunk(i)).collect();

        println!("Generated {} chunks", chunks.len());

        // 测试基本的 save_or_update 功能
        {
            println!("\n=== Testing save_or_update functionality ===");
            let client = IndexClient::<CodefuseChunk>::create(
                CodefuseChunk::to_schema(),
                &index_path.join("save_or_update_test"),
                4,
            )
            .unwrap();

            // 第一次保存 - 应该是新增
            println!("First save (should be insert)...");
            let start = Instant::now();
            client.save_or_update_batch(&chunks).unwrap();
            client.commit().unwrap();
            let duration = start.elapsed();
            println!("First save took: {:?}", duration);

            // 修改数据内容
            let updated_chunks: Vec<CodefuseChunk> = chunks
                .iter()
                .map(|chunk| {
                    let mut updated = chunk.clone();
                    updated.content = format!("UPDATED: {}", chunk.content);
                    updated.summary = format!("UPDATED: {}", chunk.summary);
                    updated
                })
                .collect();

            // 第二次保存 - 应该是更新
            println!("Second save (should be update)...");
            let start = Instant::now();
            client.save_or_update_batch(&updated_chunks).unwrap();
            client.commit().unwrap();
            let duration = start.elapsed();
            println!("Second save took: {:?}", duration);

            println!("save_or_update functionality test completed successfully!");
        }

        println!("\n=== Functionality Test Completed ===");
    }

    #[test]
    #[ignore] // 使用 ignore 标记，需要手动运行
    fn test_performance_comparison_large() {
        use std::time::Instant;
        use tempfile::TempDir;

        // 创建临时目录用于测试
        let temp_dir = TempDir::new().unwrap();
        let index_path = temp_dir.path().join("test_index");

        // 生成10000个随机数据
        println!("Generating 10000 random CodefuseChunk objects...");
        let chunks: Vec<CodefuseChunk> = (0..10000).map(|i| generate_random_chunk(i)).collect();

        println!("Generated {} chunks", chunks.len());

        // 测试 save_batch 性能
        {
            println!("\n=== Testing save_batch performance ===");
            let client = IndexClient::<CodefuseChunk>::create(
                CodefuseChunk::to_schema(),
                &index_path.join("save_batch"),
                4,
            )
            .unwrap();

            let start = Instant::now();
            client.save_batch(&chunks).unwrap();
            client.commit().unwrap();
            let duration = start.elapsed();

            println!("save_batch took: {:?}", duration);
            println!("Average per item: {:?}", duration / chunks.len() as u32);
        }

        // 测试 save_or_update_batch 性能（全新数据）
        {
            println!("\n=== Testing save_or_update_batch performance (new data) ===");
            let client = IndexClient::<CodefuseChunk>::create(
                CodefuseChunk::to_schema(),
                &index_path.join("save_or_update_new"),
                4,
            )
            .unwrap();

            let start = Instant::now();
            client.save_or_update_batch(&chunks).unwrap();
            client.commit().unwrap();
            let duration = start.elapsed();

            println!("save_or_update_batch (new data) took: {:?}", duration);
            println!("Average per item: {:?}", duration / chunks.len() as u32);
        }

        // 测试 save_or_update_batch 性能（更新已存在数据）
        {
            println!("\n=== Testing save_or_update_batch performance (update existing) ===");
            let client = IndexClient::<CodefuseChunk>::create(
                CodefuseChunk::to_schema(),
                &index_path.join("save_or_update_existing"),
                4,
            )
            .unwrap();

            // 先插入数据
            client.save_batch(&chunks).unwrap();
            client.commit().unwrap();

            // 修改数据内容
            let updated_chunks: Vec<CodefuseChunk> = chunks
                .iter()
                .map(|chunk| {
                    let mut updated = chunk.clone();
                    updated.content = format!("UPDATED: {}", chunk.content);
                    updated.summary = format!("UPDATED: {}", chunk.summary);
                    updated
                })
                .collect();

            let start = Instant::now();
            client.save_or_update_batch(&updated_chunks).unwrap();
            client.commit().unwrap();
            let duration = start.elapsed();

            println!(
                "save_or_update_batch (update existing) took: {:?}",
                duration
            );
            println!("Average per item: {:?}", duration / chunks.len() as u32);
        }

        // 测试混合场景：50% 新数据，50% 更新数据
        {
            println!("\n=== Testing save_or_update_batch performance (mixed scenario) ===");
            let client = IndexClient::<CodefuseChunk>::create(
                CodefuseChunk::to_schema(),
                &index_path.join("save_or_update_mixed"),
                4,
            )
            .unwrap();

            // 先插入前5000条数据
            let existing_chunks = &chunks[0..5000];
            client.save_batch(existing_chunks).unwrap();
            client.commit().unwrap();

            // 创建混合数据：前5000条是更新，后5000条是新数据
            let mut mixed_chunks = Vec::new();

            // 前5000条：更新已存在的数据
            for chunk in &chunks[0..5000] {
                let mut updated = chunk.clone();
                updated.content = format!("UPDATED: {}", chunk.content);
                mixed_chunks.push(updated);
            }

            // 后5000条：全新数据
            for i in 5000..10000 {
                mixed_chunks.push(generate_random_chunk(i + 10000)); // 使用不同的ID避免冲突
            }

            let start = Instant::now();
            client.save_or_update_batch(&mixed_chunks).unwrap();
            client.commit().unwrap();
            let duration = start.elapsed();

            println!(
                "save_or_update_batch (mixed: 50% update, 50% new) took: {:?}",
                duration
            );
            println!(
                "Average per item: {:?}",
                duration / mixed_chunks.len() as u32
            );
        }

        println!("\n=== Performance Test Completed ===");
    }
}

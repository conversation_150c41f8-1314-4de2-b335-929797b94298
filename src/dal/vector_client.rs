use crate::config::runtime_config::AGENT_CONFIG;
use crate::dal::kv_client::{KvClient, KV_CLIENT};

use crate::remote::http_client::{post_request, DEFAULT_TIMEOUT_MILLIS};
use crate::remote::rpc_model::BaseResponse;
use crate::tools::common_tools::{
    expand_user_home, V_C_PREFIX
};

use crate::domain::code_chat_domain::CodefuseChunk;
use anyhow::{anyhow, Result};
use arrow_array::{Array, FixedSizeListArray, Float32Array, Float64Array, RecordBatch, RecordBatchIterator, StringArray, UInt64Array};
use arrow_buffer::OffsetBuffer;
use arrow_schema::{DataType, Field, Schema};
use futures::TryStreamExt;
use lancedb::query::{ExecutableQuery, QueryBase};
use lancedb::{connect, Connection, Table};
use lancedb::table::{OptimizeAction, CompactionOptions};
use chrono::Duration as ChronoDuration;
use log::{debug, error, info, warn};
use once_cell::sync::Lazy;
use rocksdb::{DBWithThreadMode, Options, SingleThreaded, DB};
use serde::{Deserialize, Serialize};
use std::cmp::{Ordering, Reverse};
use std::collections::{BinaryHeap, HashMap};
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::thread::sleep;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::runtime::Runtime;
use tokio::sync::{RwLock, mpsc, Mutex};
use tokio::task::JoinHandle;
use crate::dal::remote_client::{cge_embedding, update_event_track, CgeEmbeddingRequestBean, IndexTypeEnum, EMBEDDING_BUILD};

const VECTOR_TABL_NAME: &str = "CODEFUSE_VECTOR";

pub const VECTOR_FIELD_ID: &str = "id";
pub const VECTOR_FIELD_PROJECT_URL: &str = "project_url";

/// 写入队列的命令类型
#[derive(Debug)]
enum WriteCommand {
    BatchUpsert(Vec<VectorItem>, tokio::sync::oneshot::Sender<Result<()>>),
    ForceFlush(tokio::sync::oneshot::Sender<Result<()>>),
    Shutdown,
}


pub static VECTOR_CLIENT: Lazy<VectorClient> = Lazy::new(|| {
    let data_cache_url = expand_user_home(&AGENT_CONFIG.base_data_url)
        .unwrap()
        .join("vectory");
    if !data_cache_url.exists() {
        let _ = fs::create_dir_all(&data_cache_url);
    }

    // 使用 block_in_place 来避免嵌套 runtime 问题
    tokio::task::block_in_place(|| {
        tokio::runtime::Handle::current().block_on(async {
            VectorClient::new(data_cache_url, 2048).await.unwrap()
        })
    })
});


///向量客户端
pub struct VectorClient {
    //链接
    conn: Connection,
    //表
    table: Table,
    //写入队列发送端
    write_sender: mpsc::UnboundedSender<WriteCommand>,
    //写入任务句柄
    _write_task: JoinHandle<()>,
    //存储监控任务句柄
    _storage_monitor_task: JoinHandle<()>,
    //数据库路径
    db_path: PathBuf,
}

impl VectorClient {
    ///创建向量client
    pub async fn new<P: AsRef<Path>>(path: P,vector_size:i32) -> Result<Self> {
        let db_path = path.as_ref().to_path_buf();
        let path_str = path.as_ref().to_str().ok_or_else(|| anyhow!("Invalid path"))?;

        // 如果启用了压缩优化，可以在这里设置连接选项
        let connection =  connect(path_str).execute().await?;

        // 尝试打开现有表，如果不存在则创建新表
        let table = match connection.open_table(VECTOR_TABL_NAME).execute().await {
            Ok(table) => {
                info!("Successfully opened existing table: {}", VECTOR_TABL_NAME);
                table
            }
            Err(e) => {
                warn!("Table {} does not exist, creating new table. Error: {:?}", VECTOR_TABL_NAME, e);

                // 创建表的schema，与VectorItem结构体保持一致
                // 注意：LanceDB要求向量列使用FixedSizeList<Float32>类型
                // 这里使用一个默认的向量维度，实际使用时会根据数据动态调整
                let schema = Arc::new(Schema::new(vec![
                    Field::new("id", DataType::Utf8, false),           // id字段，字符串类型，不允许为空
                    Field::new("vector", DataType::FixedSizeList(Arc::new(Field::new("item", DataType::Float32, true)), vector_size), true), // vector字段，f32定长数组，测试用3维
                    Field::new("project_url", DataType::Utf8, true),   // project_url字段，字符串类型
                    Field::new("created_at", DataType::UInt64, false), // created_at字段，u64类型，记录创建时间毫秒数
                ]));

                // 创建空的RecordBatch用于初始化表
                let empty_batch = RecordBatch::new_empty(schema.clone());
                let batches = vec![empty_batch];
                let batch_reader = RecordBatchIterator::new(batches.into_iter().map(Ok), schema);

                let new_table = connection
                    .create_table(VECTOR_TABL_NAME, Box::new(batch_reader))
                    .execute()
                    .await?;

                info!("Successfully created new table: {}", VECTOR_TABL_NAME);
                new_table
            }
        };

        // 创建写入队列
        let (write_sender, write_receiver) = mpsc::unbounded_channel();

        // 启动写入任务
        let table_clone = table.clone();
        let write_task = tokio::spawn(Self::write_worker(table_clone, write_receiver));

        // 启动存储监控任务
        let table_clone_for_monitor = table.clone();
        let db_path_clone = db_path.clone();
        let storage_monitor_task = tokio::spawn(Self::storage_monitor_worker(table_clone_for_monitor, db_path_clone));

        Ok(VectorClient {
            conn: connection,
            table,
            write_sender,
            _write_task: write_task,
            _storage_monitor_task: storage_monitor_task,
            db_path,
        })
    }

    /// 写入工作线程，确保所有写入操作串行执行
    async fn write_worker(table: Table, mut receiver: mpsc::UnboundedReceiver<WriteCommand>) {
        let mut write_buffer: Vec<VectorItem> = Vec::new();
        let batch_size = AGENT_CONFIG.vector_batch_write_size;

        // 创建一个定时器，用于定期刷新缓冲区（1000ms间隔，确保数据及时写入）
        let mut flush_interval = tokio::time::interval(Duration::from_millis(1000));

        loop {
            tokio::select! {
                // 处理写入命令
                command = receiver.recv() => {
                    match command {
                        Some(WriteCommand::BatchUpsert(items, response_sender)) => {
                            // 将数据添加到缓冲区
                            write_buffer.extend(items);

                            // 如果缓冲区达到批量大小，立即刷新
                            if write_buffer.len() >= batch_size {
                                let result = Self::flush_buffer(&table, &mut write_buffer).await;
                                let _ = response_sender.send(result);
                            } else {
                                // 数据已缓冲，但还未写入数据库
                                // 会在定时器触发时或程序关闭时写入
                                let _ = response_sender.send(Ok(()));
                            }
                        }
                        Some(WriteCommand::ForceFlush(response_sender)) => {
                            // 强制刷新缓冲区
                            let result = if !write_buffer.is_empty() {
                                Self::flush_buffer(&table, &mut write_buffer).await
                            } else {
                                Ok(())
                            };
                            let _ = response_sender.send(result);
                        }
                        Some(WriteCommand::Shutdown) => {
                            // 在关闭前刷新剩余数据
                            if !write_buffer.is_empty() {
                                let _ = Self::flush_buffer(&table, &mut write_buffer).await;
                            }
                            break;
                        }
                        None => {
                            // 通道关闭，刷新剩余数据并退出
                            if !write_buffer.is_empty() {
                                let _ = Self::flush_buffer(&table, &mut write_buffer).await;
                            }
                            break;
                        }
                    }
                }
                // 定期刷新缓冲区
                _ = flush_interval.tick() => {
                    if !write_buffer.is_empty() {
                        let _ = Self::flush_buffer(&table, &mut write_buffer).await;
                    }
                }
            }
        }
    }

    /// 刷新写入缓冲区
    async fn flush_buffer(table: &Table, buffer: &mut Vec<VectorItem>) -> Result<()> {
        if buffer.is_empty() {
            return Ok(());
        }

        info!("Flushing {} items to vector database", buffer.len());

        let result = Self::batch_upsert_internal(table, buffer.clone()).await;

        match &result {
            Ok(_) => {
                info!("Successfully flushed {} items to vector database", buffer.len());
                buffer.clear();
            }
            Err(e) => {
                error!("Failed to flush buffer to vector database: {}", e);
                // 在错误情况下也清空缓冲区，避免无限重试
                buffer.clear();
            }
        }

        result
    }

    /// 存储监控工作线程，定期检查存储大小并执行清理
    async fn storage_monitor_worker(table: Table, db_path: PathBuf) {
        let check_interval = Duration::from_millis(AGENT_CONFIG.vector_db_storage_check_interval);
        let max_storage_size = AGENT_CONFIG.vector_db_max_storage_size;
        let retention_days = AGENT_CONFIG.vector_db_data_retention_days;

        info!("Storage monitor started: max_size={}GB, check_interval={}ms, retention_days={}",
              max_storage_size / (1024 * 1024 * 1024),
              AGENT_CONFIG.vector_db_storage_check_interval,
              retention_days);

        let mut interval = tokio::time::interval(check_interval);

        loop {
            interval.tick().await;

            match Self::check_and_cleanup_storage(&table, &db_path, max_storage_size, retention_days).await {
                Ok(cleaned) => {
                    if cleaned {
                        info!("Storage cleanup completed successfully");
                    }
                }
                Err(e) => {
                    error!("Storage monitoring error: {}", e);
                }
            }
        }
    }

    /// 检查存储大小并执行清理
    async fn check_and_cleanup_storage(table: &Table, db_path: &PathBuf, max_size: u64, retention_days: u32) -> Result<bool> {
        // 计算数据库目录大小
        let current_size = Self::calculate_directory_size(db_path)?;

        info!("Current database size: {}MB / {}MB",
              current_size / (1024 * 1024),
              max_size / (1024 * 1024));

        // 如果超过80%的限制，开始清理
        let cleanup_threshold = (max_size as f64 * 0.8) as u64;

        if current_size > cleanup_threshold {
            warn!("Database size ({} MB) exceeds 80% of limit ({} MB), starting cleanup",
                  current_size / (1024 * 1024),
                  cleanup_threshold / (1024 * 1024));

            // 执行激进的压缩优化，合并小fragments
            info!("Starting aggressive table compaction...");
            let compact_result = table.optimize(OptimizeAction::Compact {
                options: CompactionOptions {
                    target_rows_per_fragment: 10_000, // 每个fragment目标10,000行，减少fragment数量
                    defer_index_remap: false, // 立即重新映射索引
                    ..Default::default()
                },
                remap_options: None,
            }).await;
            if let Err(e) = compact_result {
                error!("Failed to compact table: {}", e);
                return Ok(false);
            }
            info!("Aggressive table compaction completed");

            // 清理旧版本，释放磁盘空间
            info!("Starting old versions cleanup (prune)...");
            let old_version_result = table.optimize(OptimizeAction::Prune {
                older_than: Some(ChronoDuration::seconds(0)), // 删除所有旧版本
                delete_unverified: Some(true), // 删除未验证的文件
                error_if_tagged_old_versions: Some(false), // 不因标记版本而报错
            }).await;
            if let Err(e) = old_version_result {
                error!("Failed to prune old versions: {}", e);
                return Ok(false);
            }
            info!("Old versions cleanup completed");


            // 执行数据清理
            let result = Self::cleanup_old_data(table, retention_days).await;
            if let Err(e) = result {
                error!("Failed to cleanup old data: {}", e);
                return Ok(false);
            }


            // 重新计算大小
            let new_size = Self::calculate_directory_size(db_path)?;
            info!("Cleanup completed. New size: {}MB", new_size / (1024 * 1024));

            return Ok(true);
        }

        Ok(false)
    }

    /// 计算目录大小
    fn calculate_directory_size(dir_path: &PathBuf) -> Result<u64> {
        let mut total_size = 0u64;

        if dir_path.exists() && dir_path.is_dir() {
            for entry in fs::read_dir(dir_path)? {
                let entry = entry?;
                let path = entry.path();

                if path.is_file() {
                    total_size += entry.metadata()?.len();
                } else if path.is_dir() {
                    total_size += Self::calculate_directory_size(&path)?;
                }
            }
        }

        Ok(total_size)
    }

    /// 清理旧数据 - 每次删除最早的500条数据
    async fn cleanup_old_data(table: &Table, _retention_days: u32) -> Result<()> {
        info!("Starting cleanup of oldest {} records",AGENT_CONFIG.vector_del_count);

        let total_count = table.count_rows(None).await?;
        info!("Total records before cleanup: {}", total_count);

        if total_count <= AGENT_CONFIG.vector_del_count {
            info!("Total records ({}) is less than or equal to {}, no cleanup needed", total_count,AGENT_CONFIG.vector_del_count);
            return Ok(());
        }

        // 查询最早的500条记录的ID
        let query_result = table
            .query()
            .select(lancedb::query::Select::Columns(vec!["id".to_string(), "created_at".to_string()]))
            .limit(AGENT_CONFIG.vector_del_count)
            .execute()
            .await?;

        let mut oldest_ids = Vec::new();
        let mut stream = query_result;

        while let Some(batch) = stream.try_next().await? {
            // 获取 id 列
            let id_column = batch.column_by_name("id")
                .ok_or_else(|| anyhow!("Missing 'id' column in query results"))?;
            let id_array = id_column.as_any().downcast_ref::<StringArray>()
                .ok_or_else(|| anyhow!("Failed to cast 'id' column to StringArray"))?;

            // 获取 created_at 列
            let created_at_column = batch.column_by_name("created_at")
                .ok_or_else(|| anyhow!("Missing 'created_at' column in query results"))?;
            let created_at_array = created_at_column.as_any().downcast_ref::<UInt64Array>()
                .ok_or_else(|| anyhow!("Failed to cast 'created_at' column to UInt64Array"))?;

            // 收集ID和时间戳
            for i in 0..batch.num_rows() {
                let id = id_array.value(i);
                let created_at = created_at_array.value(i);
                oldest_ids.push((id.to_string(), created_at));
            }
        }

        // 按创建时间排序，取最早的500条
        oldest_ids.sort_by_key(|(_, created_at)| *created_at);
        oldest_ids.truncate(AGENT_CONFIG.vector_del_count);

        if oldest_ids.is_empty() {
            warn!("No records found to delete");
            return Ok(());
        }

        info!("Found {} oldest records to delete", oldest_ids.len());

        // 构建删除条件 - 使用ID列表
        let id_list: Vec<String> = oldest_ids.iter().map(|(id, _)| format!("'{}'", id)).collect();
        let delete_filter = format!("id IN ({})", id_list.join(", "));

        // 执行删除操作
        let delete_result = table.delete(&delete_filter).await?;

        info!(
            "Successfully deleted {} oldest records, new table version: {}",
            oldest_ids.len(), delete_result.version
        );

        let new_total_count = table.count_rows(None).await?;
        info!("Total records after cleanup: {}", new_total_count);

        Ok(())
    }

    /// 简单插入数据，不检查ID唯一性
    pub async fn insert(&self, item: VectorItem) -> Result<()> {
        // 检查是否需要进行向量操作：只有当 AGENT_CONFIG.index_extra_type 包含 CHUNK_VECTOR 时才进行向量相关操作
        if !self.is_vector_avaliable().await{
            info!("跳过向量插入操作(insert),磁盘稳定性校验为false");
            return Ok(());
        }


        // 创建Arrow数组
        let id_array = StringArray::from(vec![item.id.clone()]);
        let project_url_array = StringArray::from(vec![item.project_url]);
        let created_at_array = UInt64Array::from(vec![item.created_at]);

        // 创建向量数组 - 将Vec<f32>转换为Arrow的FixedSizeList数组
        let vector_len = item.vector.len() as i32;
        let vector_values = arrow_array::Float32Array::from(item.vector);
        let vector_field = Arc::new(Field::new("item", DataType::Float32, true));
        let vector_array = arrow_array::FixedSizeListArray::new(
            vector_field,
            vector_len,
            Arc::new(vector_values),
            None,
        );

        // 创建schema
        let schema = Arc::new(Schema::new(vec![
            Field::new("id", DataType::Utf8, false),
            Field::new("vector", DataType::FixedSizeList(Arc::new(Field::new("item", DataType::Float32, true)), vector_len), true),
            Field::new("project_url", DataType::Utf8, true),
            Field::new("created_at", DataType::UInt64, false),
        ]));

        // 创建RecordBatch
        let batch = RecordBatch::try_new(
            schema.clone(),
            vec![
                Arc::new(id_array),
                Arc::new(vector_array),
                Arc::new(project_url_array),
                Arc::new(created_at_array),
            ],
        )?;

        // 创建RecordBatchIterator
        let batches = vec![batch];
        let batch_reader = RecordBatchIterator::new(batches.into_iter().map(Ok), schema);

        // 插入数据
        self.table.add(Box::new(batch_reader)).execute().await?;

        info!("Successfully inserted vector item with id: {}", item.id);
        Ok(())
    }

    /// Upsert操作：如果ID存在则更新，如果ID不存在则新增
    pub async fn upsert(&self, item: VectorItem) -> Result<()> {
        // 检查是否需要进行向量操作：只有当 AGENT_CONFIG.index_extra_type 包含 CHUNK_VECTOR 时才进行向量相关操作
        if !self.is_vector_avaliable().await{
            info!("跳过向量插入操作(upsert),磁盘稳定性校验为false");
            return Ok(());
        }
        // 创建Arrow数组
        let id_array = StringArray::from(vec![item.id.clone()]);
        let project_url_array = StringArray::from(vec![item.project_url]);
        let created_at_array = UInt64Array::from(vec![item.created_at]);

        // 创建向量数组 - 将Vec<f32>转换为Arrow的FixedSizeList数组
        let vector_len = item.vector.len() as i32;
        let vector_values = arrow_array::Float32Array::from(item.vector);
        let vector_field = Arc::new(Field::new("item", DataType::Float32, true));
        let vector_array = arrow_array::FixedSizeListArray::new(
            vector_field,
            vector_len,
            Arc::new(vector_values),
            None,
        );

        // 创建schema
        let schema = Arc::new(Schema::new(vec![
            Field::new("id", DataType::Utf8, false),
            Field::new("vector", DataType::FixedSizeList(Arc::new(Field::new("item", DataType::Float32, true)), vector_len), true),
            Field::new("project_url", DataType::Utf8, true),
            Field::new("created_at", DataType::UInt64, false),
        ]));

        // 创建RecordBatch
        let batch = RecordBatch::try_new(
            schema.clone(),
            vec![
                Arc::new(id_array),
                Arc::new(vector_array),
                Arc::new(project_url_array),
                Arc::new(created_at_array),
            ],
        )?;

        // 创建RecordBatchIterator
        let batches = vec![batch];
        let batch_reader = RecordBatchIterator::new(batches.into_iter().map(Ok), schema);

        // 使用merge_insert进行upsert操作
        let mut merge_builder = self.table.merge_insert(&["id"]);
        merge_builder
            .when_matched_update_all(None)
            .when_not_matched_insert_all();
        let merge_result = merge_builder.execute(Box::new(batch_reader)).await?;

        info!(
            "Successfully upserted vector item with id: {}, inserted: {}, updated: {}",
            item.id, merge_result.num_inserted_rows, merge_result.num_updated_rows
        );
        Ok(())
    }

    /// 批量Upsert操作：批量处理多个VectorItem，如果ID存在则更新，如果ID不存在则新增
    /// 使用写入队列确保单线程写入
    pub async fn batch_upsert(&self, item_vec: Vec<VectorItem>) -> Result<()> {
        // 检查是否需要进行向量操作：只有当 AGENT_CONFIG.index_extra_type 包含 CHUNK_VECTOR 时才进行向量相关操作
        if !self.is_vector_avaliable().await{
            info!("跳过向量插入操作(batch_upsert),磁盘稳定性校验为false");
            return Ok(());
        }
        if item_vec.is_empty() {
            warn!("batch_upsert called with empty items vector");
            return Ok(());
        }

        let batch_size = item_vec.len();
        info!("Queuing batch upsert operation with {} items", batch_size);

        // 创建响应通道
        let (response_sender, response_receiver) = tokio::sync::oneshot::channel();

        // 发送到写入队列
        if let Err(_) = self.write_sender.send(WriteCommand::BatchUpsert(item_vec, response_sender)) {
            return Err(anyhow!("Failed to send batch upsert command to write queue"));
        }

        // 等待写入完成
        match response_receiver.await {
            Ok(result) => result,
            Err(_) => Err(anyhow!("Failed to receive response from write queue")),
        }
    }

    /// 内部批量Upsert实现，直接操作数据库
    async fn batch_upsert_internal(table: &Table, item_vec: Vec<VectorItem>) -> Result<()> {
        // 检查是否需要进行向量操作：只有当 AGENT_CONFIG.index_extra_type 包含 CHUNK_VECTOR 时才进行向量相关操作
        // 注意：这里需要通过VECTOR_CLIENT来访问is_vector_avaliable方法
        if !VECTOR_CLIENT.is_vector_avaliable().await{
            info!("跳过向量插入操作(batch_upsert_internal),磁盘稳定性校验为false");
            return Ok(());
        }

        if item_vec.is_empty() {
            warn!("batch_upsert_internal called with empty items vector");
            return Ok(());
        }

        let batch_size = item_vec.len();
        info!("Starting internal batch upsert operation with {} items", batch_size);

        // 提取所有字段的数据
        let mut ids = Vec::with_capacity(batch_size);
        let mut project_urls = Vec::with_capacity(batch_size);
        let mut created_ats = Vec::with_capacity(batch_size);
        let mut all_vectors = Vec::new();
        let mut vector_lengths = Vec::with_capacity(batch_size);

        for item in item_vec {
            ids.push(item.id);
            project_urls.push(item.project_url);
            created_ats.push(item.created_at);
            vector_lengths.push(item.vector.len());
            all_vectors.extend(item.vector);
        }

        // 创建Arrow数组
        let id_array = StringArray::from(ids);
        let project_url_array = StringArray::from(project_urls);
        let created_at_array = UInt64Array::from(created_ats);

        // 创建向量数组 - 将所有向量数据合并为一个大的Float32Array，然后创建FixedSizeListArray
        // 假设所有向量都有相同的长度（这是FixedSizeList的要求）
        let vector_len = if vector_lengths.is_empty() { 0 } else { vector_lengths[0] as i32 };
        let vector_values = arrow_array::Float32Array::from(all_vectors);
        let vector_field = Arc::new(Field::new("item", DataType::Float32, true));
        let vector_array = arrow_array::FixedSizeListArray::new(
            vector_field,
            vector_len,
            Arc::new(vector_values),
            None,
        );

        // 创建schema
        let schema = Arc::new(Schema::new(vec![
            Field::new("id", DataType::Utf8, false),
            Field::new("vector", DataType::FixedSizeList(Arc::new(Field::new("item", DataType::Float32, true)), vector_len), true),
            Field::new("project_url", DataType::Utf8, true),
            Field::new("created_at", DataType::UInt64, false),
        ]));

        // 创建RecordBatch
        let batch = RecordBatch::try_new(
            schema.clone(),
            vec![
                Arc::new(id_array),
                Arc::new(vector_array),
                Arc::new(project_url_array),
                Arc::new(created_at_array),
            ],
        )?;

        // 创建RecordBatchIterator
        let batches = vec![batch];
        let batch_reader = RecordBatchIterator::new(batches.into_iter().map(Ok), schema);

        // 使用merge_insert进行批量upsert操作
        let mut merge_builder = table.merge_insert(&["id"]);
        merge_builder
            .when_matched_update_all(None)
            .when_not_matched_insert_all();
        let merge_result = merge_builder.execute(Box::new(batch_reader)).await?;

        info!(
            "Successfully completed internal batch upsert operation: {} items processed, {} inserted, {} updated",
            batch_size, merge_result.num_inserted_rows, merge_result.num_updated_rows
        );
        Ok(())
    }
    
    
    
    
    ///向量化代码片段，返回向量化结果
    pub async fn embedding_chunk_vec(
        &self,
        chunks_vec: &Vec<CodefuseChunk>,
        user_token: &String,
        product_type: &String,
    ) -> Result<Vec<VectorItem>> {
        // 检查是否需要进行向量操作：只有当 AGENT_CONFIG.index_extra_type 包含 CHUNK_VECTOR 时才进行向量相关操作
        if !self.is_vector_avaliable().await{
            info!("跳过向量插入操作(embedding_chunk_vec),磁盘稳定性校验为false");
            return Ok(vec![]);
        }
        let start_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        let code_set = chunks_vec.iter().map(|item| item.content.clone()).collect();
        let req = CgeEmbeddingRequestBean {
            codeList: code_set,
            r#type: EMBEDDING_BUILD.to_string(),
        };

        let http_result_opt = cge_embedding(&req,AGENT_CONFIG.embedding_build_timeout).await.data;
        let end_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        info!("embedding consume time: {}ms", end_time - start_time);

        if http_result_opt.is_none() {
            error!("code_embedding http result is none");
            return Err(anyhow!("code_embedding error"));
        }
        let vector_result = http_result_opt.unwrap().result;

        //如果http返回的向量结果长度和请求的code_list长度不一致，则返回错误
        if vector_result.len() != chunks_vec.len() {
            error!(
                "code_embedding result len not equal code_list len,req={:?},result len={}",
                req, vector_result.len()
            );
            return Err(anyhow!("batch result len not equal batch len"));
        }

        let mut vector_item_vec = Vec::new();
        for (index, item) in chunks_vec.iter().enumerate() {
            let value_f32 = vector_result[index].clone();
            let created_at = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64;

            let vector_item = VectorItem{
                vector: value_f32,
                id: item.id.clone(),
                project_url: item.project_url.clone(),
                created_at,
            };
            vector_item_vec.push(vector_item);
        }

        Ok(vector_item_vec)
    }


    pub async fn query_top_n(&self, query: &QueryTopNVectorRequestBean) -> Result<Vec<(VectorItem,f64)>> {
        info!("Starting query_top_n with project_url: {}, top_n: {}", query.project_url, query.top_n);
        // 重新打开表以确保获取最新数据
        // let table = self.conn.open_table(VECTOR_TABL_NAME).execute().await?;
        // 构建过滤条件，只查询匹配的 project_url 的数据
        let filter = format!("project_url = '{}'", query.project_url);

        // 执行向量相似度搜索
        let search_result = self.table
            .query()
            .nearest_to(query.query.clone())?
            .column("vector")  // 指定向量列名称
            .only_if(filter)
            .limit(query.top_n as usize)
            .execute()
            .await?;

        // 收集结果
        let mut results = Vec::new();
        let mut stream = search_result;

        while let Some(batch) = stream.try_next().await? {
            // 获取 id 列
            let id_column = batch.column_by_name("id")
                .ok_or_else(|| anyhow!("Missing 'id' column in search results"))?;
            let id_array = id_column.as_any().downcast_ref::<StringArray>()
                .ok_or_else(|| anyhow!("Failed to cast 'id' column to StringArray"))?;

            // 获取 project_url 列
            let project_url_column = batch.column_by_name("project_url")
                .ok_or_else(|| anyhow!("Missing 'project_url' column in search results"))?;
            let project_url_array = project_url_column.as_any().downcast_ref::<StringArray>()
                .ok_or_else(|| anyhow!("Failed to cast 'project_url' column to StringArray"))?;

            // 获取 vector 列
            let vector_column = batch.column_by_name("vector")
                .ok_or_else(|| anyhow!("Missing 'vector' column in search results"))?;
            let vector_array = vector_column.as_any().downcast_ref::<arrow_array::FixedSizeListArray>()
                .ok_or_else(|| anyhow!("Failed to cast 'vector' column to FixedSizeListArray"))?;

            // 获取距离列（LanceDB 自动添加的 _distance 列）
            let distance_column = batch.column_by_name("_distance")
                .ok_or_else(|| anyhow!("Missing '_distance' column in search results"))?;

            // 尝试不同的数据类型转换
            let distances: Vec<f64> = if let Some(float32_array) = distance_column.as_any().downcast_ref::<Float32Array>() {
                // 如果是 Float32Array，转换为 f64
                (0..float32_array.len()).map(|i| float32_array.value(i) as f64).collect()
            } else if let Some(float64_array) = distance_column.as_any().downcast_ref::<Float64Array>() {
                // 如果是 Float64Array，直接使用
                (0..float64_array.len()).map(|i| float64_array.value(i)).collect()
            } else {
                return Err(anyhow!("Failed to cast '_distance' column to Float32Array or Float64Array, actual type: {:?}", distance_column.data_type()));
            };

            // 将结果添加到向量中
            for i in 0..batch.num_rows() {
                let id = id_array.value(i);
                let project_url = project_url_array.value(i);
                let distance = distances[i];
                // 提取向量数据
                // let vector_list = vector_array.value(i);
                // let vector_values = vector_list.as_any().downcast_ref::<arrow_array::Float32Array>()
                //     .ok_or_else(|| anyhow!("Failed to cast vector values to Float32Array"))?;
                // let vector: Vec<f32> = (0..vector_values.len()).map(|j| vector_values.value(j)).collect();

                // 构建 VectorItem,查询时候就暂时不用转换搜索结果的向量了
                let item = VectorItem {
                    vector:vec![],
                    id: id.to_string(),
                    project_url: project_url.to_string(),
                    created_at: 0, // 查询时不需要实际的创建时间
                };

                // 将距离转换为相似度分数 (距离越小，相似度越高)
                // 这里使用一个简单的转换公式：similarity = 1.0 / (1.0 + distance)
                let similarity_score = 1.0 / (1.0 + distance);

                results.push((item, similarity_score));
            }
        }
        info!("Query completed, found {} results", results.len());
        Ok(results)
    }

    /// 删除指定project_url的所有数据
    pub async fn delete_with_key_and_value(&self, key: &str,value:&str) -> Result<()> {
        if !self.is_vector_avaliable().await{
            info!("跳过向量插入操作(delete_with_key_and_value),磁盘稳定性校验为false");
            return Ok(());
        }
        info!("Starting delete vector operation for key: {} , value: {}", key,value);

        // 构建过滤条件，删除匹配的 project_url 的所有数据
        let filter = format!("{} = '{}'", key,value);

        // 执行删除操作
        let delete_result = self.table.delete(&filter).await?;

        info!(
            "Successfully completed delete_project operation for key: {}, value : {} , version: {}",
            key,value, delete_result.version
        );

        Ok(())
    }

    /// 清空LanceDB中的所有数据
    pub async fn clean(&self) -> Result<()> {
        if !self.is_vector_avaliable().await{
            info!("跳过向量插入操作(clean),磁盘稳定性校验为false");
            return Ok(());
        }
        info!("Starting clean operation to delete all data from LanceDB table");

        // 执行删除操作，使用一个匹配所有记录的条件
        // 由于id字段不允许为空，我们可以使用 "id IS NOT NULL" 来匹配所有记录
        let delete_result = self.table.delete("id IS NOT NULL").await?;

        info!(
            "Successfully completed clean operation, all data deleted from LanceDB table, version: {}",
            delete_result.version
        );

        Ok(())
    }

    /// 查询指定project_url的所有数据id集合
    /// 使用分页查询确保能获取所有符合条件的数据，不受数量限制
    pub async fn query_all_ids_by_project(&self, project_url: &str) -> Result<Vec<String>> {
        info!("Starting query_all_ids_by_project for project_url: {}", project_url);

        if project_url.is_empty() {
            return Ok(Vec::new());
        }

        // 首先获取该项目的总记录数
        let total_count = self.count_all(Some(project_url)).await?;
        info!("Total records for project_url '{}': {}", project_url, total_count);

        if total_count == 0 {
            return Ok(Vec::new());
        }

        // 设置分页参数
        let page_size = 1000; // 每页1000条记录，平衡内存使用和查询效率
        let total_pages = (total_count + page_size - 1) / page_size; // 向上取整

        let mut all_ids = Vec::with_capacity(total_count);

        // 分页查询所有数据
        for page in 0..total_pages {
            info!("Querying page {} of {} for project_url: {}", page + 1, total_pages, project_url);

            // 构建过滤条件，只查询匹配的 project_url 的数据
            let filter = format!("project_url = '{}'", project_url);
            let offset = page * page_size;

            // 执行查询，只选择id列
            let query_result = self.table
                .query()
                .select(lancedb::query::Select::Columns(vec!["id".to_string()]))
                .only_if(filter)
                .limit(page_size)
                .offset(offset)
                .execute()
                .await?;

            let mut stream = query_result;
            let mut page_ids = Vec::new();

            while let Some(batch) = stream.try_next().await? {
                // 获取 id 列
                let id_column = batch.column_by_name("id")
                    .ok_or_else(|| anyhow!("Missing 'id' column in query results"))?;
                let id_array = id_column.as_any().downcast_ref::<StringArray>()
                    .ok_or_else(|| anyhow!("Failed to cast 'id' column to StringArray"))?;

                // 收集当前批次的所有id
                for i in 0..id_array.len() {
                    page_ids.push(id_array.value(i).to_string());
                }
            }

            info!("Page {} completed, found {} ids", page + 1, page_ids.len());
            all_ids.extend(page_ids);

            // 如果当前页的数据少于page_size，说明已经是最后一页
            if all_ids.len() >= total_count {
                break;
            }
        }

        info!("Query completed, found {} ids for project_url: {} (expected: {})",
              all_ids.len(), project_url, total_count);
        Ok(all_ids)
    }

    /// 强制刷新写入缓冲区，确保所有缓冲的数据都被写入数据库
    pub async fn force_flush(&self) -> Result<()> {
        info!("Force flushing vector client write buffer");

        // 创建响应通道
        let (response_sender, response_receiver) = tokio::sync::oneshot::channel();

        // 发送强制刷新命令
        if let Err(_) = self.write_sender.send(WriteCommand::ForceFlush(response_sender)) {
            return Err(anyhow!("Failed to send force flush command to write queue"));
        }

        // 等待刷新完成
        match response_receiver.await {
            Ok(result) => result,
            Err(_) => Err(anyhow!("Failed to receive response from write queue")),
        }
    }

    /// 优雅关闭写入队列，确保所有待写入数据都被处理
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down vector client write queue");
        if let Err(_) = self.write_sender.send(WriteCommand::Shutdown) {
            warn!("Failed to send shutdown command to write queue");
        }
        Ok(())
    }

    /// 获取存储统计信息
    pub async fn get_storage_stats(&self) -> Result<StorageStats> {
        let current_size = Self::calculate_directory_size(&self.db_path)?;
        let max_size = AGENT_CONFIG.vector_db_max_storage_size;
        let record_count = self.table.count_rows(None).await?;

        Ok(StorageStats {
            current_size_bytes: current_size,
            max_size_bytes: max_size,
            usage_percentage: (current_size as f64 / max_size as f64 * 100.0) as u32,
            record_count,
        })
    }

    //判断是否可以继续用向量
    pub async fn is_vector_avaliable(&self) -> bool{
        // 检查是否需要进行向量操作：只有当 AGENT_CONFIG.index_extra_type 包含 CHUNK_VECTOR 时才进行向量相关操作
        if !AGENT_CONFIG.index_extra_type.contains(&IndexTypeEnum::CHUNK_VECTOR) {
            info!("跳过向量操作，因为 AGENT_CONFIG.index_extra_type 不包含 CHUNK_VECTOR");
            return false;
        };
        //检查磁盘空间，无论任何操作，只要磁盘占用过大，都做对应的处理,同时返回false
        //1:如果超过AGENT_CONFIG.vector_db_max_storage_size，那么直接压缩删除数据。
        let data_cache_url = expand_user_home(&AGENT_CONFIG.base_data_url)
            .unwrap()
            .join("vectory");
        if !data_cache_url.exists() {
            warn!("目录不存在，先创建目录");
            let _ = fs::create_dir_all(&data_cache_url);
        }
        let current_size_result = Self::calculate_directory_size(&data_cache_url);
        if let Err(e) = current_size_result{
            error!("计算目录大小失败: {:?}", e);
            return false;
        }
        let max_storage_size = AGENT_CONFIG.vector_db_max_storage_size;
        let current_size = current_size_result.unwrap_or(0);
        // 如果超过80%的限制，开始清理
        let cleanup_threshold = (max_storage_size as f64 * 0.8) as u64;
        // 如果已经超过最大值一倍了，那么直接删除文件夹
        if current_size > max_storage_size*2 {
            info!("跳过向量操作，磁盘空间占用过大，{:?} , 超过了两倍阈值。 {:?}  , 直接删除向量文件夹",current_size, max_storage_size*2);
            let del_result = fs::remove_dir_all(data_cache_url.clone());
            Self::commit_error(current_size,max_storage_size).await;
            if let Err(e) = del_result{
                error!("del vectory data error {:?}",e)
            }
            return false;
        }
        if current_size > cleanup_threshold {
            info!("跳过向量操作，磁盘空间超过80%，需要进行清理");
            Self::commit_error(current_size,max_storage_size).await;
            self.manual_cleanup();
            return false;
        }
        true
    }

    pub async fn commit_error(current_size:u64,max_size:u64){
        let current_size_mb = current_size / (1024 * 1024);
        let max_size_mb = max_size / (1024 * 1024);
        let data = format!("用户目录: {} , 当前大小: {}MB , 最大限制: {}MB",&AGENT_CONFIG.base_data_url,current_size_mb,max_size_mb);

        let mut param:HashMap<&str,String> = HashMap::new();
        param.insert("type","ERROR_MSG".to_string());
        param.insert("data",data);
        update_event_track(param).await;
    }



    /// 手动触发存储清理
    pub async fn manual_cleanup(&self) -> Result<()> {
        info!("Manual storage cleanup triggered");
        let max_storage_size = AGENT_CONFIG.vector_db_max_storage_size;
        let retention_days = AGENT_CONFIG.vector_db_data_retention_days;
        let data_cache_url = expand_user_home(&AGENT_CONFIG.base_data_url)
            .unwrap()
            .join("vectory");
        if !data_cache_url.exists() {
            let _ = fs::create_dir_all(&data_cache_url);
        }
        match Self::check_and_cleanup_storage(&self.table, &data_cache_url, max_storage_size, retention_days).await {
            Ok(cleaned) => {
                if cleaned {
                    info!("Storage cleanup completed successfully");
                }
            }
            Err(e) => {
                error!("Storage monitoring error: {}", e);
            }
        }
        info!("Manual storage cleanup completed");
        Ok(())
    }
    /// 分页查询所有VectorItem数据
    /// page: 页码，从0开始
    /// page_size: 每页大小
    /// project_url_filter: 可选的项目URL过滤条件
    pub async fn query_all_paginated(&self, page: usize, page_size: usize, project_url_filter: Option<&str>) -> Result<Vec<VectorItem>> {
        info!("Starting paginated query: page={}, page_size={}, filter={:?}", page, page_size, project_url_filter);

        let mut query_builder = self.table.query();

        // 如果有项目URL过滤条件，添加过滤
        if let Some(project_url) = project_url_filter {
            let filter = format!("project_url = '{}'", project_url);
            query_builder = query_builder.only_if(filter);
        }

        // 设置分页参数
        let offset = page * page_size;
        let query_result = query_builder
            .limit(page_size)
            .offset(offset)
            .execute()
            .await?;

        // 收集结果
        let mut results = Vec::new();
        let mut stream = query_result;

        while let Some(batch) = stream.try_next().await? {
            let num_rows = batch.num_rows();
            if num_rows == 0 {
                continue;
            }

            // 解析每一行数据
            for row_idx in 0..num_rows {
                // 获取各个字段的数据
                let id_array = batch.column_by_name("id")
                    .ok_or_else(|| anyhow!("Missing 'id' column"))?
                    .as_any()
                    .downcast_ref::<StringArray>()
                    .ok_or_else(|| anyhow!("Failed to cast 'id' column to StringArray"))?;

                let project_url_array = batch.column_by_name("project_url")
                    .ok_or_else(|| anyhow!("Missing 'project_url' column"))?
                    .as_any()
                    .downcast_ref::<StringArray>()
                    .ok_or_else(|| anyhow!("Failed to cast 'project_url' column to StringArray"))?;





                // 获取向量数据（可选，因为查询时可能不需要向量）
                let vector = if let Some(vector_column) = batch.column_by_name("vector") {
                    if let Some(vector_array) = vector_column.as_any().downcast_ref::<FixedSizeListArray>() {
                        if let Some(values) = vector_array.values().as_any().downcast_ref::<Float32Array>() {
                            let start_idx = row_idx * vector_array.value_length() as usize;
                            let end_idx = start_idx + vector_array.value_length() as usize;
                            values.values()[start_idx..end_idx].to_vec()
                        } else {
                            vec![]
                        }
                    } else {
                        vec![]
                    }
                } else {
                    vec![]
                };

                // 提取字段值
                let id = id_array.value(row_idx).to_string();
                let project_url = project_url_array.value(row_idx).to_string();


                // 构建 VectorItem
                let item = VectorItem {
                    vector,
                    id,
                    project_url,
                    created_at: 0,
                };

                results.push(item);
            }
        }

        info!("Paginated query completed, found {} results on page {}", results.len(), page);
        Ok(results)
    }

    /// 获取总记录数
    pub async fn count_all(&self, project_url_filter: Option<&str>) -> Result<usize> {
        let count = if let Some(project_url) = project_url_filter {
            let filter = format!("project_url = '{}'", project_url);
            self.table.count_rows(Some(filter)).await?
        } else {
            self.table.count_rows(None).await?
        };
        Ok(count)
    }

}


#[derive(Debug, Clone)]
pub struct QueryTopNVectorRequestBean {
    pub query: Vec<f32>,
    pub top_n: usize,
    pub project_url: String,
}

/// 存储统计信息
#[derive(Debug, Clone)]
pub struct StorageStats {
    pub current_size_bytes: u64,
    pub max_size_bytes: u64,
    pub usage_percentage: u32,
    pub record_count: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VectorItem {
    pub vector: Vec<f32>,
    pub id: String,
    pub project_url: String,
    pub created_at: u64, // 记录创建时间的毫秒数
}

impl VectorItem {
    /// 创建新的VectorItem实例
    pub fn new(id: String, vector: Vec<f32>, project_url: String) -> Self {
        let created_at = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        VectorItem {
            id,
            vector,
            project_url,
            created_at,
        }
    }
}




// 辅助函数：执行分页查询测试（纯读取操作）
pub async fn test_paginated_query(client: &VectorClient) -> Result<()> {
    info!("=== 开始LanceDB分页查询测试 ===");

    // 1. 获取总记录数
    let total_count = client.count_all(None).await?;
    info!("LanceDB中总记录数: {}", total_count);

    if total_count == 0 {
        info!("LanceDB中没有数据，测试结束");
        return Ok(());
    }

    // 2. 设置分页参数
    let page_size = 100; // 每页100条记录，提高查询效率
    let total_pages = (total_count + page_size - 1) / page_size; // 向上取整

    info!("每页大小: {}, 总页数: {}", page_size, total_pages);
    info!("");

    // 3. 收集所有文件URL及其对应的记录数
    let mut file_url_counts = std::collections::HashMap::new();
    let mut project_urls = std::collections::HashSet::new();

    info!("正在收集所有文件URL信息...");
    for page in 0..total_pages {
        let items = client.query_all_paginated(page, page_size, None).await?;

        if items.is_empty() {
            continue;
        }

        // 收集文件URL统计信息
        for item in items.iter() {
            let file_url = process_paths(item.id.clone(), item.project_url.clone());
            if file_url == "/src/FilterPanel/mockData.ts"{
                println!("关注最大chunk数： {}",item.project_url)
            }
            // 统计每个文件URL的记录数
            *file_url_counts.entry(file_url).or_insert(0) += 1;
            // 收集项目URL
            project_urls.insert(item.project_url.clone());
            // if item.file_url == "/home/<USER>/fxq01073839/business_release/giftprod/app/biz/giftcash/src/main/java/com/alipay/giftprod/cash/crowd/share/card/generator/common/CommonCashCardGenerator.java"{
            //     println!("=== 文件明细 ===");
            //     println!("文件ID: {}", item.id);
            //     println!("文件起始行: {} , {}", item.start_no,item.end_no);
            //     println!("文件ID: {}", item.code);
            // }



        }
    }

    // 4. 打印文件URL统计结果
    info!("=== 文件URL统计结果 ===");
    info!("文件URL总数: {}", file_url_counts.len());
    info!("");

    // 按记录数降序排列文件URL
    let mut sorted_file_urls: Vec<_> = file_url_counts.iter().collect();
    sorted_file_urls.sort_by(|a, b| b.1.cmp(a.1));

    info!("每个文件URL及其对应的数据记录数:");
    for (file_url, count) in sorted_file_urls {
        info!("  文件: {}", file_url);
        info!("  记录数: {}", count);
        info!("");
    }

    // 5. 显示项目统计信息
    info!("=== 项目统计信息 ===");
    info!("项目数量: {}", project_urls.len());
    info!("项目列表:");
    for project_url in &project_urls {
        info!("  - {}", project_url);
    }
    info!("");

    // 6. 测试带项目URL过滤的分页查询
    info!("\n=== 测试项目URL过滤查询 ===");

    // 先获取第一页数据，找到一个项目URL进行过滤测试
    let first_page_items = client.query_all_paginated(0, 1, None).await?;
    if let Some(first_item) = first_page_items.first() {
        let test_project_url = &first_item.project_url;
        info!("测试项目URL过滤: {}", test_project_url);

        let filtered_count = client.count_all(Some(test_project_url)).await?;
        info!("该项目的总记录数: {}", filtered_count);

        if filtered_count > 0 {
            let filtered_items = client.query_all_paginated(0, 3, Some(test_project_url)).await?;
            info!("该项目的前3条记录:");
            for (idx, item) in filtered_items.iter().enumerate() {
                let file_url = process_paths(item.id.clone(), item.project_url.clone());
                let file_name = std::path::Path::new(&file_url)
                    .file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("unknown");
                info!("  {}. ID: {}", idx + 1, item.id);
                info!("     文件: {}", file_name);
                info!("     仓库: {}", item.project_url);
            }
        }
    }

    info!("\n=== LanceDB分页查询测试完成 ===");
    info!("测试总结:");
    info!("- 成功连接到LanceDB数据库");
    info!("- 成功执行分页查询");
    info!("- 成功执行过滤查询");
    info!("- 所有操作均为只读操作，未修改数据库");

    Ok(())
}
fn process_paths(a: String, b: String) -> String {
    // 1. 去掉 A 中 # 后面的内容
    let a = a.split('#').next().unwrap_or(a.as_str());

    // 2. 给 B 添加 "VC_" 前缀
    let b_with_prefix = format!("VC_{}", b);

    // 3. 找到共同前缀的长度
    let common_prefix_len = a.chars()
        .zip(b_with_prefix.chars())
        .take_while(|(a_char, b_char)| a_char == b_char)
        .count();

    // 4. 从 A 中截取共同前缀后的部分
    let result = &a[common_prefix_len..];

    result.to_string()
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use crate::config::runtime_config::AGENT_CONFIG_ENV_KEY;

    #[tokio::test]
    async fn test_batch_upsert() -> Result<()> {
        // 创建临时目录用于测试
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test_batch_upsert");

        // 创建向量客户端
        let client = VectorClient::new(&db_path,3).await?;

        // 创建测试数据
        let test_items = vec![
            VectorItem::new(
                "test_id_1".to_string(),
                vec![0.1, 0.2, 0.3, 0.4, 0.5],
                "https://github.com/test/repo1".to_string(),
            ),
            VectorItem::new(
                "test_id_2".to_string(),
                vec![0.6, 0.7, 0.8, 0.9, 1.0],
                "https://github.com/test/repo1".to_string(),
            ),
            VectorItem::new(
                "test_id_3".to_string(),
                vec![0.2, 0.4, 0.6, 0.8, 1.0],
                "https://github.com/test/repo3".to_string(),
            ),
        ];

        // 执行批量upsert
        client.batch_upsert(test_items).await?;

        // 创建更新数据（包含相同ID和新ID）
        let update_items = vec![
            VectorItem::new(
                "test_id_1".to_string(), // 更新现有记录
                vec![1.1, 1.2, 1.3, 1.4, 1.5],
                "https://github.com/test/repo1".to_string(),
            ),
            VectorItem::new(
                "test_id_4".to_string(), // 新记录
                vec![0.5, 0.5, 0.5, 0.5, 0.5],
                "https://github.com/test/repo4".to_string(),
            ),
        ];

        // 执行第二次批量upsert
        client.batch_upsert(update_items).await?;

        println!("Batch upsert test completed successfully!");
        Ok(())
    }




    #[tokio::test]
    async fn test_query_top_n() {
        // 创建临时目录用于测试
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test_db");

        // 创建 VectorClient 实例
        let client = VectorClient::new(db_path.to_str().unwrap(),3).await.unwrap();

        // 创建测试数据
        let test_items = vec![
            VectorItem::new(
                "test1".to_string(),
                vec![1.0, 0.0, 0.0],
                "https://github.com/test/repo1".to_string(),
            ),
            VectorItem::new(
                "test2".to_string(),
                vec![0.0, 1.0, 0.0],
                "https://github.com/test/repo1".to_string(),
            ),
            VectorItem::new(
                "test3".to_string(),
                vec![8.0, 0.0, 1.0],
                "https://github.com/test/repo1".to_string(),
            ),
            VectorItem::new(
                "test4".to_string(),
                vec![8.0, 0.0, 1.0],
                "https://github.com/test/repo2".to_string(),
            ),
        ];

        // 插入测试数据
        client.batch_upsert(test_items).await.unwrap();

        // 等待一下确保数据已经被写入
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        // // 先测试简单的查询，不使用向量搜索
        // // 只是验证表已经创建并且数据已经插入
        // let table = client.conn.open_table(VECTOR_TABL_NAME).execute().await.unwrap();
        // let scan_result = table.query().execute().await.unwrap();
        // let mut count = 0;
        // let mut stream = scan_result;
        // while let Some(_batch) = stream.try_next().await.unwrap() {
        //     count += 1;
        // }
        // 
        // // 验证数据已经插入
        // assert!(count > 0, "Should have inserted some data");
        // println!("Successfully inserted and retrieved {} batches of data", count);
        // 
        let q = QueryTopNVectorRequestBean{
            query: vec![8.0, 0.0, 1.1],
            top_n: 3,
            project_url: "https://github.com/test/repo1".to_string(),
        };

        let x = client.query_top_n(&q).await;
        match x {
            Ok(results) => {
                println!("查询结果:");
                for (i, (vector_item, similarity_score)) in results.iter().enumerate() {
                    println!("  {}. ID: {}, 相似度分数: {:.4}", i + 1, vector_item.id, similarity_score);
                    println!("      项目URL: {}", vector_item.project_url);
                    println!("      向量: {:?}", vector_item.vector);
                    println!();
                }
            }
            Err(err) => {
                println!("查询错误: {:?}",err)
            }
        }
        println!("{:?}", "123123");

    }

    #[tokio::test]
    async fn test_delete_project() -> Result<()> {
        // 创建临时目录用于测试
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test_delete_project");

        // 创建向量客户端
        let client = VectorClient::new(&db_path, 3).await?;

        // 创建测试数据，包含不同的project_url
        let test_items = vec![
            VectorItem::new(
                "test_id_1".to_string(),
                vec![0.1, 0.2, 0.3],
                "https://github.com/test/repo1".to_string(),
            ),
            VectorItem::new(
                "test_id_2".to_string(),
                vec![0.4, 0.5, 0.6],
                "https://github.com/test/repo1".to_string(),
            ),
            VectorItem::new(
                "test_id_3".to_string(),
                vec![0.7, 0.8, 0.9],
                "https://github.com/test/repo2".to_string(),
            ),
            VectorItem::new(
                "test_id_4".to_string(),
                vec![1.0, 1.1, 1.2],
                "https://github.com/test/repo2".to_string(),
            ),
        ];

        // 插入测试数据
        client.batch_upsert(test_items).await?;

        // 等待一下确保数据已经被写入
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        // 验证数据已经插入 - 应该有4条记录
        let total_count = client.table.count_rows(None).await?;
        assert_eq!(total_count, 4, "Should have 4 records initially");

        // 删除 project_url 为 "https://github.com/test/repo1" 的数据
        client.delete_with_key_and_value("project_url","https://github.com/test/repo1").await?;

        // 等待一下确保删除操作完成
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        // 验证删除操作 - 应该只剩下2条记录（repo2的数据）
        let remaining_count = client.table.count_rows(None).await?;
        assert_eq!(remaining_count, 2, "Should have 2 records after deleting repo1 data");


        client.delete_with_key_and_value("id","test_id_4").await?;
        // 验证删除操作 - 应该只剩下1条记录（repo2的数据）
        let remaining_count = client.table.count_rows(None).await?;
        assert_eq!(remaining_count, 1, "Should have 1 records after deleting repo1 data");


        // 验证剩余的数据都是 repo2 的
        let repo2_filter = "project_url = 'https://github.com/test/repo2'".to_string();
        let repo2_count = client.table.count_rows(Some(repo2_filter)).await?;
        assert_eq!(repo2_count, 1, "Should have 1 records for repo2");

        // 验证 repo1 的数据已经被完全删除
        let repo1_filter = "project_url = 'https://github.com/test/repo1'".to_string();
        let repo1_count = client.table.count_rows(Some(repo1_filter)).await?;
        assert_eq!(repo1_count, 0, "Should have 0 records for repo1 after deletion");

        // 删除不存在的 project_url，应该不会出错
        client.delete_with_key_and_value("project_url","https://github.com/test/nonexistent").await?;

        // 验证数据没有变化
        let final_count = client.table.count_rows(None).await?;
        assert_eq!(final_count, 1, "Should still have 1 record after deleting non-existent project");

        println!("Delete project test completed successfully!");
        Ok(())
    }

    #[tokio::test]
    async fn test_clean() -> Result<()> {
        // 创建临时目录用于测试
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test_clean");

        // 创建向量客户端
        let client = VectorClient::new(&db_path, 3).await?;

        // 创建测试数据
        let test_items = vec![
            VectorItem::new(
                "test_id_1".to_string(),
                vec![0.1, 0.2, 0.3],
                "https://github.com/test/repo1".to_string(),
            ),
            VectorItem::new(
                "test_id_2".to_string(),
                vec![0.4, 0.5, 0.6],
                "https://github.com/test/repo2".to_string(),
            ),
            VectorItem::new(
                "test_id_3".to_string(),
                vec![0.7, 0.8, 0.9],
                "https://github.com/test/repo3".to_string(),
            ),
        ];

        // 插入测试数据
        client.batch_upsert(test_items).await?;

        // 等待一下确保数据已经被写入
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        // 验证数据已经插入 - 应该有3条记录
        let initial_count = client.table.count_rows(None).await?;
        assert_eq!(initial_count, 3, "Should have 3 records initially");

        // 执行清空操作
        client.clean().await?;

        // 等待一下确保删除操作完成
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        // 验证所有数据都被删除 - 应该有0条记录
        let final_count = client.table.count_rows(None).await?;
        assert_eq!(final_count, 0, "Should have 0 records after clean operation");

        println!("Clean test completed successfully!");
        Ok(())
    }

    #[tokio::test]
    async fn test_concurrent_batch_upsert() -> Result<()> {
        // 创建临时目录用于测试
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test_concurrent_batch_upsert");

        // 创建向量客户端
        let client = Arc::new(VectorClient::new(&db_path, 3).await?);

        // 创建多个并发任务
        let mut handles = vec![];

        for task_id in 0..10 {
            let client_clone = client.clone();
            let handle = tokio::spawn(async move {
                let test_items = vec![
                    VectorItem::new(
                        format!("task_{}_id_1", task_id),
                        vec![0.1 * task_id as f32, 0.2 * task_id as f32, 0.3 * task_id as f32],
                        format!("https://github.com/test/repo{}", task_id),
                    ),
                    VectorItem::new(
                        format!("task_{}_id_2", task_id),
                        vec![0.4 * task_id as f32, 0.5 * task_id as f32, 0.6 * task_id as f32],
                        format!("https://github.com/test/repo{}", task_id),
                    ),
                ];

                // 执行批量upsert
                client_clone.batch_upsert(test_items).await
            });
            handles.push(handle);
        }

        // 等待所有任务完成
        for handle in handles {
            handle.await.unwrap()?;
        }

        // 等待一下确保所有数据都被写入
        tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;

        // 验证数据已经插入 - 应该有20条记录（10个任务 * 2条记录）
        let total_count = client.table.count_rows(None).await?;
        assert_eq!(total_count, 20, "Should have 20 records after concurrent batch upsert");

        println!("Concurrent batch upsert test completed successfully!");
        Ok(())
    }

    #[tokio::test]
    async fn test_force_flush() -> Result<()> {
        // 创建临时目录用于测试
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test_force_flush");

        // 创建向量客户端
        let client = VectorClient::new(&db_path, 3).await?;

        // 创建少量测试数据（不足批量大小）
        let test_items = vec![
            VectorItem::new(
                "small_batch_1".to_string(),
                vec![0.1, 0.2, 0.3],
                "https://github.com/test/small_repo".to_string(),
            ),
        ];

        // 执行批量upsert（数据会被缓冲）
        client.batch_upsert(test_items).await?;

        // 立即检查数据库，应该还没有数据（因为还在缓冲区中）
        let count_before_flush = client.table.count_rows(None).await?;

        // 强制刷新缓冲区
        client.force_flush().await?;

        // 现在检查数据库，应该有数据了
        let count_after_flush = client.table.count_rows(None).await?;
        assert_eq!(count_after_flush, 1, "Should have 1 record after force flush");

        println!("Force flush test completed successfully!");
        Ok(())
    }

    #[tokio::test]
    async fn test_duplicate_id_upsert() -> Result<()> {
        let args = format!(
            "--base-data-url=~/.tmp/codefuse_dev"
        );
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);
        // 创建临时目录用于测试
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test_duplicate_id");

        // 创建向量客户端
        let client = VectorClient::new(&db_path, 3).await?;

        println!("=== 测试重复ID的upsert行为 ===");

        // 第一次插入数据
        let first_batch = vec![
            VectorItem::new(
                "duplicate_id_1".to_string(),
                vec![1.0, 2.0, 3.0],
                "https://github.com/test/repo1".to_string(),
            ),
            VectorItem::new(
                "duplicate_id_2".to_string(),
                vec![4.0, 5.0, 6.0],
                "https://github.com/test/repo1".to_string(),
            ),
            VectorItem::new(
                "unique_id_1".to_string(),
                vec![7.0, 8.0, 9.0],
                "https://github.com/test/repo1".to_string(),
            ),
        ];

        println!("第一次插入3条记录...");
        client.batch_upsert(first_batch).await?;
        client.force_flush().await?; // 确保数据写入

        // 检查第一次插入后的记录数
        let count_after_first = client.table.count_rows(None).await?;
        println!("第一次插入后记录数: {}", count_after_first);

        // 第二次插入数据，包含重复ID和新ID
        let second_batch = vec![
            VectorItem::new(
                "duplicate_id_1".to_string(), // 重复ID，应该更新
                vec![10.0, 20.0, 30.0], // 不同的向量值
                "https://github.com/test/repo2".to_string(), // 不同的project_url
            ),
            VectorItem::new(
                "duplicate_id_2".to_string(), // 重复ID，应该更新
                vec![40.0, 50.0, 60.0], // 不同的向量值
                "https://github.com/test/repo2".to_string(), // 不同的project_url
            ),
            VectorItem::new(
                "unique_id_2".to_string(), // 新ID，应该插入
                vec![70.0, 80.0, 90.0],
                "https://github.com/test/repo2".to_string(),
            ),
        ];

        println!("第二次插入3条记录（其中2条是重复ID）...");
        client.batch_upsert(second_batch).await?;
        client.force_flush().await?; // 确保数据写入

        // 检查第二次插入后的记录数
        let count_after_second = client.table.count_rows(None).await?;
        println!("第二次插入后记录数: {}", count_after_second);

        // 如果upsert正常工作，应该只有4条记录（3+1，因为2条是更新）
        assert_eq!(count_after_second, 4, "应该只有4条记录，因为有2条是更新操作");

        println!("\n=== 查询所有记录并验证内容 ===");

        // 查询所有记录
        let scan_result = client.table.query().execute().await?;
        let mut stream = scan_result;
        let mut all_records = Vec::new();

        while let Some(batch) = stream.try_next().await? {
            // 获取各列数据
            let id_column = batch.column_by_name("id")
                .ok_or_else(|| anyhow!("Missing 'id' column"))?;
            let id_array = id_column.as_any().downcast_ref::<StringArray>()
                .ok_or_else(|| anyhow!("Failed to cast 'id' column to StringArray"))?;

            let project_url_column = batch.column_by_name("project_url")
                .ok_or_else(|| anyhow!("Missing 'project_url' column"))?;
            let project_url_array = project_url_column.as_any().downcast_ref::<StringArray>()
                .ok_or_else(|| anyhow!("Failed to cast 'project_url' column to StringArray"))?;

            let vector_column = batch.column_by_name("vector")
                .ok_or_else(|| anyhow!("Missing 'vector' column"))?;
            let vector_array = vector_column.as_any().downcast_ref::<arrow_array::FixedSizeListArray>()
                .ok_or_else(|| anyhow!("Failed to cast 'vector' column to FixedSizeListArray"))?;

            // 提取每条记录
            for i in 0..batch.num_rows() {
                let id = id_array.value(i);
                let project_url = project_url_array.value(i);

                // 提取向量数据
                let vector_list = vector_array.value(i);
                let vector_values = vector_list.as_any().downcast_ref::<arrow_array::Float32Array>()
                    .ok_or_else(|| anyhow!("Failed to cast vector values to Float32Array"))?;
                let vector: Vec<f32> = (0..vector_values.len()).map(|j| vector_values.value(j)).collect();

                all_records.push((id.to_string(), project_url.to_string(), vector));
            }
        }

        // 打印所有记录
        println!("总记录数: {}", all_records.len());
        for (i, (id, project_url, vector)) in all_records.iter().enumerate() {
            println!("记录 {}: ID={}, project_url={}, vector={:?}", i + 1, id, project_url, vector);
        }

        // 验证特定ID的记录
        println!("\n=== 验证重复ID的更新情况 ===");

        // 检查duplicate_id_1的记录
        let duplicate_1_records: Vec<_> = all_records.iter()
            .filter(|(id, _, _)| id == "duplicate_id_1")
            .collect();
        println!("ID 'duplicate_id_1' 的记录数: {}", duplicate_1_records.len());
        assert_eq!(duplicate_1_records.len(), 1, "duplicate_id_1 应该只有1条记录");

        if let Some((_, project_url, vector)) = duplicate_1_records.first() {
            println!("duplicate_id_1 的内容: project_url={}, vector={:?}", project_url, vector);
            // 验证是否是更新后的值
            assert_eq!(project_url, "https://github.com/test/repo2", "project_url应该是更新后的值");
            assert_eq!(vector, &vec![10.0, 20.0, 30.0], "vector应该是更新后的值");
        }

        // 检查duplicate_id_2的记录
        let duplicate_2_records: Vec<_> = all_records.iter()
            .filter(|(id, _, _)| id == "duplicate_id_2")
            .collect();
        println!("ID 'duplicate_id_2' 的记录数: {}", duplicate_2_records.len());
        assert_eq!(duplicate_2_records.len(), 1, "duplicate_id_2 应该只有1条记录");

        if let Some((_, project_url, vector)) = duplicate_2_records.first() {
            println!("duplicate_id_2 的内容: project_url={}, vector={:?}", project_url, vector);
            // 验证是否是更新后的值
            assert_eq!(project_url, "https://github.com/test/repo2", "project_url应该是更新后的值");
            assert_eq!(vector, &vec![40.0, 50.0, 60.0], "vector应该是更新后的值");
        }

        // 验证所有ID都是唯一的
        let mut unique_ids = std::collections::HashSet::new();
        for (id, _, _) in &all_records {
            assert!(unique_ids.insert(id.clone()), "发现重复ID: {}", id);
        }

        println!("\n✅ 测试通过！upsert功能正常工作，重复ID被正确更新而不是插入新记录");
        Ok(())
    }

    #[tokio::test]
    async fn test_storage_stats() -> Result<()> {
        // 创建临时目录用于测试
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test_storage_stats");

        // 创建向量客户端
        let client = VectorClient::new(&db_path, 3).await?;

        // 创建测试数据
        let test_items = vec![
            VectorItem::new(
                "storage_test_1".to_string(),
                vec![0.1, 0.2, 0.3],
                "https://github.com/test/storage_repo".to_string(),
            ),
            VectorItem::new(
                "storage_test_2".to_string(),
                vec![0.4, 0.5, 0.6],
                "https://github.com/test/storage_repo".to_string(),
            ),
        ];

        // 插入测试数据
        client.batch_upsert(test_items).await?;
        client.force_flush().await?;

        // 获取存储统计信息
        let stats = client.get_storage_stats().await?;

        println!("=== 存储统计信息 ===");
        println!("当前大小: {} bytes ({} MB)", stats.current_size_bytes, stats.current_size_bytes / (1024 * 1024));
        println!("最大大小: {} bytes ({} GB)", stats.max_size_bytes, stats.max_size_bytes / (1024 * 1024 * 1024));
        println!("使用率: {}%", stats.usage_percentage);
        println!("记录数: {}", stats.record_count);

        // 验证统计信息
        assert!(stats.current_size_bytes > 0, "当前大小应该大于0");
        assert_eq!(stats.record_count, 2, "应该有2条记录");
        assert!(stats.usage_percentage < 100, "使用率应该小于100%");

        println!("\n✅ 存储统计测试通过！");
        Ok(())
    }

    #[tokio::test]
    async fn test_directory_size_calculation() -> Result<()> {
        // 创建临时目录用于测试
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test_size_calc");

        // 创建向量客户端并插入一些数据
        let client = VectorClient::new(&db_path, 3).await?;

        let test_items = vec![
            VectorItem::new(
                "size_test_1".to_string(),
                vec![1.0, 2.0, 3.0],
                "https://github.com/test/size_repo".to_string(),
            ),
        ];

        client.batch_upsert(test_items).await?;
        client.force_flush().await?;

        // 计算目录大小
        let size = VectorClient::calculate_directory_size(&db_path)?;

        println!("数据库目录大小: {} bytes", size);
        assert!(size > 0, "数据库目录大小应该大于0");

        println!("✅ 目录大小计算测试通过！");
        Ok(())
    }

    #[tokio::test]
    async fn test_storage()->Result<()>{
        let args = format!(
            "--base-data-url=~/.tmp/codefuse_dev"
        );
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);
        println!("=== LanceDB 存储管理示例 ===\n");

        // 创建向量客户端
        let db_path = "/tmp/lancedb_storage_demo";
        let client = VectorClient::new(db_path, 128).await?;

        println!("1. 创建测试数据...");

        // 创建一些测试向量数据
        let mut test_items = Vec::new();
        for i in 0..1000 {
            let vector: Vec<f32> = (0..128).map(|j| (i * j) as f32 * 0.001).collect();
            let item = VectorItem::new(
                format!("test_item_{}", i),
                vector,
                format!("https://github.com/test/repo_{}", i % 10),
            );
            test_items.push(item);
        }

        // 批量插入数据
        client.batch_upsert(test_items).await?;
        client.force_flush().await?;

        println!("2. 获取存储统计信息...");

        // 获取存储统计
        let stats = client.get_storage_stats().await?;
        println!("   当前大小: {:.2} MB", stats.current_size_bytes as f64 / (1024.0 * 1024.0));
        println!("   最大限制: {:.2} GB", stats.max_size_bytes as f64 / (1024.0 * 1024.0 * 1024.0));
        println!("   使用率: {}%", stats.usage_percentage);
        println!("   记录数: {}", stats.record_count);

        println!("\n3. 演示查询功能...");

        // 执行向量相似度查询
        let query_vector: Vec<f32> = (0..128).map(|i| i as f32 * 0.001).collect();
        let query = crate::dal::vector_client::QueryTopNVectorRequestBean {
            query: query_vector,
            top_n: 5,
            project_url: "https://github.com/test/repo_1".to_string(),
        };

        let results = client.query_top_n(&query).await?;
        println!("   找到 {} 个相似结果", results.len());

        for (i, (item, score)) in results.iter().enumerate() {
            println!("   {}. ID: {}, 相似度: {:.4}", i + 1, item.id, score);
        }

        println!("\n4. 演示手动清理功能...");

        // 手动触发存储清理
        client.manual_cleanup().await?;

        // 再次获取统计信息
        let stats_after = client.get_storage_stats().await?;
        println!("   清理后大小: {:.2} MB", stats_after.current_size_bytes as f64 / (1024.0 * 1024.0));
        println!("   清理后记录数: {}", stats_after.record_count);

        println!("\n=== 存储管理配置说明 ===");
        println!("可以通过以下命令行参数配置存储管理：");
        println!("  --vector-db-max-storage-size: 最大存储大小（字节），默认3GB");
        println!("  --vector-db-storage-check-interval: 存储检查间隔（毫秒），默认5分钟");
        println!("  --vector-db-data-retention-days: 数据保留天数，默认30天");
        println!("  --vector-db-enable-compression: 启用压缩优化，默认启用");

        println!("\n=== 压缩和优化特性 ===");
        println!("1. LanceDB 使用 Lance 格式，内置列式存储和压缩");
        println!("2. 自动存储监控，超过80%限制时触发清理");
        println!("3. 可配置的数据保留策略");
        println!("4. 实时存储统计信息");

        // 优雅关闭
        client.shutdown().await?;

        println!("\n✅ 示例完成！");
        Ok(())
    }

    #[tokio::test]
    async fn test_query_vectory_directory_paginated() -> Result<()> {
        let scan_max_file_size = 10000000;
        let args = format!(
            "--base-data-url=/tmp/.codefuse --scan-skip-file-size={}",
            scan_max_file_size
        );
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // 指定要查询的LanceDB向量数据库目录
        let vectory_path = "/Users/<USER>/.codefuse/vectory";

        // 检查目录是否存在
        if !std::path::Path::new(vectory_path).exists() {
            println!("Error: LanceDB directory {} does not exist!", vectory_path);
            println!("Please ensure the LanceDB data directory exists before running this test.");
            return Err(anyhow!("LanceDB directory not found: {}", vectory_path));
        }

        println!("=== 连接到现有的LanceDB数据库 ===");
        println!("数据库路径: {}", vectory_path);

        // 连接到现有的LanceDB数据库（只读模式）
        // 注意：这里的向量维度参数不会影响读取，因为我们只是连接到现有数据库
        let client = VectorClient::new(vectory_path, 2048).await?;

        println!("成功连接到LanceDB数据库");

        // 执行分页查询测试（纯读取操作）
        test_paginated_query(&client).await?;

        Ok(())
    }





}



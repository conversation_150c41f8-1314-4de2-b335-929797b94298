use std::collections::HashMap;
use std::fs;
use std::path::Path;

use anyhow::Result;
use log::{error, info};
use once_cell::sync::Lazy;
use rocksdb::{DBWithThreadMode, IteratorMode, Options, ReadOptions, SingleThreaded, WriteBatch, WriteOptions, DB};

use crate::config::runtime_config::AGENT_CONFIG;
use crate::tools::common_tools::expand_user_home;

const DB_WAL_SIZE: u64 = 1 << 29;

pub static KV_CLIENT: Lazy<KvClient> = Lazy::new(|| {
    // let data_cache_url = expand_user_home(&AGENT_CONFIG.base_data_url).unwrap().join("agent_data");
    let data_cache_url = expand_user_home(&AGENT_CONFIG.base_data_url).unwrap().join("temp");
    if !data_cache_url.exists() {
        let _ = fs::create_dir_all(&data_cache_url);
    }
    let client_result = KvClient::new(data_cache_url);
    if client_result.is_err() {
        error!("init kv client failed");
    };
    //如果索引库创建异常，那么直接抛出，让进程启动失败
    client_result.unwrap()
});


pub struct KvClient {
    database: DBWithThreadMode<SingleThreaded>,
}

impl KvClient {
    pub fn new<P: AsRef<Path> + Clone>(path: P) -> Result<KvClient> {
        let mut opts = Options::default();
        opts.create_if_missing(true);
        opts.set_max_total_wal_size(DB_WAL_SIZE);
        let db_rst = DB::open(&opts, path.clone());
        match db_rst {
            Ok(db) => {
                Ok(KvClient {
                    database: db,
                })
            }
            Err(err) => {
                let error_string = err.to_string();
                if error_string.contains("IO error: No such file or directory") {
                    error!("error occur when opening db: {}", err);
                    info!("clear_directory and try to reopen the db");
                    if !path.as_ref().exists() {
                        let _ = fs::create_dir_all(&path.as_ref());
                    }
                    //清空当tmp目录
                    let _ = fs::remove_dir_all(&path);
                    //recover_data();   //todo
                    //清空当chat目录
                    let _ = fs::remove_dir_all(&path);
                    let db = DB::open(&opts, path).unwrap();
                    Ok(KvClient {
                        database: db,
                    })
                }else {
                    panic!("{}", err);
                }
            }
        }
    }

    ///插入k-v数据
    pub fn insert(&self, key: &String, value: &String) -> Result<()> {
        let result = self.database.put(key, value);
        match result {
            Ok(_) => Ok(()),
            Err(e) => {
                error!("insert kv failed, err:{}", e);
                Err(anyhow::anyhow!("insert kv failed"))
            }
        }
    }

    pub fn get(&self, key: &String) -> Result<Option<String>> {
        let result = self.database.get(key);
        match result {
            Ok(value_opt) => {
                match value_opt {
                    Some(value) => Ok(Some(String::from_utf8(value).unwrap())),
                    None => Ok(None),
                }
            }
            Err(e) => {
                error!("get key failed, key: {} ,err:{}",key, e);
                Err(anyhow::anyhow!("get key failed"))
            }
        }
    }

    pub fn get_from_prefix(&self, prefix_key: &String) -> Result<Option<HashMap<String, String>>> {
        // 使用前缀搜索 "abc"
        let mut read_opts = ReadOptions::default();
        // 设置迭代器的前缀模式
        read_opts.set_prefix_same_as_start(true);
        let iter = self.database.iterator_opt(
            IteratorMode::From(prefix_key.as_bytes(), rocksdb::Direction::Forward),
            read_opts,
        );
        let mut result: HashMap<String, String> = HashMap::new();
        for item in iter {
            match item {
                Ok((key, value)) => {
                    if key.starts_with(prefix_key.as_bytes()) {
                        let key = std::str::from_utf8(&key).unwrap();
                        let value = std::str::from_utf8(&value).unwrap();
                        result.insert(key.to_string(),value.to_string());
                    } else {
                        // 如果遇到不以指定前缀开头的键，就停止遍历
                        break;
                    }
                }
                Err(e) => {
                    error!("get_from_prefix kv failed, err:{}", e);
                    return Err(anyhow::anyhow!("get_from_prefix kv failed"));
                }
            }
        }
        Ok(Some(result))
    }

    pub fn delete(&self, key: &String) -> Result<()> {
        let result = self.database.delete(key);
        match result {
            Ok(_) => Ok(()),
            Err(e) => {
                error!("delete kv failed, err:{}", e);
                Err(anyhow::anyhow!("delete kv failed"))
            }
        }
    }

    pub fn delete_from_prefix(&self, prefix_key: &String) -> Result<()> {
        let mut batch = WriteBatch::default();
        let mut read_opts = ReadOptions::default();
        read_opts.set_prefix_same_as_start(true);

        // 创建一个迭代器，从指定的前缀开始
        let iter = self.database.iterator_opt(
            IteratorMode::From(prefix_key.as_bytes(), rocksdb::Direction::Forward),
            read_opts,
        );

        // 遍历所有以指定前缀开头的键，并将它们添加到删除批处理中
        for item in iter {
            match item {
                Ok((key, _)) => {
                    if key.starts_with(prefix_key.as_bytes()) {
                        batch.delete(&key);
                    } else {
                        // 如果遇到不以指定前缀开头的键，就停止遍历
                        break;
                    }
                }
                Err(e) => {
                    error!("delete kv failed, err:{}", e);
                    return Err(anyhow::anyhow!("delete kv failed"));
                }
            }
        }

        // 创建写入选项
        let mut write_opts = WriteOptions::default();
        write_opts.set_sync(true);  // 确保写入立即同步到磁盘
        // 执行批处理
        self.database.write_opt(batch, &write_opts);
        Ok(())
    }

    pub fn clean(&self) -> Result<()> {
        // 开始一个写入批处理
        let mut batch = WriteBatch::default();
        // 遍历数据库并删除每一个键值对
        let mut iter = self.database.raw_iterator();
        iter.seek_to_first();
        while iter.valid() {
            batch.delete(iter.key().unwrap());
            iter.next();
        }
        // 提交批处理
        self.database.write(batch).unwrap();
        info!("Database cleared via batch delete.");
        Ok(())
    }
}
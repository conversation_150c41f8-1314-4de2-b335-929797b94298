use crate::config::runtime_config::AGENT_CONFIG;
use crate::domain::ap_data::AtomicSearchTypeEnum::WEB_FETCH;
use crate::domain::ap_data::{
    DeepsearchRelateCheckItem, DeepsearchRelateCheckResItem, GenerateNewQueryRequestBean,
    QeurySplitRequestBean, QueryResultFilterRequestBean, QueryWikiRequestBean, WikiTypeEnum,
};
use crate::domain::code_chat_domain::{ChatRelatedCodeModel, ATOMIC_WEB_FETCH};
use crate::remote::http_client::{
    get_request_with_header, post_request, post_request_sync, post_request_with_header,
    DEFAULT_TIMEOUT_MILLIS,
};
use crate::remote::rpc_model::{build_success_response, BaseResponse};
use clap::ValueEnum;
use log::{debug, error, info};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::{HashMap, HashSet};
use std::str::FromStr;
use std::time::Instant;
use tokio::task;

const CGE_EMBEDDING_URL: &str = "https://caselike.alipay.com/v1/localCore/cgeEmbedding";
const CHAT_QUERY_CHANGE_URL: &str = "https://caselike.alipay.com/v1/localRepo/queryChange";
const M_QUERY_FILTER_URL: &str = "https://caselike.alipay.com/v1/localCore/mqueryFilter";
//query重写（augment模式用）
const CHAT_QUERY_REWRITE_URL: &str = "https://caselike.alipay.com/v1/localCore/queryRewrite";
const M_QUERY_RERANK_URL: &str = "https://caselike.alipay.com/v1/localCore/mqueryRerank";
const CHAT_REQUEST_SUMMARY_URL: &str = "https://caselike.alipay.com/v1/localRepo/codeSummary";
const CHAT_RERANK_URL: &str = "https://caselike.alipay.com/v1/localRepo/rerank";
const CHAT_DOC_SEARCH_URL: &str = "https://caselike.alipay.com/v1/function/docSearch";

//上传panic信息
pub const SUBMIT_EVENT_TRACK: &str = "https://caselike.alipay.com/v1/common/submitReportEventTrack";
//请求要扩展的import信息
pub const REQUEST_EXT_IMPORT_INFO: &str =
    "https://caselike.alipay.com/v1/function/composerRagSearch";
//deepsearch的问题拆分
pub const DEEPSEARCH_QUERY_SPLIT: &str = "https://caselike.alipay.com/v1/localCore/splitQuestion";
//deepsearch的过滤
pub const DEEPSEARCH_RESULT_FILTER: &str =
    "https://caselike.alipay.com/v1/localCore/subqueryFilterWoWiki";
// 是否新的一轮检索
pub const DEEPSEARCH_NEW_QUERY: &str = "https://caselike.alipay.com/v1/localCore/generateNewQuery";
//wiki请求地址, 暂时用pre进行调试
pub const QUERY_WIKI_CONTENT: &str = "https://cfsearch.antgroup-inc.cn/openapi/wiki/search";
//web fetch请求地址
const WEB_FETCH_URL: &str = "https://caselike.alipay.com/v1/codebase/search/web/fetch";
//查询仓库最新commitId的地址
const LATEST_COMMIT_URL: &str = "https://cfsearch.antgroup-inc.cn/openapi/index/latestCommit";

pub const EMBEDDING_QUERY: &str = "BATCH_QUERY";
pub const EMBEDDING_BUILD: &str = "BATCH_EMBEDDING";

///上报埋点
pub async fn update_event_track(param: HashMap<&str, String>) -> BaseResponse<()> {
    let start_time = Instant::now();
    let base_response = post_request::<ResponseResult, HashMap<&str, String>>(
        SUBMIT_EVENT_TRACK,
        &param,
        1000,
        "chat_query_rewrite failed",
    )
    .await;
    if let Some(response) = base_response {
        info!(
            "update_event_track http time: {} ms",
            start_time.elapsed().as_millis()
        );
        BaseResponse::default()
    } else {
        error!(
            "chat_query_rewrite http time: {} ms,param: {:?}",
            start_time.elapsed().as_millis(),
            param
        );
        BaseResponse::default()
    }
}

///query重写
pub async fn chat_query_rewrite(
    chat_request_bean: &QueryChangeRequestBean,
) -> BaseResponse<QueryChangeDetail> {
    let start_time = Instant::now();
    let mut param = HashMap::new();
    param.insert("model", "QWEN25_CODE_7B_INSTRUCT_REPO_QA".to_string());
    param.insert("query", chat_request_bean.question.clone());
    let base_response = post_request::<QueryChangeResponse, HashMap<&str, String>>(
        CHAT_QUERY_REWRITE_URL, // 确认是否应为不同的URL
        &param,
        AGENT_CONFIG.embedding_query_timeout,
        "chat_query_rewrite failed",
    )
    .await;
    if let Some(response) = base_response {
        info!(
            "chat_query_rewrite http time: {} ms",
            start_time.elapsed().as_millis()
        );
        build_success_response(response.data.unwrap().result)
    } else {
        error!(
            "chat_query_rewrite http time: {} ms,param: {:?}",
            start_time.elapsed().as_millis(),
            chat_request_bean
        );
        BaseResponse::default()
    }
}

///mquery过滤(Augment模式用),模型默认DeepSeek-V3
pub async fn mquery_filter(
    query: String,
    search_result: Vec<ChatRelatedCodeModel>,
) -> BaseResponse<Vec<ChatRelatedCodeModel>> {
    let start_time = Instant::now();
    let mquery_request = MQueryFilterRequestBean {
        model: "DeepSeek-V3".to_string(),
        query: query.clone(),
        codeChunkList: search_result,
    };
    let base_response = post_request::<Vec<ChatRelatedCodeModel>, MQueryFilterRequestBean>(
        M_QUERY_FILTER_URL, // 确认是否应为不同的URL
        &mquery_request,
        DEFAULT_TIMEOUT_MILLIS,
        "mquery_filter failed",
    )
    .await;
    if let Some(response) = base_response {
        info!(
            "mquery_filter http time: {} ms",
            start_time.elapsed().as_millis()
        );
        build_success_response(response.data.unwrap())
    } else {
        error!(
            "mquery_filter http time: {} ms,param: {:?}",
            start_time.elapsed().as_millis(),
            mquery_request
        );
        BaseResponse::default()
    }
}

///mquery的rerank
pub async fn mquery_rerank(
    query: String,
    search_result: Vec<ChatRelatedCodeModel>,
) -> BaseResponse<Vec<ChatRelatedCodeModel>> {
    let start_time = Instant::now();
    let mquery_request = MQueryRerankRequestBean {
        model: "DeepSeek-V3".to_string(),
        query,
        top: 200,
        codeChunkList: search_result,
    };
    let base_response = post_request::<Vec<ChatRelatedCodeModel>, MQueryRerankRequestBean>(
        M_QUERY_RERANK_URL, // 确认是否应为不同的URL
        &mquery_request,
        DEFAULT_TIMEOUT_MILLIS,
        "mquery_rerank failed",
    )
    .await;
    if let Some(response) = base_response {
        info!(
            "mquery_rerank http time: {} ms",
            start_time.elapsed().as_millis()
        );
        build_success_response(response.data.unwrap())
    } else {
        error!(
            "mquery_rerank http time: {} ms,param: {:?}",
            start_time.elapsed().as_millis(),
            mquery_request
        );
        BaseResponse::default()
    }
}

///query改写
pub async fn chat_query_change(
    chat_request_bean: &QueryChangeRequestBean,
) -> BaseResponse<QueryChangeDetail> {
    let start_time = Instant::now();
    let base_response = post_request::<QueryChangeResponse, QueryChangeRequestBean>(
        CHAT_QUERY_CHANGE_URL, // 确认是否应为不同的URL
        &chat_request_bean,
        DEFAULT_TIMEOUT_MILLIS,
        "chat_query_change failed",
    )
    .await;
    if let Some(response) = base_response {
        info!(
            "chat_query_change http time: {} ms",
            start_time.elapsed().as_millis()
        );
        build_success_response(response.data.unwrap().result)
    } else {
        error!(
            "chat_query_change http time: {} ms,param: {:?}",
            start_time.elapsed().as_millis(),
            chat_request_bean
        );
        BaseResponse::default()
    }
}

/// CGE embedding function based on the curl command
pub async fn cge_embedding(
    cge_embedding_request: &CgeEmbeddingRequestBean,
    time_out: u64,
) -> BaseResponse<CgeEmbeddingResponse> {
    let start_time = Instant::now();

    let base_response = post_request::<CgeEmbeddingResponse, CgeEmbeddingRequestBean>(
        CGE_EMBEDDING_URL,
        cge_embedding_request,
        time_out,
        "cge_embedding failed",
    )
    .await;

    if let Some(response) = base_response {
        info!(
            "cge_embedding http time: {} ms",
            start_time.elapsed().as_millis()
        );
        response
    } else {
        error!(
            "cge_embedding http time: {} ms, code_list length: {}",
            start_time.elapsed().as_millis(),
            cge_embedding_request.codeList.len()
        );
        BaseResponse::default()
    }
}



///deepsearch的问题拆分
pub async fn deepsearch_query_split(
    request_bean: &QeurySplitRequestBean,
) -> BaseResponse<Vec<String>> {
    let start_time = Instant::now();
    let base_response = post_request::<Vec<String>, QeurySplitRequestBean>(
        DEEPSEARCH_QUERY_SPLIT, // serer url
        request_bean,
        DEFAULT_TIMEOUT_MILLIS * 6 * 2,
        "deepsearch_query_split failed",
    )
    .await;
    if let Some(response) = base_response {
        info!(
            "deepsearch_query_split http time: {} ms",
            start_time.elapsed().as_millis()
        );
        response
    } else {
        error!(
            "deepsearch_query_split http time: {} ms,param: {:?}",
            start_time.elapsed().as_millis(),
            request_bean
        );
        BaseResponse::default()
    }
}
///deepsearch过滤,
pub async fn deepsearch_filter_no_wiki(
    filter_request: &QueryResultFilterRequestBean,
) -> BaseResponse<Vec<DeepsearchRelateCheckResItem>> {
    let start_time = Instant::now();
    let base_response =
        post_request::<Vec<DeepsearchRelateCheckResItem>, QueryResultFilterRequestBean>(
            DEEPSEARCH_RESULT_FILTER, // serer url
            filter_request,
            DEFAULT_TIMEOUT_MILLIS * 6 * 2,
            "deepsearch_filter failed",
        )
        .await;
    if let Some(response) = base_response {
        info!(
            "deepsearch_filter http time: {} ms",
            start_time.elapsed().as_millis()
        );
        response
    } else {
        error!(
            "deepsearch_filter http time: {} ms,param: {:?}",
            start_time.elapsed().as_millis(),
            filter_request
        );
        BaseResponse::default()
    }
}

///deepsearch是否新触发新一轮query
pub async fn deepsearch_new_query(
    request_bean: &GenerateNewQueryRequestBean,
) -> BaseResponse<Vec<String>> {
    let start_time = Instant::now();
    let base_response = post_request::<Vec<String>, GenerateNewQueryRequestBean>(
        DEEPSEARCH_NEW_QUERY, // serer url
        request_bean,
        DEFAULT_TIMEOUT_MILLIS * 6 * 2,
        "deepsearch_new_query failed",
    )
    .await;
    if let Some(response) = base_response {
        info!(
            "deepsearch_new_query http time: {} ms",
            start_time.elapsed().as_millis()
        );
        response
    } else {
        error!(
            "deepsearch_new_query http time: {} ms,param: {:?}",
            start_time.elapsed().as_millis(),
            request_bean
        );
        BaseResponse::default()
    }
}
///查询仓库wiki, 后面需要接入caselike
pub async fn query_repo_wiki_entry(
    query_wiki: &QueryWikiRequestBean,
) -> Option<BaseResponse<String>> {
    let mut headers = HashMap::new();
    headers.insert("Content-Type", "application/json; charset=utf-8");
    headers.insert("codefusesearch_token", "b1eHTFGKRgOQmNI9C0tdYQ");
    headers.insert("codefusesearch_user", "test");
    headers.insert("Cookie", "BUSERVICE_SSO_V2=C0143B4798C08B92F40FEE4DB4981E899B115F32892AA0E53E70C6275B8941577DC4F6033004B2D86E58D7C5B5D232BD; spanner=jEWDQseFnTvf8rKDhs69LBcFY9witOEY4EJoL7C0n0A=");

    post_request_with_header(
        QUERY_WIKI_CONTENT,
        &query_wiki,
        headers,
        DEFAULT_TIMEOUT_MILLIS * 2,
        "query_repo_wifi failed",
    )
    .await
}

///查询web fetch接口，获取网页内容
pub async fn query_web_fetch(url: String) -> Option<ChatRelatedCodeModel> {
    let start_time = Instant::now();
    let request_bean = WebFetchRequestBean { url: url.clone() };

    let base_response = post_request::<WebFetchResponseData, WebFetchRequestBean>(
        WEB_FETCH_URL,
        &request_bean,
        DEFAULT_TIMEOUT_MILLIS,
        "query_web_fetch failed",
    )
    .await;

    if let Some(response) = base_response {
        info!(
            "query_web_fetch http time: {} ms",
            start_time.elapsed().as_millis()
        );
        if let Some(data) = response.data {
            let chat_related_code = ChatRelatedCodeModel {
                relativePath: data.url,
                snippet: data.doc,
                startLine: 0,
                endLine: 0,
                source: Some(ATOMIC_WEB_FETCH.to_string()),
                title: Some(data.title),
            };
            Some(chat_related_code)
        } else {
            error!("query_web_fetch response data is None");
            None
        }
    } else {
        error!(
            "query_web_fetch http time: {} ms, url: {}",
            start_time.elapsed().as_millis(),
            url
        );
        None
    }
}

///查询仓库最新commitId
pub async fn query_latest_commit(repo_url: String) -> Option<(String, String, String)> {
    let start_time = Instant::now();

    // 构建请求URL，添加查询参数
    let url_with_params = format!(
        "{}?repoURL={}&taskName=REPO_BUILD_V1",
        LATEST_COMMIT_URL,
        urlencoding::encode(&repo_url)
    );

    // 设置请求头
    let mut headers = HashMap::new();
    headers.insert("Content-Type", "application/json; charset=utf-8");
    headers.insert("codefusesearch_token", "b1eHTFGKRgOQmNI9C0tdYQ");
    headers.insert("codefusesearch_user", "test");

    // 发送GET请求
    let response = get_request_with_header::<LatestCommitResponse>(
        &url_with_params,
        headers,
        2000,
        "query_latest_commit failed",
    )
    .await;

    if let Some(commit_response) = response {
        info!(
            "query_latest_commit http time: {} ms",
            start_time.elapsed().as_millis()
        );

        // 检查返回状态和数据
        if commit_response.errorCode == 0 && commit_response.errorType == "SUCCESS" {
            if let Some(data) = commit_response.data.clone() {
                if data.state == "SUCCESS" {
                    return Some((data.branch, data.lastBuildCommit, data.repoURL));
                }
            }
        }

        error!(
            "query_latest_commit failed: state is not SUCCESS or data is None,reponse: {:?}",
            commit_response
        );
        None
    } else {
        error!(
            "query_latest_commit http time: {} ms, repo_url: {}",
            start_time.elapsed().as_millis(),
            repo_url
        );
        None
    }
}



///问题改写详情
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryChangeDetail {
    //中文关键词集合
    pub keyword_zh: Option<HashSet<String>>,
    //英文关键词集合
    pub keyword_en: Option<HashSet<String>>,
    //英文query
    pub question_en: Option<HashSet<String>>,
    //关键词
    pub keyword_code: Option<HashSet<String>>,
    //中文query
    pub question_zh: Option<HashSet<String>>,
    //原始query
    pub question: Option<HashSet<String>>,
}

///问题改写返回值
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryChangeResponse {
    //改写结果详情
    pub result: QueryChangeDetail,
    //请求id
    pub traceId: String,
}

///query改写请求
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryChangeRequestBean {
    //用户token
    pub userToken: String,
    //产品类型
    pub productType: String,
    //问题
    pub question: String,
}

///web fetch请求
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebFetchRequestBean {
    //要获取的URL
    pub url: String,
}

///web fetch响应数据
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebFetchResponseData {
    //页面标题
    pub title: String,
    //页面URL
    pub url: String,
    //页面内容
    pub doc: String,
    //页面摘要
    pub docAbstract: String,
}

///mquery排序接口
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MQueryRerankRequestBean {
    //模型
    pub model: String,
    //用户问题
    pub query: String,
    //top值
    pub top: usize,
    pub codeChunkList: Vec<ChatRelatedCodeModel>,
}

///MQuery过滤的请求参数
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MQueryFilterRequestBean {
    //用户token
    pub model: String,
    //产品类型
    pub query: String,
    //问题
    pub codeChunkList: Vec<ChatRelatedCodeModel>,
}



#[derive(Serialize, Deserialize, Hash, Eq, PartialEq, Debug, Clone, ValueEnum)]
pub enum IndexTypeEnum {
    //文件内容检索
    FILE_CONTENT,
    //函数内容检索
    METHOD_CONTENT,
    //chunk内容检索
    CHUNK_CONTENT,
    //chunk向量检索
    CHUNK_VECTOR,
    //类注释
    CLASS_ANNOTATION,
    //方法注释
    METHOD_ANNOTATION,
}

impl FromStr for IndexTypeEnum {
    type Err = ();

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_uppercase().as_str() {
            "FILE_CONTENT" => Ok(IndexTypeEnum::FILE_CONTENT),
            "METHOD_CONTENT" => Ok(IndexTypeEnum::METHOD_CONTENT),
            "CHUNK_CONTENT" => Ok(IndexTypeEnum::CHUNK_CONTENT),
            "CHUNK_VECTOR" => Ok(IndexTypeEnum::CHUNK_VECTOR),
            "CLASS_ANNOTATION" => Ok(IndexTypeEnum::CLASS_ANNOTATION),
            "METHOD_ANNOTATION" => Ok(IndexTypeEnum::METHOD_ANNOTATION),
            _ => Err(()),
        }
    }
}

pub fn vec_to_hashset(vec: Vec<String>) -> HashSet<IndexTypeEnum> {
    vec.into_iter().filter_map(|s| s.parse().ok()).collect()
}

///CGE embedding请求
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CgeEmbeddingRequestBean {
    pub codeList: Vec<String>,
    pub r#type: String,
}

///CGE embedding响应
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CgeEmbeddingResponse {
    pub traceId: Option<String>,
    pub result: Vec<Vec<f32>>,
}



///在project里进行查询
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryInProject {
    pub project_url: String,
    pub field_to_search: HashSet<String>,
    pub word_to_search: HashSet<String>,
    pub top_num: usize,     //查询top, 优先级高于分页
    pub page_size: usize,   //每页的数据量
    pub page_number: usize, //第几页
}

impl Default for QueryInProject {
    fn default() -> Self {
        crate::dal::remote_client::QueryInProject {
            project_url: "".to_string(),
            field_to_search: HashSet::new(),
            word_to_search: HashSet::new(),
            top_num: 0,
            page_size: 0,
            page_number: 0,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TraceInfo {
    pub r#type: String,
    pub data: String,
    pub ideVersion: String,
    pub pluginVersion: String,
    pub userToken: String,
    pub deviceInfo: DeviceInfo,
    pub occurTime: String,
}

impl Default for TraceInfo {
    fn default() -> Self {
        TraceInfo {
            r#type: "PANIC_MSG".to_string(),
            data: "".to_string(),
            ideVersion: "".to_string(),
            pluginVersion: "".to_string(),
            userToken: "".to_string(),
            deviceInfo: DeviceInfo::default(),
            occurTime: "".to_string(),
        }
    }
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DeviceInfo {
    //系统类型
    pub os_type: String,
    //系统名称
    pub os_name: String,
    //系统版本
    pub os_version: String,
    //cpu的核数
    pub cpu_num: usize,
    //cpu架构
    pub cpu_arch: String,
    //总的mem
    pub total_mem: String,
    //可用mem
    pub free_mem: String,
}

impl Default for DeviceInfo {
    fn default() -> Self {
        DeviceInfo {
            os_type: "".to_string(),
            os_name: "".to_string(),
            os_version: "".to_string(),
            cpu_num: 0,
            cpu_arch: "".to_string(),
            total_mem: "".to_string(),
            free_mem: "".to_string(),
        }
    }
}

///查询最新commitId的响应数据
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LatestCommitResponse {
    pub errorCode: i32,
    pub errorType: String,
    pub errorMsg: Option<String>,
    pub stackTrace: Option<String>,
    pub data: Option<LatestCommitData>,
    pub traceId: String,
}

///最新commitId的数据部分
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LatestCommitData {
    pub id: i64,
    pub repoURL: String,
    pub domain: String,
    pub groupPath: String,
    pub projectPath: String,
    pub branch: String,
    pub lastBuildCommit: String,
    pub taskName: String,
    pub autoUpdate: bool,
    pub state: String,
    pub lastBuildStartTime: Option<String>,
    pub lastBuildEndTime: Option<String>,
    pub gmtCreate: Option<String>,
    pub gmtModified: Option<String>,
    pub priority: i32,
    pub taskRecordId: i64,
    pub extension: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ResponseResult {
    SUCCESS,
    FAILURE,
}

#[cfg(test)]
mod tests {
    use crate::dal::remote_client::{
        chat_query_rewrite, query_latest_commit, query_web_fetch,
        QueryChangeRequestBean,
    };


    #[tokio::test]
    async fn test_query_rewrite() {
        let param = QueryChangeRequestBean {
            userToken: "1".to_string(),
            productType: "IDEA".to_string(),
            question: "如何计算token".to_string(),
        };
        let r = chat_query_rewrite(&param).await;
        println!("结果：{:?}", r);
    }

    #[tokio::test]
    async fn test_query_web_fetch() {
        let url = "https://cn.dubbo.apache.org/zh-cn/overview/what/overview/".to_string();
        let r = query_web_fetch(url).await;
        println!("web fetch结果：{:?}", r);
    }

    #[tokio::test]
    async fn test_query_latest_commit() {
        let repo_url = "https://code.alipay.com/78/codegencore.git".to_string();
        let r = query_latest_commit(repo_url).await;
        println!("latest commit结果：{:?}", r);
    }
}

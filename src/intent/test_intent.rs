use crate::api::req_model::QueryRelatedRequestBean;
use crate::it::java_model::class_info::ClassInfo;
use crate::it::java_parser::context::{ExpandContext, ParserContext};
use crate::it::java_parser::parser::parse_class_context;
use crate::utils::file_utils::path_str_to_posix;

#[derive(Debug)]
enum TestIntent {
    IT(String),
    UT(String),
}

impl TestIntent {
    fn as_str(&self) -> &str {
        match self {
            TestIntent::IT(_) => "it",
            TestIntent::UT(_) => "ut",
        }
    }
}

///
/// 识别测试意图，Java
///
/// # 字段
/// - `QueryRelatedRequestBean`:  圈选代码相关bean
///
/// # 返回
/// 识别结果，生成单测还是集成测试
///
pub fn intent_recognition(query_related_request: &QueryRelatedRequestBean) -> Option<String> {
    let intent_it = TestIntent::IT(String::from("it"));
    let intent_ut = TestIntent::UT(String::from("ut"));

    // 如果不是java语言，则认为是单测
    if query_related_request.language != "java" {
        return Some(intent_ut.as_str().to_string());
    }

    // 1. 首先判断是否有公共方法，如果没有，则认为是单测
    let mut has_public_method = false;
    let code_snippet: &String = &query_related_request.selectedContent;
    if code_snippet.contains("public ") {
        has_public_method = true;
    }
    if !has_public_method {
        // 不存在公共方法，则认为是要生成单测
        return Some(intent_ut.as_str().to_string());
    }

    // 解析ClassInfo，判断是否是rpc服务
    let project_url = path_str_to_posix(&query_related_request.projectUrl);
    // 只解析最简单的ClassInfo信息, 以及method的基本数据
    let mut parser_context = ParserContext::basic_class_info(&query_related_request.fileContent);
    parser_context.skip_method = false;
    let mut file_class_info: ClassInfo = parse_class_context(&mut parser_context);
    if file_class_info.is_rpc_service(project_url.as_str()) {
        // Rpc服务，认为是接口测试
        return Some(intent_it.as_str().to_string());
    }
    if file_class_info.is_http_service() {
        // 判断是否http服务
        let mut parser_context = ParserContext::basic_class_info(&query_related_request.selectedContent);
        parser_context.skip_method = false;
        let selected_class_info = parse_class_context(&mut parser_context);
        if selected_class_info.method_list.len() == 1 {
            let selected_method_info = &selected_class_info.method_list[0];
            let mut expand_context = ExpandContext::build(&query_related_request.projectUrl);
            if let Some((find_method_info, _)) = file_class_info.find_method(selected_method_info, &mut expand_context) {
                if find_method_info.is_http_method() {
                    // Http服务，认为是接口测试
                    return Some(intent_it.as_str().to_string());
                }
            }
        }
    }
    return Some(intent_ut.as_str().to_string());
}

#[cfg(test)]
mod test {
    use std::fs;

    use crate::api::req_model::IntentionType;

    use super::*;

    #[test]
    fn test_intent_recognition_annotation() {
        let intent_it = TestIntent::IT(String::from("it"));
        let intent_ut = TestIntent::UT(String::from("ut"));

        // rpc provider
        let selected_content = r#"public MobileGwDTO queryPortraitInfo(RcqualitydataGWPTReq request){"#;
        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/rcqualitydataprod/app/biz/service/impl/src/main/java/com/alipay/rcqualitydataprod/biz/service/mobilegw/impl/RcqualitydataGwFacadeImpl.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/rcqualitydataprod".to_string(),
            fileUrl: "app/biz/service/impl/src/main/java/com/alipay/rcqualitydataprod/biz/service/mobilegw/impl/RcqualitydataGwFacadeImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };
        let rpc_provider_annotation_result = intent_recognition(&query_related_request);
        assert_eq!(intent_it.as_str().to_string(), rpc_provider_annotation_result.unwrap());

        // sofa service
        let sofa_service_annotation_file_content = "package com.alipay.sofaboot.rpc.demo.server.service.impl;\\n\\nimport com.alipay.sofa.runtime.api.annotation.SofaService;\\nimport com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;\\nimport com.alipay.sofaboot.rpc.demo.server.facade.AnnotateSampleService;\\nimport org.springframework.stereotype.Component;\\n\\n/**\\n * 通过 {@link SofaService} 注解发布 RPC 服务\\n */\\n@SofaService(bindings = { @SofaServiceBinding(bindingType = \"tr\") })\\n@Component\\npublic class AnnotateSampleServiceImpl implements AnnotateSampleService {\\n\\n    @Override\\n    public String message() {\\n        return \"Congratulations! Here is call result from Annotate Pub Service of Server SOFABoot Server!\";\\n    }\\n}\\n\\n";
        let sofa_service_query_related_request = QueryRelatedRequestBean {
            fileContent: sofa_service_annotation_file_content.to_string(),
            ..query_related_request.clone()
        };
        let sofa_service_annotation_result = intent_recognition(&sofa_service_query_related_request);
        assert_eq!(intent_it.as_str().to_string(), sofa_service_annotation_result.unwrap());

        let private_query_related_request = QueryRelatedRequestBean {
            selectedContent: "private void func1() {}".to_string(),
            ..query_related_request.clone()
        };
        let private_result = intent_recognition(&private_query_related_request);
        assert_eq!(intent_ut.as_str().to_string(), private_result.unwrap());
    }

    #[test]
    #[ignore]
    fn test_xml_tr_for_relative_path_bugfix() {
        let intent_it = TestIntent::IT(String::from("it"));
        let intent_ut = TestIntent::UT(String::from("ut"));

        // rpc provider
        let new_annotation_file_content = "/**\n * Alipay.com Inc.\n * Copyright (c) 2004-2022 All Rights Reserved.\n */\npackage com.alipay.riskanalysis.biz.service.impl.riskassessorder;\n\nimport com.alibaba.common.logging.Logger;\nimport com.alibaba.common.logging.LoggerFactory;\nimport com.alipay.auditprod.common.service.facade.domain.RiskDomainIdEnum;\nimport com.alipay.riskanalysis.biz.shared.assess.RiskAssessOrderManager;\nimport com.alipay.riskanalysis.common.service.facade.riskassessorder.AutoChangeAnalysisFacade;\nimport com.alipay.riskanalysis.common.service.facade.riskassessorder.enumeration.AutoChangeAnalysisResultCodeEnum;\nimport com.alipay.riskanalysis.common.service.facade.riskassessorder.request.IterationAutoChangeAnalysisRequest;\nimport com.alipay.riskanalysis.common.service.facade.riskassessorder.result.IterationAutoChangeAnalysisResult;\nimport com.alipay.riskanalysis.common.service.facade.risksourceorder.enumeration.RiskSourceType;\nimport com.alipay.riskanalysis.common.service.facade.risksourceorder.result.RiskAnalysisResult;\nimport com.alipay.riskanalysis.common.service.integration.linke.model.LinkeIterationInfo;\nimport com.alipay.riskanalysis.common.util.Asserts;\nimport com.alipay.riskanalysis.common.util.LoggerUtil;\nimport com.alipay.riskanalysis.common.util.ObjectConvertUtil;\nimport com.alipay.riskanalysis.common.util.page.Criteria;\nimport com.alipay.riskanalysis.common.util.page.PageModel;\nimport com.alipay.riskanalysis.core.model.assess.convertor.AutoChangeAssessItemConvert;\nimport com.alipay.riskanalysis.core.model.assess.enumeration.TriggerStatusEnum;\nimport com.alipay.riskanalysis.core.model.assess.model.AutoChangeAssessItemResult;\nimport com.alipay.riskanalysis.core.model.assess.model.ChangeAssessItemResult;\nimport com.alipay.riskanalysis.core.model.assess.model.RiskAssessOrder;\nimport com.alipay.riskanalysis.core.model.common.model.RiskCategory;\nimport com.alipay.riskanalysis.core.model.risksourceorder.model.RiskSourceOrder;\nimport com.alipay.riskanalysis.core.service.assess.RiskAssessOrderService;\nimport com.alipay.riskanalysis.core.service.common.RiskCategoryService;\nimport com.alipay.riskanalysis.core.service.risksourceorder.RiskSourceOrderService;\nimport com.alipay.riskanalysis.core.service.risksourceorder.repository.RiskSourceRepository;\nimport org.apache.commons.collections.CollectionUtils;\nimport org.springframework.beans.factory.annotation.Autowired;\n\nimport java.util.Collections;\nimport java.util.List;\nimport java.util.Objects;\nimport java.util.stream.Collectors;\n\n/**\n * <AUTHOR> * @version : AutoChangeAnalysisFacadeImpl.java, v 0.1 2022年07月30日 9:23 AM muhui Exp $\n */\npublic class AutoChangeAnalysisFacadeImpl implements AutoChangeAnalysisFacade {\n\n    private static final Logger LOGGER = LoggerFactory.getLogger(AutoChangeAnalysisFacadeImpl.class);\n\n    @Autowired\n    private RiskAssessOrderManager riskAssessOrderManager;\n\n    @Autowired\n    private RiskCategoryService riskCategoryService;\n\n    @Autowired\n    private RiskSourceRepository riskSourceRepository;\n\n    @Autowired\n    private RiskAssessOrderService riskAssessOrderService;\n\n    @Autowired\n    private RiskSourceOrderService linkeIterationSourceOrderRiskService;\n\n    @Override\n    public RiskAnalysisResult<IterationAutoChangeAnalysisResult> findAutoChangeAnalysisResult(\n            IterationAutoChangeAnalysisRequest request) {\n        checkRequest(request);\n\n        //检查迭代是否有对应的风险评估单，如果是大支付租户，没有则创建对应的风险评估单\n        createRiskAssessOrderIfAbsent(request.getRiskDomainId(), request.getIterationId());\n\n        PageModel<AutoChangeAssessItemResult> resultPageModel = riskAssessOrderManager.findAutoChangeItemResult(\n                convertRequest(request));\n        //填充变更评估项流水的目录名称\n        if (null != resultPageModel && CollectionUtils.isNotEmpty(resultPageModel.getResultList())) {\n            fillCategoryName(resultPageModel.getResultList());\n        }\n\n        //构造返回结果\n        IterationAutoChangeAnalysisResult result = convertIterationAutoChangeAnalysisResult(resultPageModel);\n        fillResultCode(request.getIterationId(), result);\n        return RiskAnalysisResult.buildSuccess(result);\n    }\n\n    private void checkRequest(IterationAutoChangeAnalysisRequest request) {\n        Asserts.notNull(request, \"Request can not be null\");\n        Asserts.notNull(request.getRiskDomainId(), \"RiskDomainId can not be blank\");\n        Asserts.notBlank(request.getIterationId(), \"IterationId can not be blank\");\n    }\n\n    private PageModel<AutoChangeAssessItemResult> convertRequest(IterationAutoChangeAnalysisRequest request) {\n        PageModel<AutoChangeAssessItemResult> queryModel = new PageModel<>(request.getPageSize(), request.getPageNum());\n        Criteria criteria = queryModel.getCriteria();\n        criteria.put(\"relatedId\", request.getIterationId());\n\n        return queryModel;\n    }\n\n    private void createRiskAssessOrderIfAbsent(String riskDomainId, String iterationId) {\n        //如果不是大支付租户,不自动创建风险评估单\n        if (!RiskDomainIdEnum.ALIPAY.getCode().equals(riskDomainId)) {\n            return;\n        }\n\n        //如果迭代没有创建风险评估单，创建对应的风险评估单\n        if (!judgeRiskAssessOrderExist(iterationId)) {\n            LinkeIterationInfo linkeIterationInfo\n                    = linkeIterationSourceOrderRiskService.findLinkeIterationInfoByRelatedId(\n                    RiskSourceType.LINKE_ITERATION, iterationId);\n            RiskSourceOrder riskSourceOrder = linkeIterationSourceOrderRiskService.buildRiskSourceOrderFromLinke(\n                    riskDomainId, iterationId, true, null, linkeIterationInfo);\n            LoggerUtil.info(LOGGER, \"AutoChangeAnalysisFacadeImpl addRiskSourceOrder start, iterationId: {0}\",\n                    iterationId);\n            linkeIterationSourceOrderRiskService.addRiskSourceOrder(riskSourceOrder, linkeIterationInfo, false);\n            LoggerUtil.info(LOGGER, \"AutoChangeAnalysisFacadeImpl addRiskSourceOrder succeeded, iterationId: {0}\",\n                    iterationId);\n\n            LoggerUtil.info(LOGGER, \"AutoChangeAnalysisFacadeImpl createFromLinkeForRiskDomain start, iterationId: {0}\",\n                    iterationId);\n            riskAssessOrderManager.createFromLinkeForRiskDomain(riskSourceOrder, linkeIterationInfo);\n            LoggerUtil.info(LOGGER,\n                    \"AutoChangeAnalysisFacadeImpl createFromLinkeForRiskDomain succeeded, iterationId: {0}\",\n                    iterationId);\n        }\n\n    }\n\n    private boolean judgeRiskAssessOrderExist(String iterationId) {\n        List<RiskAssessOrder> riskAssessOrders = getRiskAssessOrderByIterationId(iterationId);\n        return CollectionUtils.isNotEmpty(riskAssessOrders);\n    }\n\n    private List<RiskAssessOrder> getRiskAssessOrderByIterationId(String iterationId) {\n        List<RiskSourceOrder> riskSourceOrders = riskSourceRepository.findBriefSourceOrdersByTypeAndRelatedIdList(\n                RiskSourceType.LINKE_ITERATION, Collections.singletonList(iterationId));\n        if (CollectionUtils.isNotEmpty(riskSourceOrders)) {\n            Long riskSourceId = riskSourceOrders.get(0).getId();\n            return riskAssessOrderService.findFormalByIterationId(Collections.singletonList(riskSourceId));\n        }\n\n        return Collections.emptyList();\n    }\n\n    private void fillCategoryName(List<AutoChangeAssessItemResult> autoChangeAssessItemResults) {\n        autoChangeAssessItemResults.forEach(autoChangeAssessItemResult -> {\n            ChangeAssessItemResult changeAssessItemResult = autoChangeAssessItemResult.getChangeAssessItemResult();\n            if (null != changeAssessItemResult && CollectionUtils.isNotEmpty(\n                    changeAssessItemResult.getRiskCategoryList())) {\n                List<String> riskCategoryIds = changeAssessItemResult.getRiskCategoryList().stream().filter(\n                        Objects::nonNull).map(RiskCategory::getId).collect(Collectors.toList());\n                List<RiskCategory> fullCategoryList = riskCategoryService.findList(riskCategoryIds);\n                changeAssessItemResult.setRiskCategoryList(fullCategoryList);\n            }\n        });\n    }\n\n    private IterationAutoChangeAnalysisResult convertIterationAutoChangeAnalysisResult(\n            PageModel<AutoChangeAssessItemResult> pageModel) {\n        IterationAutoChangeAnalysisResult result = new IterationAutoChangeAnalysisResult();\n\n        result.setPageNum(pageModel.getPage());\n        result.setPageSize(pageModel.getPageSize());\n        result.setTotal(pageModel.getTotalRows());\n        result.setAnalysisResultDTOList(\n                ObjectConvertUtil.convertList(pageModel.getResultList(), AutoChangeAssessItemConvert::model2Dto));\n        result.setResultCode(AutoChangeAnalysisResultCodeEnum.IN_ANALYSIS.getCode());\n        return result;\n    }\n\n    private void fillResultCode(String iterationId, IterationAutoChangeAnalysisResult result) {\n        if (CollectionUtils.isNotEmpty(result.getAnalysisResultDTOList())) {\n            result.setResultCode(AutoChangeAnalysisResultCodeEnum.FINISHED.getCode());\n            return;\n        }\n        List<RiskAssessOrder> assessOrders = getRiskAssessOrderByIterationId(iterationId);\n        if (CollectionUtils.isEmpty(assessOrders)) {\n            //默认兜底：结果码是分析中\n            return;\n        }\n\n        if (assessOrders.size() > 1) {\n            LoggerUtil.warn(LOGGER, \"AutoChangeAnalysisFacadeImpl fillResultCode warn: find multi-riskAssessOrder\"\n                    + \"for linke iterationId:{0}\", iterationId);\n        }\n        RiskAssessOrder riskAssessOrder = assessOrders.get(0);\n        TriggerStatusEnum triggerStatus = riskAssessOrder.getTriggerStatus();\n        if (null == triggerStatus) {\n            //triggerStatus为null时，还没开始分析\n            return;\n        }\n\n        switch (triggerStatus) {\n            case NOT_FINISHED:\n                result.setResultCode(AutoChangeAnalysisResultCodeEnum.IN_ANALYSIS.getCode());\n                break;\n            case NEW_CHANGE:\n            case NO_CHANGE:\n                result.setResultCode(AutoChangeAnalysisResultCodeEnum.FINISHED.getCode());\n                break;\n            case OVER_TIME:\n                result.setResultCode(AutoChangeAnalysisResultCodeEnum.OVER_TIME.getCode());\n                break;\n            default:\n                break;\n        }\n    }\n}\n";
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "123".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/riskanalysis".to_string(),
            fileUrl: "com/alipay/riskanalysis/biz/service/impl/riskassessorder/AutoChangeAnalysisFacadeImpl.java".to_string(),
            language: "java".to_string(),
            intention: "CODE_GENERATE_TEST".to_string(),
            fileContent: new_annotation_file_content.to_string(),
            selectedContent: "public void func1() {}".to_string(),
            intentionType: IntentionType::UnitTest,
            ..default_req_bean
        };
        let rpc_provider_annotation_result = intent_recognition(&query_related_request);
        assert_eq!(intent_it.as_str().to_string(), rpc_provider_annotation_result.unwrap());
    }
}


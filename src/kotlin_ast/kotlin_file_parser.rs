use crate::java_ast::java_file_parser::{create_snippet_record, create_snippet_record_for_method};
use crate::kotlin_ast::kotlin_ast_handle::{extract_class_base_model, extract_class_field_map, extract_import_set, extract_package_declaration, extract_unfinish_method, find_target_child, find_target_child_by_condition};
use crate::scan::common_scan::{is_offset_in_node, MAX_LOOP_TIME};
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_common_service::tools::code_tokenizer::code_snippet_tokenizer;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{CodeInfo, ScanFileRecord, ScanSnippetRecord};
use log::{debug, error};
use std::collections::{HashMap, HashSet};
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};
use agent_db::tools::common_tools::{FILE_PREFIX, LINE_ENDING, SEPARATORSIMPLE, SNIPPET_PREFIX};
use tree_sitter::Parser;

pub fn scan_kotlin_code_snippet(file_path: &Path, file_name_suffix: &String, code_lines: &Vec<String>) -> Option<ScanFileRecord> {
    let mut file_record = ScanFileRecord::default();
    file_record.file_url.push_str(file_path.to_str()?);
    file_record.file_name_suffix = file_name_suffix.clone();

    let mut snippet_index: usize = 1;
    for window_start in (0..code_lines.len()).step_by(AGENT_CONFIG.code_complation_slide_size as usize) {
        let window_end = usize::min(window_start + AGENT_CONFIG.code_complation_window_size as usize, code_lines.len());
        let content_arr = &code_lines[window_start..window_end];
        let code_snippet = content_arr.join(LINE_ENDING);

        let result = code_snippet_tokenizer(code_snippet, window_start, window_end, snippet_index, file_path.to_str().unwrap(), file_name_suffix);
        match result {
            Ok(mut scan_snippet_record) => {
                let file_snippet_key = format!("{}{}{}{}", SNIPPET_PREFIX, file_path.to_str().unwrap(), SEPARATORSIMPLE, &snippet_index.to_string());
                if window_end < code_lines.len() {
                    let next_index = snippet_index + 1;
                    let next_key = format!("{}{}{}{}", SNIPPET_PREFIX, file_path.to_str().unwrap(), SEPARATORSIMPLE, &next_index.to_string());
                    scan_snippet_record.next_key = Some(next_key);
                }
                let scan_snippet_record_str = serde_json::to_string(&scan_snippet_record).unwrap();
                let r = KV_CLIENT.insert(&file_snippet_key, &scan_snippet_record_str);
                if r.is_err() {
                    error!("code snippet insert error: {:?}",r.err());
                    continue;
                }
            }
            Err(e) => {
                error!("code snippet tokenizer error: {}",e);
            }
        }
        snippet_index = snippet_index + 1;
    }
    file_record.total_snippet_num = snippet_index;

    let code_content = code_lines.join(LINE_ENDING);
    let mut parser = Parser::new();
    parser.set_language(&tree_sitter_kotlin::language()).unwrap();
    let tree = parser.parse(&code_content, None)?;
    let mut cursor = tree.walk();
    let root_node = cursor.node();
    let error_node_opt = find_target_child(&root_node, "ERROR");
    if error_node_opt.is_some() { //解析错误，代码不完整
        return None;
    }
    cursor.goto_first_child();
    let mut code_info = CodeInfo::default();
    let mut to_remove: Vec<(usize, usize)> = Vec::new();
    let mut import_set: HashSet<String> = HashSet::new();
    let mut package_opt: Option<String> = None;

    let start_millis = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    loop {
        let end_millis = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        //兜底策略,防止死循环导致agent卡死
        if end_millis - start_millis > MAX_LOOP_TIME {
            break;
        }
        let node = cursor.node();
        // debug!("node kind: {} {:?}", node.kind(), &code_content[node.start_byte()..node.end_byte()]);
        let mut goto_child = false;
        match node.kind() {
            "property_declaration" => {}
            "class_declaration" | "object_declaration" => {
                extract_class_base_model(&&code_content, &mut code_info, &mut cursor);
                goto_child = true;
            }
            "function_declaration" | "secondary_constructor" => {
                let body_node_opt = find_target_child(&node, "function_body");
                if let Some(body_node) = body_node_opt {
                    to_remove.push((body_node.start_byte(), body_node.end_byte()));
                }
            }
            "anonymous_initializer" => {
                to_remove.push((node.start_byte(), node.end_byte()));
            }
            "import_header" => {
                extract_import_set(&&code_content, &mut import_set, &node);
            }
            "package_header" => {
                package_opt = extract_package_declaration(&&code_content, &node);
            }
            _ => {
                goto_child = true;
            }
        }
        //移动下一个子节点,如果没有子节点返回false
        if !goto_child || !cursor.goto_first_child() {
            //移动下一个兄弟节点,如果没有返回false
            while !cursor.goto_next_sibling() {
                //遍历所有兄弟节点, 游标返回父节点
                if !cursor.goto_parent() {
                    // 已经遍历了整棵树
                    break;
                }
            }
        }
        if cursor.node() == tree.root_node() {
            // 遍历结束
            break;
        }
    }

    //逆序移除范围，这样索引就不会发生变化
    to_remove.reverse();
    let mut code_struct = code_content.clone();
    for &(mut start, mut end) in &to_remove {
        // 确保start和end在字符边界上
        while !code_struct.is_char_boundary(start) && start > 0 {
            start -= 1;
        }
        while !code_struct.is_char_boundary(end) && end < code_struct.len() {
            end += 1;
        }
        if end > code_struct.len() {
            end = code_struct.len();
        }
        // 检查一下start和end的有效性，确保它们在字符串范围内
        if start <= end && end <= code_struct.len() {
            code_struct.replace_range(start..end, "{ }");
        } else {
            error!("Invalid range: {:?}..{:?} , content: {}", start, end,code_struct);
        }
    }
    code_info.code_struct = code_struct;
    if let (Some(class_name), Some(package_value)) = (&code_info.class_name, package_opt) {
        if class_name.trim().len() > 0 && package_value.len() > 0 {
            code_info.full_qualified_name = Some(format!("{}.{}", package_value, class_name));
        }
    }
    if import_set.len() > 0 {
        code_info.import_set = Some(import_set);
    }
    file_record.code_info = Some(code_info);
    let file_key = format!("{}{}", FILE_PREFIX, file_path.to_str()?);
    let scan_file_record_str = serde_json::to_string(&file_record).unwrap();
    let insert_result = KV_CLIENT.insert(&file_key, &scan_file_record_str);
    return match insert_result {
        Ok(_) => {
            Some(file_record)
        }
        _ => {
            None
        }
    };
}

pub fn parse_kotlin_prompt_content(prompt: &String, suffix: &String) -> Option<PromptDeclaration> {
    let code_content = format!("{}{}", prompt, suffix);
    let edit_offset = prompt.len();
    let mut parser = Parser::new();
    parser.set_language(&tree_sitter_kotlin::language()).unwrap();
    let tree = parser.parse(&code_content, None)?;
    let mut cursor = tree.walk();
    let root_node = cursor.node();
    //当前代码所在区块解析错误，代码不完整
    let error_node_opt = find_target_child_by_condition(&root_node, |child_node| {
        return child_node.kind() == "ERROR" && is_offset_in_node(edit_offset, &child_node);
    });
    if error_node_opt.is_some() {
        return None;
    }
    cursor.goto_first_child(); //跳过sourcefile root node
    let mut import_set: HashSet<String> = HashSet::new();
    let mut result = PromptDeclaration::new();
    let mut in_this_class = false;
    let start_millis = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    loop {
        let end_millis = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        //兜底策略,防止死循环导致agent卡死
        if end_millis - start_millis > MAX_LOOP_TIME {
            break;
        }
        let node = cursor.node();
        // debug!("node kind: {} {:?}", node.kind(), &code_content[node.start_byte()..node.end_byte()]);
        let mut goto_child = false;
        match node.kind() {
            "class_declaration" => {
                if is_offset_in_node(edit_offset, &node) {
                    in_this_class = true;
                    let mut code_info: CodeInfo = CodeInfo::default();
                    extract_class_base_model(&&code_content, &mut code_info, &mut cursor);
                    result.extends_class = code_info.extend_class_name.clone();
                    result.implements_class_name_set = code_info.implements_class_name_set.clone();
                    if let Some(field_class_map) = code_info.fild_name_class_map {
                        if field_class_map.len() > 0 {
                            let field_class_set: HashSet<String> = field_class_map.values().cloned().collect();
                            result.field_set = Some(field_class_set);
                        }
                    }
                    goto_child = true;
                } else {
                    in_this_class = false;
                }
            }
            "property_declaration" | "anonymous_initializer" => {
                //skip node
            }
            "function_declaration" => {
                if in_this_class && result.method_declaration.is_none() {
                    let java_method_declaration_opt = extract_unfinish_method(&&code_content, &node, edit_offset);
                    if let Some(java_method_declaration) = java_method_declaration_opt {
                        result.method_declaration = Some(java_method_declaration);
                    }
                }
            }
            "import_header" => {
                extract_import_set(&&code_content, &mut import_set, &node);
            }
            "package_header" => {
                let package_opt = extract_package_declaration(&&code_content, &node);
                if let Some(package_name) = package_opt {
                    result.package_name = Some(package_name);
                }
            }
            _ => {
                goto_child = true;
                // debug!("node kind: {} {:?}", node.kind(), &code_content[node.start_byte()..node.end_byte()]);
            }
        }
        //移动下一个子节点,如果没有子节点返回false
        if !goto_child || !cursor.goto_first_child() {
            //移动下一个兄弟节点,如果没有返回false
            while !cursor.goto_next_sibling() {
                //遍历所有兄弟节点, 游标返回父节点
                if !cursor.goto_parent() {
                    // 已经遍历了整棵树
                    break;
                }
            }
        }
        if cursor.node() == tree.root_node() {
            // 遍历结束
            break;
        }
    }
    if import_set.len() > 0 {
        result.import_set = Some(import_set);
    }
    return if result.method_declaration.is_some() || result.field_set.is_some() || result.import_set.is_some() || result.extends_class.is_some() {
        Some(result)
    } else {
        None
    };
}
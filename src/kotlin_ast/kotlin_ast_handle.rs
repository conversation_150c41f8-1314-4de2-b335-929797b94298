use crate::java_ast::java_ast_handle::{IMPORT_PREFIX, PACKAGE_PREFIX};
use crate::scan::common_scan::{get_real_class_name, is_offset_in_node, is_same_class_name};
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_db::domain::code_kv_index_domain::{ClassInfo, CodeInfo, MethodDeclaration, ScanFileRecord};
use once_cell::sync::Lazy;
use std::collections::{HashMap, HashSet};
use agent_db::tools::common_tools::LINE_ENDING;
use tree_sitter::{Node, TreeCursor};

///抽取package信息
pub fn extract_package_declaration(code_content: &&String, node: &Node) -> Option<String> {
    let package_str = &code_content[node.start_byte()..node.end_byte()];
    let mut last_char_index: usize = package_str.len();
    let last_char_opt = package_str.find(";");
    if let Some(last_char) = last_char_opt {
        last_char_index = last_char;
    }
    if package_str.starts_with(PACKAGE_PREFIX) {
        let package_str = package_str[IMPORT_PREFIX.len()..last_char_index].trim();
        return Some(package_str.to_string());
    }
    return None;
}

///抽取import信息
pub fn extract_import_set(code_content: &&String, import_set: &mut HashSet<String>, node: &Node) {
    let import_str = &code_content[node.start_byte()..node.end_byte()];
    //如果包含*,一般代表引入了某个package 暂不处理
    if !import_str.contains("*") {
        let mut last_char_index: usize = import_str.len();
        let last_char_opt = import_str.find(";");
        if let Some(last_char) = last_char_opt {
            last_char_index = last_char;
        }
        if import_str.starts_with(IMPORT_PREFIX) {
            let mut import_str = import_str[IMPORT_PREFIX.len()..last_char_index].trim();
            import_set.insert(import_str.to_string());
        }
    }
}

///class基础信息
pub fn extract_class_base_model(code_content: &&String, code_info: &mut CodeInfo, node_cursor: &mut TreeCursor) {
    let mut class_type = "class";
    let mut visibility_modifier_opt: Option<String> = Some("public".to_string()); //kotlin默认可见是public
    let mut class_name_opt: Option<String> = None;
    let mut extend_class_name_opt: Option<String> = None;
    let mut implements_class_name_set_opt: Option<HashSet<String>> = None;
    let mut class_field_map = HashMap::new();
    node_cursor.goto_first_child();
    loop {
        let node = node_cursor.node();
        // debug!("node kind: {} {:?}", node.kind(), &code_content[node.start_byte()..node.end_byte()]);
        match node.kind() {
            "enmu" => {
                class_type = "enum"
            }
            "interface" => {
                class_type = "interface"
            }
            "object" => {
                class_type = "object"
            }
            "modifiers" => {
                let mut modifiers_str = &code_content[node.start_byte()..node.end_byte()];
                let modifier_value = match modifiers_str.rsplit_once(LINE_ENDING) {
                    Some((_, last)) => last,
                    None => modifiers_str
                };
                visibility_modifier_opt = Some(modifier_value.to_string());
            }
            "type_identifier" => {
                let identifier_str = &code_content[node.start_byte()..node.end_byte()];
                class_name_opt = Some(identifier_str.to_string());
            }
            "primary_constructor" => {
                let mut primary_constructor_cursor = node.walk();
                for child_node in node.children(&mut primary_constructor_cursor) {
                    if child_node.kind() == "class_parameter" {
                        extract_constructor_field_map(&mut class_field_map, &&code_content, &child_node)
                    }
                }
            }
            "delegation_specifier" => {
                let delegation_str = &code_content[node.start_byte()..node.end_byte()];
                if let Some(index) = delegation_str.find("(") {
                    extend_class_name_opt = Some(delegation_str[..index].to_string());
                } else {
                    if let Some(interface_set) = &mut implements_class_name_set_opt {
                        interface_set.insert(delegation_str.to_string());
                    } else {
                        let mut interface_set: HashSet<String> = HashSet::new();
                        interface_set.insert(delegation_str.to_string());
                        implements_class_name_set_opt = Some(interface_set);
                    }
                }
            }
            "class_body" => {
                let mut class_body_cursor = node.walk();
                for child_node in node.children(&mut class_body_cursor) {
                    if child_node.kind() == "property_declaration" {
                        extract_class_field_map(&mut class_field_map, &&code_content, &child_node)
                    }
                }
                break;
            }
            _ => {}
        }
        if !node_cursor.goto_next_sibling() {
            break;
        }
    }
    return if code_info.class_name.is_some() {
        let class_info = ClassInfo {
            class_type: class_type.to_string(),
            class_name: class_name_opt.unwrap_or_default(),
            extend_class_name: extend_class_name_opt,
            field_name_class_map: Some(class_field_map),
            implements_class_name_set: implements_class_name_set_opt,
            code_struct: "".to_string(),
        };
        if let Some(class_list) = &mut code_info.class_list {
            class_list.push(class_info);
        } else {
            let mut class_list: Vec<ClassInfo> = Vec::new();
            class_list.push(class_info);
            code_info.class_list = Some(class_list);
        }
    } else {
        code_info.class_type = Some(class_type.to_string());
        code_info.class_name = class_name_opt;
        code_info.extend_class_name = extend_class_name_opt;
        code_info.implements_class_name_set = implements_class_name_set_opt;
        code_info.fild_name_class_map = Some(class_field_map);
    };
}

/// 抽取class级别的字段类型
pub fn extract_class_field_map(class_field_map: &mut HashMap<String, String>, code_content: &&String, node: &Node) {
    let field_declaration_node_opt = find_target_child(node, "variable_declaration");
    if let Some(field_declaration_node) = field_declaration_node_opt {
        let simple_identifier_node_opt = find_target_child(&field_declaration_node, "simple_identifier");
        if let Some(simple_identifier_node) = simple_identifier_node_opt {
            let identifier = &code_content[simple_identifier_node.start_byte()..simple_identifier_node.end_byte()];
            let mut field_type_opt = extract_user_type(code_content, &field_declaration_node, true);
            if let Some(field_type) = field_type_opt {
                if !is_sdk_class(&field_type) {
                    class_field_map.insert(identifier.to_string(), field_type);
                }
            }
        }
    }
}

fn extract_constructor_field_map(class_field_map: &mut HashMap<String, String>, code_content: &&String, node: &Node) {
    let simple_identifier_node_opt = find_target_child(node, "simple_identifier");
    if let Some(simple_identifier_node) = simple_identifier_node_opt {
        let identifier = &code_content[simple_identifier_node.start_byte()..simple_identifier_node.end_byte()];
        let mut field_type_opt = extract_user_type(code_content, node, true);
        if let Some(field_type) = field_type_opt {
            if !is_sdk_class(&field_type) {
                class_field_map.insert(identifier.to_string(), field_type);
            }
        }
    }
}

fn extract_user_type(code_content: &&String, node: &Node, check_null: bool) -> Option<String> {
    let user_type_node_opt = find_target_child(node, "user_type");
    if let Some(user_type_node) = user_type_node_opt {
        let user_type_str = &code_content[user_type_node.start_byte()..user_type_node.end_byte()];
        let field_type = user_type_str.to_string();
        if !is_sdk_class(&field_type) {
            return Some(field_type);
        }
    } else if check_null {
        let null_node_opt = find_target_child(node, "nullable_type");
        if let Some(null_node) = null_node_opt {
            return extract_user_type(code_content, &null_node, false);
        }
    }
    return None;
}

pub fn find_target_child<'a>(node: &'a Node, target_kind: &str) -> Option<Node<'a>> {
    let mut cursor = node.walk();
    for child_node in node.children(&mut cursor) {
        if child_node.kind() == target_kind {
            return Some(child_node);
        }
    }
    return None;
}

pub fn find_target_child_by_condition<'a, F>(node: &'a Node, condition_fn: F) -> Option<Node<'a>>
where
    F: Fn(&Node) -> bool,
{
    let mut cursor = node.walk();
    for child_node in node.children(&mut cursor) {
        if condition_fn(&child_node) {
            return Some(child_node);
        }
    }
    return None;
}

///抽取未写完的java方法，如果写完的代码不用管
/// 主要用在补全场景，函数体内的补全时对依赖代码做排序
pub fn extract_unfinish_method(code_content: &&String, node: &Node, edit_offset: usize) -> Option<MethodDeclaration> {
    if !is_offset_in_node(edit_offset, node) {
        //非当前编辑的方法
        return None;
    }
    let mut result = MethodDeclaration::default();
    let mut return_class: String = String::new();
    let mut param_class_vec: Vec<String> = Vec::new();
    let mut method_name: String = String::new();
    let mut cursor = node.walk();
    for method_node in node.children(&mut cursor) {
        // debug!("node kind: {} {:?}", method_node.kind(), &code_content[method_node.start_byte()..method_node.end_byte()]);
        match method_node.kind() {
            "user_type" => {
                //返回值
                let node_value = &code_content[method_node.start_byte()..method_node.end_byte()];
                let return_type = node_value.to_string();
                if !is_sdk_class(&return_type) {
                    return_class = return_type;
                }
            }
            "nullable_type" => {
                //返回值
                let mut return_type_opt = extract_user_type(code_content, &method_node, false);
                if let Some(return_type) = return_type_opt {
                    if !is_sdk_class(&return_type) {
                        return_class = return_type;
                    }
                }
            }
            "simple_identifier" => {
                //解析方法名
                let node_value = &code_content[method_node.start_byte()..method_node.end_byte()];
                method_name = node_value.to_string();
            }
            "function_value_parameters" => {
                //解析方法参数
                let mut param_cursor = method_node.walk();
                for param_node in method_node.children(&mut param_cursor) {
                    match param_node.kind() {
                        "parameter" => {
                            let mut param_type_opt = extract_user_type(code_content, &param_node, true);
                            if let Some(param_type) = param_type_opt {
                                if !is_sdk_class(&param_type) {
                                    param_class_vec.push(param_type);
                                }
                            }
                        }
                        _ => {}
                    }
                }
            }
            _ => {}
        }
    }
    if method_name.len() > 0 {
        result.method_name = method_name;
    }
    if param_class_vec.len() > 0 {
        result.param_class_vec = Some(param_class_vec);
    }
    if return_class.len() > 0 {
        result.return_class = Some(return_class);
    }
    //当method_name不为空，并且有参数或者有返回值或者方法体内有类变量，返回result，否则返回None(如果仅有method_name)，没有其他信息，返回上游也没啥用
    return if result.method_name.len() > 0 && (result.param_class_vec.is_some() || result.return_class.is_some()) {
        Some(result)
    } else {
        None
    };
}

pub fn code_prompt_related_care_kotlin(current_context: &PromptDeclaration) -> bool {
    if let Some(current_method_info) = &current_context.method_declaration {
        if let Some(param_vec) = &current_method_info.param_class_vec {
            for class_name in param_vec.iter() {
                if !is_sdk_class(class_name) {
                    return true;
                }
            }
        }
        if let Some(inner_class_declaration) = &current_method_info.inner_class_declaration {
            for class_name in inner_class_declaration.iter() {
                if !is_sdk_class(class_name) {
                    return true;
                }
            }
        }
        if let Some(class_name) = &current_method_info.return_class {
            if !is_sdk_class(class_name) {
                return true;
            }
        }
    }
    if let Some(class_field_set) = &current_context.field_set {
        for class_name in class_field_set.iter() {
            if !is_sdk_class(class_name) {
                return true;
            }
        }
    }
    if let Some(extends_class) = &current_context.extends_class {
        if !is_sdk_class(extends_class) {
            return true;
        }
    }
    if let Some(import_set) = &current_context.import_set {
        for class_name in import_set.iter() {
            if !is_sdk_import(class_name) {
                return true;
            }
        }
    }
    return false;
}

//寻找匹配的名字
pub fn find_by_class_name_kotlin(file_record: &ScanFileRecord, class_name_iter: &Vec<String>) -> (bool, usize) {
    for (index, target_class_name) in class_name_iter.iter().enumerate() {
        if is_sdk_class(target_class_name) {
            continue;
        }
        if let Some(code_info) = &file_record.code_info {
            if let Some(record_class_name) = &code_info.class_name {
                if is_same_class_name(record_class_name, target_class_name) {
                    return (true, index);
                }
            }
            if let Some(record_class_list) = &code_info.class_list {
                for record_class_info in record_class_list.iter() {
                    if is_same_class_name(&record_class_info.class_name, target_class_name) {
                        return (true, index);
                    }
                }
            }
        }
    }
    return (false, 0);
}

static SDK_CLASS: Lazy<HashSet<String>> = Lazy::new(|| {
    let mut set: HashSet<String> = HashSet::new();
    set.insert("String".to_string());
    set.insert("Runnable".to_string());
    set.insert("Int".to_string());
    set.insert("Long".to_string());
    set.insert("Number".to_string());
    set.insert("Object".to_string());
    set.insert("Boolean".to_string());
    set
});

fn is_sdk_class(class_name: &String) -> bool {
    return SDK_CLASS.contains(get_real_class_name(class_name).as_str());
}

fn is_sdk_import(import_class: &String) -> bool {
    return import_class.starts_with("java.")
        || import_class.starts_with("javax.")
        || import_class.starts_with("android.")
        || import_class.starts_with("kotlin")
        || import_class.starts_with("kotlinx");
}
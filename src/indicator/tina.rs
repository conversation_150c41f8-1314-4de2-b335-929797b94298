use agent_db::remote::rpc_model::{build_error_response, build_success_response, BaseResponse};
use log::info;
use std::{collections::HashMap, path::PathBuf, env};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};

use agent_common_service::model::chat_model::{ChatPluginModel, ChatRelatedRequestBean, ChatRelatedResponse};
use agent_db::dal::remote_client::search_doc_segment;
use crate::utils::{parse::{parse_reference_target, read_and_parse_json, TINA_NOT_LAUNCHED, RUNTIME_JSON, TINA_CARD, TINA_NORMAL}, path::combine_host, query::fetch_from_api};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TinaPromptVariable {
    #[serde(rename = "contextCode")]
    pub context_code: String,
    #[serde(rename = "defineData")]
    pub define_data: String,
}

#[derive(Debug, Deserialize)]
struct TinaResponse {
    success: bool,
    #[serde(rename = "type")]
    scene_type: String,
    data: String,
}

#[derive(Deserialize, Debug)]
struct TinaRuntimeConfig {
    port: Value,
    host: String,
    #[serde(rename = "type")]
    scene_type: String,
}

pub struct ApiEndpoints {
    endpoints: HashMap<String, String>,
}

impl ApiEndpoints {
    pub fn new() -> Self {
        let mut endpoints = HashMap::new();
        endpoints.insert("context_code".to_string(), "/api/aiCode/getContextCode".to_string());
        endpoints.insert("define_data".to_string(), "/api/aiCode/getDefineData".to_string());
        Self { endpoints }
    }

    pub fn get(&self, key: &str) -> Option<&String> {
        self.endpoints.get(key)
    }
}

pub async fn tina_copilot(chat_request: &ChatRelatedRequestBean) -> BaseResponse<ChatRelatedResponse> {
  let project_path = PathBuf::from(&chat_request.projectUrl.clone().unwrap());
  let runtime_path = if env::var("RICHLAB_MOCK").is_ok() {
    PathBuf::from("/Users/<USER>/codefuse/codefuse_local_agent/agent_richlab_service/src/api/mock.json")
  } else {
    project_path.join(RUNTIME_JSON)
  };

  if !runtime_path.exists() {
      // 1. 未启动 tina
      info!("build_error_response: no runtime_path {:?}", runtime_path);
      return build_error_response(20, TINA_NOT_LAUNCHED.to_string());
    }

    // 2.是否是正确场景

    // 3. 生码目标文件
    // 营销，生码目标文件需要用户明确选中：src/logic/xxx.ts
    // 卡片，生码目标文件为 src/biz-models/tina-biz.ts
    let api_endpoints = ApiEndpoints::new();

    // 获取 Tina 端口
    let tina_runtime: TinaRuntimeConfig = match read_and_parse_json(runtime_path.to_str().unwrap()) {
      Ok(response) => response,
      Err(err) => {
        info!("build_error_response: read_and_parse_json error {:?}", err);
        return build_error_response(20, TINA_NOT_LAUNCHED.to_string());
      }
    };
    info!("tina runtime: {}，{}", tina_runtime.host, tina_runtime.scene_type);

    let (logic_name, ref_target_context_code) = parse_reference_target(&tina_runtime.scene_type, &chat_request.referenceList);
    let mut final_target_context_code = String::new();
    // 卡片：12900001
    let assistant_plugin_id = 12900001;

    match (&tina_runtime.scene_type[..], &logic_name, &ref_target_context_code) {
        (TINA_NORMAL, Some(logic_name), Some(ref_target_context_code)) => {
            // 营销
            final_target_context_code = ref_target_context_code.to_string();
            info!("scene_type: 营销");
        },
        (TINA_CARD, _, Some(ref_target_context_code)) => {
            // 卡片 选中了目标biz文件
            final_target_context_code = ref_target_context_code.to_string();
            info!("scene_type: 卡片 选中了目标biz文件");
        },
        (TINA_CARD, _, None) => {
            // 卡片 未中目标biz文件，默认取 tina_model.ts
            info!("scene_type: 卡片 未中目标biz文件，默认取 tina_model.ts");

            let context_code_endpoint = combine_host(api_endpoints.get("context_code").unwrap(), &tina_runtime.host);
            let context_code_response: TinaResponse = match fetch_from_api(&context_code_endpoint, None).await {
              Ok(response) => response,
              Err(err) => {
                info!("build_error_response: fetch_from_api error {:?}", context_code_endpoint);
                return build_error_response(21, TINA_NOT_LAUNCHED.to_string());
              }
            };
            final_target_context_code = context_code_response.data;
        },
        (TINA_NORMAL, None, _) => {
          info!("scene_type error");
          return build_error_response(21, "请选择目标逻辑文件".to_string());
        },
        _ => {
          info!("scene_type error：{:?}, {:?}, {:?}", &tina_runtime.scene_type[..], &logic_name, ref_target_context_code);
        }
    }

    info!("final_target_context_code: {:?}", final_target_context_code);

    let define_data_payload = json!({
        "logicName": logic_name.unwrap_or("__NONE__".to_string()),
    });

    // defineData
    let define_data_endpoint = combine_host(api_endpoints.get("define_data").unwrap(), &tina_runtime.host);
    let define_data_response: TinaResponse = match fetch_from_api(&define_data_endpoint, Some(&define_data_payload)).await {
      Ok(response) => response,
      Err(err) => {
        info!("build_error_response: fetch_from_api error {:?}， {:?}", define_data_endpoint, err);
        return build_error_response(20, TINA_NOT_LAUNCHED.to_string());
      }
    };
    let define_data = &define_data_response.data;
    let scene_id_string: String = match chat_request.level1Instruction {
        Some(id) => id.to_string(),
        None => String::new(), // 或者使用某个默认值
    };
    let scene_id: &String = &scene_id_string;
    let rag_response = search_doc_segment(&chat_request.query, scene_id, 10).await;
    let mut rag_snippets = vec![];

    info!("RAG query={}, rag_response:  {:?}", &chat_request.query, rag_response);

    for doc in rag_response.data.unwrap().result.unwrap() {
      // if (doc.similarity > 0.7) {
        rag_snippets.push(Value::from(doc.segmentContent))
      // }
    }

    let serialized_snippets = serde_json::to_string(&rag_snippets).expect("");

    let mut prompt_variable = HashMap::new();
        prompt_variable.insert("contextCode".to_string(), Value::String(final_target_context_code.to_string()));
        prompt_variable.insert("defineData".to_string(), Value::String(define_data.to_string()));
        prompt_variable.insert("context".to_string(), Value::String(serialized_snippets));

    let plugin_id = chat_request.level2Instruction
        .as_ref()
        .and_then(|instruction| Some(instruction.pluginId))
        .unwrap_or(assistant_plugin_id);

    let level2instruction = Some(ChatPluginModel {
        pluginId: plugin_id,
        command: Some("xiaojin_prompt".to_string()),
        pluginTips: Some("".to_string()),
        pluginName: Some("".to_string()),
    });

    // 返回 defineData、contextCode
    return build_success_response(ChatRelatedResponse {
        extraData: None,
        localRepoSelectedList: None,
        level1Instruction: chat_request.level1Instruction,
        level2Instruction: level2instruction,
        assitantParam: Some(prompt_variable),
        localRepoReferenceRagList: None,
        chatStatus: None,
        necessaryIndexPercent: None,
        questionUid: None,
        sessionId: None,
    })
}

use agent_common_service::model::code_complete_model::{
    CodeCompletionRequestBean, CodeModel, CompletionResultModel, ContextAwareInfo,
};
use agent_common_service::model::code_edit_model::{
    CodeEditRequest, CodeEditResponseData, FileContent, FileDiff, NextTabRequest,
};
use agent_common_service::service::code_edit::CodeEditStrategy;
use agent_db::remote::http_client::{post_request, DEFAULT_TIMEOUT_MILLIS};
use agent_db::remote::rpc_model::{build_error_response, BaseResponse};
use agent_db::tools::common_tools::LINE_ENDING;
use log::{debug, error, info, warn};
use serde_json;
use std::vec;
use tokio;
use tokio::runtime::Handle;
use tokio::time::Instant;
use uuid::Uuid;

use crate::constants::LOCK_TIMEOUT_MS;
use crate::function::cursor_tab_diff_historical::CURSOR_DIFF_HISTORICAL_INSTANCE;

const CODE_EDIT_URL: &str = "https://caselike.alipay.com/v1/function/codeEdit";
const NEXT_TAB_URL: &str = "https://caselike.alipay.com/v1/function/nextTab";

pub struct CodeFuseCodeEditStrategy;

impl CodeFuseCodeEditStrategy {
    /// 获取diff轨迹，使用带超时的锁机制
    pub async fn get_diff_trajectories_safe() -> Vec<FileDiff> {
        info!("=== get_diff_trajectories_safe START ===");

        let start_time = Instant::now();

        let lock_start_time = Instant::now();
        let lock_result = tokio::time::timeout(
            tokio::time::Duration::from_millis(LOCK_TIMEOUT_MS),
            CURSOR_DIFF_HISTORICAL_INSTANCE.lock(),
        )
        .await;
        let lock_acquisition_time = lock_start_time.elapsed();

        let result = match lock_result {
            Ok(mut manager) => match manager.get_diff_trajectories().await {
                Ok(trajectories) => {
                    let file_diffs: Vec<FileDiff> = trajectories
                        .into_iter()
                        .map(|trajectory| FileDiff {
                            fileUrl: trajectory.file_name,
                            diffs: trajectory.diff_history,
                        })
                        .collect();
                    file_diffs
                }
                Err(e) => {
                    warn!("获取diff轨迹数据失败: {:?}, 返回空列表", e);
                    vec![]
                }
            },
            Err(_) => {
                warn!("获取diff轨迹锁超时(超过{}ms), 返回空列表", LOCK_TIMEOUT_MS);
                vec![]
            }
        };

        let total_time = start_time.elapsed();
        info!(
            "get_diff_trajectories_safe result: {} file diffs, total time: {:?}, lock acquisition: {:?}",
            result.len(),
            total_time,
            lock_acquisition_time,
        );
        info!("=== get_diff_trajectories_safe END ===");

        result
    }
}

impl CodeEditStrategy for CodeFuseCodeEditStrategy {
    async fn code_edit(
        code_completion_request: CodeCompletionRequestBean,
    ) -> BaseResponse<CompletionResultModel> {
        let code_edit_request = Self::convert_request(code_completion_request.clone()).await;
        let start_time = Instant::now();
        info!(
            "Code edit request: {}",
            serde_json::to_string(&code_edit_request).unwrap()
        );
        let base_response = post_request::<CompletionResultModel, _>(
            CODE_EDIT_URL,
            &code_edit_request,
            DEFAULT_TIMEOUT_MILLIS,
            "codefuse agent code complete failed",
        )
        .await;
        if let Some(mut response) = base_response {
            let elapsed_time = start_time.elapsed().as_millis();
            info!(
                "Code edit http success: {} ms, response={:?}",
                elapsed_time, response
            );
            response
        } else {
            let elapsed_time = start_time.elapsed().as_millis();
            error!(
                "code_complete http failed: {} ms, param: {:?}",
                elapsed_time, code_edit_request
            );
            build_error_response(3, "codefuse agent code complete failed".to_string())
        }
    }

    async fn next_tab(
        code_completion_request: CodeCompletionRequestBean,
    ) -> BaseResponse<CompletionResultModel> {
        let code_edit_request =
            Self::convert_next_tab_request(code_completion_request.clone()).await;
        let start_time = Instant::now();
        info!(
            "Code edit request: {}",
            serde_json::to_string(&code_edit_request).unwrap()
        );
        let base_response = post_request::<CompletionResultModel, _>(
            NEXT_TAB_URL,
            &code_edit_request,
            DEFAULT_TIMEOUT_MILLIS,
            "codefuse agent next tab failed",
        )
        .await;
        let elapsed_time = start_time.elapsed().as_millis();
        if let Some(mut response) = base_response {
            info!(
                "Next tab http success: {} ms, response={:?}",
                elapsed_time, response
            );
            response
        } else {
            if elapsed_time >= DEFAULT_TIMEOUT_MILLIS as u128 {
                error!(
                    " next_tab http TIMEOUT: {} ms (>= {} ms), param: {:?}",
                    elapsed_time, DEFAULT_TIMEOUT_MILLIS, code_edit_request
                );
            } else {
                error!(
                    " next_tab http FAILED: {} ms, param: {:?}",
                    elapsed_time, code_edit_request
                );
            }
            build_error_response(3, "codefuse agent next tab failed".to_string())
        }
    }

    async fn convert_request(input: CodeCompletionRequestBean) -> CodeEditRequest {
        CodeEditRequest {
            userToken: input.userToken,
            pluginVersion: input.pluginVersion,
            productType: input.productType,
            sessionId: input.sessionId,
            repo: input.repo,
            fileUrl: input.fileUrl,
            language: input.language,
            prefix: input.prompt,
            suffix: input.suffix,
            sourceCodePrefix: input.sourceCodePrefix.unwrap_or_default(),
            sourceCodeSuffix: input.sourceCodeSuffix.unwrap_or_default(),
            fileDiffs: input.fileDiffs.unwrap(),
            recordInfo: input.recordInfo,
            similarSnippets: input.similarSnippets,
            relatedSnippets: input.relatedSnippets,
        }
    }

    async fn convert_next_tab_request(input: CodeCompletionRequestBean) -> NextTabRequest {
        NextTabRequest {
            userToken: input.userToken,
            pluginVersion: input.pluginVersion,
            productType: input.productType,
            sessionId: input.sessionId,
            repo: input.repo,
            fileUrl: input.fileUrl,
            language: input.language,
            prefix: input.prompt,
            suffix: input.suffix,
            sourceCodePrefix: input.sourceCodePrefix.unwrap_or_default(),
            sourceCodeSuffix: input.sourceCodeSuffix.unwrap_or_default(),
            fileDiffs: input.fileDiffs.unwrap(),
            originalCode: input.originalCode.unwrap_or_default(),
            recordInfo: input.recordInfo,
            similarSnippets: input.similarSnippets,
            relatedSnippets: input.relatedSnippets,
        }
    }
}

use agent_android_service::android_strategy::code_complete_scan_strategy_android::AndroidScanStrategy;
use anyhow::Result;
use log::{debug, error, info};
use once_cell::sync::Lazy;
use std::collections::{BinaryHeap, HashMap, HashSet};
use std::path::Path;
use std::sync::atomic::AtomicBool;
use std::sync::Arc;
use tokio::sync::RwLock;

use crate::function::build_cache_data::build_cache_data;
use crate::function::codecomplete_rag_executor_strategy::CodeFuseCodeCompleteRagExecutorStrategy;
use crate::function::codecomplete_scan_strategy::{execute_scan_file, CodeFuseScanStrategy};
use crate::utils::strategy_utils::{get_rag_strategy, is_android};
use agent_common_service::model::code_complete_model::{CodeCompletionRequestBean, ContextAwareInfo};
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_common_service::service::code_complete_rag_executor::CodeCompleteRagExecutorStrategy;
use agent_common_service::service::code_complete_rag_framework::CodeCompleteRagFrameworkStrategy;
use agent_common_service::service::code_scan::CodeScanStrategy;
use agent_common_service::tools::code_tokenizer::{code_snippet_tokenizer, token_to_code};
use agent_common_service::tools::common_tools::{CacheRelatedModuleEnum, CacheTypeEnum, SimilarityCodeInfo};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::ScanFileRecord;

///代码补全时缓存数据
pub static SIMILARITY_CACHE: Lazy<Arc<RwLock<CacheSimilarityInfo>>> = Lazy::new(|| {
    Arc::new(RwLock::new(CacheSimilarityInfo::new()))
});
pub static RELATED_CACHE: Lazy<Arc<RwLock<CacheRelatedInfo>>> = Lazy::new(|| {
    Arc::new(RwLock::new(CacheRelatedInfo::new()))
});

pub struct CodeFuseCodeCompleteRagStrategy;


//缓存-相似片段集合
pub struct CacheSimilarityInfo {
    pub file_url: String,
    pub similarity_snippet_vec: Vec<SimilarityCodeInfo>,
}

impl CacheSimilarityInfo {
    pub fn new() -> Self {
        CacheSimilarityInfo {
            file_url: "".to_string(),
            similarity_snippet_vec: vec![],
        }
    }
}


//缓存-相关片段
pub struct CacheRelatedInfo {
    //代码路径
    pub file_url: String,
    //当前代码上下文
    pub current_code_context: PromptDeclaration,
    //模块化相关性缓存
    //key: CacheRelatedModuleEnum
    //value: key 类型对应的相关性缓存
    pub module_related_map: HashMap<CacheRelatedModuleEnum, Vec<ContextAwareInfo>>,
}

impl CacheRelatedInfo {
    fn new() -> Self {
        CacheRelatedInfo {
            file_url: "".to_string(),
            current_code_context: PromptDeclaration::new(),
            module_related_map: Default::default(),
        }
    }
}

/******************* agent侧临时处理索引的增量更新，但存在数据更新不及时问题.后续插件侧触发增量更新实际 start *******************/
///记录当前编辑的文件内容
pub static CURRENT_FILE_DATA: Lazy<Arc<RwLock<CurrentFile>>> = Lazy::new(|| {
    Arc::new(RwLock::new(CurrentFile::new()))
});

///当前文件
pub struct CurrentFile {
    pub file_url: String,
    pub file_content: String,
}
impl CurrentFile {
    pub fn new() -> Self {
        CurrentFile {
            file_url: String::new(),
            file_content: String::new(),
        }
    }
}

///如果文件路径和缓存file_url一样，将当前编辑内容缓存在本地。
/// 如果文件路径变化了，在rebuild_file_index_increment函数更新
pub async fn save_file_with_not_change(file_url: &String, new_conent: &String, force_update: bool) {
    let mut current_file_data = CURRENT_FILE_DATA.write().await;
    if force_update || (current_file_data.file_url.len() == 0 || current_file_data.file_url == file_url.clone()) {
        current_file_data.file_url = file_url.clone();
        current_file_data.file_content = new_conent.clone();
        debug!("save file change content. file_url={}",&current_file_data.file_url);
    }
}

///增量更新单文件索引数据
pub async fn rebuild_file_index_increment(code_complete_request: &CodeCompletionRequestBean, new_conent: String) {
    let project_url_opt = &code_complete_request.projectUrl;
    //如果路径不同，看是否正在进行扫描，如果正在进行，那么直接返回。如果不是，那么从索引数据构建缓存
    if CodeFuseScanStrategy::is_scan_ing() || project_url_opt.is_none() {
        return;
    }
    let cache_file_url = get_current_cache_file_url().await;
    let file_url = &code_complete_request.fileUrl;
    let project_url = project_url_opt.as_ref().expect("Project URL should be present").clone();

    //只有缓存文件变更的情况下走增量更新
    if &cache_file_url != file_url {
        info!("rebuild file index increment,file_url={}", &file_url);
        let file_path = Path::new(file_url);
        let android_flag = if is_android(&code_complete_request) {
            true
        } else {
            false
        };
        let file_url_path = Path::new(file_path);
        let scan_file_result: Result<Option<ScanFileRecord>> = if android_flag {
            AndroidScanStrategy::execute_scan_file(file_url_path, None)
        } else {
            execute_scan_file(file_url_path, &project_url, None)
        };

        match scan_file_result {
            Ok(scan_file_record) => {
                debug!("rebuild file index increment success,file_url={}", &file_url);
            }
            Err(e) => {
                error!("rebuild file index increment error:{}", e);
            }
        }
        let _ = save_file_with_not_change(file_url, &new_conent, true);
    }
}

async fn get_current_cache_file_url() -> String {
    let current_file_data = CURRENT_FILE_DATA.read().await;
    return current_file_data.file_url.clone();
}

/******************* agent侧临时处理索引的增量更新，但存在数据更新不及时问题.后续插件侧触发增量更新实际 end *******************/

pub static BUILD_SIMILARITY_IS_RUNNING: Lazy<Arc<AtomicBool>> = Lazy::new(|| Arc::new(AtomicBool::new(false)));
pub static BUILD_RELATE_IS_RUNNING: Lazy<Arc<AtomicBool>> = Lazy::new(|| Arc::new(AtomicBool::new(false)));

///代码补全默认rag实现框架，包含rag检索实现+缓存处理
/// 一般来讲各业务方只关注rag检索实现，缓存由此实现层统一处理
impl CodeCompleteRagFrameworkStrategy for CodeFuseCodeCompleteRagStrategy {
    ///获取相似代码片段
    async fn get_similarity_snippet(code_complete_request: &CodeCompletionRequestBean) -> Result<Option<Vec<ContextAwareInfo>>> {
        let similarity_cache_data = SIMILARITY_CACHE.read().await;
        let cache_file_url = &similarity_cache_data.file_url.clone();
        let cache_code_snippet_vec = &similarity_cache_data.similarity_snippet_vec;

        let rag_strategy: Box<dyn CodeCompleteRagExecutorStrategy> = get_rag_strategy(code_complete_request);

        //如果路径相同，用缓存数据计算相似片段
        if &code_complete_request.fileUrl == cache_file_url && cache_code_snippet_vec.len() > 0 {
            let result = rag_strategy.similarity_code_compare(cache_code_snippet_vec, code_complete_request);
            match result {
                Ok(similarity_code_info) => {
                    if similarity_code_info.len() > 0 {
                        return Ok(Some(similarity_code_info));
                    }
                    return Ok(None);
                }
                Err(e) => {
                    error!("get_similarity_snippet error:{}", e);
                    return Ok(None);
                }
            }
        }
        //如果路径不同，看是否正在进行扫描，如果正在进行，那么直接返回。如果不是，那么从索引数据构建缓存
        //step 1 检查扫描动作
        if CodeFuseScanStrategy::is_scan_ing() {
            return Ok(None);
        }
        //step 2 从索引数据构建缓存
        tokio::spawn(build_cache_data(code_complete_request.clone(), CacheTypeEnum::SIMILARITY));
        return Ok(None);
    }


    ///获取相关代码片段
    async fn get_related_snippet(code_complete_request: &CodeCompletionRequestBean) -> Result<(Option<Vec<ContextAwareInfo>>)> {
        let related_cache_data = RELATED_CACHE.read().await;
        let cache_file_url = &related_cache_data.file_url.clone();
        let cache_module_related_map = &related_cache_data.module_related_map;
        let cache_context = &related_cache_data.current_code_context;
        let rag_strategy: Box<dyn CodeCompleteRagExecutorStrategy> = get_rag_strategy(code_complete_request);


        //查询当前代码光标上下文数据
        let current_context_opt = rag_strategy.extract_prompt_declaration(code_complete_request);
        //如果当前没有上下文，那就没必要做相关提取，直接返回空
        if current_context_opt.is_none() {
            return Ok(None);
        }
        let current_context = current_context_opt.unwrap();
        // 检查是否命中缓存
        if &code_complete_request.fileUrl == cache_file_url {
            let mut result: Vec<ContextAwareInfo> = vec![];
            let language = code_complete_request.language.as_str();
            let import_data = rag_strategy.get_context_aware_from_cache(CacheRelatedModuleEnum::IMPORT, cache_module_related_map, cache_context, &current_context, language);
            let field_data = rag_strategy.get_context_aware_from_cache(CacheRelatedModuleEnum::FIELD, cache_module_related_map, cache_context, &current_context, language);
            let method_data = rag_strategy.get_context_aware_from_cache(CacheRelatedModuleEnum::METHOD, cache_module_related_map, cache_context, &current_context, language);
            let extend_data = rag_strategy.get_context_aware_from_cache(CacheRelatedModuleEnum::EXTENDS, cache_module_related_map, cache_context, &current_context, language);
            //只要有一个模块修改，触发增量更新
            let need_update_cache = import_data.0 || field_data.0 || method_data.0 || extend_data.0;
            if need_update_cache {
                tokio::spawn(build_cache_data(code_complete_request.clone(), CacheTypeEnum::RELATED));
            }

            for opt_vec in [import_data.1, field_data.1, method_data.1, extend_data.1].iter() {
                if let Some(vec) = opt_vec {
                    if !vec.is_empty() {
                        result.extend(vec.clone());
                    }
                }
            }
            if result.len() == 0 {
                return Ok(None);
            }
            return Ok(Some(result));
        }
        //如果路径不同，看是否正在进行扫描，如果正在进行，那么直接返回。如果不是，那么从索引数据构建缓存
        //step 1 检查扫描动作
        if CodeFuseScanStrategy::is_scan_ing() {
            return Ok(None);
        }
        //step 2 从索引数据构建缓存
        tokio::spawn(build_cache_data(code_complete_request.clone(), CacheTypeEnum::RELATED));
        return Ok(None);
    }
}


#[cfg(test)]
mod tests {
    // use super::*;
    // use crate::ast::typescript::parsers::prompt_declaration::get_ts_prompt_declaration;
    // use crate::function::build_cache_data::rebuild_index;
    // use agent_codefuse_service::make_workspace_path;
    // use agent_codefuse_service::tests_before_all;
    // use tokio::fs::read_to_string;

    #[tokio::test]
    async fn test_similarity() {
        // tests_before_all!();
        //
        // let folder_path = make_workspace_path!("fixtures/repo/core");
        // let file_url = make_workspace_path!("fixtures/repo/core/packages/comments/src/browser/comments.contribution.ts");
        //
        // let truncation_offset = 3604;
        //
        // let file_content = read_to_string(file_url).await.unwrap();
        // let (prompt, suffix) = file_content.split_at(truncation_offset);
        //
        // let code_completion_request = CodeCompletionRequestBean {
        //     projectUrl: Some(folder_path.to_string()),
        //     prompt: format!("{} \nthis.", prompt),
        //     suffix: String::from(suffix),
        //     language: String::from("typescript"),
        //     fileUrl: String::from(file_url),
        //     ideVersion: String::from("0.0.0"),
        //     recordInfo: None,
        //     similarSnippets: None,
        //     pluginVersion: String::from("0.0.0"),
        //     productType: String::from("agent_client"),
        //     sessionId: String::from("sessionId"),
        //     skipFilter: false,
        //     timeOut: 5000,
        //     userToken: String::from("userToken"),
        //     repo: None,
        //     relatedSnippets: None,
        //     fileNameSuffix: Some(String::from("ts")),
        // };
        //
        // // 首次执行要建索引
        // KV_CLIENT.clean();
        // rebuild_index(folder_path.to_string()).await.expect("rebuild index not work");
        //
        // build_cache_data(code_completion_request.clone(), CacheTypeEnum::RELATED).await;
        //
        // let result = CodeFuseCodeCompleteRagStrategy::get_related_snippet(&code_completion_request).await;
        // assert_eq!(result.is_ok(), true);
        //
        // let result = result.unwrap();
        // assert_eq!(result.is_some(), true);
        //
        // let result = result.unwrap();
        // println!("result: {:#?}", result);
        // result.iter().for_each(|item| {
        //     println!("path: {}", item.filePath);
        // });
        // println!("len : {}", result.len());
    }

    #[tokio::test]
    async fn test_ts_prompt_declaration() {
        // tests_before_all!();
        //
        // let folder_path = make_workspace_path!("fixtures/repo/core");
        // let file_url = make_workspace_path!("fixtures/repo/core/packages/comments/src/browser/comments.contribution.ts");
        //
        // let truncation_offset = 3604;
        //
        // let file_content = read_to_string(file_url).await.unwrap();
        // let (prompt, suffix) = file_content.split_at(truncation_offset);
        //
        // let code_completion_request = CodeCompletionRequestBean {
        //     projectUrl: Some(folder_path.to_string()),
        //     prompt: format!("{} \nthis.", prompt),
        //     suffix: String::from(suffix),
        //     language: String::from("typescript"),
        //     fileUrl: String::from(file_url),
        //     ideVersion: String::from("0.0.0"),
        //     recordInfo: None,
        //     similarSnippets: None,
        //     pluginVersion: String::from("0.0.0"),
        //     productType: String::from("agent_client"),
        //     sessionId: String::from("sessionId"),
        //     skipFilter: false,
        //     timeOut: 5000,
        //     userToken: String::from("userToken"),
        //     repo: None,
        //     relatedSnippets: None,
        //     fileNameSuffix: Some(String::from("ts")),
        // };
        //
        //
        // let result = get_ts_prompt_declaration(&code_completion_request);
        // assert_eq!(result.is_some(), true);
        //
        // let result = result.unwrap();
        // println!("result: {:#?}", result);
    }
}
use crate::utils::parallel_file_scan::{
    execute_scan_for_index_repository, finish_sync_build_task_status, is_build_process_timeout,
};
use crate::utils::path::get_all_file_by_url;
use agent_common_service::model::chat_model::ChatRelatedRequestBean;
use agent_db::config::runtime_config::AGENT_CONFIG;
use log::{debug, error, info};
use std::collections::{HashMap, HashSet};
use std::sync::Arc;

use crate::dialogue::codefuse_index_repository::CHUNK_CLIENT;
use crate::dialogue::repo_index_operator::{
    monitor_git_repo_branch, RepoStatusEnum,
};
use crate::function::chat_strategy::{
    ChatFlowContext, TaskNode, TaskNodeEnum, DEFAULT_INDEX_PROCESS_VALUE,
};
use crate::service::index_service::{IndexService, IndexServiceResponse};
use agent_db::dal::remote_client::IndexTypeEnum;
use agent_db::dal::remote_client::IndexTypeEnum::{CHUNK_CONTENT, FILE_CONTENT, METHOD_CONTENT};
use ignore::DirEntry;
use serde_json::Value;
use std::thread::sleep;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::Mutex;
use tokio::time::timeout;

// 构建任务节点
#[derive(Debug)]
pub struct BuildTaskNode {
    //任务状态
    pub status: Arc<Mutex<TaskNodeEnum>>,
    //启动时间
    pub start_time: Arc<Mutex<u128>>,
    //同步任务的状态(由于要让上游不卡顿，同步任务改成异步执行，但是底层实现不变。所以要sync_flag记录同步任务状态)
    pub sync_flag: Arc<Mutex<bool>>,
    //索引状态
    pub repo_status: Arc<Mutex<Option<RepoStatusEnum>>>,

    pub file_vec: Arc<Mutex<Option<Vec<DirEntry>>>>,
    pub task_type: u8,
}
impl BuildTaskNode {
    pub fn new(taskType: u8) -> Self {
        BuildTaskNode {
            status: Arc::new(Mutex::new(TaskNodeEnum::READY)),
            start_time: Arc::new(Mutex::new(0)),
            sync_flag: Arc::new(Mutex::new(false)),
            repo_status: Arc::new(Mutex::new(None)),
            file_vec: Arc::new(Mutex::new(None)),
            task_type: taskType,
        }
    }
    //修改开始时间
    async fn change_time(&self, new_time: u128) {
        let mut start_time = self.start_time.lock().await;
        *start_time = new_time;
    }
    //获取开始时间
    async fn get_start_time(&self) -> u128 {
        let start_time = self.start_time.lock().await;
        start_time.clone()
    }

    pub async fn change_sync_flag(&self, flag: bool) {
        let mut sync_flag = self.sync_flag.lock().await;
        *sync_flag = flag;
    }

    pub async fn change_repo_status(&self, repo_status: Option<RepoStatusEnum>) {
        let mut current_repo = self.repo_status.lock().await;
        *current_repo = repo_status;
    }

    pub async fn change_file_vec(&self, file_vec: Option<Vec<DirEntry>>) {
        let mut current_file_vec = self.file_vec.lock().await;
        *current_file_vec = file_vec;
    }

    pub async fn change_status(&self, status: TaskNodeEnum) {
        let mut current_status = self.status.lock().await;
        *current_status = status;
    }
    pub async fn get_repo_status(&self) -> RepoStatusEnum {
        let current_repo = self.repo_status.lock().await;
        current_repo.clone().unwrap_or(RepoStatusEnum::NOTHING)
    }

    async fn get_sync_flag(&self) -> bool {
        let sync_flag = self.sync_flag.lock().await;
        sync_flag.clone()
    }

    async fn get_status(&self) -> TaskNodeEnum {
        let status = self.status.lock().await;
        status.clone()
    }

    async fn get_file_vec(&self) -> Vec<DirEntry> {
        let file_vec = self.file_vec.lock().await;
        file_vec.clone().unwrap_or(vec![])
    }
}

impl TaskNode for BuildTaskNode {
    async fn start(
        &self,
        chat_related_request: &ChatRelatedRequestBean,
        chat_flow_context: &mut ChatFlowContext,
    ) -> TaskNodeEnum {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        self.change_time(current_time).await;

        let project_url = chat_related_request
            .projectUrl
            .clone()
            .unwrap_or("".to_string());
        let branch = chat_related_request
            .branch
            .clone()
            .unwrap_or("".to_string());

        let mut extra_data: HashMap<&str, String> = HashMap::new();
        let repo_status = monitor_git_repo_branch(&project_url, &branch).await;
        extra_data.insert("current_process_percent", repo_status.0.to_string());
        info!(
            "current_process_percent: {:?},current_repo_status: {:?}",
            repo_status.0, repo_status.1
        );
        self.change_repo_status(Some(repo_status.1.clone())).await;
        let t1 = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        let (all_vec, mut data_vec, _diff_vec, branch_name, repo_url) =
            get_all_file_by_url(&project_url, false).await;
        let t2 = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        info!("t1: {} ,len: {}", t2 - t1, all_vec.len());
        self.change_file_vec(Some(all_vec.clone())).await;
        if all_vec.len() == 0 {
            error!("{} no file found", project_url.clone());
            self.change_status(TaskNodeEnum::ERROR).await;
            let consume_time = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis()
                - current_time;
            chat_flow_context.build_consume_time_total = consume_time;
            TaskNodeEnum::ERROR
        } else {
            match repo_status.1 {
                RepoStatusEnum::CREATE | RepoStatusEnum::UPDATE => {
                    let build_start = SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_millis();

                    // 不等待 IndexService::build_index 执行结果，不阻塞当前调用线程
                    let project_url_clone = project_url.clone();
                    let branch_clone = branch.clone();
                    let update_handle = tokio::spawn(async move {
                        info!("UPDATE分支：开始执行 IndexService::build_index");
                        let response = IndexService::build_index(
                            project_url_clone.clone(),
                            branch_clone.clone(),
                            AGENT_CONFIG.concurrency_timeout_secs,
                        )
                        .await;
                        match &response {
                            IndexServiceResponse { success: true, .. } => {
                                info!("UPDATE分支：IndexService::build_index 成功完成: project_url={}, branch={}, response={:?}",
                                                  project_url_clone, branch_clone, response);
                            }
                            IndexServiceResponse { success: false, .. } => {
                                error!("UPDATE分支：IndexService::build_index 执行失败: project_url={}, branch={}, response={:?}",
                                                   project_url_clone, branch_clone, response);
                            }
                        }
                        response
                    });
                    if repo_status.1 == RepoStatusEnum::CREATE {
                        sleep(Duration::from_secs(5));
                        CHUNK_CLIENT.commit();
                    }

                    // 将任务句柄存储起来，确保任务不会被意外丢弃
                    // 注意：这里不等待结果，让任务在后台继续执行
                    info!("UPDATE分支：已启动后台索引构建任务，任务将在后台继续执行");
                    let sync_build_consume_time = SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_millis()
                        - build_start;
                    chat_flow_context.build_consume_time_sync = sync_build_consume_time;
                }
                _ => {}
            };
            let consume_time = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis()
                - current_time;
            chat_flow_context.build_consume_time_total = consume_time;
            self.change_status(TaskNodeEnum::END).await;
            TaskNodeEnum::END
        }
    }

    //判断当前节点是否超时
    async fn is_timeout(&self, current_millis: u128) -> bool {
        let start_time = self.get_start_time().await;
        current_millis - start_time > AGENT_CONFIG.chat_task_time_out
    }

    async fn reset(&self) {
        self.change_status(TaskNodeEnum::READY).await;
        self.change_time(0).await;
        self.change_sync_flag(false).await;
        self.change_repo_status(None).await;
        self.change_file_vec(None).await;
    }
}

///判断是否课执行下一步，如果不行，等待500ms再次判断，总计判断3次（1.5s)
async fn is_ready(status: Arc<Mutex<TaskNodeEnum>>) -> bool {
    for _ in 0..3 {
        // 获取锁并匹配枚举值
        let mut status_guard = status.lock().await;
        match *status_guard {
            TaskNodeEnum::READY => {
                *status_guard = TaskNodeEnum::RUNNING; // 修改状态
                return true; // 修改成功后返回 true
            }
            _ => sleep(Duration::from_millis(500)),
        }
    }
    false
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::ast::java::strategy::CodeFuseJavaAstStrategy;
    use crate::ast::js::constant::JS_VALID_EXTENSIONS;
    use crate::ast::js::strategy::CodeFuseJsAstStrategy;
    use crate::dialogue::codefuse_index_repository::{build_chunk_index, build_file_index};
    use crate::dialogue::repo_index_operator::is_need_interrupt_repo_build;
    use agent_common_service::service::code_ast_analysis::AstStrategy;
    use agent_db::config::agent_logger::init_logger;
    use agent_db::domain::code_chat_domain::CodefuseChunk;
    use agent_db::tools::common_tools::LINE_ENDING;
    use std::fs;
}

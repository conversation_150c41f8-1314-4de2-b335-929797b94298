use crate::dialogue::codefuse_index_repository::{CHUNK_CLIENT, FILE_CLIET, METHOD_CLIENT};
use crate::dialogue::data_struct::DataStatus;
use crate::dialogue::repo_index_operator::{
    save_git_repo_cache, save_repo_build_status, RepoStatusEnum,
};
use crate::function::chat_strategy::{
    calculate_index_build_progress, query_count_in_kv, ChatFlowContext, TaskNode, TaskNodeEnum,
    AUGMENT_TASK_TYPE,
};
use crate::function::chatflow::build_index_task::BuildTaskNode;
use crate::function::chatflow::query_index_task_v2::QueryIndexNodeV2;
use crate::function::chatflow::understand_query_task_v2::UnderStandQueryNodeV2;
use crate::utils::path::get_all_file_by_url;
use agent_common_service::model::chat_model::{
    Chat<PERSON>lowStatusEnum, Chat<PERSON><PERSON>tedRequestBean, IndexOperateRequestBean,
};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::remote_client::{QueryChangeDetail, QueryInProject};
use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;
use agent_db::remote::rpc_model::BaseResponse;
use log::{error, info, warn};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::Mutex;

//上下文key
pub const CONTEXT_QUERY_CHANGE_KEY: &str = "QUERY_CHANGE_RESULT";

/// 对话任务流控制中心
/// 目前仅支持单线程请求，后续可扩展为多线程（多个IDE窗口同时发请求)
pub struct ChatFlowTaskManagerV2 {
    //当前任务状态
    task_node: Mutex<ChatFlowStatusEnum>,
    //构建任务节点
    build_task_node: BuildTaskNode,
    //query理解节点
    understand_query_node: UnderStandQueryNodeV2,
    //检索节点
    search_node: QueryIndexNodeV2,
    //对话上下文
    chat_context: Mutex<ChatFlowContext>,
}

///对话任务流控制中心实现
impl ChatFlowTaskManagerV2 {
    pub async fn get_build_repo_status(&self) -> RepoStatusEnum {
        self.build_task_node.get_repo_status().await
    }

    pub fn new() -> Self {
        ChatFlowTaskManagerV2 {
            task_node: Mutex::new(ChatFlowStatusEnum::INIT),
            build_task_node: BuildTaskNode::new(AUGMENT_TASK_TYPE),
            understand_query_node: UnderStandQueryNodeV2::new(),
            search_node: QueryIndexNodeV2::new(),
            chat_context: Mutex::from(ChatFlowContext::new()),
        }
    }

    //清空上下文
    async fn clean_data(&self) {
        self.build_task_node.reset().await;
        self.understand_query_node.reset().await;
        self.search_node.reset().await;
        let mut chat_context = self.chat_context.lock().await;
        *chat_context = ChatFlowContext::new();
    }

    async fn change_status(&self, target_status: ChatFlowStatusEnum) {
        let mut status = self.task_node.lock().await;
        *status = target_status;
    }

    //判断是否和当前正在处理的仓库和分支一致
    pub async fn is_current_project_and_branch(
        &self,
        project_url: &String,
        branch: &String,
    ) -> bool {
        let chat_context = self.get_current_context().await;
        //无论如何更新下questionUid，没有任何影响
        //chat_context.question_uid = question_uid.clone();
        if project_url.clone() == chat_context.project_url && branch.clone() == chat_context.branch
        {
            true
        } else {
            false
        }
    }

    pub async fn finish_sync_build_task(&self, project_url: &String, branch: &String) {
        if !self
            .is_current_project_and_branch(project_url, branch)
            .await
        {
            //如果projectUrl和branch都已经变了，就没必要做其他处理了
            return;
        }
        self.build_task_node.change_sync_flag(true).await;
    }
    //获取当前上下文
    pub async fn get_current_context(&self) -> ChatFlowContext {
        let chat_context = self.chat_context.lock().await;
        chat_context.clone()
    }
    //改变上下文的基础信息
    async fn change_base_info(&self, project_url: &String, branch: &String) {
        let mut chat_context = self.chat_context.lock().await;
        chat_context.branch = branch.clone();
        chat_context.project_url = project_url.clone();
    }
    //改变上下文所有信息
    async fn change_all_context(&self, chat_flow_context: ChatFlowContext) {
        let mut chat_context = self.chat_context.lock().await;
        *chat_context = chat_flow_context;
    }

    //构建任务流程
    async fn build_process(
        &self,
        chat_related_request: &ChatRelatedRequestBean,
    ) -> (TaskNodeEnum, ChatFlowContext) {
        self.change_status(ChatFlowStatusEnum::BUILD_INDEX).await;
        let mut chat_context = self.get_current_context().await;
        let node_status = self
            .build_task_node
            .start(chat_related_request, &mut chat_context)
            .await;
        (node_status, chat_context)
    }
    //query改写流程
    async fn understand_query_process(
        &self,
        chat_related_request: &ChatRelatedRequestBean,
    ) -> (TaskNodeEnum, ChatFlowContext) {
        self.change_status(ChatFlowStatusEnum::UNDERSTAND_QUERY)
            .await;
        let mut chat_context = self.get_current_context().await;
        let node_status = self
            .understand_query_node
            .start(chat_related_request, &mut chat_context)
            .await;
        (node_status, chat_context)
    }
    //检索数据
    async fn query_index_process(
        &self,
        chat_related_request: &ChatRelatedRequestBean,
    ) -> (TaskNodeEnum, ChatFlowContext) {
        self.change_status(ChatFlowStatusEnum::QUERY_INDEX).await;
        let mut chat_context = self.get_current_context().await;
        let node_status = self
            .search_node
            .start(chat_related_request, &mut chat_context)
            .await;
        (node_status, chat_context)
    }
    //构建是否超时
    pub async fn is_build_process_timeout(&self, current_millis: u128) -> bool {
        self.build_task_node.is_timeout(current_millis).await
    }
    async fn get_status(&self, id: &String, query: &String) -> (ChatFlowStatusEnum) {
        let mut chat_context = self.chat_context.lock().await;
        if id.clone() == chat_context.question_uid && query.clone() == chat_context.query {
            let current_node = self.task_node.lock().await;
            // let current_status = self.task_status.lock().await;
            // (current_node.clone(),current_status.clone())
            current_node.clone()
        } else {
            self.build_task_node.reset().await;
            self.understand_query_node.reset().await;
            self.search_node.reset().await;
            // project_url 和 branch保留
            let project_url = chat_context.project_url.clone();
            let branch = chat_context.branch.clone();
            *chat_context = ChatFlowContext::new();
            chat_context.question_uid = id.clone();
            chat_context.project_url = project_url;
            chat_context.branch = branch;
            chat_context.query = query.clone();
            ChatFlowStatusEnum::CHANGE
        }
    }

    pub async fn process(
        &self,
        chat_related_request: &ChatRelatedRequestBean,
    ) -> (ChatFlowStatusEnum, TaskNodeEnum, Option<ChatFlowContext>) {
        let start_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        let current_question_uid = &chat_related_request
            .questionUid
            .clone()
            .unwrap_or("".to_string());
        let current_project_url = &chat_related_request
            .projectUrl
            .clone()
            .unwrap_or("".to_string());
        let current_branch = &chat_related_request
            .branch
            .clone()
            .unwrap_or("".to_string());
        let is_new = self
            .is_current_project_and_branch(current_project_url, current_branch)
            .await;
        if !is_new {
            warn!(
                "project or branch has changed, clean data. question: {}, project: {}, branch: {}",
                current_question_uid, current_project_url, current_branch
            );
            self.clean_data().await;
            self.change_base_info(current_project_url, current_branch)
                .await;
        }
        let build_node_result = self.build_process(chat_related_request).await;
        self.change_status(ChatFlowStatusEnum::UNDERSTAND_QUERY).await;
        self.change_all_context(build_node_result.1).await;
        let understand_node_result = self.understand_query_process(chat_related_request).await;
        self.change_status(ChatFlowStatusEnum::QUERY_INDEX).await;
        self.change_all_context(understand_node_result.1).await;
        let search_result = self.query_index_process(chat_related_request).await;
        self.change_status(ChatFlowStatusEnum::ANSWER).await;
        self.change_all_context(search_result.1).await;
        let mut chat_context = self.get_current_context().await;
        let result = chat_context.clone();
        let consume_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis()
            - start_time;
        info!("chat flow end,current questionId: {:?}, query: {:?}, status: {:?},result: {:?},consume time: {}",chat_related_request.questionUid,
            chat_related_request.query,self.task_node.lock().await.clone(),result,consume_time);
        let mut status = self.task_node.lock().await;
        (status.clone(), TaskNodeEnum::END, Some(result))

        // let current_task = self.get_status(current_question_uid,&chat_related_request.query).await;
        // info!("questionId: {:?} , project: {:?}, branch: {:?}, current_task: {:?}",current_question_uid,current_project_url,current_branch, current_task);
        //
        // let result = match current_task {
        //     //INIT或者CHANGE都视为重新跑对话流程，返回值只会有BUILD_INDEX,UNDERSTAND_QUERY,ERROR状态
        //     ChatFlowStatusEnum::INIT | ChatFlowStatusEnum::CHANGE|ChatFlowStatusEnum::BUILD_INDEX => {
        //         // 1. 如果初始化状态收到请求,则进行构建索引操作，同时，将状态置为BUILD_INDEX
        //         let build_node_result = self.build_process(chat_related_request).await;
        //         let context = build_node_result.1;
        //         self.change_all_context(context.clone()).await;
        //         match build_node_result.0 {
        //             TaskNodeEnum::RUNNING => {
        //                 let mut status = self.task_node.lock().await;
        //                 (status.clone(), TaskNodeEnum::RUNNING, Some(context))
        //             }
        //             TaskNodeEnum::END => {
        //                 //索引构建完成，直接走下一个流程,但这里不用关心节点执行状态，待下次插件轮询时候会重新调用understand_query_process获取状态
        //                 self.change_status(ChatFlowStatusEnum::UNDERSTAND_QUERY).await;
        //                 let result = self.get_current_context().await;
        //                 let mut status = self.task_node.lock().await;
        //                 (status.clone(), TaskNodeEnum::END, Some(result))
        //
        //             }
        //             _ => {
        //                 error!("build process status error.current status: {:?}", current_task);
        //                 let mut status = self.task_node.lock().await;
        //                 (status.clone(), TaskNodeEnum::ERROR, None)
        //             }
        //         }
        //     }
        //
        //     ChatFlowStatusEnum::UNDERSTAND_QUERY => {
        //         // 2. query改写
        //         let understand_node_result =
        //             self.understand_query_process(chat_related_request).await;
        //         let context = understand_node_result.1;
        //         self.change_all_context(context.clone()).await;
        //         match understand_node_result.0 {
        //             TaskNodeEnum::RUNNING => {
        //                 let mut status = self.task_node.lock().await;
        //                 (status.clone(), TaskNodeEnum::RUNNING, Some(context))
        //             }
        //             TaskNodeEnum::END => {
        //                 //query理解完成，直接走下一个流程,但这里不用关心节点执行状态，待下次插件轮询时候会重新调用query_index_process获取状态
        //                 self.change_status(ChatFlowStatusEnum::QUERY_INDEX).await;
        //                 let result = self.get_current_context().await;
        //                 let mut status = self.task_node.lock().await;
        //                 (status.clone(), TaskNodeEnum::END, Some(result))
        //             }
        //             _ => {
        //                 let mut status = self.task_node.lock().await;
        //                 let current_task = status.clone();
        //                 error!(
        //                     "understand query process status error.current status: {:?}",
        //                     current_task
        //                 );
        //                 (current_task.clone(), TaskNodeEnum::ERROR, None)
        //             }
        //         }
        //     }
        //     ChatFlowStatusEnum::QUERY_INDEX => {
        //         // 3. 检索
        //         let search_result = self.query_index_process(chat_related_request).await;
        //         let context = search_result.1;
        //         self.change_all_context(context.clone()).await;
        //         match search_result.0 {
        //             TaskNodeEnum::RUNNING => {
        //                 let mut status = self.task_node.lock().await;
        //                 (status.clone(), TaskNodeEnum::RUNNING, Some(context))
        //             }
        //             TaskNodeEnum::END => {
        //                 //检索完成，将状态置为ANSWER，直接返回context，
        //                 self.change_status(ChatFlowStatusEnum::ANSWER).await;
        //                 let result = self.get_current_context().await;
        //                 let mut status = self.task_node.lock().await;
        //                 (status.clone(), TaskNodeEnum::END, Some(result))
        //             }
        //             _ => {
        //                 let mut status = self.task_node.lock().await;
        //                 let current_task = status.clone();
        //                 error!(
        //                     "query index process status error.current status: {:?}",
        //                     current_task
        //                 );
        //                 (current_task.clone(), TaskNodeEnum::ERROR, None)
        //             }
        //         }
        //     }
        //     ChatFlowStatusEnum::ANSWER => {
        //         // 检索完成，直接返回context即可,但这个状态不会持续多次，如果持续多次那么插件侧调用逻辑有问题。所以这里打印下日志
        //         let mut chat_context = self.get_current_context().await;
        //         let result = chat_context.clone();
        //         let mut status = self.task_node.lock().await;
        //         (status.clone(), TaskNodeEnum::END,Some(result))
        //     }
        //     _ => {
        //         // 4. 未知状态
        //         error!("unknown status.current status: {:?}", current_task);
        //         let mut status = self.task_node.lock().await;
        //         (status.clone(), TaskNodeEnum::ERROR,None)
        //     }
        // };
        // info!("chat flow end,current questionId: {:?}, query: {:?}, status: {:?},result: {:?},consume time: {}",chat_related_request.questionUid,
        //     chat_related_request.query,self.task_node.lock().await.clone(),result,start_time.elapsed().as_millis());
        // result
    }

    async fn change_question_uid(&self, current_question_uid: &String) {
        //修改question_uid
        let mut chat_context = self.chat_context.lock().await;
        chat_context.question_uid = current_question_uid.clone();
    }
}

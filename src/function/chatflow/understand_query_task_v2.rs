use crate::function::chat_strategy::{ChatFlowContext, TaskNode, TaskNodeEnum};
use crate::function::chatflow::build_index_task::BuildTaskNode;
use agent_common_service::model::chat_model::ChatRelatedRequestBean;
use agent_db::dal::remote_client::{chat_query_change, chat_query_rewrite, QueryChangeRequestBean};
use log::{error, info};
use serde_json::Value;
use std::collections::HashMap;
use std::sync::Arc;
use std::thread::sleep;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::Mutex;

//query理解
pub struct UnderStandQueryNodeV2 {
    status: Arc<Mutex<TaskNodeEnum>>,
}

impl UnderStandQueryNodeV2 {
    pub fn new() -> Self {
        UnderStandQueryNodeV2 {
            status: Arc::new(Mutex::new(TaskNodeEnum::READY)),
        }
    }
}

impl TaskNode for UnderStandQueryNodeV2 {
    async fn start(
        &self,
        chat_related_request: &ChatRelatedRequestBean,
        chat_flow_context: &mut ChatFlowContext,
    ) -> TaskNodeEnum {

        let current_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() ;
        let query_change_request = QueryChangeRequestBean {
            userToken: chat_related_request.userToken.clone().unwrap_or("".to_string()),
            productType: chat_related_request.productType.clone().unwrap_or("".to_string()),
            question: chat_related_request.query.clone(),
        };
        let query_change = chat_query_rewrite(&query_change_request).await;
        let query_change_data = query_change.data;
        if query_change.errorCode != 0 || query_change_data.is_none() {
            error!("query change error:{:?}", query_change.errorMsg);
            change_status(self.status.clone(), TaskNodeEnum::ERROR).await;
            let consume_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() - current_time;
            chat_flow_context.understand_consume_time = consume_time;
            TaskNodeEnum::ERROR
        }else{
            change_status(self.status.clone(), TaskNodeEnum::END).await;
            let consume_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() - current_time;
            chat_flow_context.query_change_detail = query_change_data.clone();
            chat_flow_context.understand_consume_time = consume_time;
            TaskNodeEnum::END
        }

    }

    async fn is_timeout(&self, current_millis: u128) -> bool {
        false
    }

    async fn reset(&self) {
        let mut status_guard = self.status.lock().await;
        *status_guard = TaskNodeEnum::READY; // 修改状态
    }
}

///判断是否课执行下一步，如果不行，等待500ms再次判断，总计判断3次（1.5s)
async fn is_ready(status: Arc<Mutex<TaskNodeEnum>>) -> bool {
    for _ in 0..3 {
        // 获取锁并匹配枚举值
        let mut status_guard = status.lock().await;
        match *status_guard {
            TaskNodeEnum::READY => {
                *status_guard = TaskNodeEnum::RUNNING; // 修改状态
                return true; // 修改成功后返回 true
            }
            _ => sleep(Duration::from_millis(500)),
        }
    }
    false
}

async fn change_status(status: Arc<Mutex<TaskNodeEnum>>, next_status: TaskNodeEnum) {
    let mut status_guard = status.lock().await;
    *status_guard = next_status; // 修改状态
}

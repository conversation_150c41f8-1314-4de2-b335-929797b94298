use crate::dialogue::codefuse_index_repository::{query_index_entry, CHUNK_CLIENT, FILE_CLIET};
use crate::dialogue::data_struct::{QueryChatItemResult, QueryChatRelatedData, QueryIndexRequest};
use crate::dialogue::misc_util::{
    convert_from_codefusechunk_to_model, convert_from_codefusemethod_to_model, doc_to_chunk,
    doc_to_file, get_simplify_method_vec, query_method_list, CLASS_REGEX,
};
use crate::function::chat_strategy::{ChatFlowContext, TaskNode, TaskNodeEnum};
use agent_common_service::model::chat_model::ChatRelatedRequestBean;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::remote_client::{
    mquery_filter, mquery_rerank,  IndexTypeEnum, QueryChangeDetail,
    QueryChangeRequestBean,
};
use agent_db::domain::code_chat_domain::{
    Chat<PERSON><PERSON>tedCodeModel, CodefuseChunk, CodefuseFile, CodefuseMethod,
};
use agent_db::remote::rpc_model::build_success_response;
use agent_db::tools::common_tools::LINE_ENDING;
use futures::future::join_all;
use lazy_static::lazy_static;
use log::{error, info, warn};
use once_cell::sync::Lazy;
use regex::Regex;
use serde_json::Value;
use std::collections::{HashMap, HashSet};
use std::fs::File;
use std::io::{BufRead, BufReader};
use std::sync::Arc;
use std::thread::sleep;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tantivy::tokenizer::{TextAnalyzer, TokenizerManager};
use tokio::sync::Mutex;

//query检索
pub struct QueryIndexNodeV2 {
    status: Arc<Mutex<TaskNodeEnum>>,
}
impl QueryIndexNodeV2 {
    pub fn new() -> Self {
        QueryIndexNodeV2 {
            status: Arc::new(Mutex::new(TaskNodeEnum::READY)),
        }
    }
}

// 使用lazy_static来初始化静态变量
lazy_static! {
    static ref INDEX_TYPE: Mutex<Vec<IndexTypeEnum>> = Mutex::new(Vec::new());
}
pub fn handler_search_chunk_data(file_group_result: &mut HashMap<String, Vec<ChatRelatedCodeModel>>, repeat_data_map: &mut HashMap<String,Vec<String>>, chunk_data: CodefuseChunk) -> bool {

    let empty_vec: Vec<String> = Vec::new();
    let repeat_data = repeat_data_map.get(&chunk_data.file_url).unwrap_or(&empty_vec);
    let file_group_vec = file_group_result
        .entry(chunk_data.file_url.clone())
        .or_insert(vec![]);
    if repeat_data.contains(&chunk_data.content) {
        warn!("repeat data,skip this chunk");
        return true;
    }
    file_group_vec.push(ChatRelatedCodeModel {
        relativePath: chunk_data.file_url,
        snippet: chunk_data.content,
        startLine: chunk_data.start_line as usize,
        endLine: chunk_data.end_line as usize,
        source: None,
        title: None,
    });

    false
}

fn constract_bm25_query(chat_flow_context: &&mut ChatFlowContext,query:&String) -> String {
    let question_change_detail_opt = &chat_flow_context.query_change_detail;
    let mut combined_string = String::new();
    combined_string.push_str(query);
    if let Some(question_change_detail) = question_change_detail_opt {
        if let Some(keyword_en_value) = &question_change_detail.keyword_en {
            if !combined_string.is_empty() {
                combined_string.push(' ');
            }
            combined_string.push_str(
                &keyword_en_value
                    .iter()
                    .cloned()
                    .collect::<Vec<String>>()
                    .join(" "),
            );
        }
    }
    combined_string
}

// 获取所有元素
pub async fn get_all_data() -> Option<Vec<IndexTypeEnum>> {
    let vec = INDEX_TYPE.lock().await;
    Some(vec.clone())
}

// 更新指定索引的元素
pub async fn update_data(data: Vec<IndexTypeEnum>) -> Result<(), String> {
    let mut vec = INDEX_TYPE.lock().await;
    vec.clear();
    vec.extend(data);
    Ok(())
}

impl TaskNode for QueryIndexNodeV2 {
    async fn start(
        &self,
        chat_related_request: &ChatRelatedRequestBean,
        chat_flow_context: &mut ChatFlowContext,
    ) -> TaskNodeEnum {
        let query = chat_related_request.query.clone();
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        //step 1 ：先处理query改写结果，v2版本BM25用改写结果，向量用原query
        let combined_string = constract_bm25_query(&chat_flow_context,&query);

        //step 2: v2版本只有chunk级别的BM25+向量。
        let content_field = "content".to_string();
        let project_url = chat_related_request
            .projectUrl
            .clone()
            .unwrap_or("".to_string());
        let user_token = chat_related_request
            .userToken
            .clone()
            .unwrap_or("".to_string());
        let product_type = chat_related_request
            .productType
            .clone()
            .unwrap_or("".to_string());

        let bm25_chunk_result = CHUNK_CLIENT.query_term_with_field(
            &content_field,
            &combined_string,
            &project_url,
            AGENT_CONFIG.index_search_top_num,
        );
        let query_vec = vec![query.clone()];
        let chunk_vector_result = query_index_entry(
            HashSet::new(),
            query_vec,
            HashSet::from([IndexTypeEnum::CHUNK_VECTOR]),
            &user_token,
            &project_url,
            &product_type,
            AGENT_CONFIG.index_search_top_num,
        );
        let query_search = tokio::join!(bm25_chunk_result, chunk_vector_result);

        let search_end = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        chat_flow_context.search_time = search_end-current_time;

        //step 3: 处理检索结果
        let mut file_group_result: HashMap<String, Vec<ChatRelatedCodeModel>> =
            HashMap::new();
        //去重，key: file_url, value: content列表
        let mut repeat_data_map:HashMap<String,Vec<String>> = HashMap::new();

        //处理BM25结果
        match query_search.0 {
            Ok(chunk_result) => {
                chat_flow_context.bm25_search_time = chunk_result.1;
                for item in chunk_result.0 {
                    let chunk_data = doc_to_chunk(item.0, item.1).data;
                    if handler_search_chunk_data(&mut file_group_result, &mut repeat_data_map, chunk_data) { continue; }
                }
            }
            Err(e) => {
                error!("search chunk error:{}", e);
            }
        }
        //处理向量结果
        match query_search.1 {
            None => {
                warn!(
                            "query: {}, chunk vector result is none",
                            chat_related_request.query
                        );
            }
            Some(query_related_data) => {
                chat_flow_context.vector_search_time = query_related_data.search_time.unwrap_or(0);
                let chunk_vector_data_opt = query_related_data.chunk_vector_data;
                match chunk_vector_data_opt {
                    None => {
                        warn!(
                                    "query: {}, chunk vector result is none",
                                    chat_related_request.query
                                );
                    }
                    Some(chunk_vector_data_vec) => {
                        for query_chat_item_result in chunk_vector_data_vec {
                            let chunk_data = query_chat_item_result.search_result.data;
                            if handler_search_chunk_data(&mut file_group_result, &mut repeat_data_map, chunk_data) { continue; }
                        }
                    }
                }
            }
        }
        //排序，value值按从小到大排序
        sort_file_group_result(&mut file_group_result);

        let handle_search_result_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        chat_flow_context.handler_search_time = handle_search_result_time-search_end;


        //step 4: 过滤,每个文件一个线程，多文件同时过滤
        let all_value: Vec<ChatRelatedCodeModel> = file_group_result
            .values()
            .flat_map(|v| v.iter().cloned())
            .collect();
        let filter_group_value: Vec<Vec<ChatRelatedCodeModel>> =
            all_value.chunks(5).map(|chunk| chunk.to_vec()).collect();

        // 主处理逻辑
        let mut filter_item_result_vec = Vec::new();
        let BATCH_SIZE = 5;
        // 将 filter_group_value 切分成大小为 BATCH_SIZE 的批次
        for chunk in filter_group_value.chunks(BATCH_SIZE) {
            // 为每个 filter_item 创建异步任务
            let futures = chunk.iter().map(|filter_item| {
                let query = chat_related_request.query.clone();
                let filter_item = filter_item.clone();
                async move {
                    let result = mquery_filter(query, filter_item.clone()).await;
                    if result.errorCode != 0 || result.data.is_none() {
                        warn!("filter result is null: {:?}", result);
                    }
                    (result, filter_item)
                }
            });

            // 并发执行所有任务
            let results = join_all(futures).await;

            // 处理结果
            for (result, filter_item) in results {
                filter_item_result_vec.extend(result.data.unwrap_or(filter_item));
            }
        }

        let filter_end_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        chat_flow_context.filter_time = filter_end_time-handle_search_result_time;

        //过滤结果再次按文件级别聚合
        let mut filter_group_result: HashMap<String, Vec<ChatRelatedCodeModel>> =
            HashMap::new();

        for filter_item in filter_item_result_vec {
            let path = &filter_item.relativePath;
            let file_group_vec =
                filter_group_result.entry(path.clone()).or_insert(vec![]);
            file_group_vec.push(filter_item);
        }
        //step 5 : 文件级别rerank
        let mut rerank_param = vec![];
        for filter_result in filter_group_result {
            let mut filter_result_vec = filter_result.1;
            if filter_result_vec.len() == 0 {
                warn!("filter result is empty,file_url {:?}", filter_result.0)
            } else {
                // 按 startLine 排序
                filter_result_vec.sort_by(|a, b| a.startLine.cmp(&b.startLine));

                // 合并重叠的 chunks
                let merged_chunks = merge_overlapping_chunks(filter_result_vec);

                // 将合并后的 chunks 添加到 rerank_param
                rerank_param.extend(merged_chunks);
            }
        }

        // let rerank = mquery_rerank(query, rerank_param).await;
        let end_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();

        chat_flow_context.rerank_time = end_time-filter_end_time;
        chat_flow_context.query_index_time = end_time-current_time;


        chat_flow_context.result = Some(rerank_param);
        //设置成最新状态
        let mut status_guard = self.status.lock().await;
        *status_guard = TaskNodeEnum::END;
        TaskNodeEnum::END
    }
    //查询索引暂时不需要超时时间，默认返回false
    async fn is_timeout(&self, current_millis: u128) -> bool {
        false
    }

    async fn reset(&self) {
        let mut status_guard = self.status.lock().await;
        *status_guard = TaskNodeEnum::READY; // 修改状态
    }
}

///判断是否课执行下一步，如果不行，等待500ms再次判断，总计判断3次（1.5s)
async fn is_ready(status: Arc<tokio::sync::Mutex<TaskNodeEnum>>) -> bool {
    for _ in 0..3 {
        // 获取锁并匹配枚举值
        let mut status_guard = status.lock().await;
        match *status_guard {
            TaskNodeEnum::READY => {
                *status_guard = TaskNodeEnum::RUNNING; // 修改状态
                return true; // 修改成功后返回 true
            }
            _ => sleep(Duration::from_millis(500)),
        }
    }
    false
}

pub fn sort_file_group_result(file_group_result: &mut HashMap<String, Vec<ChatRelatedCodeModel>>) {
    // 遍历 HashMap 中的每个 Vec，对其进行排序
    for vec in file_group_result.values_mut() {
        vec.sort_by(|a, b| a.startLine.cmp(&b.startLine));
    }
}

/// 合并重叠的 chunks
/// 参考 Java 代码逻辑，按文件级别聚合并对 startLine 从小到大排序后，对 chunk 做 merge 处理
pub fn merge_overlapping_chunks(chunks: Vec<ChatRelatedCodeModel>) -> Vec<ChatRelatedCodeModel> {
    if chunks.is_empty() {
        return Vec::new();
    }

    let mut merged = Vec::new();
    let mut current = chunks[0].clone();

    for i in 1..chunks.len() {
        let next = &chunks[i];

        // 如果当前 chunk 与下一个 chunk 重叠，合并它们
        if current.endLine >= next.startLine {
            current.snippet = merge_snippets(&current, next);
            current.endLine = current.endLine.max(next.endLine);
        } else {
            // 如果不重叠，添加当前 chunk 到结果中，并将下一个 chunk 设为当前 chunk
            merged.push(current);
            current = next.clone();
        }
    }

    // 添加最后一个处理的 chunk
    merged.push(current);

    merged
}

/// 合并两个 chunk 的 snippet 内容
fn merge_snippets(chunk1: &ChatRelatedCodeModel, chunk2: &ChatRelatedCodeModel) -> String {
    let lines1: Vec<&str> = chunk1.snippet.split('\n').collect();
    let lines2: Vec<&str> = chunk2.snippet.split('\n').collect();

    let mut merged_snippet = String::new();

    // 添加第一个 chunk 的所有行
    for line in &lines1 {
        merged_snippet.push_str(line);
        merged_snippet.push('\n');
    }

    // 添加第二个 chunk 中不重叠的行
    let overlap_start = if chunk1.endLine >= chunk2.startLine {
        (chunk1.endLine - chunk2.startLine + 1).max(0) as usize
    } else {
        0
    };

    for i in overlap_start..lines2.len() {
        merged_snippet.push_str(lines2[i]);
        merged_snippet.push('\n');
    }

    merged_snippet.trim_end().to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_merge_overlapping_chunks() {
        // 测试用例1: 两个重叠的 chunks
        let chunk1 = ChatRelatedCodeModel {
            relativePath: "test.rs".to_string(),
            snippet: "line1\nline2\nline3".to_string(),
            startLine: 1,
            endLine: 3,
            source: None,
            title: None,
        };

        let chunk2 = ChatRelatedCodeModel {
            relativePath: "test.rs".to_string(),
            snippet: "line3\nline4\nline5".to_string(),
            startLine: 3,
            endLine: 5,
            source: None,
            title: None,
        };

        let chunks = vec![chunk1, chunk2];
        let merged = merge_overlapping_chunks(chunks);

        assert_eq!(merged.len(), 1);
        assert_eq!(merged[0].startLine, 1);
        assert_eq!(merged[0].endLine, 5);
        assert_eq!(merged[0].snippet, "line1\nline2\nline3\nline4\nline5");
    }

    #[test]
    fn test_merge_non_overlapping_chunks() {
        // 测试用例2: 两个不重叠的 chunks
        let chunk1 = ChatRelatedCodeModel {
            relativePath: "test.rs".to_string(),
            snippet: "line1\nline2".to_string(),
            startLine: 1,
            endLine: 2,
            source: None,
            title: None,
        };

        let chunk2 = ChatRelatedCodeModel {
            relativePath: "test.rs".to_string(),
            snippet: "line4\nline5".to_string(),
            startLine: 4,
            endLine: 5,
            source: None,
            title: None,
        };

        let chunks = vec![chunk1, chunk2];
        let merged = merge_overlapping_chunks(chunks);

        assert_eq!(merged.len(), 2);
        assert_eq!(merged[0].startLine, 1);
        assert_eq!(merged[0].endLine, 2);
        assert_eq!(merged[1].startLine, 4);
        assert_eq!(merged[1].endLine, 5);
    }

    #[test]
    fn test_merge_empty_chunks() {
        // 测试用例3: 空的 chunks 列表
        let chunks = vec![];
        let merged = merge_overlapping_chunks(chunks);
        assert_eq!(merged.len(), 0);
    }
}



fn query_class_construct(
    file_url: &String,
    detail_method_vec: &Vec<CodefuseMethod>,
    method_info_vec: &Vec<CodefuseMethod>,
) -> Option<CodefuseFile> {
    let mut query_param = HashMap::new();
    query_param.insert("id".to_string(), file_url.clone());
    let file_vec_result = FILE_CLIET.query(&query_param, 1);

    match file_vec_result {
        Ok(file_data_vec) => {
            if file_data_vec.len() == 1 {
                for file_data in file_data_vec {
                    let file_info = doc_to_file(file_data.0, file_data.1).data;
                    if !file_info.id.ends_with(".java") {
                        continue;
                    }
                    //只有命中class结构声明才去组装class结构
                    if let Some(captures) = Regex::new(CLASS_REGEX)
                        .unwrap()
                        .captures(&file_info.content)
                    {
                        let mut result = String::new();
                        if file_info.annotate.len() > 0 {
                            result.push_str(&file_info.annotate);
                            result.push_str(LINE_ENDING)
                        }
                        if let Some(class_type) = captures.get(2) {
                            let class_declaration = class_type.as_str();
                            result.push_str(&class_declaration.to_string());
                            result.push_str(LINE_ENDING);
                            //1:detail_method_vec和method_info_vec都是0，说明检索到的chunk没命中任何函数，那么尝试重新查询所有函数

                            if detail_method_vec.len() == 0 && method_info_vec.len() == 0 {
                                let method_vec = query_method_list(&file_info.id, 0, 0);
                                let simple_method_vec = get_simplify_method_vec(&method_vec.1);
                                match simple_method_vec {
                                    Some(method_vec) => {
                                        for method in method_vec {
                                            result.push_str(&method.content);
                                            result.push_str(LINE_ENDING);
                                        }
                                    }
                                    None => {
                                        info!("query_class_construct,simple_method_vec is None.file_url:{:?}",file_url);
                                    }
                                }
                            } else {
                                for method_info in detail_method_vec {
                                    result.push_str(&method_info.content);
                                    result.push_str(LINE_ENDING);
                                }
                                let simple_method_vec = get_simplify_method_vec(&method_info_vec);
                                match simple_method_vec {
                                    Some(method_vec) => {
                                        for method in method_vec {
                                            result.push_str(&method.content);
                                            result.push_str(LINE_ENDING);
                                        }
                                    }
                                    None => {
                                        info!("query_class_construct,simple_method_vec is None.file_url:{:?}",file_url);
                                    }
                                }
                            }
                            result.push_str("}");

                            let result = CodefuseFile {
                                id: file_url.clone(),
                                content: result.clone(),
                                summary: "".to_string(),
                                annotate: "".to_string(),
                                project_url: "".to_string(),
                                branch: "".to_string(),
                                hash: "".to_string(),
                                feature: "".to_string(),
                                summary_keyword: Vec::new(),
                                has_summary: 0,
                                has_summary_vector: 0,
                            };
                            return Some(result);
                        }
                    }
                }
            } else {
                error!(
                    "query_class_construct,size not 1:{:?}，file_url : {}",
                    file_data_vec, file_url
                )
            }
        }
        Err(e) => {
            error!("query_class_construct error:{:?}", e);
        }
    }
    None
}



///补充查询类型是codefusemethod的数据
pub fn addition_for_codefusemethod_data(
    method_content_data_value: &mut Vec<QueryChatItemResult<CodefuseMethod>>,
) {
    for method_content_result in method_content_data_value.iter_mut() {
        let item = &method_content_result.search_result.data;
        let method_info = query_method_list(&item.file_url, item.start_line, item.end_line);
        let detail_method_vec = &method_info.0;
        let simple_method_vec = &method_info.1;
        method_content_result.extend_file_result =
            query_class_construct(&item.file_url, detail_method_vec, simple_method_vec);
    }
}
///补充查询类型是codefusechunk的数据
pub fn addition_for_codefusechunk_data(
    chunk_content_data_value: &mut Vec<QueryChatItemResult<CodefuseChunk>>,
) {
    for chunk_content_result in chunk_content_data_value.iter_mut() {
        let item = &chunk_content_result.search_result.data;
        let method_info = query_method_list(&item.file_url, item.start_line, item.end_line);
        let detail_method_vec = &method_info.0;
        let simple_method_vec = &method_info.1;
        if detail_method_vec.len() > 0 {
            chunk_content_result.extend_method_result = Some(detail_method_vec.clone());
        }
        chunk_content_result.extend_file_result =
            query_class_construct(&item.file_url, detail_method_vec, simple_method_vec);
    }
}

fn convert_codefusemethod_to_model(
    method_content_data: Vec<QueryChatItemResult<CodefuseMethod>>,
    valid_method_content_container: &mut Vec<ChatRelatedCodeModel>,
    keys: &mut HashSet<String>,
) {
    if method_content_data.len() > 0 {
        for item in method_content_data {
            let codefuse_method = item.search_result.data;
            if !keys.contains(&codefuse_method.id) {
                keys.insert(codefuse_method.id.clone());
                let chat_related_code_model = convert_from_codefusemethod_to_model(codefuse_method);
                valid_method_content_container.push(chat_related_code_model);
            }
        }
    }
}
fn convert_codefusechunk_to_model(
    chunk_content_data: Vec<QueryChatItemResult<CodefuseChunk>>,
    valid_method_content_container: &mut Vec<ChatRelatedCodeModel>,
    keys: &mut HashSet<String>,
) {
    if chunk_content_data.len() > 0 {
        for item in chunk_content_data {
            match item.extend_method_result {
                Some(extend_method_result) => {
                    if extend_method_result.len() > 0 {
                        for codefuse_method in extend_method_result {
                            let codefuse_method = codefuse_method.clone();
                            if !keys.contains(&codefuse_method.id) {
                                keys.insert(codefuse_method.id.clone());
                                let chat_related_code_model =
                                    convert_from_codefusemethod_to_model(codefuse_method);
                                valid_method_content_container.push(chat_related_code_model);
                            }
                        }
                    }
                }
                None => {
                    ///如果没有extend_method_result,用chunk的数据进行替代
                    let codefuse_chunk_clone = item.search_result.data.clone();
                    if !keys.contains(&codefuse_chunk_clone.id) {
                        keys.insert(codefuse_chunk_clone.id.clone());
                        let chat_related_code_model =
                            convert_from_codefusechunk_to_model(codefuse_chunk_clone);
                        valid_method_content_container.push(chat_related_code_model);
                    }
                }
            }
        }
    }
}


///将检索结果填充到返回值
/// 后续删除
pub fn search_result_to_model(query_data: QueryChatRelatedData) -> Vec<ChatRelatedCodeModel> {
    //存储有效的ChatRelatedCodeModel
    let mut valid_method_content_container = Vec::new();
    //存储已经存在的key, 避免重复添加
    let mut keys = HashSet::<String>::new();
    match query_data.file_content_data {
        Some(file_content_data) => {
            //todo!()
        }
        None => {}
    }
    match query_data.method_content_data {
        Some(method_content_data) => {
            convert_codefusemethod_to_model(method_content_data, &mut valid_method_content_container, &mut keys);
        }
        None => {}
    }
    match query_data.chunk_content_data {
        Some(chunk_content_data) => {
            convert_codefusechunk_to_model(chunk_content_data, &mut valid_method_content_container, &mut keys);
        }
        None => {}
    }
    match query_data.chunk_vector_data {
        Some(chunk_content_data) => {
            convert_codefusechunk_to_model(chunk_content_data, &mut valid_method_content_container, &mut keys);
        }
        None => {}
    }
    valid_method_content_container
}

///将query改写结果转换为key_word_set和question_set
/// 后续删除
pub fn convert_query_change_result(question: &String, query_change_result: QueryChangeDetail) -> (HashSet<String>, HashSet<String>) {
    let mut key_word_set = HashSet::new();
    if let Some(keyword_zh_value) = query_change_result.keyword_zh {
        key_word_set.extend(keyword_zh_value);
    }
    if let Some(keyword_en_value) = query_change_result.keyword_en {
        key_word_set.extend(keyword_en_value);
    }
    let mut question_set = HashSet::new();

    if let Some(question_zh_value) = query_change_result.question_zh {
        question_set.extend(question_zh_value);
    }
    if let Some(question_en_value) = query_change_result.question_en {
        question_set.extend(question_en_value);
    }
    if let Some(question_value) = query_change_result.question {
        question_set.extend(question_value);
    }
    if !question_set.contains(question) && question.trim().len() > 0 {
        question_set.insert(question.clone());
    }
    (key_word_set, question_set)
}
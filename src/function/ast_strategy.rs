use crate::ast::js::constant::JS_VALID_EXTENSIONS;


// pub fn build_related_module_score_by_strategy(code_complete_request: &CodeCompletionRequestBean, current_context: &PromptDeclaration, file_record: &ScanFileRecord) -> Option<CacheRelatedModule> {
//     match code_complete_request.language.as_str() {
//         "java" => CodeFuseJavaAstStrategy::build_related_module_score(code_complete_request, current_context, file_record),
//         "typescript" => CodeFuseTypeScriptAstStrategy::build_related_module_score(code_complete_request, current_context, file_record),
//         "javascript" => CodeFuseTypeScriptAstStrategy::build_related_module_score(code_complete_request, current_context, file_record),
//         _ => None
//     }
// }

pub fn is_language_related(language1: &str, language2: &str) -> bool {
    if language1 == language2 {
        return true;
    }

    if JS_VALID_EXTENSIONS.contains(&language1) && JS_VALID_EXTENSIONS.contains(&language2) {
        return true;
    }
    false
}


#[cfg(test)]
mod test {
    #[test]
    fn test_is_language_related() {
        let cases: Vec<(&str, &str, bool)> = vec![
            ("java", "java", true),
            ("java", "ts", false),
            ("ts", "ts", true),
            ("ts", "java", false),
            ("js", "ts", true),
            ("ts", "js", true),
        ];
        for (language1, language2, expected) in cases {
            assert_eq!(super::is_language_related(language1, language2), expected);
        }
    }
}
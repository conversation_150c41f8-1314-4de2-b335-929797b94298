use std::collections::HashMap;

use crate::ast::java::strategy::CodeFuseJavaAstStrategy;
use crate::ast::js::strategy::CodeFuseJsAstStrategy;
use agent_common_service::model::code_complete_model::{CodeCompletionRequestBean, ContextAwareInfo};
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_common_service::service::code_ast_analysis::AstStrategy;
use agent_common_service::service::code_complete_rag_executor::CodeCompleteRagExecutorStrategy;
use agent_common_service::tools::common_tools::CacheRelatedModuleEnum;

pub struct CodeFuseCodeCompleteRagExecutorStrategy;

///负责codefuse插件默认rag具体执行策略
/// 实现了CodeCompleteRagExecutorStrategy，部分函数使用默认实现
impl CodeCompleteRagExecutorStrategy for CodeFuseCodeCompleteRagExecutorStrategy {
    fn get_context_aware_from_cache(&self,related_module_enum: CacheRelatedModuleEnum,
        cache_module_lated_map: &HashMap<CacheRelatedModuleEnum, Vec<ContextAwareInfo>>,
        cache_context: &PromptDeclaration,
        current_context: &PromptDeclaration,
        language: &str) -> (bool, Option<Vec<ContextAwareInfo>>) {
        match language {
            "typescript" | "typescriptreact" | "javascript" | "javascriptreact" => CodeFuseJsAstStrategy::get_context_aware_from_cache(related_module_enum, cache_module_lated_map, cache_context, current_context),
            // 兜底默认实现
            _ => self.default_get_context_aware_from_cache(related_module_enum, cache_module_lated_map, cache_context, current_context, language),
        }
    }

    fn extract_prompt_declaration(&self, code_complete_request: &CodeCompletionRequestBean) -> Option<PromptDeclaration> {
        match code_complete_request.language.as_str() {
            "java" => CodeFuseJavaAstStrategy::get_prompt_declaration(code_complete_request),
            "typescript" | "typescriptreact" | "javascript" | "javascriptreact" => CodeFuseJsAstStrategy::get_prompt_declaration(code_complete_request),
            _ => None,
        }
    }
}


use crate::dialogue::codefuse_index_repository::{query_index_entry, CHUNK_CLIENT, FILE_CLIET, METHOD_CLIENT};
use crate::utils::path::get_all_file_by_url;
use agent_db::dal::vector_client::VECTOR_CLIENT;
use crate::dialogue::data_struct::{DataStatus, QueryChatItemResult, QueryChatRelatedData, QueryChatRelatedResponse, QueryIndexRequest};
use crate::dialogue::misc_util::convert_from_codefusemethod_to_model;
use crate::function::chatflow::chat_flow_task_V2::ChatFlowTaskManagerV2;
use agent_common_service::model::chat_model::ChatFlowStatusEnum::BUILD_INDEX;
use agent_common_service::model::chat_model::{ChatCodeReferenceModel, ChatFlowStatusEnum, ChatPluginModel, ChatRelatedRequestBean, Chat<PERSON><PERSON>tedResponse, IndexOperateRequestBean};
use agent_common_service::service::project_chat::ProjectChatStrategy;
use agent_db::config::runtime_config::{AgentConfig, AGENT_CONFIG};
use agent_db::dal::kv_client::{KvClient, KV_CLIENT};
use agent_db::dal::remote_client::IndexTypeEnum::METHOD_CONTENT;
use agent_db::dal::remote_client::{IndexTypeEnum, QueryChangeDetail, QueryInProject};
use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;
use agent_db::remote::rpc_model::{build_error_response, build_success_response, BaseResponse};
use agent_db::tools::common_tools::{expand_user_home, V_C_PREFIX};
use anyhow::Result;
use log::{error, info, warn};
use once_cell::sync::Lazy;
use serde_json::Value;
use std::collections::{HashMap, HashSet};
use std::fs;
use std::option::Option;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use serde::{Deserialize, Serialize};
use tokio::sync::Mutex;
use uuid::Uuid;

pub struct CodeFuseChatStrategy;

pub const CHAT_RAG_TYPE_KEY: &str = "CHAT_RAG_TYPE";
pub const CHAT_RAG_REQ_KEY: &str = "CHAT_RAG_REQ";
pub const CHAT_RAG_RESPONSE_KEY: &str = "CHAT_RAG_RESPONSE";
pub const CHAT_RAG_BUILD_TIME_KEY: &str = "CHAT_RAG_BUILD_CONSUME";
pub const CHAT_RAG_SYNC_BUILD_TIME_KEY: &str = "CHAT_RAG_SYNC_BUILD_TIME";
pub const CHAT_RAG_UNDERSTAND_TIME_KEY: &str = "CHAT_RAG_UNDERSTAND_CONSUME";
pub const CHAT_RAG_QUERY_INDEX_KEY: &str = "CHAT_RAG_QUERY_INDEX_CONSUME";
pub const QUERY_CHANGE_RESULT_KEY: &str = "QUERY_CHANGE_RESULT";
pub const QUERY_SEARCH_TIME_KEY: &str = "QUERY_SEARCH_TIME";
pub const QUERY_RERANK_TIME_KEY: &str = "QUERY_RERANK_TIME";
pub const QUERY_FILTER_TIME_KEY: &str = "QUERY_FILTER_TIME";
pub const QUERY_BM25_TIME_KEY: &str = "QUERY_BM25_TIME";
pub const QUERY_VECTOR_TIME_KEY: &str = "QUERY_VECTOR_TIME";
pub const QUERY_SEARCH_HANDLER_TIME_KEY: &str = "QUERY_SEARCH_HANDLER_TIME";

pub const TOTAL_CHAT_TIME_KEY:&str = "TOTAL_CHAT_TIME_KEY";

//默认进度条20.超过20说明bm25索引构建完成
pub const DEFAULT_INDEX_PROCESS_VALUE:u8 = 20u8;

//augment流程
pub const AUGMENT_TASK_TYPE: u8 = 1;
//deepsearch流程
pub const DEEPSEARCH_TASK_TYPE: u8 = 2;
//tab页切换， 不会触发project_url, branch的检查
pub const TAB_SWITCH_TASK_TYPE: u8 = 99;
//searchrouter流程

///任务节点状态
pub trait TaskNode {
    //开始任务，timeout
    async fn start(
        &self,
        chat_related_request: &ChatRelatedRequestBean,
        chat_flow_context: &mut ChatFlowContext,
    ) -> TaskNodeEnum;
    //判断是否超时
    async fn is_timeout(&self, current_millis: u128) -> bool;
//重置任务节点
    async fn reset(&self);
}

pub static CHAT_TASK_MANAGER_V2: Lazy<Arc<ChatFlowTaskManagerV2>> =
    Lazy::new(|| Arc::new(ChatFlowTaskManagerV2::new()));

impl ProjectChatStrategy for CodeFuseChatStrategy {
    async fn query_chat_related_info(
        chat_related_request: &ChatRelatedRequestBean,
    ) -> Result<BaseResponse<ChatRelatedResponse>> {
        let mut chat_related_response = ChatRelatedResponse {
            level1Instruction: chat_related_request.level1Instruction.clone(),
            level2Instruction: chat_related_request.level2Instruction.clone(),
            assitantParam: None,
            extraData: None,
            localRepoSelectedList: None,
            localRepoReferenceRagList: None,
            chatStatus: None,
            necessaryIndexPercent: None,
            questionUid: None,
            sessionId: None,
        };

        let task_result = CHAT_TASK_MANAGER_V2.process(chat_related_request).await;
        chat_related_response.chatStatus = Some(task_result.0.clone());
        chat_related_response.questionUid = chat_related_request.questionUid.clone();
        chat_related_response.sessionId = chat_related_request.sessionId.clone();

        info!("question_id: {:?},task step : {:?}",chat_related_request.questionUid, task_result.0);
        if task_result.0 == ChatFlowStatusEnum::ANSWER {
            let mut extra_data: HashMap<String, String> = HashMap::new();
            let context_opt = task_result.2;
            match context_opt {
                None => {}
                Some(data) => {
                    extra_data.insert(
                        CHAT_RAG_BUILD_TIME_KEY.to_string(),
                        data.build_consume_time_total.to_string(),
                    );
                    extra_data.insert(
                        CHAT_RAG_UNDERSTAND_TIME_KEY.to_string(),
                        data.understand_consume_time.to_string(),
                    );
                    extra_data.insert(
                        CHAT_RAG_QUERY_INDEX_KEY.to_string(),
                        data.query_index_time.to_string(),
                    );
                    if let Some(query_change_result) = data.query_change_detail{
                        extra_data.insert(
                            QUERY_CHANGE_RESULT_KEY.to_string(),
                            serde_json::to_string(&query_change_result)?,
                        );
                    }
                    chat_related_response.localRepoReferenceRagList = data.result;
                }
            }
        }
        //将用户选中代码填充到返回值
        if chat_related_request.referenceList.is_some(){
            let reference_list = chat_related_request.referenceList.clone().unwrap();
            if reference_list.len()>0 {
                let mut select_data:Vec<ChatRelatedCodeModel> = vec![];
                for reference in reference_list {
                    if let Some(content) = reference.content{
                        let item = ChatRelatedCodeModel{
                            relativePath: reference.url,
                            snippet: content,
                            startLine: reference.lineStart,
                            endLine: reference.lineEnd,
                            source: None,
                            title: None,
                        };
                        select_data.push(item);
                    }
                }
                if select_data.len()>0{
                    chat_related_response.localRepoSelectedList = Some(select_data);
                }
            }
        }
        let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        calculate_index_build_process(&mut chat_related_response, &chat_related_request.projectUrl.clone().unwrap()).await;
        let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        info!("query_chat_related_info calculate_index_build_process cost time {}", end_time - start_time,);
        Ok(build_success_response(chat_related_response))
    }

}
///计算索引构建的进度
async fn calculate_index_build_process( chat_related_response: &mut ChatRelatedResponse,  projectUrl: &String) {
    let build_index_progress = calculate_index_build_progress( projectUrl).await;
    chat_related_response.necessaryIndexPercent = build_index_progress;
}

//计算索引构建的进度
pub async fn calculate_index_build_progress( projectUrl: &String) -> Option<u8> {
    let mut query_in_project = QueryInProject::default();
    query_in_project.project_url = projectUrl.clone();

    let mut sum: f64 = 0.0;
    let mut num: f64 = 0.0;

    let mut index_extra_type: HashSet<IndexTypeEnum> = AGENT_CONFIG.index_extra_type.iter().cloned().collect();
    for index_type in index_extra_type {
        match index_type {
            IndexTypeEnum::CHUNK_VECTOR => {
                let chunk_count = CHUNK_CLIENT.query_count_in_project(query_in_project.clone()).await.unwrap();
                let chunk_no_vector_count = CHUNK_CLIENT.query_no_content_vector_count_in_project(&query_in_project.project_url.clone()).await.unwrap_or(0);
                let chunk_vector_count = chunk_count - chunk_no_vector_count;
                info!("chunk_count: {}, chunk_vector_count: {}", chunk_count, chunk_vector_count);
                sum = sum + chunk_count as f64;
                num = num + chunk_vector_count as f64;
            }

            _ => {}
        }
    }

    let file_count = FILE_CLIET.query_count_in_project(query_in_project.clone()).await.unwrap();
    if sum == 0.0 && file_count != 0 {
        return Some(DEFAULT_INDEX_PROCESS_VALUE);
    } else if sum == 0.0 && file_count == 0 {
        return Some(0);
    }

    let progress = (num / sum) * (100 - DEFAULT_INDEX_PROCESS_VALUE) as f64 + DEFAULT_INDEX_PROCESS_VALUE as f64;
    Some(progress.floor() as u8)
}

pub fn query_count_in_kv(prefix: &str, project_url: String, total_count: usize) -> usize {
    let chunk_prefix = format!("{}{}", prefix, project_url);
    let chunk_prefix_result = KV_CLIENT.get_from_prefix(&chunk_prefix);
    if let Ok(chunk_prefix_result) = chunk_prefix_result {
        total_count - chunk_prefix_result.unwrap().len()
    } else {
        9999
    }
}





pub async fn query_data_status(request: IndexOperateRequestBean) -> DataStatus {
    let mut query_in_project = QueryInProject::default();
    let project_url = request.projectUrl.clone().unwrap_or("".to_string());
    let (all_vec, data_vec, _diff_vec,branch, repo_url) = get_all_file_by_url(&project_url, request.diffAnalysis.unwrap_or(false)).await;
    query_in_project.project_url = project_url.clone();
    // 使用CHUNK_CLIENT查找指定project_url的数据总量
    let q1 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    let chunk_count = CHUNK_CLIENT.query_count_in_project(query_in_project.clone()).await.unwrap_or(0);
    let q2 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    let index_file_vec_result = CHUNK_CLIENT.query_file_vec(&project_url).await.unwrap_or(vec![]);
    let q3 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    let save_file_size = index_file_vec_result.len();
    let chunk_vector_count = VECTOR_CLIENT.count_all(Some(&project_url)).await.unwrap_or(0);
    let q4 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();

    info!("q1-q4: {},{},{},{}",q2-q1,q3-q2,q4-q3,q4-q1);
    // 检查data_vec长度和x结果长度是否匹配，如果不匹配则打印差异
    if request.printDetail.unwrap_or(false){
        if all_vec.len() != index_file_vec_result.len() {
            warn!("文件数量不匹配: data_vec长度={}, indexed_files长度={}", all_vec.len(), index_file_vec_result.len());
            // 将data_vec转换为相对路径字符串集合用于比较
            let base = std::path::Path::new(&project_url);
            let data_vec_paths: std::collections::HashSet<String> = all_vec.iter()
                .map(|entry| {
                    match entry.path().strip_prefix(base) {
                        Ok(rel_path) => rel_path.to_string_lossy().to_string(),
                        Err(_) => entry.path().to_string_lossy().to_string(),
                    }
                })
                .collect();
            let indexed_files_set: std::collections::HashSet<String> = index_file_vec_result.iter().cloned().collect();

            // 找出在data_vec中但不在indexed_files中的文件
            let missing_in_index: Vec<&String> = data_vec_paths.difference(&indexed_files_set).collect();
            if !missing_in_index.is_empty() {
                warn!("在文件系统中存在但未建立索引的文件 ({} 个):", missing_in_index.len());
                for (i, path) in missing_in_index.iter().enumerate() {
                    if i < 10 { // 只打印前10个，避免日志过多
                        warn!("  未索引: {}", path);
                    } else if i == 10 {
                        warn!("  ... 还有 {} 个文件未索引", missing_in_index.len() - 10);
                        break;
                    }
                }
            }

            // 找出在indexed_files中但不在data_vec中的文件
            let extra_in_index: Vec<&String> = indexed_files_set.difference(&data_vec_paths).collect();
            if !extra_in_index.is_empty() {
                warn!("已建立索引但在文件系统中不存在的文件 ({} 个):", extra_in_index.len());
                for (i, path) in extra_in_index.iter().enumerate() {
                    if i < 10 { // 只打印前10个，避免日志过多
                        warn!("  多余索引: {}", path);
                    } else if i == 10 {
                        warn!("  ... 还有 {} 个多余索引", extra_in_index.len() - 10);
                        break;
                    }
                }
            }
        }
        // 检查chunk_vector_count和chunk_count数据是否一致
        if chunk_vector_count != chunk_count {
            warn!("向量索引数量和代码片段索引数量不一致: chunk_vector_count={}, chunk_count={}", chunk_vector_count, chunk_count);

            // 查询向量索引库中指定project_url的所有数据id集合A
            let vector_ids_result = VECTOR_CLIENT.query_all_ids_by_project(&project_url).await;
            // 查询代码片段索引库中指定project_url的所有数据id集合B
            let chunk_ids_result = CHUNK_CLIENT.query_all_ids_by_project(&project_url).await;

            match (vector_ids_result, chunk_ids_result) {
                (Ok(vector_ids), Ok(chunk_ids)) => {
                    let vector_ids_set: std::collections::HashSet<String> = vector_ids.into_iter().collect();
                    let chunk_ids_set: std::collections::HashSet<String> = chunk_ids.into_iter().collect();

                    // 找出在vector_ids中但不在chunk_ids中的id（多余的向量）
                    let extra_vector_ids: Vec<&String> = vector_ids_set.difference(&chunk_ids_set).collect();
                    if !extra_vector_ids.is_empty() {
                        warn!("向量索引中存在但代码片段索引中不存在的id ({} 个):", extra_vector_ids.len());
                        for (i, id) in extra_vector_ids.iter().enumerate() {
                            if i < 10 { // 只打印前10个，避免日志过多
                                warn!("  多余向量id: {}", id);
                            } else if i == 10 {
                                warn!("  ... 还有 {} 个多余向量id", extra_vector_ids.len() - 10);
                                break;
                            }
                        }

                        // 删除多余的向量id
                        // info!("开始删除多余的向量id，共 {} 个", extra_vector_ids.len());
                        // for id in &extra_vector_ids {
                        //     match VECTOR_CLIENT.delete_with_key_and_value("id", id).await {
                        //         Ok(_) => {
                        //             info!("成功删除向量id: {}", id);
                        //         }
                        //         Err(e) => {
                        //             error!("删除向量id失败: {}, 错误: {:?}", id, e);
                        //         }
                        //     }
                        // }
                        // info!("完成删除多余的向量id");
                    }

                    // 找出在chunk_ids中但不在vector_ids中的id（缺失的向量）
                    let missing_vector_ids: Vec<&String> = chunk_ids_set.difference(&vector_ids_set).collect();
                    if !missing_vector_ids.is_empty() {
                        warn!("代码片段索引中存在但向量索引中不存在的id ({} 个):", missing_vector_ids.len());

                        // 查询这些缺失id对应的file_url
                        let missing_ids_vec: Vec<String> = missing_vector_ids.iter().map(|s| (*s).clone()).collect();
                        match CHUNK_CLIENT.query_file_urls_by_ids(&missing_ids_vec, &project_url).await {
                            Ok(missing_file_urls) => {
                                if !missing_file_urls.is_empty() {
                                    warn!("缺失向量的文件路径 ({} 个不重复文件):", missing_file_urls.len());
                                    for (i, file_url) in missing_file_urls.iter().enumerate() {
                                        if i < 10 { // 只打印前10个，避免日志过多
                                            warn!("  缺失向量文件: {}", file_url);
                                        } else if i == 10 {
                                            warn!("  ... 还有 {} 个缺失向量文件", missing_file_urls.len() - 10);
                                            break;
                                        }
                                    }
                                }
                            }
                            Err(e) => {
                                error!("查询缺失向量id对应的file_url失败: {:?}", e);
                                // 如果查询file_url失败，仍然打印id信息
                                for (i, id) in missing_vector_ids.iter().enumerate() {
                                    if i < 10 { // 只打印前10个，避免日志过多
                                        warn!("  缺失向量id: {}", id);
                                    } else if i == 10 {
                                        warn!("  ... 还有 {} 个缺失向量id", missing_vector_ids.len() - 10);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                (Err(e1), Err(e2)) => {
                    error!("查询向量索引id和代码片段索引id都失败: vector_error={:?}, chunk_error={:?}", e1, e2);
                }
                (Err(e), _) => {
                    error!("查询向量索引id失败: {:?}", e);
                }
                (_, Err(e)) => {
                    error!("查询代码片段索引id失败: {:?}", e);
                }
            }
        }
    }


    // 3.1: 文件chunk索引构建百分比 = CHUNK_CLIENT查出来的file_url数量/data_vec数量，超过1按1算
    let file_chunk_progress = if all_vec.len() > 0 {
        let ratio = save_file_size as f64 / all_vec.len() as f64;
        if ratio > 1.0 { 1.0 } else { ratio }
    } else {
        0.0
    };

    let vector_progress = if chunk_count > 0 {
        let ratio = chunk_vector_count as f64 / chunk_count as f64;
        if ratio > 1.0 { 1.0 } else { ratio }
    } else {
        0.0
    };


    let mut data_status = DataStatus::default();
    data_status.file_total_count = all_vec.len();
    data_status.file_count = save_file_size; // 使用从chunk中查询到的file_url数量
    data_status.chunk_save_count = chunk_count;
    data_status.chunk_progress = file_chunk_progress;
    //chunk没有vector的数量
    data_status.chunk_vector_save_count = chunk_vector_count;
    data_status.chunk_vector_progress = vector_progress;
    // 3.3: 文件chunk索引构建百分比占总进度20%，向量索引构建百分比占总进度80%
    let total_progress = (file_chunk_progress * 0.2 + vector_progress * 0.8) * 100.0;

    let process  = total_progress.floor() as u8;
    if process >= 70 {
        data_status.build_index_progress = 100;
    } else {
        data_status.build_index_progress = process;
    }
    data_status.build_index_progress_inner = process;

    // 必要索引进度基于file_url数量计算
    let nessary_index_progress = (file_chunk_progress * 100.0) as u8;

    //这里做兜底检查，tanity存储是异步的，有可能nessary索引不足50%，但是整体索引数据>20%。如果是这种情况，直接将nessary索引设置为100
    if data_status.build_index_progress > 20 && nessary_index_progress <= 50 {
        warn!("nessary index is not enough, but total index is enough, set nessary index to 100");
        data_status.build_nessary_index_progress = 100;
    } else {
        data_status.build_nessary_index_progress = nessary_index_progress;
    }
    data_status
}

///对话上下文
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChatFlowContext {
    //questionUid
    pub question_uid: String,
    //项目地址
    pub project_url: String,
    //分支
    pub branch: String,
    //用户query
    pub query:String,
    //改写结果
    pub query_change_detail: Option<QueryChangeDetail>,
    //检索结果
    pub result: Option<Vec<ChatRelatedCodeModel>>,
    //构建总耗时
    pub build_consume_time_total: u128,
    //同步构建耗时
    pub build_consume_time_sync: u128,
    //query理解耗时
    pub understand_consume_time: u128,
    //检索耗时
    pub query_index_time: u128,
    //search耗时
    pub search_time:u128,
    //过滤耗时
    pub filter_time:u128,
    //rerank耗时
    pub rerank_time:u128,
    pub handler_search_time:u128,
    pub bm25_search_time:u128,
    pub vector_search_time:u128
}

impl ChatFlowContext {
    pub fn new() -> Self {
        ChatFlowContext {
            question_uid: "".to_string(),
            project_url: "".to_string(),
            branch: "".to_string(),
            query: "".to_string(),
            query_change_detail: None,
            result: None,
            build_consume_time_total: 0,
            build_consume_time_sync: 0,
            understand_consume_time: 0,
            query_index_time: 0,
            search_time: 0,
            filter_time: 0,
            rerank_time: 0,
            handler_search_time: 0,
            bm25_search_time: 0,
            vector_search_time: 0,
        }
    }
}

///任务节点状态
/// READY： 可以执行任务
/// RUN：任务执行中
/// END：任务执行结束，注意，由于任务有处理时间，当状态是END是代表当前任务不在继续执行，而代码层面正在跑的逻辑还在执行，所以END状态不代表可以执行下一个任务
#[derive(Serialize, Deserialize, Hash, Eq, PartialEq, Debug, Clone)]
pub enum TaskNodeEnum {
    //准备
    READY,
    // 运行中
    RUNNING,
    //异常
    ERROR,
    // 结束
    END,
}
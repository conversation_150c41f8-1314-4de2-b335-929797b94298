use crate::function::build_cache_data::{find_by_class_name, MatchedRelatedResult};
use agent_common_service::model::code_complete_model::ContextAwareInfo;
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_common_service::tools::common_tools::CacheRelatedModuleEnum;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{CodeInfo, ScanFileRecord};
use agent_db::tools::common_tools::R_PREFIX;
use log::error;
use std::collections::HashMap;
use std::path::Path;

fn get_context_aware_info(decay_flag: bool, score: f64, desc_str: String, exist_data: Vec<ContextAwareInfo>) -> Option<ContextAwareInfo> {
    return None;
}

//todo 这里的判断条件有问题,需要验证，find_by_class_name存在类似现象，filePath是/分割，class是.分割
fn exist(full_class_name: &String, exist_data_vec: &Vec<ContextAwareInfo>) -> bool {
    // if cache_data_vec.is_none() {
    //     return false;
    // }
    for cache in exist_data_vec {
        let file_path = Path::new(&cache.filePath);

        if full_class_name.ends_with(file_path.file_stem().unwrap().to_str().unwrap()) {
            return true;
        }
    }
    return false;
}


///实验性功能，只针对idea端java项目，从索引中查找二方jar的相关信息
/// 由上游函数判断是否走到这里
pub fn build_related_module_score_with_jar(project_url: &String, current_context: &PromptDeclaration, related_data: &mut HashMap<CacheRelatedModuleEnum, Vec<ContextAwareInfo>>) -> bool {
    let mut method_vec: Vec<ContextAwareInfo> = vec![];
    let mut filed_vec: Vec<ContextAwareInfo> = vec![];
    let mut import_class_vec: Vec<ContextAwareInfo> = vec![];
    let mut extend_class_vec: Vec<ContextAwareInfo> = vec![];
    let key = format!("{}{}", R_PREFIX, project_url);
    let jar_result = KV_CLIENT.get_from_prefix(&key);
    if jar_result.is_err() {
        error!("build_related_module_score_with_jar error:{}", jar_result.err().unwrap());
        return false;
    }
    let jar_data_opt = jar_result.unwrap();
    if jar_data_opt.is_none() {
        return false;
    }
    let all_jar_file_result = jar_data_opt.unwrap();
    let exist_data_vec: Vec<ContextAwareInfo> = related_data.values().flat_map(|v| v.iter().cloned()).collect();

    for (key, value) in all_jar_file_result {
        let scan_file_data_result: serde_json::error::Result<CodeInfo> = serde_json::from_str(value.as_str());
        match scan_file_data_result {
            Err(e) => {
                error!("build_jar_related_cache error:{}", e)
            }
            Ok(code_info) => {
                //jar是com.alipay.xxx , param有可能仅是类名，也可能是com.alipay.xxx
                let jar_class_name = code_info.full_qualified_name.as_ref().unwrap().split('.').last().unwrap();
                if let Some(current_method_info) = &current_context.method_declaration {
                    let param_vec_opt = &current_method_info.param_class_vec;
                    //step 1 解析参数类型，默认得分6
                    if let Some(param_vec) = param_vec_opt {
                        let is_hit = build_module_data(jar_class_name, &code_info, &exist_data_vec, &mut method_vec, param_vec, 0.6, "ANT-JAR_methodParam".to_string(), false);
                        if is_hit {
                            continue;
                        }
                    }
                    //step 2 解析函数体,默认得分5
                    let inner_class_declaration_opt = &current_method_info.inner_class_declaration;
                    if let Some(inner_class_declaration) = inner_class_declaration_opt {
                        let is_hit = build_module_data(jar_class_name, &code_info, &exist_data_vec, &mut method_vec, inner_class_declaration, 0.5, "ANT-JAR_methodInnerClass".to_string(), true);
                        if is_hit {
                            continue;
                        }
                    }
                    //step 3 解析返回值类型，默认得分4
                    let return_class_opt = &current_method_info.return_class;
                    if let Some(return_class) = return_class_opt {
                        let return_vec = vec![return_class.to_string()];
                        let is_hit = build_module_data(jar_class_name, &code_info, &exist_data_vec, &mut method_vec, &return_vec, 0.4, "ANT-JAR_returnClass".to_string(), false);
                        if is_hit {
                            continue;
                        }
                    }
                }

                //step 4 解析类变量，默认得分2
                if let Some(class_field_set) = &current_context.field_set {
                    let class_field_vec: Vec<String> = class_field_set.iter().cloned().collect();
                    let is_hit = build_module_data(jar_class_name, &code_info, &exist_data_vec, &mut filed_vec, &class_field_vec, 0.3, "ANT-JAR_classField".to_string(), false);
                    if is_hit {
                        continue;
                    }
                }
                //step 5 解析类变量，默认得分2
                if let Some(extends_class) = &current_context.extends_class {
                    let extends_vec = vec![extends_class.to_string()];
                    let is_hit = build_module_data(jar_class_name, &code_info, &exist_data_vec, &mut extend_class_vec, &extends_vec, 0.2, "ANT-JAR_extendClass".to_string(), false);
                    if is_hit {
                        continue;
                    }
                }
                //step 解析import信息 解析类变量，默认得分2
                if let Some(import_set) = &current_context.import_set {
                    let import_vec = import_set.iter().cloned().collect();
                    let is_hit = build_module_data(jar_class_name, &code_info, &exist_data_vec, &mut import_class_vec, &import_vec, 0.1, "ANT-JAR_importClass".to_string(), false);
                    if is_hit {
                        continue;
                    }
                }
            }
        }
    }

    let mut is_hit = false;

    if method_vec.len() > 0 {
        is_hit = true;
        let current_method_vec = related_data.entry(CacheRelatedModuleEnum::METHOD).or_insert_with(Vec::new);
        current_method_vec.extend(method_vec);
    }
    if filed_vec.len() > 0 {
        is_hit = true;
        let current_field_vec = related_data.entry(CacheRelatedModuleEnum::FIELD).or_insert_with(Vec::new);
        current_field_vec.extend(filed_vec);
    }
    if extend_class_vec.len() > 0 {
        is_hit = true;
        let current_extend_vec = related_data.entry(CacheRelatedModuleEnum::EXTENDS).or_insert_with(Vec::new);
        current_extend_vec.extend(extend_class_vec);
    }
    if import_class_vec.len() > 0 {
        is_hit = true;
        let current_import_vec = related_data.entry(CacheRelatedModuleEnum::IMPORT).or_insert_with(Vec::new);
        current_import_vec.extend(import_class_vec);
    }
    return is_hit;
}

fn build_module_data(jar_class_name: &str, code_info: &CodeInfo, exist_data_vec: &Vec<ContextAwareInfo>, target_data: &mut Vec<ContextAwareInfo>, current_data: &Vec<String>, score: f64, desc_str: String, decay_flag: bool) -> bool {
    let jar_name = code_info.full_qualified_name.as_ref().expect("jar codeinfo class must have name").clone();
    let content = code_info.code_struct.clone();
    for (index, param) in current_data.iter().enumerate() {
        let exist_flag = exist(param, exist_data_vec);
        //如果不存在，从索引的二方jar中查找
        if !exist_flag {
            if param.ends_with(&jar_class_name) {
                if decay_flag {
                    let r = ContextAwareInfo {
                        filePath: jar_name.clone(),
                        content: content.clone(),
                        score: score + 0.1 * index as f64,
                        extraData: Some(desc_str.clone()),
                    };
                    target_data.push(r);
                    return true;
                } else {
                    let r = ContextAwareInfo {
                        filePath: jar_name.clone(),
                        content: content.clone(),
                        score: score,
                        extraData: Some(desc_str.clone()),
                    };
                    target_data.push(r);
                    return true;
                }
            };
        }
    }
    return false;
}
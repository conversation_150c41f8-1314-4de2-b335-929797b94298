use std::collections::{<PERSON><PERSON><PERSON><PERSON><PERSON>, HashMap, HashSet};
use std::path::Path;
use std::sync::atomic::Ordering;

use log::{debug, error, info};

use crate::ast::js::strategy::CodeFuseJsAstStrategy;
use crate::function::ant_jar_related::build_related_module_score_with_jar;
use crate::function::ast_strategy::is_language_related;
use crate::function::codecomplete_rag_framework_strategy::{
    rebuild_file_index_increment, BUILD_RELATE_IS_RUNNING, BUILD_SIMILARITY_IS_RUNNING,
    RELATED_CACHE, SIMILARITY_CACHE,
};
use crate::function::codecomplete_scan_strategy::CodeFuseScanStrategy;
use crate::utils::strategy_utils::{
    get_cache_strategy, get_current_prompt_declaration, get_current_similarity_score_data,
    get_rag_strategy, get_related_module, get_target_similarity_score_data, is_android,
};
use agent_common_service::model::code_complete_model::{
    CodeCompletionRequestBean, ContextAwareInfo,
};
use agent_common_service::model::code_scan_model::ScanConfig;
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_common_service::service::abstract_similarity::{AssembleComponentHandler, Similarity};
use agent_common_service::service::code_ast_analysis::AstStrategy;
use agent_common_service::service::code_complete_cache::{
    assemble_similarity_codeinfo, CodeCompleteCacheStrategy,
};
use agent_common_service::service::code_complete_rag_executor::CodeCompleteRagExecutorStrategy;
use agent_common_service::service::code_scan::CodeScanStrategy;
use agent_common_service::tools::code_tokenizer::token_to_code;
use agent_common_service::tools::common_tools::{
    CacheRelatedModuleEnum, CacheTypeEnum, SimilarityCodeInfo,
};
use agent_common_service::tools::related_module_score::CacheRelatedModule;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{ScanFileRecord, ScanSnippetRecord};
use agent_db::tools::common_tools::FILE_PREFIX;
use anyhow::Result;


const  similarity_import_size: u16 = 2000;
const similarity_dir_size: u16 = 500;
const  similarity_name_size: u16 = 500;



pub struct MatchedRelatedResult {
    import_result: Vec<ContextAwareInfo>,
    field_result: Vec<ContextAwareInfo>,
    extends_result: Vec<ContextAwareInfo>,
    method_result: Vec<ContextAwareInfo>,
}

impl MatchedRelatedResult {
    pub fn new() -> Self {
        //和 CacheRelatedModuleEnum 一一对应的模块相关结果集
        let import_result: Vec<ContextAwareInfo> = Vec::new();
        let field_result: Vec<ContextAwareInfo> = Vec::new();
        let extends_result: Vec<ContextAwareInfo> = Vec::new();
        let method_result: Vec<ContextAwareInfo> = Vec::new();

        Self {
            import_result,
            field_result,
            extends_result,
            method_result,
        }
    }

    pub fn push_matched(&mut self, module_cache_result: Option<CacheRelatedModule>) {
        match module_cache_result {
            Some(module) => match module.module_type {
                CacheRelatedModuleEnum::IMPORT => {
                    self.import_result.push(module.info);
                }
                CacheRelatedModuleEnum::FIELD => {
                    self.field_result.push(module.info);
                }
                CacheRelatedModuleEnum::EXTENDS => {
                    self.extends_result.push(module.info);
                }
                CacheRelatedModuleEnum::METHOD => {
                    self.method_result.push(module.info);
                }
            },
            _ => {}
        }
    }

    pub fn build_cache_result(self) -> HashMap<CacheRelatedModuleEnum, Vec<ContextAwareInfo>> {
        let mut result: HashMap<CacheRelatedModuleEnum, Vec<ContextAwareInfo>> = HashMap::new();
        if !self.import_result.is_empty() {
            result.insert(CacheRelatedModuleEnum::IMPORT, self.import_result);
        }
        if !self.field_result.is_empty() {
            result.insert(CacheRelatedModuleEnum::FIELD, self.field_result);
        }

        if !self.extends_result.is_empty() {
            result.insert(CacheRelatedModuleEnum::EXTENDS, self.extends_result);
        }

        if !self.method_result.is_empty() {
            result.insert(CacheRelatedModuleEnum::METHOD, self.method_result);
        }

        result
    }
}

pub async fn build_cache_data(
    code_complete_request: CodeCompletionRequestBean,
    cache_type: CacheTypeEnum,
) {
    let content = format!(
        "{}{}",
        &code_complete_request.prompt, &code_complete_request.suffix
    );
    match cache_type {
        CacheTypeEnum::RELATED => {
            let running = BUILD_RELATE_IS_RUNNING.clone();
            // 检查函数是否已经在运行
            if running
                .compare_exchange(false, true, Ordering::SeqCst, Ordering::SeqCst)
                .is_err()
            {
                info!("build {:?} cache is already running", cache_type);
                return;
            }
            execute_build_related_cache(&code_complete_request).await;
            //rebuild_file_index_increment增量更新函数，触发一次即可。所以放在related还是similarity都可以
            rebuild_file_index_increment(&code_complete_request, content).await;
            running.store(false, Ordering::SeqCst);
        }
        CacheTypeEnum::SIMILARITY => {
            let running = BUILD_SIMILARITY_IS_RUNNING.clone();
            // 检查函数是否已经在运行
            if running
                .compare_exchange(false, true, Ordering::SeqCst, Ordering::SeqCst)
                .is_err()
            {
                info!("build {:?} cache is already running", cache_type);
                return;
            }
            execute_build_similarity_cache(&code_complete_request).await;
            // rebuild_file_index_increment(&code_complete_request.projectUrl.unwrap(), &code_complete_request.fileUrl, content).await;
            running.store(false, Ordering::SeqCst);
        }
    }
}

///构建相关性缓存
async fn execute_build_related_cache(code_complete_request: &CodeCompletionRequestBean) {
    let current_context_opt = get_current_prompt_declaration(code_complete_request);
    if current_context_opt.is_none() {
        return;
    }
    let current_file_url = &code_complete_request.fileUrl;
    let current_file_name_suffix = &code_complete_request
        .fileNameSuffix
        .clone()
        .unwrap_or("".to_string());

    let current_context = current_context_opt.unwrap();
    let project_url = &code_complete_request.projectUrl.clone().unwrap();
    let project_file_list_key = format!("{}{}", FILE_PREFIX, project_url);
    let all_project_file_result = KV_CLIENT.get_from_prefix(&project_file_list_key);

    let mut matched_result = MatchedRelatedResult::new();

    match all_project_file_result {
        Err(e) => {
            error!("get all project file error:{}", e);
        }
        Ok(all_project_file_opt) => match all_project_file_opt {
            None => {
                let project_url_opt = code_complete_request.projectUrl.clone();
                let _ = rebuild_index(project_url_opt.unwrap(), code_complete_request).await;
            }
            Some(all_project_file) => {
                if all_project_file.is_empty() {
                    let project_url_opt = code_complete_request.projectUrl.clone();
                    let _ = rebuild_index(project_url_opt.unwrap(), code_complete_request).await;
                } else {
                    for (key, value) in all_project_file {
                        let scan_file_data_result: serde_json::error::Result<ScanFileRecord> =
                            serde_json::from_str(value.as_str());
                        match scan_file_data_result {
                            Err(e) => {
                                error!("build_related_cache error:{}", e)
                            }
                            Ok(scan_file_record) => {
                                if !is_language_related(
                                    &scan_file_record.file_name_suffix,
                                    current_file_name_suffix,
                                ) || current_file_url == &scan_file_record.file_url
                                {
                                    continue;
                                }

                                let scan_file_code_info_opt = &scan_file_record.code_info;
                                if scan_file_code_info_opt.is_none() {
                                    continue;
                                }
                                let result = get_related_module(
                                    code_complete_request,
                                    &current_context,
                                    &scan_file_record,
                                );
                                if let Some(result) = result {
                                    result.into_iter().for_each(|item| {
                                        matched_result.push_matched(Some(item));
                                    });
                                }
                            }
                        }
                    }
                }
            }
        },
    }

    let mut result = matched_result.build_cache_result();

    let jar_hit_flag =
        build_related_module_score_with_jar(project_url, &current_context, &mut result);
    debug!("hit ant jar flag:{}", jar_hit_flag);

    if result.len() > 0 {
        save_related_cache(code_complete_request, current_context, result).await;
    }
}

///保存缓存数据
async fn save_related_cache(
    code_complete_request: &CodeCompletionRequestBean,
    prompt_declaration: PromptDeclaration,
    result: HashMap<CacheRelatedModuleEnum, Vec<ContextAwareInfo>>,
) {
    let mut related_cache = RELATED_CACHE.write().await;
    related_cache.file_url = code_complete_request.fileUrl.clone();
    related_cache.current_code_context = prompt_declaration;
    related_cache.module_related_map = result;
    drop(related_cache)
}

///构建当前代码片段每个元素的相关数据和分数
pub fn build_related_module_score(
    current_context: &PromptDeclaration,
    file_recored: &ScanFileRecord,
) -> (Option<CacheRelatedModuleEnum>, Option<ContextAwareInfo>) {
    let file_path = Path::new(&file_recored.file_url);

    let file_name = file_path.file_stem().unwrap().to_str().unwrap();

    if let Some(current_method_info) = &current_context.method_declaration {
        let param_vec_opt = &current_method_info.param_class_vec;
        //step 1 解析参数类型，默认得分6
        if let Some(param_vec) = param_vec_opt {
            let aware_data_opt = get_context_aware_info(
                file_name,
                false,
                0.6,
                "methodParam".to_string(),
                file_recored,
                param_vec,
            );
            if let Some(aware_data) = aware_data_opt {
                return (Some(CacheRelatedModuleEnum::METHOD), Some(aware_data));
            }
        }
        //step 2 解析函数体,默认得分5
        let inner_class_declaration_opt = &current_method_info.inner_class_declaration;
        if let Some(inner_class_declaration) = inner_class_declaration_opt {
            //衰减标识为true，离光标位置越近越好
            let aware_data_opt = get_context_aware_info(
                file_name,
                true,
                0.5,
                "methodInnerClass".to_string(),
                file_recored,
                inner_class_declaration,
            );
            if let Some(aware_data) = aware_data_opt {
                return (Some(CacheRelatedModuleEnum::METHOD), Some(aware_data));
            }
        }
        //step 3 解析返回值类型，默认得分4
        let return_class_opt = &current_method_info.return_class;
        if let Some(return_class) = return_class_opt {
            let return_vec = vec![return_class.to_string()];
            let aware_data_opt = get_context_aware_info(
                file_name,
                false,
                0.4,
                "returnClass".to_string(),
                file_recored,
                &return_vec,
            );
            if let Some(aware_data) = aware_data_opt {
                return (Some(CacheRelatedModuleEnum::METHOD), Some(aware_data));
            }
        }
    }
    //step 4 解析类变量，默认得分2
    if let Some(class_field_set) = &current_context.field_set {
        let class_field_vec: Vec<String> = class_field_set.iter().cloned().collect();
        let aware_data_opt = get_context_aware_info(
            file_name,
            false,
            0.3,
            "classField".to_string(),
            file_recored,
            &class_field_vec,
        );
        if let Some(aware_data) = aware_data_opt {
            return (Some(CacheRelatedModuleEnum::FIELD), Some(aware_data));
        }
    }
    //step 5 解析类变量，默认得分2
    if let Some(extends_class) = &current_context.extends_class {
        let extends_vec = vec![extends_class.to_string()];
        let aware_data_opt = get_context_aware_info(
            file_name,
            false,
            0.2,
            "extendClass".to_string(),
            file_recored,
            &extends_vec,
        );
        if let Some(aware_data) = aware_data_opt {
            return (Some(CacheRelatedModuleEnum::EXTENDS), Some(aware_data));
        }
    }
    //step 解析import信息 解析类变量，默认得分2
    if let Some(import_set) = &current_context.import_set {
        let code_info_value = file_recored.code_info.clone().unwrap();

        let full_qualified_opt = code_info_value.full_qualified_name.clone();
        if let Some(full_qualified_value) = full_qualified_opt {
            if import_set.contains(&full_qualified_value) {
                let item = ContextAwareInfo {
                    filePath: file_recored.file_url.clone(),
                    content: code_info_value.code_struct.clone(),
                    score: 0.1,
                    extraData: Some("importClass".to_string()),
                };
                return (Some(CacheRelatedModuleEnum::IMPORT), Some(item));
            }
        }
    }
    return (None, None);
}

fn get_context_aware_info(
    file_name: &str,
    decay_flag: bool,
    score: f64,
    desc_str: String,
    file_record: &ScanFileRecord,
    class_name_iter: &Vec<String>,
) -> Option<ContextAwareInfo> {
    let hit = find_by_file_name(file_name, class_name_iter);
    if hit.0 {
        if decay_flag {
            return Some(ContextAwareInfo {
                filePath: file_record.file_url.clone(),
                content: file_record.code_info.clone().unwrap().code_struct,
                score: score + 0.1 * hit.1 as f64,
                extraData: Some(desc_str),
            });
        } else {
            return Some(ContextAwareInfo {
                filePath: file_record.file_url.clone(),
                content: file_record.code_info.clone().unwrap().code_struct,
                score: score,
                extraData: Some(desc_str),
            });
        }
    }
    return None;
}

//寻找匹配的名字
pub fn find_by_class_name(
    file_record: &ScanFileRecord,
    class_name_iter: &Vec<String>,
) -> (bool, usize) {
    for (index, class_name) in class_name_iter.iter().enumerate() {
        let real_class_name = class_name.to_owned() + "." + &*file_record.file_name_suffix;
        if file_record.file_url.ends_with(&real_class_name) {
            return (true, index);
        }
    }
    return (false, 0);
}

pub fn find_by_file_name(file_name: &str, class_name_iter: &Vec<String>) -> (bool, usize) {
    for (index, class_name) in class_name_iter.iter().enumerate() {
        // let real_class_name = class_name.to_owned() + "." + &*file_record.file_name_suffix;
        if class_name.ends_with(file_name) {
            return (true, index);
        }
    }
    return (false, 0);
}

/// 构建相似性缓存
/// step 1 先基于规则(3种不同类型)找到目标文件集合，并
/// step 2 缓存目标文件代码片段
async fn execute_build_similarity_cache(code_complete_request: &CodeCompletionRequestBean) {
    let current_file_url = &code_complete_request.fileUrl;
    let current_file_name_suffix = &code_complete_request
        .fileNameSuffix
        .clone()
        .unwrap_or("".to_string());
    let project_url_opt = &code_complete_request.projectUrl;
    let project_url = project_url_opt.clone().unwrap();
    let project_file_list_key = format!("{}{}", FILE_PREFIX, project_url);
    let all_project_file_result = KV_CLIENT.get_from_prefix(&project_file_list_key);

    let current_data = get_current_similarity_score_data(code_complete_request);

    let mut current_package_heap: BinaryHeap<Similarity<Option<String>>> = BinaryHeap::new();
    let mut similarity_import_heap: BinaryHeap<Similarity<Option<String>>> = BinaryHeap::new();
    let mut similarity_name_heap: BinaryHeap<Similarity<Option<String>>> = BinaryHeap::new();

    match all_project_file_result {
        Ok(all_project_file_opt) => {
            match all_project_file_opt {
                Some(all_project_file) => {
                    if all_project_file.is_empty() {
                        let project_url_opt = code_complete_request.projectUrl.clone();
                        let _ =
                            rebuild_index(project_url_opt.unwrap(), code_complete_request).await;
                    } else {
                        //开始遍历文件列表，寻找和当前文件最相关的
                        for (key, value) in all_project_file {
                            let scan_file_data_result: serde_json::error::Result<ScanFileRecord> =
                                serde_json::from_str(value.as_str());
                            match scan_file_data_result {
                                Ok(scan_file_record) => {
                                    if !is_language_related(
                                        &scan_file_record.file_name_suffix,
                                        current_file_name_suffix,
                                    ) || current_file_url == &scan_file_record.file_url
                                    {
                                        continue;
                                    }
                                    // 顺序： current_package_opt,similarity_import_opt,similarity_name_opt
                                    let similarity_score_opt = get_target_similarity_score_data(
                                        code_complete_request,
                                        &scan_file_record,
                                        &current_data,
                                    );

                                    if similarity_score_opt.0.is_some() {
                                        current_package_heap.push(similarity_score_opt.0.unwrap());
                                    }
                                    if similarity_score_opt.1.is_some() {
                                        similarity_import_heap
                                            .push(similarity_score_opt.1.unwrap());
                                    }
                                    if similarity_score_opt.2.is_some() {
                                        similarity_name_heap.push(similarity_score_opt.2.unwrap());
                                    }
                                }
                                Err(e) => {
                                    error!("build_similarity_data error:{}", e)
                                }
                            }
                        }
                    }
                }

                None => {
                    let project_url_opt = code_complete_request.projectUrl.clone();
                    let _ = rebuild_index(project_url_opt.unwrap(), code_complete_request).await;
                }
            }
        }

        Err(e) => {
            error!("build_similarity_data error:{}", e)
        }
    }
    //分别找到各类型的相似度计算候选集
    let mut result: Vec<SimilarityCodeInfo> = vec![];
    let mut exist_key: HashSet<String> = HashSet::new();
    assemble_similarity_codeinfo(
        &mut current_package_heap,
        similarity_dir_size,
        &mut result,
        &mut exist_key,
    );
    assemble_similarity_codeinfo(
        &mut similarity_import_heap,
        similarity_import_size,
        &mut result,
        &mut exist_key,
    );
    assemble_similarity_codeinfo(
        &mut similarity_name_heap,
        similarity_name_size,
        &mut result,
        &mut exist_key,
    );
    if result.len() > 0 {
        save_similarity_cache(code_complete_request, result).await;
    }
}

///保存缓存数据
async fn save_similarity_cache(
    code_complete_request: &CodeCompletionRequestBean,
    similarity_code_info_vec: Vec<SimilarityCodeInfo>,
) {
    let mut similarity_cache = SIMILARITY_CACHE.write().await;
    similarity_cache.file_url = code_complete_request.fileUrl.clone();
    similarity_cache.similarity_snippet_vec = similarity_code_info_vec;
    drop(similarity_cache)
}

pub async fn rebuild_index(
    project_url: String,
    code_complete_request: &CodeCompletionRequestBean,
) -> Result<()> {
    let scan_config = ScanConfig {
        url: project_url.clone(),
        branch: None,
        code_completion_request_bean: Some(code_complete_request.clone()),
    };
    let r = CodeFuseScanStrategy::scan_project_from_url(scan_config).await;
    if r.is_err() {
        error!("rebuild index error:{}", r.err().unwrap())
    }
    let is_android = is_android(code_complete_request);
    if !is_android  {
        let r = CodeFuseScanStrategy::execute_dependency_scan(&project_url).await;
        if r.is_err() {
            error!("rebuild dependency error:{}", r.err().unwrap())
        }
    }
    Ok(())
}

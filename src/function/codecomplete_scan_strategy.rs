use agent_android_service::android_strategy::code_complete_scan_strategy_android::AndroidScanStrategy;
use agent_common_service::model::code_complete_model::QueryJarInfoRequestBean;
use anyhow::Result;
use ignore::gitignore::GitignoreBuilder;
use ignore::{Dir<PERSON><PERSON><PERSON>, Walk, WalkBuilder};
use log::{debug, error, info, warn};
use once_cell::sync::Lazy;
use std::fs::File;
use std::io::{BufRead, BufReader};
use std::path::Path;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use std::{fs, thread};

use crate::function::codecomplete_strategy::CodeFuseCodeCompleteStrategy;
use crate::service::code_scan::{check_and_del_old_project, extra_and_save_similarity_code_fragment, extra_related_code_fragment, skip_dir, skip_file};
use crate::utils::strategy_utils::is_android;
use agent_common_service::model::code_scan_model::ScanConfig;
use agent_common_service::service::code_complete::CodeCompleteStrategy;
use agent_common_service::service::code_scan::CodeScanStrategy;
use agent_common_service::tools::xml_tools::{get_dependency_list, get_pom_content_without_variable, Dependency};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{CodeInfo, ScanFileRecord, ScanProjectRecord, PROJECT_DATA_KEY};
use agent_db::tools::common_tools::{expand_user_home, FILE_PREFIX, R_PREFIX, SEPARATORSIMPLE};

pub struct CodeFuseScanStrategy;

//全局开关，为了保障本地的资源消耗，同一时间只允许一个线程扫描
static SHARED_IS_RUNNING: Lazy<Arc<AtomicBool>> = Lazy::new(|| Arc::new(AtomicBool::new(false)));

const POM_FILE: &str = "pom.xml";

impl CodeScanStrategy for CodeFuseScanStrategy {
    async fn scan_project_from_url(scan_config: ScanConfig) -> Result<()> {
        info!("scan_project_from_url: {:?}",scan_config);
        let running = SHARED_IS_RUNNING.clone();
        // 检查函数是否已经在运行
        if running.compare_exchange(false, true, Ordering::SeqCst, Ordering::SeqCst).is_err() {
            info!("Function is already running, request rejected.");
            return Ok(());
        }
        let _ = execute_scan_all_code(&scan_config);
        // 设置函数为未运行状态
        running.store(false, Ordering::SeqCst);
        Ok(())
    }

    fn is_scan_ing() -> bool {
        let running = SHARED_IS_RUNNING.clone();
        return running.load(Ordering::SeqCst);
    }


    ///扫描项目根目录下的pom.xml，保存依赖jar信息
    async fn execute_dependency_scan(project_url: &String) -> Result<()> {
        let start_scan_project = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let pom_file_path = Path::new(project_url).join(POM_FILE);
        if !pom_file_path.is_file() {
            return Ok(());
        }
        //step 1 读取pom.xml文件内容（同时替换变量)
        let content_opt = get_pom_content_without_variable(&pom_file_path);
        if content_opt.is_none() {
            return Ok(());
        }
        let dependency_list_opt = get_dependency_list(&content_opt.unwrap());
        if dependency_list_opt.is_none() {
            return Ok(());
        }
        let dependency_list = dependency_list_opt.unwrap();
        debug!("dependency jar size: {}",dependency_list.len());
        for dependency in dependency_list {
            build_ant_jar_info(dependency, &project_url).await;
            //构建二/三方jar是独立流程，中间也需要sleep下，防止agent占用资源过多
            if AGENT_CONFIG.scan_interval_time > 0 {
                //sleep一段时间，释放cpu
                thread::sleep(Duration::from_millis(AGENT_CONFIG.scan_interval_time))
            }
        }
        let end_scan_project = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        debug!("execute_dependency_scan end: {:?}",end_scan_project - start_scan_project);
        Ok(())
    }
}

const DEFAULT_PAGESIZE: usize = 20;
//最大循环时间：10s，防止死循环
const MAX_LOOP_TIME: u128 = 1000 * 60;

async fn build_ant_jar_info(dependency: Dependency, project_url: &String) {
    let mut query_request = QueryJarInfoRequestBean {
        groupId: dependency.groupId,
        artifactId: dependency.artifactId,
        version: dependency.version,
        pageNo: 1,
        pageSize: DEFAULT_PAGESIZE,
    };

    let start_millis = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    let flag = true;
    while flag {
        let end_millis = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        //兜底策略,防止死循环导致agent卡死
        if end_millis - start_millis > MAX_LOOP_TIME {
            break;
        }
        let dependency_info_result = CodeFuseCodeCompleteStrategy::query_dependency_info(query_request.clone()).await;
        let dependency_result_opt = dependency_info_result.data;
        if dependency_result_opt.is_none() {
            break;
        }
        let dependency_result = dependency_result_opt.unwrap();
        if dependency_result.len() == 0 {
            break;
        }
        debug!("dependency_info_result pageNo: {} , totalSize: {}", query_request.pageNo,dependency_info_result.totalCount.unwrap());
        for ant_jar_info in dependency_result {
            if ant_jar_info.codeStruct.len() == 0 || ant_jar_info.fullyQualifiedName.len() == 0 {
                continue;
            }
            let value_model = CodeInfo {
                code_struct: ant_jar_info.codeStruct,
                full_qualified_name: Some(ant_jar_info.fullyQualifiedName),
                import_set: None,
                class_type: None,
                class_name: None,
                extend_class_name: None,
                implements_class_name_set: None,
                fild_name_class_map: None,
                class_list: None,
                extend_typescript: None,
            };
            let value = serde_json::to_string(&value_model).unwrap();
            let key = format!("{}{}{}{}", R_PREFIX, project_url,SEPARATORSIMPLE, &value_model.full_qualified_name.unwrap());
            let _ = KV_CLIENT.insert(&key, &value);
        }
        if dependency_info_result.totalCount.unwrap() == 0 {
            break;
        }
        if (query_request.pageNo * DEFAULT_PAGESIZE) >= dependency_info_result.totalCount.unwrap() {
            break;
        }
        query_request.pageNo += 1;
    }
}

///开始执行全量扫描
/// 1.如果项目索引时间过期，或者是个新项目，check_and_del_old_project的返回值，index_file_num会是0
/// 2.index_file_num>0,
fn execute_scan_all_code(scan_config: &ScanConfig) -> Result<()> {
    let project_url = &scan_config.url;
    let code_completion_request = &scan_config.code_completion_request_bean;
    let android_flag = if code_completion_request.is_some() && is_android(&code_completion_request.clone().unwrap()) {
        true
    } else {
        false
    };

    //step 1: 先检查已有仓库数据，如果仓库变更，那么删除。
    let project_info_opt = check_and_del_old_project(project_url);
    if project_info_opt.is_none() {
        //一般来讲不会为none，为none说明check_and_del_old_project过程中kv操作异常，那么直接返回不做处理
        error!("project_info is none,kv has error when exec check_and_del_old_project.");
        return Ok(());
    }
    let mut project_info = project_info_opt.unwrap();
    if project_info.index_file_num > 0 {
        info!("project_info index data has exist.no need to scan all project");
        return Ok(());
    }
    let base = Path::new(project_url);

    let mut file_num: usize = 0;
    let start_scan_project = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    //step 2: 此处用ignore包的WalkDir，天然过滤.ignore文件和隐藏文件，但是注意。如果package下有多个子ignore仓库。会跳过子仓库
    for result in WalkBuilder::new(base).filter_entry(|e| !skip_dir(e)).build() {
        match result {
            Ok(dir) => {
                let file_type = dir.file_type();
                if file_type.is_none() {
                    continue;
                }
                if !file_type.unwrap().is_file() {
                    continue;
                }

                let start_scan_file = Instant::now();
                let scan_file_record_result: Result<Option<ScanFileRecord>> = if android_flag {
                    AndroidScanStrategy::execute_scan_file(dir.path(), None)
                } else {
                    execute_scan_file(dir.path(), project_url, None)
                };
                match scan_file_record_result {
                    Ok(scan_file_record_opt) => {
                        match scan_file_record_opt {
                            Some(_) => {
                                file_num = file_num + 1;
                            }
                            None => {
                                debug!("scan_file_record_opt is none,file_path:{}", dir.path().display());
                            }
                        }
                    }
                    Err(e) => {
                        error!("scan_file_record_result is err,file_path:{}, {}", dir.path().display(), e);
                    }
                }
                debug!("scan single file consume time: {}", start_scan_file.elapsed().as_millis())
            }
            Err(e) => {
                error!("Error while scanning directory: {}", e);
            }
        }
        if AGENT_CONFIG.scan_interval_time > 0 {
            //sleep一段时间，释放cpu
            thread::sleep(Duration::from_millis(AGENT_CONFIG.scan_interval_time))
        }
    }
    project_info.index_file_num = file_num;
    project_info.record_millis = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as usize;
    let scan_project_value = serde_json::to_string(&project_info).unwrap();
    let _ = KV_CLIENT.insert(project_url, &scan_project_value);
    let end_scan_project = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    debug!("scan project consume time: {}", end_scan_project - start_scan_project);
    Ok(())
}

///扫描单文件,调用这个函数前，已经明确dir是个文件而非目录
/// 如果file_content_opt不为空，那么不用读取文件内容，直接用file_content_opt的值
pub fn execute_scan_file(file_path: &Path, project_url: &String, file_content_opt: Option<String>) -> Result<Option<ScanFileRecord>> {
    //(1) 判断是否是文件，如果是文件，那么直接扫描
    //(2) 文件后缀
    //(3) 文件行级别数据
    let file_info = skip_file(file_path, file_content_opt);
    if file_info.0 {
        debug!("skip file,file_path:{}", file_path.display());
        return Ok(None);
    };
    let file_name_suffix = file_info.1.unwrap();
    let lines = file_info.2.unwrap();
    //先通过相似度切片，获取scanFile。然后通过相关性分析，填充scanFile.code_info值
    if AGENT_CONFIG.similarity_suffix_arr.contains(&file_name_suffix) {
        let scan_file_result = extra_and_save_similarity_code_fragment(file_path, &file_name_suffix, &lines, project_url);
        if scan_file_result.is_err() {
            error!("scan_file_result is err,file_path:{},{:?}", file_path.display(), scan_file_result);
            return Err(anyhow::anyhow!("scan_File_Result is err"));
        }
        let file_record_opt = scan_file_result.unwrap();
        return match file_record_opt {
            Some(mut file_record) => {
                if AGENT_CONFIG.related_suffix_arr.contains(&file_name_suffix) {
                    file_record.code_info = extra_related_code_fragment(&file_path, &file_name_suffix, &lines, project_url);
                }
                let file_key = format!("{}{}", FILE_PREFIX, file_path.to_str().unwrap());
                let scan_file_record_str = serde_json::to_string(&file_record).unwrap();
                let _ = KV_CLIENT.insert(&file_key, &scan_file_record_str);
                Ok(Some(file_record))
            }
            None => {
                Ok(None)
            }
        };
    }
    Ok(None)
}


#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::{AtomicBool, Ordering};
    use std::sync::Arc;


    use once_cell::sync::Lazy;


    static SHARED_IS_RUNNING: Lazy<Arc<AtomicBool>> = Lazy::new(|| Arc::new(AtomicBool::new(false)));

    #[test]
    fn it_works() {
        let running = SHARED_IS_RUNNING.clone();
        println!("11 {}", running.load(Ordering::SeqCst));

        if running.compare_exchange(false, true, Ordering::SeqCst, Ordering::SeqCst).is_err() {
            println!("22 Function is already running, request rejected.");
        }
        println!("33 {}", running.load(Ordering::SeqCst));

        if running.compare_exchange(false, true, Ordering::SeqCst, Ordering::SeqCst).is_err() {
            println!("44 Function is already running, request rejected.");
        }
        running.store(false, Ordering::SeqCst);
        println!("55 {}", running.load(Ordering::SeqCst));
    }

}

use std::error::Error;

/// 哈希版本标识符
const HASH_VERSION: &str = "cursorhashversionC7wtBsDmlFaPg4ToTvIlm";

/// 表示文本编辑的范围
#[derive(Debug, <PERSON><PERSON>)]
pub struct IRange {
    pub start_line_number: usize,
    pub start_column: usize,
    pub end_line_number: usize,
    pub end_column: usize,
}

/// 表示模型内容的变化
#[derive(Debug, <PERSON><PERSON>)]
pub struct IModelContentChange {
    pub range: IRange,
    pub range_offset: usize,
    pub range_length: usize,
    pub text: String,
}

/// 计算哈希值的基础函数
/// 使用无符号整数和模运算来避免溢出
fn hash_base(value: u32, hash: u32) -> u32 {
    hash.wrapping_mul(31).wrapping_add(value)
}

/// 计算字符串的哈希值
fn hash_string(s: &str, hash: u32) -> u32 {
    let mut result = hash_base(149417, hash);
    for &byte in s.as_bytes() {
        result = hash_base(byte as u32, result);
    }
    result
}

/// 计算模型的哈希值
pub fn cpp_model_hash(content: &str, version: u32) -> String {
    match version {
        0 => hash_string(content, 0).to_string(),
        1 => {
            let prefix = format!("{}:1:", HASH_VERSION);
            if content.len() > 10_000 {
                format!("{}{}", prefix, hash_string(content, 0))
            } else {
                format!("{}{}", prefix, content)
            }
        }
        _ => panic!("Unsupported hash version"),
    }
}

/// 计算两个范围相交的行数
pub fn num_intersecting_lines(range1: &IRange, range2: &IRange) -> usize {
    let start = range1.start_line_number.max(range2.start_line_number);
    let mut end = range1.end_line_number.min(range2.end_line_number);
    
    // 如果开始行大于结束行，将结束行设置为开始行
    if start > end {
        end = start;
    }
    
    end - start
}

/// 合并编辑范围
pub fn merge_edit_ranges(
    first_added_range: &IRange,
    first_removed_range: &IRange,
    second_added_range: &IRange,
    second_removed_range: &IRange,
) -> Result<(IRange, IRange), Box<dyn Error>> {
    // 验证输入参数的有效性
    if first_removed_range.start_line_number != first_added_range.start_line_number {
        return Err(format!(
            "Range validation failed: firstRemovedRange.startLineNumber ({}) !== firstAddedRange.startLineNumber ({})",
            first_removed_range.start_line_number,
            first_added_range.start_line_number
        ).into());
    }
    
    if second_removed_range.start_line_number != second_added_range.start_line_number {
        return Err(format!(
            "Range validation failed: secondRemovedRange.startLineNumber ({}) !== secondAddedRange.startLineNumber ({})",
            second_removed_range.start_line_number,
            second_added_range.start_line_number
        ).into());
    }

    // 验证范围的有效性
    if first_removed_range.start_line_number > first_removed_range.end_line_number {
        return Err(format!(
            "Invalid first_removed_range: start_line_number ({}) > end_line_number ({})",
            first_removed_range.start_line_number,
            first_removed_range.end_line_number
        ).into());
    }
    
    if second_removed_range.start_line_number > second_removed_range.end_line_number {
        return Err(format!(
            "Invalid second_removed_range: start_line_number ({}) > end_line_number ({})",
            second_removed_range.start_line_number,
            second_removed_range.end_line_number
        ).into());
    }
    
    if first_added_range.start_line_number > first_added_range.end_line_number {
        return Err(format!(
            "Invalid first_added_range: start_line_number ({}) > end_line_number ({})",
            first_added_range.start_line_number,
            first_added_range.end_line_number
        ).into());
    }
    
    if second_added_range.start_line_number > second_added_range.end_line_number {
        return Err(format!(
            "Invalid second_added_range: start_line_number ({}) > end_line_number ({})",
            second_added_range.start_line_number,
            second_added_range.end_line_number
        ).into());
    }

    // 验证范围关系的合理性
    if second_removed_range.end_line_number < first_added_range.end_line_number {
        return Err(format!(
            "Invalid range relationship: second_removed_range.end_line_number ({}) < first_added_range.end_line_number ({})",
            second_removed_range.end_line_number,
            first_added_range.end_line_number
        ).into());
    }

    // 计算合并后的范围
    let start_line = first_removed_range.start_line_number.min(second_removed_range.start_line_number);
    
    let offset = second_removed_range.end_line_number.checked_sub(first_added_range.end_line_number)
        .ok_or_else(|| "Integer overflow in range calculation: second_removed_range.end_line_number - first_added_range.end_line_number")?;

    let past_range = IRange {
        start_line_number: start_line,
        start_column: 1,
        end_line_number: first_removed_range.end_line_number.checked_add(offset)
            .ok_or_else(|| "Integer overflow in past_range calculation: first_removed_range.end_line_number + offset")?,
        end_column: 1,
    };

    let current_start = start_line;
    let intersecting_lines = num_intersecting_lines(
        second_removed_range,
        &IRange {
            start_line_number: 1,
            start_column: 1,
            end_line_number: first_added_range.end_line_number,
            end_column: 1,
        },
    );

    let current_end = first_added_range.end_line_number.max(second_added_range.end_line_number)
        .checked_add(
            num_intersecting_lines(second_added_range, &IRange {
                start_line_number: 1,
                start_column: 1,
                end_line_number: first_added_range.end_line_number,
                end_column: 1,
            })
            .checked_sub(intersecting_lines)
            .ok_or_else(|| "Integer overflow in intersecting lines calculation")?
        )
        .ok_or_else(|| "Integer overflow in current_end calculation")?;

    let current_range = IRange {
        start_line_number: current_start,
        start_column: 1,
        end_line_number: current_end,
        end_column: 1,
    };

    Ok((past_range, current_range))
}

/// 将变更应用到范围
pub fn apply_change_to_range(
    original_range: &IRange,
    change_range: &IRange,
    new_range: &IRange,
) -> IRange {
    let calculate_offset = |line: usize| -> usize {
        if line > change_range.start_line_number {
            if line > change_range.end_line_number {
                new_range.end_line_number.checked_sub(change_range.end_line_number).unwrap_or(0)
            } else {
                new_range.end_line_number.checked_sub(line).unwrap_or(0)
            }
        } else {
            0
        }
    };

    IRange {
        start_line_number: new_range.start_line_number.min(original_range.start_line_number + calculate_offset(original_range.start_line_number)),
        start_column: 1,
        end_line_number: new_range.end_line_number.max(original_range.end_line_number + calculate_offset(original_range.end_line_number)),
        end_column: 1,
    }
}

/// 修改模型内容
pub fn change_model(content: &str, change: &IModelContentChange) -> Result<String, Box<dyn Error + Send + Sync>> {
    let mut lines: Vec<&str> = content.split('\n').collect();
    
    // 验证范围 - 检查开始和结束行号是否都在有效范围内
    if change.range.start_line_number > lines.len() + 1 
        || (change.range.start_line_number == lines.len() + 1 && change.range.start_column != 1) {
        return Err("Start of the range is outside the file.".into());
    }
    
    // 检查结束行号是否超出范围
    if change.range.end_line_number > lines.len() + 1 {
        return Err("End of the range is outside the file.".into());
    }

    let start_line = change.range.start_line_number - 1;
    let end_line = change.range.end_line_number - 1;
    
    // 确保 end_line 不超过数组长度
    let end_line = end_line.min(lines.len() - 1);
    
    // 获取要修改的文本部分
    let prefix = if start_line < lines.len() {
        let line = lines[start_line];
        let mut chars = line.chars();
        let prefix_len: usize = chars.by_ref().take(change.range.start_column - 1).map(|c| c.len_utf8()).sum();
        &line[..prefix_len.min(line.len())]
    } else {
        ""
    };
    
    let suffix = if end_line < lines.len() {
        let line = lines[end_line];
        if change.range.end_column == 1 {
            ""
        } else {
            let mut chars = line.chars();
            let suffix_start: usize = chars.by_ref().take(change.range.end_column - 1).map(|c| c.len_utf8()).sum();
            &line[suffix_start.min(line.len())..]
        }
    } else {
        ""
    };

    // 应用变更 - 使用安全的范围
    let new_content = format!("{}{}{}", prefix, change.text, suffix);
    lines.splice(start_line..=end_line, new_content.split('\n'));

    Ok(lines.join("\n"))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cpp_model_hash() {
        assert_eq!(cpp_model_hash("test", 0), "149417".to_string());
        assert!(cpp_model_hash("test", 1).starts_with(&format!("{}:1:", HASH_VERSION)));
    }

    #[test]
    fn test_cpp_model_hash_large_content() {
        // 测试大文件内容，确保不会溢出
        let large_content = "x".repeat(100_000);
        let result = cpp_model_hash(&large_content, 0);
        assert!(result.parse::<u32>().is_ok());
        
        let result_v1 = cpp_model_hash(&large_content, 1);
        assert!(result_v1.starts_with(&format!("{}:1:", HASH_VERSION)));
    }

    #[test]
    fn test_num_intersecting_lines() {
        let range1 = IRange {
            start_line_number: 1,
            start_column: 1,
            end_line_number: 5,
            end_column: 1,
        };
        let range2 = IRange {
            start_line_number: 3,
            start_column: 1,
            end_line_number: 7,
            end_column: 1,
        };
        assert_eq!(num_intersecting_lines(&range1, &range2), 2);
    }

    #[test]
    fn test_merge_edit_ranges_success() {
        let first_added = IRange {
            start_line_number: 1,
            start_column: 1,
            end_line_number: 3,
            end_column: 1,
        };
        let first_removed = IRange {
            start_line_number: 1,
            start_column: 1,
            end_line_number: 2,
            end_column: 1,
        };
        let second_added = IRange {
            start_line_number: 2,
            start_column: 1,
            end_line_number: 4,
            end_column: 1,
        };
        let second_removed = IRange {
            start_line_number: 2,
            start_column: 1,
            end_line_number: 3,
            end_column: 1,
        };

        let result = merge_edit_ranges(&first_added, &first_removed, &second_added, &second_removed);
        assert!(result.is_ok());
        
        let (past_range, current_range) = result.unwrap();
        assert_eq!(past_range.start_line_number, 1);
        assert_eq!(current_range.start_line_number, 1);
    }

    #[test]
    fn test_merge_edit_ranges_validation_error() {
        let first_added = IRange {
            start_line_number: 1,
            start_column: 1,
            end_line_number: 3,
            end_column: 1,
        };
        let first_removed = IRange {
            start_line_number: 2, // 不匹配 first_added.start_line_number
            start_column: 1,
            end_line_number: 2,
            end_column: 1,
        };
        let second_added = IRange {
            start_line_number: 2,
            start_column: 1,
            end_line_number: 4,
            end_column: 1,
        };
        let second_removed = IRange {
            start_line_number: 2,
            start_column: 1,
            end_line_number: 3,
            end_column: 1,
        };

        let result = merge_edit_ranges(&first_added, &first_removed, &second_added, &second_removed);
        assert!(result.is_err());
        
        let error_msg = result.unwrap_err().to_string();
        assert!(error_msg.contains("firstRemovedRange.startLineNumber"));
        assert!(error_msg.contains("firstAddedRange.startLineNumber"));
    }

    #[test]
    fn test_merge_edit_ranges_invalid_range() {
        let first_added = IRange {
            start_line_number: 1,
            start_column: 1,
            end_line_number: 3,
            end_column: 1,
        };
        let first_removed = IRange {
            start_line_number: 1,
            start_column: 1,
            end_line_number: 2,
            end_column: 1,
        };
        let second_added = IRange {
            start_line_number: 2,
            start_column: 1,
            end_line_number: 4,
            end_column: 1,
        };
        let second_removed = IRange {
            start_line_number: 2,
            start_column: 1,
            end_line_number: 1, // 无效范围：end < start
            end_column: 1,
        };

        let result = merge_edit_ranges(&first_added, &first_removed, &second_added, &second_removed);
        assert!(result.is_err());
        
        let error_msg = result.unwrap_err().to_string();
        assert!(error_msg.contains("Invalid second_removed_range"));
    }

    #[test]
    fn test_change_model() {
        let content = "line1\nline2\nline3";
        let change = IModelContentChange {
            range: IRange {
                start_line_number: 2,
                start_column: 1,
                end_line_number: 2,
                end_column: 6,
            },
            range_offset: 0,
            range_length: 5,
            text: "new line".to_string(),
        };
        let result = change_model(content, &change).unwrap();
        assert_eq!(result, "line1\nnew line\nline3");
    }

    #[test]
    fn test_change_model_out_of_bounds() {
        let content = "line1\nline2\nline3";
        
        // 测试结束行号超出范围的情况
        let change = IModelContentChange {
            range: IRange {
                start_line_number: 2,
                start_column: 1,
                end_line_number: 10, // 超出文件行数
                end_column: 1,
            },
            range_offset: 0,
            range_length: 5,
            text: "new line".to_string(),
        };
        
        let result = change_model(content, &change);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("End of the range is outside the file"));
    }
}

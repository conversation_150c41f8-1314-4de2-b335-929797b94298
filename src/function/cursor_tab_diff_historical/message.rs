use serde::{Deserialize, Serialize};

/// 表示文件的差异历史
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CppFileDiffHistory {
    /// 文件名
    pub file_name: String,
    /// 差异历史记录
    pub diff_history: Vec<String>,
    /// 差异历史的时间戳
    pub diff_history_timestamps: Vec<u64>,
}

impl CppFileDiffHistory {
    /// 创建一个新的文件差异历史
    pub fn new(file_name: String, diff_history: Vec<String>, diff_history_timestamps: Vec<u64>) -> Self {
        Self {
            file_name,
            diff_history,
            diff_history_timestamps,
        }
    }

    /// 从 JSON 对象创建文件差异历史
    pub fn from_json(json: &str) -> Result<Self, serde_json::Error> {
        serde_json::from_str(json)
    }

    /// 转换为 JSON 字符串
    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string(self)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cpp_file_diff_history() {
        let history = CppFileDiffHistory::new(
            "test.rs".to_string(),
            vec!["diff1".to_string(), "diff2".to_string()],
            vec![1234567890, 1234567891],
        );

        assert_eq!(history.file_name, "test.rs");
        assert_eq!(history.diff_history.len(), 2);
        assert_eq!(history.diff_history_timestamps.len(), 2);
    }

    #[test]
    fn test_serialization() {
        let history = CppFileDiffHistory::new(
            "test.rs".to_string(),
            vec!["diff1".to_string(), "diff2".to_string()],
            vec![1234567890, 1234567891],
        );

        let json = history.to_json().unwrap();
        let deserialized = CppFileDiffHistory::from_json(&json).unwrap();

        assert_eq!(history.file_name, deserialized.file_name);
        assert_eq!(history.diff_history, deserialized.diff_history);
        assert_eq!(history.diff_history_timestamps, deserialized.diff_history_timestamps);
    }
}

//! Cursor diff historical implementation in Rust
//! 
//! This library provides functionality for tracking and managing text edits
//! in a cursor-based editor.

pub mod types;
pub mod diff;
pub mod diff_history;
pub mod historical;
pub mod manager;
pub mod lock;
pub mod utils;
pub mod range;
pub mod message;

pub use diff::*;
pub use diff_history::*;
pub use historical::*;
pub use manager::*;
pub use range::*;
// Re-export commonly used types
pub use types::*;

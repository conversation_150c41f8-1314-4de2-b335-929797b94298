use crate::function::cursor_tab_diff_historical::diff_history::EditHistoryFormatter;
use crate::function::cursor_tab_diff_historical::range::{get_new_line_range, get_old_line_range};
use crate::function::cursor_tab_diff_historical::utils::{change_model, IModelContentChange};
use anyhow::Result;

/// 处理单个变更
async fn process_single_change(
    formatter: &mut EditHistoryFormatter,
    change: &IModelContentChange,
    new_model_value: &str,
    unique_model_identifier: &str,
    use_full_file_diff: bool,
) -> Result<()> {
    formatter.process_model_change(crate::function::cursor_tab_diff_historical::diff_history::ModelChangeArgs {
        unique_model_identifier: unique_model_identifier.to_string(),
        new_model_value: new_model_value.to_string(),
        deleted_range_in_old_model_one_indexed: get_old_line_range(change),
        added_range_in_new_model_one_indexed: get_new_line_range(change),
        use_full_file_diff_for_this_call: use_full_file_diff,
    }).await;
    Ok(())
}

/// 处理多个变更
async fn process_multiple_changes(
    formatter: &mut EditHistoryFormatter,
    changes: &[IModelContentChange],
    new_model_value: &str,
    unique_model_identifier: &str,
    use_full_file_diff: bool,
    previous_model_value: &str,
) -> Result<()> {
    let mut current_model = previous_model_value.to_string();
    
    for change in changes.iter().rev() {
        // 捕获 change_model 的错误，如果失败则记录错误并跳过此变更
        let updated_model = match change_model(&current_model, change) {
            Ok(model) => model,
            Err(e) => {
                log::error!("Failed to apply change to model: {}", e);
                // 如果无法应用变更，跳过此变更继续处理下一个
                continue;
            }
        };
        formatter.process_model_change(crate::function::cursor_tab_diff_historical::diff_history::ModelChangeArgs {
            unique_model_identifier: unique_model_identifier.to_string(),
            new_model_value: updated_model.clone(),
            deleted_range_in_old_model_one_indexed: get_old_line_range(change),
            added_range_in_new_model_one_indexed: get_new_line_range(change),
            use_full_file_diff_for_this_call: use_full_file_diff,
        }).await;
        current_model = updated_model;
    }
    Ok(())
}

/// 格式化差异历史记录
pub async fn format_diff_history(
    file_path: &str,
    formatter: &mut EditHistoryFormatter,
    current_content: &str,
    changes: &[IModelContentChange],
    new_version: usize,
) -> Result<Option<crate::function::cursor_tab_diff_historical::message::CppFileDiffHistory>> {
    let current_version = formatter.get_model_version(file_path);
    
    // 检查模型是否存在
    if formatter.get_model_value(file_path).is_none() {
        formatter.init_model(file_path, &current_content, Some(&new_version.to_string()));
        return Ok(None);
    }
    
    // 获取当前模型值
    let current_model_value = formatter.get_model_value(file_path).unwrap().to_string();
    
    // 处理变更
    let mut changes_to_process = changes.to_vec();
    changes_to_process.sort_by(|a, b| {
        if b.range.start_line_number == a.range.start_line_number {
            b.range.start_column.cmp(&a.range.start_column)
        } else {
            b.range.start_line_number.cmp(&a.range.start_line_number)
        }
    });
    
    // 计算是否需要完整差异
    let mut needs_full_diff = false;
    match change_model(&current_model_value, &changes_to_process[0]) {
        Ok(updated_model) => {
            if updated_model != current_content {
                needs_full_diff = true;
            }
        }
        Err(_) => {
            needs_full_diff = true;
        }
    }
    
    if current_version.map_or(true, |v| v.parse::<usize>().unwrap_or(0) + 1 != new_version) {
        needs_full_diff = true;
    }
    
    // 更新版本
    formatter.update_model_version(file_path, &new_version.to_string());
    
    // 处理变更
    if changes_to_process.len() == 1 {
        process_single_change(
            formatter,
            &changes_to_process[0],
            &current_content,
            file_path,
            needs_full_diff,
        ).await?;
    } else {
        process_multiple_changes(
            formatter,
            &changes_to_process,
            &current_content,
            file_path,
            needs_full_diff,
            &current_model_value,
        ).await?;
    }
    
    // 编译差异轨迹
    let result = formatter.compile_global_diff_trajectories(None, None).await?;

    Ok(result.first().cloned())
}

#[cfg(test)]
mod tests {
    use super::*;



    fn create_test_formatter() -> EditHistoryFormatter {
        EditHistoryFormatter::new(crate::function::cursor_tab_diff_historical::diff_history::EditHistoryFormatterConfig {
            diff_history_size_limit: Some(10),
            patch_string_size_limit: Some(5),
            whitespace_patch_string_size_limit: Some(2),
            keep_recent_model_hash_count: Some(5),
            skip_model_hash: false,
            save_one_before_last_model_value: Some(true),
            keep_whitespace_only_changes: Some(true),
            always_use_full_file_diff: Some(false),
            git_diff_extra_context_radius: Some(0),
            merging_behavior: None,
            include_unchanged_lines: Some(true),
        })
    }


    #[tokio::test]
    async fn test_version_management() {
        let mut formatter = create_test_formatter();
        let file_path = "test_file.txt";
        let initial_content = "line1\nline2\nline3";
        
        // 初始化模型
        formatter.init_model(file_path, initial_content, Some("1"));
        
        // 验证初始版本
        assert_eq!(formatter.get_model_version(file_path), Some("1"));
        
        // 更新版本
        formatter.update_model_version(file_path, "2");
        assert_eq!(formatter.get_model_version(file_path), Some("2"));
    }

} 
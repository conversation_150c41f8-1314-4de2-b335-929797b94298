use crate::function::cursor_tab_diff_historical::utils::IModelContentChange;

/// 表示行范围的简单结构
#[derive(Debug, <PERSON><PERSON>)]
pub struct LineRange {
    pub start_line_number: usize,
    pub end_line_number_exclusive: usize,
}

/// 获取旧的行范围
pub fn get_old_line_range(change: &IModelContentChange) -> LineRange {
    LineRange {
        start_line_number: change.range.start_line_number,
        end_line_number_exclusive: change.range.end_line_number + 1,
    }
}

/// 获取新的行范围
pub fn get_new_line_range(change: &IModelContentChange) -> LineRange {
    LineRange {
        start_line_number: change.range.start_line_number,
        end_line_number_exclusive: change.range.start_line_number + change.text.split('\n').count(),
    }
}

#[cfg(test)]
mod tests {
    #[test]
    fn test_get_old_line_range() {
        // let change = IModelContentChange {
        //     range: crate::mult_strategy::code_edits_diff_historical::utils::IRange {
        //         start_line_number: 1,
        //         start_column: 1,
        //         end_line_number: 3,
        //         end_column: 1,
        //     },
        //     range_offset: 0,
        //     range_length: 10,
        //     text: "new text".to_string(),
        // };
        // 
        // let range = get_old_line_range(&change);
        // assert_eq!(range.start_line_number, 1);
        // assert_eq!(range.end_line_number_exclusive, 4);
    }

    #[test]
    fn test_get_new_line_range() {
        // let change = IModelContentChange {
        //     range: crate::mult_strategy::code_edits_diff_historical::utils::IRange {
        //         start_line_number: 1,
        //         start_column: 1,
        //         end_line_number: 3,
        //         end_column: 1,
        //     },
        //     range_offset: 0,
        //     range_length: 10,
        //     text: "line1\nline2\nline3".to_string(),
        // };
        // 
        // let range = get_new_line_range(&change);
        // assert_eq!(range.start_line_number, 1);
        // assert_eq!(range.end_line_number_exclusive, 4);
    }
}

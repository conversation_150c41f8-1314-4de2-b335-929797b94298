use crate::function::cursor_tab_diff_historical::diff;
use crate::{
    function::cursor_tab_diff_historical::diff::{calculate_diff, DiffConfig, DiffR<PERSON>ult, RangePair as DiffRangePair},
    function::cursor_tab_diff_historical::lock::ModelLockManager,
    function::cursor_tab_diff_historical::message::CppFileDiffHistory,
    function::cursor_tab_diff_historical::range::LineRange,
    function::cursor_tab_diff_historical::utils::{apply_change_to_range, cpp_model_hash, merge_edit_ranges, IRange},
};
use anyhow::Result;
use log::error;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::time::timeout;

/// 合并行为配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum MergingBehavior {
    #[serde(rename = "merged diff history")]
    MergedDiffHistory { radius: usize },
    #[serde(rename = "whitespace compatible")]
    WhitespaceCompatible,
}

/// 模型哈希记录
#[derive(Debug, Clone)]
struct ModelHashRecord {
    model_hash: String,
    seen_at: u64,
}

/// 差异状态
#[derive(Debug, Clone)]
struct DiffState {
    one_before_last_model: Option<String>,
    oldest_model: String,
    new_model: String,
    diff_history: Vec<DiffHistoryEntry>,
    last_edit_time: usize,
    model_version: Option<String>,
    added_range_in_new_model_one_indexed: Option<LineRange>,
    ranges_of_accumulated_changes: Option<RangePair>,
    cached_patch_string: Option<DiffResult>,
}

/// 差异历史条目
#[derive(Debug, Clone)]
struct DiffHistoryEntry {
    patch: DiffResult,
    last_edit_time: usize,
}

/// 范围对
#[derive(Debug, Clone)]
struct RangePair {
    current_range: LineRange,
    past_range: LineRange,
}

/// 编辑历史格式化器
pub struct EditHistoryFormatter {
    diff_history_size_limit: usize,
    patch_string_size_limit: usize,
    whitespace_patch_string_size_limit: usize,
    keep_recent_model_hash_count: usize,
    timeout_count: usize,
    use_full_file_diffs_til_unmerged: bool,
    include_unchanged_lines: bool,
    previous_compiled_global_diff_trajectories: Vec<CppFileDiffHistory>,
    latest_compiled_global_diff_trajectories_version: Option<String>,
    version: Option<String>,
    diff_state_by_identifier: HashMap<String, DiffState>,
    process_model_change_loop_running: bool,
    changes_to_process_args: Vec<ModelChangeArgs>,
    max_loop_duration_ms: u64,
    recent_model_hashes: Option<HashMap<String, Vec<ModelHashRecord>>>,
    save_one_before_last_model_value: bool,
    keep_whitespace_only_changes: bool,
    always_use_full_file_diff: bool,
    git_diff_extra_context_radius: usize,
    merging_behavior: MergingBehavior,
    change_index: usize,
    model_lock_manager: ModelLockManager,
}

/// 模型变更参数
#[derive(Debug)]
pub struct ModelChangeArgs {
    pub use_full_file_diff_for_this_call: bool,
    pub unique_model_identifier: String,
    pub new_model_value: String,
    pub added_range_in_new_model_one_indexed: LineRange,
    pub deleted_range_in_old_model_one_indexed: LineRange,
}

impl EditHistoryFormatter {
    pub fn new(config: EditHistoryFormatterConfig) -> Self {
        Self {
            diff_history_size_limit: config.diff_history_size_limit.unwrap_or(90),
            patch_string_size_limit: config.patch_string_size_limit.unwrap_or(40),
            whitespace_patch_string_size_limit: config.whitespace_patch_string_size_limit.unwrap_or(5),
            keep_recent_model_hash_count: config.keep_recent_model_hash_count.unwrap_or(20),
            timeout_count: 0,
            use_full_file_diffs_til_unmerged: false,
            include_unchanged_lines: config.include_unchanged_lines.unwrap_or(true),
            previous_compiled_global_diff_trajectories: Vec::new(),
            latest_compiled_global_diff_trajectories_version: None,
            version: None,
            diff_state_by_identifier: HashMap::new(),
            process_model_change_loop_running: false,
            changes_to_process_args: Vec::new(),
            max_loop_duration_ms: 5000,
            recent_model_hashes: if config.skip_model_hash {
                None
            } else {
                Some(HashMap::new())
            },
            save_one_before_last_model_value: config.save_one_before_last_model_value.unwrap_or(false),
            keep_whitespace_only_changes: config.keep_whitespace_only_changes.unwrap_or(false),
            always_use_full_file_diff: config.always_use_full_file_diff.unwrap_or(false),
            git_diff_extra_context_radius: config.git_diff_extra_context_radius.unwrap_or(0),
            merging_behavior: config.merging_behavior.unwrap_or(MergingBehavior::WhitespaceCompatible),
            change_index: 0,
            model_lock_manager: ModelLockManager::new(10),
        }
    }


    pub fn init_model(&mut self, identifier: &str, model_value: &str, model_version: Option<&str>) {
        if let Some(hashes) = &mut self.recent_model_hashes {
            let model_hashes = hashes.entry(identifier.to_string()).or_insert_with(Vec::new);
            model_hashes.push(ModelHashRecord {
                model_hash: cpp_model_hash(model_value, 1),
                seen_at: SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis() as u64,
            });
            if model_hashes.len() > self.keep_recent_model_hash_count {
                model_hashes.drain(0..model_hashes.len() - self.keep_recent_model_hash_count);
            }
        }

        self.diff_state_by_identifier.insert(
            identifier.to_string(),
            DiffState {
                one_before_last_model: if self.save_one_before_last_model_value {
                    Some(model_value.to_string())
                } else {
                    None
                },
                oldest_model: model_value.to_string(),
                new_model: model_value.to_string(),
                diff_history: Vec::new(),
                last_edit_time: {
                    self.change_index += 1;
                    self.change_index
                },
                model_version: model_version.map(|s| s.to_string()),
                added_range_in_new_model_one_indexed: None,
                ranges_of_accumulated_changes: None,
                cached_patch_string: None,
            },
        );
    }

    pub fn get_model_value(&self, identifier: &str) -> Option<&str> {
        self.diff_state_by_identifier
            .get(identifier)
            .map(|state| state.new_model.as_str())
    }

    pub fn get_model_version(&self, identifier: &str) -> Option<&str> {
        self.diff_state_by_identifier
            .get(identifier)
            .and_then(|state| state.model_version.as_deref())
    }

    pub fn update_model_version(&mut self, identifier: &str, version: &str) {
        if let Some(state) = self.diff_state_by_identifier.get_mut(identifier) {
            state.model_version = Some(version.to_string());
        }
    }

    fn filter_patch(&self, patch: Option<&DiffResult>) -> bool {
        if let Some(patch) = patch {
            let line_count = patch.string.lines().count();
            !patch.string.trim().is_empty()
                && line_count <= self.patch_string_size_limit
                && (!patch.is_whitespace_change
                    || line_count <= self.whitespace_patch_string_size_limit)
        } else {
            false
        }
    }

    pub async fn compile_global_diff_trajectories(
        &mut self,
        file_names: Option<HashMap<String, String>>,
        use_full_file_diff: Option<bool>,
    ) -> Result<Vec<CppFileDiffHistory>> {
        if let Some(version) = &self.version {
            if self.latest_compiled_global_diff_trajectories_version.as_ref() == Some(version) {
                return Ok(self.previous_compiled_global_diff_trajectories.clone());
            }
        }

        let file_names = file_names.unwrap_or_default();
        let mut all_changes = Vec::new();

        // 收集所有需要处理的信息
        let mut pending_operations = Vec::new();
        for (identifier, state) in &self.diff_state_by_identifier {
            // 收集 diff history
            let mut changes: Vec<_> = state
                .diff_history
                .iter()
                .map(|entry| {
                    let file_name = file_names.get(identifier).unwrap_or(identifier).clone();
                    CppFileDiffHistory {
                        file_name,
                        diff_history: vec![entry.patch.string.clone()],
                        diff_history_timestamps: vec![entry.last_edit_time as u64],
                    }
                })
                .collect();
            all_changes.append(&mut changes);

            // 收集需要处理的 active diff
            if let Some(added_range) = &state.added_range_in_new_model_one_indexed {
                pending_operations.push((
                    identifier.clone(),
                    state.last_edit_time,
                    state.new_model.clone(),
                ));
            }
        }

        // 处理所有需要可变操作的任务
        for (identifier, last_edit_time, new_model) in pending_operations {
            match self.get_patch_of_active_diff(&identifier, use_full_file_diff, true).await {
                Ok(patch) => {
                    let file_name = file_names.get(&identifier).unwrap_or(&identifier).clone();
                    all_changes.push(CppFileDiffHistory {
                        file_name,
                        diff_history: vec![patch.string],
                        diff_history_timestamps: vec![last_edit_time as u64],
                    });
                }
                Err(e) => {
                    if e.to_string().to_lowercase().contains("timeout") {
                        self.update_oldest_model(&identifier, &new_model);
                        self.timeout_count += 1;
                    } else {
                        return Err(e);
                    }
                }
            }
        }

        // 按时间戳排序
        all_changes.sort_by_key(|change| change.diff_history_timestamps[0]);

        // 过滤和合并结果
        let filtered_changes: Vec<_> = all_changes
            .into_iter()
            .filter(|change| {
                let patch = DiffResult {
                    string: change.diff_history[0].clone(),
                    is_whitespace_change: false,
                };
                self.filter_patch(Some(&patch))
            })
            .collect();

        // 合并相同文件名的历史记录
        let mut result: Vec<CppFileDiffHistory> = Vec::new();
        for change in filtered_changes {
            if let Some(last) = result.last_mut() {
                if last.file_name == change.file_name {
                    last.diff_history.extend(change.diff_history);
                    last.diff_history_timestamps.extend(change.diff_history_timestamps);
                } else {
                    result.push(change);
                }
            } else {
                result.push(change);
            }
        }

        self.latest_compiled_global_diff_trajectories_version = self.version.clone();
        self.previous_compiled_global_diff_trajectories = result.clone();

        Ok(result)
    }

    pub async fn process_model_changes_loop_with_timeout(&mut self) {
        if self.process_model_change_loop_running {
            return;
        }

        self.process_model_change_loop_running = true;
        let timeout_future = tokio::time::sleep(Duration::from_millis(self.max_loop_duration_ms));
        
        tokio::select! {
            _ = self.process_model_changes_loop() => {
                // 正常完成
            }
            _ = timeout_future => {
                println!("processModelChangesLoop timed out");
            }
        }

        self.process_model_change_loop_running = false;
    }

    async fn process_model_changes_loop(&mut self) -> Result<()> {
        while let Some(args) = self.changes_to_process_args.pop() {
            let ModelChangeArgs {
                use_full_file_diff_for_this_call,
                unique_model_identifier,
                new_model_value,
                added_range_in_new_model_one_indexed,
                deleted_range_in_old_model_one_indexed,
            } = args;

            if use_full_file_diff_for_this_call {
                self.use_full_file_diffs_til_unmerged = true;
            }

            if self.diff_state_by_identifier.get(&unique_model_identifier).is_none() {
                self.init_model(&unique_model_identifier, &new_model_value, None);
                continue;
            }

            // 获取需要的信息
            let (should_merge, is_newest) = {
                let state = self.diff_state_by_identifier.get(&unique_model_identifier).unwrap();
                let should_merge = if let Some(ref range) = state.added_range_in_new_model_one_indexed {
                    self.should_merge(range, &deleted_range_in_old_model_one_indexed)
                } else {
                    false
                };
                let is_newest = self.newest_model_identifier() == Some(&unique_model_identifier);
                (should_merge, is_newest)
            };

            let state = self.diff_state_by_identifier.get_mut(&unique_model_identifier).unwrap();
            let added_range = state.added_range_in_new_model_one_indexed.clone();
            let last_edit_time = state.last_edit_time;
            let new_model = state.new_model.clone();

            if added_range.is_none() || last_edit_time == 0 {
                if !use_full_file_diff_for_this_call {
                    self.use_full_file_diffs_til_unmerged = false;
                }
                state.added_range_in_new_model_one_indexed = Some(added_range_in_new_model_one_indexed.clone());
                state.ranges_of_accumulated_changes = Some(RangePair {
                    current_range: added_range_in_new_model_one_indexed,
                    past_range: deleted_range_in_old_model_one_indexed,
                });
            } else if should_merge && is_newest {
                let merged_range = apply_change_to_range(
                    &added_range.as_ref().unwrap().clone().into(),
                    &deleted_range_in_old_model_one_indexed.clone().into(),
                    &added_range_in_new_model_one_indexed.clone().into(),
                );

                state.added_range_in_new_model_one_indexed = Some(merged_range.into());

                if let Some(ranges) = &mut state.ranges_of_accumulated_changes {
                    let merged_ranges = match merge_edit_ranges(
                        &ranges.current_range.clone().into(),
                        &ranges.past_range.clone().into(),
                        &added_range_in_new_model_one_indexed.clone().into(),
                        &deleted_range_in_old_model_one_indexed.clone().into(),
                    ) {
                        Ok(ranges) => ranges,
                        Err(e) => {
                            eprintln!("Failed to merge edit ranges: {}", e);
                            // 如果合并失败，保持原有范围不变
                            continue;
                        }
                    };
                    state.ranges_of_accumulated_changes = Some(RangePair {
                        current_range: merged_ranges.0.into(),
                        past_range: merged_ranges.1.into(),
                    });
                }
            } else {
                // 先释放 state 的可变引用
                drop(state);
                
                let patch = self.get_patch_of_active_diff(&unique_model_identifier, Some(false), false).await;
                let mut state = self.diff_state_by_identifier.get_mut(&unique_model_identifier).unwrap();
                
                match patch {
                    Ok(patch) => {
                        state.diff_history.push(DiffHistoryEntry {
                            patch,
                            last_edit_time,
                        });
                    }
                    Err(e) => {
                        if e.to_string().to_lowercase().contains("timeout") {
                            self.timeout_count += 1;
                        } else {
                            error!("Error in process_model_changes_loop: {}", e);
                            // 记录错误但不 panic
                            return Err(e);
                        }
                    }
                }

                // 再次释放 state 的可变引用
                drop(state);
                
                self.update_oldest_model(&unique_model_identifier, &new_model);
                self.clean_diff_trajectories();
                
                let state = self.diff_state_by_identifier.get_mut(&unique_model_identifier).unwrap();
                state.added_range_in_new_model_one_indexed = Some(added_range_in_new_model_one_indexed.clone());
                if !use_full_file_diff_for_this_call {
                    self.use_full_file_diffs_til_unmerged = false;
                }
                state.ranges_of_accumulated_changes = Some(RangePair {
                    current_range: added_range_in_new_model_one_indexed,
                    past_range: deleted_range_in_old_model_one_indexed,
                });
            }

            self.change_index += 1;
            let state = self.diff_state_by_identifier.get_mut(&unique_model_identifier).unwrap();
            state.last_edit_time = self.change_index;
            self.update_new_model(&unique_model_identifier, &new_model_value);
        }
        Ok(())
    }

    pub async fn process_model_change(&mut self, args: ModelChangeArgs) {
        if let Some(hashes) = &mut self.recent_model_hashes {
            let model_hashes = hashes
                .entry(args.unique_model_identifier.clone())
                .or_insert_with(Vec::new);
            model_hashes.push(ModelHashRecord {
                model_hash: cpp_model_hash(&args.new_model_value, 1),
                seen_at: SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis() as u64,
            });
            if model_hashes.len() > self.keep_recent_model_hash_count {
                model_hashes.drain(0..model_hashes.len() - self.keep_recent_model_hash_count);
            }
        }

        self.changes_to_process_args.push(args);
        self.process_model_changes_loop_with_timeout().await;
    }

    fn should_merge(&self, last_diff_range: &LineRange, replaced_change_range: &LineRange) -> bool {
        let is_same_range = replaced_change_range.start_line_number == last_diff_range.start_line_number
            && replaced_change_range.end_line_number_exclusive == last_diff_range.end_line_number_exclusive;
        let is_single_line = replaced_change_range.end_line_number_exclusive - replaced_change_range.start_line_number == 1;

        match &self.merging_behavior {
            MergingBehavior::MergedDiffHistory { .. } => {
                is_single_line && last_diff_range.start_line_number == replaced_change_range.end_line_number_exclusive
                    || is_same_range
            }
            MergingBehavior::WhitespaceCompatible => {
                is_same_range
                    || (is_single_line && last_diff_range.start_line_number == replaced_change_range.end_line_number_exclusive)
                    || (is_single_line && last_diff_range.end_line_number_exclusive == replaced_change_range.start_line_number)
                    || (replaced_change_range.end_line_number_exclusive - replaced_change_range.start_line_number <= 2
                        && last_diff_range.end_line_number_exclusive == replaced_change_range.end_line_number_exclusive)
            }
        }
    }

    fn clean_diff_trajectories(&mut self) {
        let mut total_history_size = 0;
        let mut filtered_histories = Vec::new();

        // 第一阶段：收集过滤后的历史记录
        for (identifier, state) in &self.diff_state_by_identifier {
            let filtered_history: Vec<_> = state.diff_history
                .iter()
                .filter(|entry| {
                    let patch = entry.patch.clone();
                    self.filter_patch(Some(&patch))
                })
                .cloned()
                .collect();
            let history_len = filtered_history.len();
            filtered_histories.push((identifier.clone(), filtered_history));
            total_history_size += history_len;
        }

        // 第二阶段：更新状态
        for (identifier, filtered_history) in filtered_histories {
            if let Some(state) = self.diff_state_by_identifier.get_mut(&identifier) {
                state.diff_history = filtered_history;
            }
        }

        while total_history_size > self.diff_history_size_limit {
            self.remove_oldest_diff_trajectory();
            total_history_size -= 1;
        }
    }

    async fn get_patch_of_active_diff(
        &mut self,
        identifier: &str,
        use_full_file_diff: Option<bool>,
        is_whitespace_check: bool,
    ) -> Result<DiffResult> {
        let state = self.diff_state_by_identifier.get_mut(identifier).unwrap();
        
        if let Some(cached_patch) = &state.cached_patch_string {
            return Ok(cached_patch.clone());
        }

        let ranges = if self.always_use_full_file_diff
            || use_full_file_diff.unwrap_or(false)
            || self.use_full_file_diffs_til_unmerged
        {
            None
        } else {
            state.ranges_of_accumulated_changes.clone().map(|ranges| DiffRangePair {
                current_range: diff::LineRange {
                    start_line_number: ranges.current_range.start_line_number,
                    end_line_number_exclusive: ranges.current_range.end_line_number_exclusive,
                },
                past_range: diff::LineRange {
                    start_line_number: ranges.past_range.start_line_number,
                    end_line_number_exclusive: ranges.past_range.end_line_number_exclusive,
                },
            })
        };

        let config = DiffConfig {
            radius: self.git_diff_extra_context_radius,
            keep_whitespace_changes: self.keep_whitespace_only_changes && is_whitespace_check,
            ranges_of_past_and_current: ranges,
            include_unchanged_lines: self.include_unchanged_lines,
            timeout: None,
        };

        let patch = calculate_diff(&state.oldest_model, &state.new_model, config).await;

        if !is_whitespace_check || !patch.as_ref().map_or(false, |p| p.is_whitespace_change) {
            state.cached_patch_string = patch.clone();
        }

        Ok(patch.unwrap_or_else(|| DiffResult {
            string: String::new(),
            is_whitespace_change: false,
        }))
    }

    fn newest_model_identifier(&self) -> Option<&str> {
        self.diff_state_by_identifier
            .iter()
            .max_by_key(|(_, state)| state.last_edit_time)
            .map(|(identifier, _)| identifier.as_str())
    }

    fn update_new_model(&mut self, identifier: &str, new_model: &str) {
        if let Some(state) = self.diff_state_by_identifier.get_mut(identifier) {
            if self.save_one_before_last_model_value {
                state.one_before_last_model = Some(state.new_model.clone());
            }
            state.new_model = new_model.to_string();
            state.cached_patch_string = None;
        }
    }

    fn update_oldest_model(&mut self, identifier: &str, oldest_model: &str) {
        if let Some(state) = self.diff_state_by_identifier.get_mut(identifier) {
            state.oldest_model = oldest_model.to_string();
            state.cached_patch_string = None;
        }
    }

    fn remove_oldest_diff_trajectory(&mut self) {
        let mut oldest_time = usize::MAX;
        let mut oldest_identifier = None;

        for (identifier, state) in &self.diff_state_by_identifier {
            if let Some(first_entry) = state.diff_history.first() {
                if first_entry.last_edit_time < oldest_time {
                    oldest_time = first_entry.last_edit_time;
                    oldest_identifier = Some(identifier.clone());
                }
            }
        }

        if let Some(identifier) = oldest_identifier {
            if let Some(state) = self.diff_state_by_identifier.get_mut(&identifier) {
                state.diff_history.remove(0);
            }
        }
    }
}

/// 编辑历史格式化器配置
#[derive(Debug, Default)]
pub struct EditHistoryFormatterConfig {
    pub diff_history_size_limit: Option<usize>,
    pub patch_string_size_limit: Option<usize>,
    pub whitespace_patch_string_size_limit: Option<usize>,
    pub keep_recent_model_hash_count: Option<usize>,
    pub skip_model_hash: bool,
    pub save_one_before_last_model_value: Option<bool>,
    pub keep_whitespace_only_changes: Option<bool>,
    pub always_use_full_file_diff: Option<bool>,
    pub git_diff_extra_context_radius: Option<usize>,
    pub merging_behavior: Option<MergingBehavior>,
    pub include_unchanged_lines: Option<bool>,
}

// 为 LineRange 实现 From<IRange> 和 Into<IRange>
impl From<LineRange> for IRange {
    fn from(range: LineRange) -> Self {
        IRange {
            start_line_number: range.start_line_number,
            start_column: 0,
            end_line_number: range.end_line_number_exclusive,
            end_column: 0,
        }
    }
}

impl From<IRange> for LineRange {
    fn from(range: IRange) -> Self {
        LineRange {
            start_line_number: range.start_line_number,
            end_line_number_exclusive: range.end_line_number,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;
    use tokio::sync::Mutex;

    fn create_test_formatter() -> EditHistoryFormatter {
        EditHistoryFormatter::new(EditHistoryFormatterConfig {
            diff_history_size_limit: Some(10),
            patch_string_size_limit: Some(5),
            whitespace_patch_string_size_limit: Some(3),
            keep_recent_model_hash_count: Some(5),
            skip_model_hash: false,
            save_one_before_last_model_value: Some(true),
            keep_whitespace_only_changes: Some(true),
            always_use_full_file_diff: Some(false),
            git_diff_extra_context_radius: Some(1),
            merging_behavior: Some(MergingBehavior::WhitespaceCompatible),
            include_unchanged_lines: Some(true),
        })
    }

    #[tokio::test]
    async fn test_init_model() {
        let mut formatter = create_test_formatter();
        formatter.init_model("test_file", "line1\nline2\nline3", Some("v1"));
        
        assert_eq!(formatter.get_model_value("test_file"), Some("line1\nline2\nline3"));
        assert_eq!(formatter.get_model_version("test_file"), Some("v1"));
    }

    #[tokio::test]
    async fn test_process_model_change() {
        let mut formatter = create_test_formatter();
        formatter.init_model("test_file", "line1\nline2\nline3", None);

        let args = ModelChangeArgs {
            use_full_file_diff_for_this_call: false,
            unique_model_identifier: "test_file".to_string(),
            new_model_value: "line1\nline2_modified\nline3".to_string(),
            added_range_in_new_model_one_indexed: LineRange {
                start_line_number: 2,
                end_line_number_exclusive: 3,
            },
            deleted_range_in_old_model_one_indexed: LineRange {
                start_line_number: 2,
                end_line_number_exclusive: 3,
            },
        };

        formatter.process_model_change(args).await;
        assert_eq!(
            formatter.get_model_value("test_file"),
            Some("line1\nline2_modified\nline3")
        );
    }

    #[tokio::test]
    async fn test_compile_global_diff_trajectories() {
        let mut formatter = create_test_formatter();
        formatter.init_model("test_file", "line1\nline2\nline3", None);

        // 添加一些变更
        let args = ModelChangeArgs {
            use_full_file_diff_for_this_call: false,
            unique_model_identifier: "test_file".to_string(),
            new_model_value: "line1\nline2_modified\nline3".to_string(),
            added_range_in_new_model_one_indexed: LineRange {
                start_line_number: 2,
                end_line_number_exclusive: 3,
            },
            deleted_range_in_old_model_one_indexed: LineRange {
                start_line_number: 2,
                end_line_number_exclusive: 3,
            },
        };

        formatter.process_model_change(args).await;

        let trajectories = formatter
            .compile_global_diff_trajectories(None, None)
            .await
            .unwrap();

        assert!(!trajectories.is_empty());
        assert!(trajectories[0].diff_history.iter().any(|line| line.contains("line2_modified")));
    }

    #[test]
    fn test_should_merge() {
        let formatter = create_test_formatter();

        // 测试相同范围
        let range1 = LineRange {
            start_line_number: 1,
            end_line_number_exclusive: 3,
        };
        let range2 = LineRange {
            start_line_number: 1,
            end_line_number_exclusive: 3,
        };
        assert!(formatter.should_merge(&range1, &range2));

        // 测试单行连续
        let range3 = LineRange {
            start_line_number: 1,
            end_line_number_exclusive: 2,
        };
        let range4 = LineRange {
            start_line_number: 2,
            end_line_number_exclusive: 3,
        };
        assert!(formatter.should_merge(&range3, &range4));
    }

    #[tokio::test]
    async fn test_clean_diff_trajectories() {
        let mut formatter = create_test_formatter();
        formatter.init_model("test_file", "line1\nline2\nline3", None);

        // 添加超过限制的变更
        for i in 0..15 {
            let args = ModelChangeArgs {
                use_full_file_diff_for_this_call: false,
                unique_model_identifier: "test_file".to_string(),
                new_model_value: format!("line1\nline2_{}\nline3", i),
                added_range_in_new_model_one_indexed: LineRange {
                    start_line_number: 2,
                    end_line_number_exclusive: 3,
                },
                deleted_range_in_old_model_one_indexed: LineRange {
                    start_line_number: 2,
                    end_line_number_exclusive: 3,
                },
            };
            formatter.process_model_change(args).await;
        }

        let trajectories = formatter
            .compile_global_diff_trajectories(None, None)
            .await
            .unwrap();

        // 验证历史记录不超过限制
        assert!(trajectories[0].diff_history.len() <= formatter.diff_history_size_limit);
    }

    #[tokio::test]
    async fn test_concurrent_model_changes() {
        let formatter = Arc::new(Mutex::new(create_test_formatter()));
        formatter.lock().await.init_model("test_file", "line1\nline2\nline3", None);

        let mut handles = vec![];
        for i in 0..5 {
            let formatter_clone = formatter.clone();
            let handle = tokio::spawn(async move {
                let args = ModelChangeArgs {
                    use_full_file_diff_for_this_call: false,
                    unique_model_identifier: "test_file".to_string(),
                    new_model_value: format!("line1\nline2_{}\nline3", i),
                    added_range_in_new_model_one_indexed: LineRange {
                        start_line_number: 2,
                        end_line_number_exclusive: 3,
                    },
                    deleted_range_in_old_model_one_indexed: LineRange {
                        start_line_number: 2,
                        end_line_number_exclusive: 3,
                    },
                };
                formatter_clone.lock().await.process_model_change(args).await;
            });
            handles.push(handle);
        }

        for handle in handles {
            handle.await.unwrap();
        }

        let mut final_state = formatter.lock().await;
        let trajectories = final_state
            .compile_global_diff_trajectories(None, None)
            .await
            .unwrap();

        assert!(!trajectories.is_empty());
        assert!(trajectories[0].diff_history.len() <= final_state.diff_history_size_limit);
    }

    #[tokio::test]
    async fn test_model_version_update() {
        let mut formatter = create_test_formatter();
        formatter.init_model("test_file", "line1\nline2\nline3", Some("v1"));
        
        formatter.update_model_version("test_file", "v2");
        assert_eq!(formatter.get_model_version("test_file"), Some("v2"));
        
        // 测试不存在的文件
        formatter.update_model_version("non_existent", "v3");
        assert_eq!(formatter.get_model_version("non_existent"), None);
    }
}

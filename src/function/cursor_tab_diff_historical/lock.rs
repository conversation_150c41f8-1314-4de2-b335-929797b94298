use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;

/// LRU 缓存实现
struct LRUCache<K, V> {
    cache: HashMap<K, V>,
    max_size: usize,
}

impl<K: std::hash::Hash + Eq + Clone, V> LRUCache<K, V> {
    fn new(max_size: usize) -> Self {
        Self {
            cache: HashMap::new(),
            max_size,
        }
    }

    fn size(&self) -> usize {
        self.cache.len()
    }

    fn get(&mut self, key: &K) -> Option<&V> {
        if let Some(value) = self.cache.remove(key) {
            self.cache.insert(key.clone(), value);
            self.cache.get(key)
        } else {
            None
        }
    }

    fn set(&mut self, key: K, value: V) {
        if self.cache.contains_key(&key) {
            self.cache.remove(&key);
        } else if self.cache.len() >= self.max_size {
            if let Some(first_key) = self.cache.keys().next().cloned() {
                self.cache.remove(&first_key);
            }
        }
        self.cache.insert(key, value);
    }

    fn delete(&mut self, key: &K) {
        self.cache.remove(key);
    }
}

/// 简单的锁实现
#[derive(Clone)]
struct SimpleLock {
    state: Arc<Mutex<LockState>>,
}

struct LockState {
    is_locked: bool,
    queue: Vec<Box<dyn FnOnce() + Send>>,
}

impl SimpleLock {
    fn new() -> Self {
        Self {
            state: Arc::new(Mutex::new(LockState {
                is_locked: false,
                queue: Vec::new(),
            })),
        }
    }

    async fn acquire(&self) -> Box<dyn FnOnce() + Send> {
        let state = self.state.clone();
        {
            let mut state = state.lock().await;
            if state.is_locked {
                let (tx, rx) = tokio::sync::oneshot::channel();
                state.queue.push(Box::new(move || {
                    let _ = tx.send(());
                }));
                let _ = rx.await;
            }
            state.is_locked = true;
        }
        Box::new(move || {
            let state = state.clone();
            tokio::spawn(async move {
                let mut state = state.lock().await;
                state.is_locked = false;
                if let Some(next) = state.queue.pop() {
                    next();
                }
                drop(state);
            });
        })
    }
}

/// 模型锁管理器
pub struct ModelLockManager {
    locks: Arc<Mutex<LRUCache<String, SimpleLock>>>,
}

impl ModelLockManager {
    pub fn new(max_size: usize) -> Self {
        Self {
            locks: Arc::new(Mutex::new(LRUCache::new(max_size))),
        }
    }

    pub async fn acquire_lock(&self, key: String) -> Box<dyn FnOnce() + Send> {
        let mut locks = self.locks.lock().await;
        let lock = locks.get(&key).cloned().unwrap_or_else(|| {
            let new_lock = SimpleLock::new();
            locks.set(key.clone(), new_lock.clone());
            new_lock
        });
        return lock.acquire().await;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_lru_cache() {
        let mut cache = LRUCache::new(2);
        cache.set("key1".to_string(), "value1");
        cache.set("key2".to_string(), "value2");
        cache.set("key3".to_string(), "value3");
        
        assert_eq!(cache.size(), 2);
        assert!(cache.get(&"key1".to_string()).is_none());
    }

    #[tokio::test]
    async fn test_model_lock_manager() {
        let manager = ModelLockManager::new(100);
        let release = manager.acquire_lock("test".to_string()).await;
        release();
    }
}

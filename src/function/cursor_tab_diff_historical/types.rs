use serde::{Deserialize, Serialize};

/// 表示文本编辑的范围
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct Range {
    /// 起始行号（从1开始）
    pub start_line_number: usize,
    /// 起始列号（从1开始）
    pub start_column: usize,
    /// 结束行号（从1开始）
    pub end_line_number: usize,
    /// 结束列号（从1开始）
    pub end_column: usize,
}

impl Range {
    /// 创建一个新的范围
    pub fn new(
        start_line_number: usize,
        start_column: usize,
        end_line_number: usize,
        end_column: usize,
    ) -> Self {
        Self {
            start_line_number,
            start_column,
            end_line_number,
            end_column,
        }
    }

    /// 检查范围是否有效
    pub fn is_valid(&self) -> bool {
        self.start_line_number <= self.end_line_number
            && (self.start_line_number != self.end_line_number
                || self.start_column <= self.end_column)
    }

    /// 检查范围是否为空
    pub fn is_empty(&self) -> bool {
        self.start_line_number == self.end_line_number
            && self.start_column == self.end_column
    }
}

/// 表示模型内容的变化
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct ModelContentChange {
    /// 变化发生的范围
    pub range: Range,
    /// 变化在文本中的偏移量
    pub range_offset: usize,
    /// 变化影响的文本长度
    pub range_length: usize,
    /// 新的文本内容
    pub text: String,
}

impl ModelContentChange {
    /// 创建一个新的内容变化
    pub fn new(range: Range, range_offset: usize, range_length: usize, text: String) -> Self {
        Self {
            range,
            range_offset,
            range_length,
            text,
        }
    }

    /// 检查变化是否有效
    pub fn is_valid(&self) -> bool {
        self.range.is_valid()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_range_validation() {
        let valid_range = Range::new(1, 1, 2, 1);
        assert!(valid_range.is_valid());

        let invalid_range = Range::new(2, 1, 1, 1);
        assert!(!invalid_range.is_valid());

        let empty_range = Range::new(1, 1, 1, 1);
        assert!(empty_range.is_empty());
    }

    #[test]
    fn test_model_content_change() {
        let range = Range::new(1, 1, 1, 5);
        let change = ModelContentChange::new(range, 0, 4, "test".to_string());
        assert!(change.is_valid());
    }
}

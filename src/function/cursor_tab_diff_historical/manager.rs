use crate::function::cursor_tab_diff_historical::diff_history::EditHistoryFormatter;
use crate::function::cursor_tab_diff_historical::utils::{IModelContentChange, IRange};
use agent_common_service::model::code_edit_model::{
    CppFileDiffHistory, DiffHistoricalInitModelRequest, DiffHistoricalRecordRequest
};
use anyhow::{anyhow, Result};
use once_cell::sync::Lazy;
use std::sync::Arc;
use tokio::sync::Mutex;

pub static CURSOR_DIFF_HISTORICAL_INSTANCE: Lazy<Arc<Mutex<CursorDiffHistoricalManager>>> =
    Lazy::new(|| Arc::new(Mutex::new(CursorDiffHistoricalManager::new())));

/// 差异历史管理器
pub struct CursorDiffHistoricalManager {
    edit_history_formatter: EditHistoryFormatter,
}

impl CursorDiffHistoricalManager {
    /// 创建一个新的差异历史管理器
    pub fn new() -> Self {
        Self {
            edit_history_formatter: EditHistoryFormatter::new(crate::function::cursor_tab_diff_historical::diff_history::EditHistoryFormatterConfig {
                diff_history_size_limit: Some(90),
                patch_string_size_limit: Some(40),
                whitespace_patch_string_size_limit: Some(5),
                keep_recent_model_hash_count: Some(20),
                skip_model_hash: false,
                save_one_before_last_model_value: Some(true),
                keep_whitespace_only_changes: Some(true),
                always_use_full_file_diff: Some(false),
                git_diff_extra_context_radius: Some(0),
                merging_behavior: None,
                include_unchanged_lines: Some(true),
            }),
        }
    }

    /// 初始化模型
    pub fn init_model(&mut self, document: &DiffHistoricalInitModelRequest) {
        let path = &document.uri;
        let relative_path = path; // 这里可以根据需要处理路径

        if self
            .edit_history_formatter
            .get_model_value(relative_path)
            .is_none()
        {
            self.edit_history_formatter.init_model(
                relative_path,
                &document.text,
                Some(&document.version.to_string()),
            );
        }
    }

    /// 处理文档变化
    pub async fn handle_document_change(
        &mut self,
        request: &DiffHistoricalRecordRequest,
    ) -> Result<()> {
        let path = &request.uri;
        let relative_path = path; // 这里可以根据需要处理路径

        if self
            .edit_history_formatter
            .get_model_value(relative_path)
            .is_none()
        {
            return Err(anyhow!("Model not initialized for path: {}", relative_path));
        }

        // 将 DiffHistoricalContentChange 转换为 IModelContentChange
        let content_changes: Vec<IModelContentChange> = request
            .content_changes
            .iter()
            .map(|change| IModelContentChange {
                range: IRange {
                    start_line_number: change.range.start_line_number as usize + 1,
                    start_column: change.range.start_column as usize + 1,
                    end_line_number: change.range.end_line_number as usize + 1,
                    end_column: change.range.end_column as usize + 1,
                },
                range_offset: change.range_offset,
                range_length: change.range_length,
                text: change.text.clone(),
            })
            .collect();

        crate::function::cursor_tab_diff_historical::historical::format_diff_history(
            relative_path,
            &mut self.edit_history_formatter,
            &request.text,
            &content_changes,
            request.version as usize,
        )
        .await?;

        Ok(())
    }

    /// 获取差异轨迹
    pub async fn get_diff_trajectories(
        &mut self,
    ) -> Result<Vec<CppFileDiffHistory>>
    {
        let trajectories = self.edit_history_formatter
            .compile_global_diff_trajectories(None, None)
            .await?;
        
        Ok(trajectories.into_iter().map(|t| CppFileDiffHistory {
            file_name: t.file_name,
            diff_history: t.diff_history,
            diff_history_timestamps: Some(t.diff_history_timestamps),
        }).collect())
    }
}

#[cfg(test)]
mod tests {

}

use similar::{Algorithm, ChangeTag, TextDiff};
use std::time::Duration;

/// 差异比较的配置
pub struct DiffConfig {
    pub radius: usize,
    pub keep_whitespace_changes: bool,
    pub ranges_of_past_and_current: Option<RangePair>,
    pub include_unchanged_lines: bool,
    pub timeout: Option<Duration>,
}

impl Default for DiffConfig {
    fn default() -> Self {
        Self {
            radius: 1,
            keep_whitespace_changes: true,
            ranges_of_past_and_current: None,
            include_unchanged_lines: true,
            timeout: Some(Duration::from_secs(2)),
        }
    }
}

/// 范围对
pub struct RangePair {
    pub past_range: LineRange,
    pub current_range: LineRange,
}

/// 行范围
pub struct LineRange {
    pub start_line_number: usize,
    pub end_line_number_exclusive: usize,
}

/// 差异比较的结果
#[derive(Debug, Clone)]
pub struct DiffResult {
    pub string: String,
    pub is_whitespace_change: bool,
}

/// 计算差异
pub async fn calculate_diff(
    past: &str,
    current: &str,
    config: DiffConfig,
) -> Option<DiffResult> {
    let (past, current, current_offset_line_number, past_offset_line_number) = 
        prepare_texts(past, current, config.ranges_of_past_and_current);

    let diff = if past.lines().count() > 100 || current.lines().count() > 100 {
        if let Some(timeout) = config.timeout {
            tokio::time::timeout(
                timeout,
                async {
                    TextDiff::configure()
                        .algorithm(Algorithm::Myers)
                        .diff_lines(&past, &current)
                },
            )
            .await
            .ok()?
        } else {
            TextDiff::configure()
                .algorithm(Algorithm::Myers)
                .diff_lines(&past, &current)
        }
    } else {
        TextDiff::configure()
            .algorithm(Algorithm::Myers)
            .diff_lines(&past, &current)
    };

    let mut min_line = usize::MAX;
    let mut max_non_empty_line = usize::MIN;
    let mut max_line = usize::MIN;
    let mut current_line = 1;

    for change in diff.iter_all_changes() {
        let count = change.value().lines().count();
        if change.tag() != ChangeTag::Equal {
            min_line = min_line.min(current_line);
            if !change.value().trim().is_empty() {
                max_non_empty_line = max_non_empty_line.max(current_line + count - 1);
            }
            max_line = max_line.max(current_line + count - 1);
        }
        current_line += count;
    }

    min_line = min_line.saturating_sub(config.radius);
    if max_non_empty_line == usize::MIN {
        max_non_empty_line = max_line;
    }
    max_non_empty_line += config.radius;

    if min_line == usize::MAX || max_non_empty_line == usize::MIN {
        return None;
    }

    let is_whitespace_change = past.replace(char::is_whitespace, "") == 
        current.replace(char::is_whitespace, "");
    if is_whitespace_change && !config.keep_whitespace_changes {
        return None;
    }

    let mut result = String::new();
    let mut past_line = min_line - 1;
    let mut current_line = min_line - 1;

    for line_num in min_line..=max_non_empty_line {
        let mut current_line_num = 1;
        let change = diff.iter_all_changes().find(|change| {
            let count = change.value().lines().count();
            let end = current_line_num + count;
            let is_in_range = current_line_num <= line_num && line_num < end;
            current_line_num = end;
            is_in_range
        });

        if let Some(change) = change {
            let count = change.value().lines().count();
            let line_index = line_num - (current_line_num - count);
            let line = change.value().lines().nth(line_index).unwrap_or("");

            match change.tag() {
                ChangeTag::Insert => {
                    current_line += 1;
                    result.push_str(&format!("{}+|{}\n", (current_line + current_offset_line_number).to_string(), line));
                }
                ChangeTag::Delete => {
                    past_line += 1;
                    result.push_str(&format!("{}-|{}\n", (past_line + past_offset_line_number).to_string(), line));
                }
                ChangeTag::Equal => {
                    if config.include_unchanged_lines {
                        past_line += 1;
                        current_line += 1;
                        result.push_str(&format!("{} |{}\n", (current_line + current_offset_line_number).to_string(), line));
                    } else {
                        past_line += 1;
                        current_line += 1;
                    }
                }
            }
        }
    }

    Some(DiffResult {
        string: result,
        is_whitespace_change,
    })
}

/// 计算删除和添加的范围
pub async fn compute_deleted_added_ranges(
    past: &str,
    current: &str,
) -> Option<RangePair> {
    let diff = TextDiff::configure()
        .algorithm(Algorithm::Myers)
        .diff_lines(past, current);

    let mut min_line = usize::MAX;
    let mut max_non_empty_line = usize::MIN;
    let mut max_line = usize::MIN;
    let mut current_line = 1;

    for change in diff.iter_all_changes() {
        let count = change.value().lines().count();
        if change.tag() != ChangeTag::Equal {
            min_line = min_line.min(current_line);
            if !change.value().trim().is_empty() {
                max_non_empty_line = max_non_empty_line.max(current_line + count - 1);
            }
            max_line = max_line.max(current_line + count - 1);
        }
        current_line += count;
    }

    min_line = min_line.max(0);
    if max_non_empty_line == usize::MIN {
        max_non_empty_line = max_line;
    }

    let mut past_line = min_line - 1;
    let mut current_line = min_line - 1;
    let mut min_added = usize::MAX;
    let mut max_added = usize::MIN;
    let mut min_deleted = usize::MAX;
    let mut max_deleted = usize::MIN;

    for line_num in min_line..=max_non_empty_line {
        let mut current_line_num = 1;
        let change = diff.iter_all_changes().find(|change| {
            let count = change.value().lines().count();
            let end = current_line_num + count;
            let is_in_range = current_line_num <= line_num && line_num < end;
            current_line_num = end;
            is_in_range
        });

        if let Some(change) = change {
            let count = change.value().lines().count();
            match change.tag() {
                ChangeTag::Insert => {
                    current_line += 1;
                    min_added = min_added.min(current_line);
                    max_added = max_added.max(current_line);
                }
                ChangeTag::Delete => {
                    past_line += 1;
                    min_deleted = min_deleted.min(past_line);
                    max_deleted = max_deleted.max(past_line);
                }
                ChangeTag::Equal => {
                    past_line += 1;
                    current_line += 1;
                }
            }
        }
    }

    if min_added == usize::MAX && min_deleted == usize::MAX {
        return None;
    }

    let (old_range, new_range) = if min_added == usize::MAX {
        (
            LineRange {
                start_line_number: min_deleted,
                end_line_number_exclusive: max_deleted + 1,
            },
            LineRange {
                start_line_number: min_deleted,
                end_line_number_exclusive: max_deleted + 1,
            },
        )
    } else if min_deleted == usize::MAX {
        (
            LineRange {
                start_line_number: min_added,
                end_line_number_exclusive: max_added + 1,
            },
            LineRange {
                start_line_number: min_added,
                end_line_number_exclusive: max_added + 1,
            },
        )
    } else {
        (
            LineRange {
                start_line_number: min_deleted,
                end_line_number_exclusive: max_deleted + 1,
            },
            LineRange {
                start_line_number: min_added,
                end_line_number_exclusive: max_added + 1,
            },
        )
    };

    Some(RangePair {
        past_range: old_range,
        current_range: new_range,
    })
}

/// 准备要比较的文本
fn prepare_texts(
    orig_past: &str,
    orig_current: &str,
    ranges: Option<RangePair>,
) -> (String, String, usize, usize) {
    let ranges = match ranges {
        Some(r) => r,
        None => {
            return (
                orig_past.to_string(),
                orig_current.to_string(),
                0,
                0,
            );
        }
    };

    let mut current_range = ranges.current_range;
    let mut past_range = ranges.past_range;
    let current_lines: Vec<&str> = orig_current.lines().collect();
    let past_lines: Vec<&str> = orig_past.lines().collect();

    if current_range.end_line_number_exclusive < current_lines.len() 
        && past_range.end_line_number_exclusive < past_lines.len() {
        current_range.end_line_number_exclusive += 1;
        past_range.end_line_number_exclusive += 1;
    }

    if current_range.start_line_number > 1 && past_range.start_line_number > 1 {
        current_range.start_line_number -= 1;
        past_range.start_line_number -= 1;
    }

    let context_before = (current_range.start_line_number - 1)
        .min(past_range.start_line_number - 1)
        .min(25)
        .max(0);

    let context_after = (current_lines.len() + 1)
        .saturating_sub(current_range.end_line_number_exclusive)
        .min(past_lines.len() + 1)
        .saturating_sub(past_range.end_line_number_exclusive)
        .min(25)
        .max(0);

    current_range.start_line_number -= context_before;
    past_range.start_line_number -= context_before;
    current_range.end_line_number_exclusive += context_after;
    past_range.end_line_number_exclusive += context_after;

    let current = current_lines
        .get(current_range.start_line_number - 1..current_range.end_line_number_exclusive - 1)
        .unwrap_or(&[])
        .join("\n");

    let past = past_lines
        .get(past_range.start_line_number - 1..past_range.end_line_number_exclusive - 1)
        .unwrap_or(&[])
        .join("\n");

    (
        past,
        current,
        current_range.start_line_number - 1,
        past_range.start_line_number - 1,
    )
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_calculate_diff() {
        let past = "line1\nline2\nline3";
        let current = "line1\nline2_modified\nline3";
        let config = DiffConfig {
            radius: 1,
            keep_whitespace_changes: true,
            ranges_of_past_and_current: None,
            include_unchanged_lines: true,
            timeout: None,
        };

        let result = calculate_diff(past, current, config).await.unwrap();
        assert!(result.string.contains("line2_modified"));
        assert!(!result.is_whitespace_change);
    }

    #[tokio::test]
    async fn test_compute_deleted_added_ranges() {
        let past = "line1\nline2\nline3";
        let current = "line1\nline2_modified\nline3";
        let result = compute_deleted_added_ranges(past, current).await.unwrap();
        assert_eq!(result.past_range.start_line_number, 2);
        assert_eq!(result.current_range.start_line_number, 2);
    }
}

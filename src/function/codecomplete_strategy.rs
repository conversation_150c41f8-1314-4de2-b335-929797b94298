use log::{debug, error, info};
use once_cell::sync::Lazy;

use std::fmt::Debug;
use std::sync::Arc;
use std::time::Duration;

use agent_common_service::model::code_complete_model::{
    CodeCompletionRequestBean, CompletionResultModel, QueryJarInfoRequestBean,
};

use agent_common_service::service::code_complete::CodeCompleteStrategy;
use agent_common_service::tools::common_tools::AntJarCodeInfo;
use agent_db::remote::http_client::{post_request, DEFAULT_TIMEOUT_MILLIS};
use agent_db::remote::rpc_model::{build_error_response, BaseResponse};
use serde::de::DeserializeOwned;
use serde::Serialize;
use tokio::time::Instant;

///codefuse默认代码补全策略
pub struct CodeFuseCodeCompleteStrategy;
///代码补全
const CODE_COMPLETION_URL: &str = "https://caselike.alipay.com/v1/function/commandGptCodegen";
///查询jar信息
const QUERY_ANT_JAR_INFO_URL: &str = "https://caselike.alipay.com/v1/antJarClassInfo/list";

impl CodeCompleteStrategy for CodeFuseCodeCompleteStrategy {
    ///默认代码补全策略
    async fn code_complete(
        code_complete_request: CodeCompletionRequestBean,
    ) -> BaseResponse<CompletionResultModel> {
        let start_time = Instant::now();
        info!(
            "Code Completion request: {}",
            serde_json::to_string(&code_complete_request).unwrap()
        );
        let base_response = post_request::<CompletionResultModel, _>(
            CODE_COMPLETION_URL,
            &code_complete_request,
            DEFAULT_TIMEOUT_MILLIS,
            "codefuse agent code complete failed",
        )
        .await;
        if let Some(response) = base_response {
            info!(
                "code_complete http time: {} ms, response={:?}",
                start_time.elapsed().as_millis(),
                response
            );
            response
        } else {
            error!(
                "code_complete http time: {} ms,param: {:?}",
                start_time.elapsed().as_millis(),
                code_complete_request
            );
            build_response(3, "codefuse agent code complete failed".to_string())
        }
    }

    async fn query_dependency_info(
        query_jar_info_request_bean: QueryJarInfoRequestBean,
    ) -> BaseResponse<Vec<AntJarCodeInfo>> {
        let start_time = Instant::now();
        let base_response = post_request::<Vec<AntJarCodeInfo>, _>(
            QUERY_ANT_JAR_INFO_URL,
            &query_jar_info_request_bean,
            DEFAULT_TIMEOUT_MILLIS,
            "query dependency info failed",
        )
        .await;
        if let Some(response) = base_response {
            debug!(
                "query_dependency_info http time: {} ms, response={:?}",
                start_time.elapsed().as_millis(),
                response
            );
            response
        } else {
            error!(
                "query_dependency_info http time: {} ms,param: {:?}",
                start_time.elapsed().as_millis(),
                query_jar_info_request_bean
            );
            BaseResponse::default()
        }
    }
}

fn build_response(error_code: u8, error_msg: String) -> BaseResponse<CompletionResultModel> {
    build_error_response(error_code, error_msg)
}

#[cfg(test)]
mod tests {
    use regex::Regex;

    const CLASS_REGEX: &str = r"(?m)^\s*(?:@\w+\s+)?(?:public|protected|private|abstract|final|static|\s)*\s*(class|enum|interface|@interface)\s+(\w+)";

    const content: &str = r#"/*
 * Ant Group
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.alipay.pairhub.biz.rpc.processor.trigger;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alipay.pairhub.biz.rpc.request.FunfairTriggerRequest;
import com.alipay.pairhub.biz.rpc.request.activity.ActivityQueryRequest;
import com.alipay.pairhub.biz.rpc.response.TriggerResponse;
import com.alipay.pairhub.comm.constant.ActivityConstant;
import com.alipay.pairhub.comm.exception.BizException;
import com.alipay.pairhub.common.enums.BusinessTypeEnum;
import com.alipay.pairhub.common.enums.activity.ActivityStatusEnum;
import com.alipay.pairhub.common.enums.operations.SampleOperationType;
import com.alipay.pairhub.common.enums.prize.PrizeRelationType;
import com.alipay.pairhub.common.template.method.BusinessProcessor;
import com.alipay.pairhub.common.utils.DateFormatUtil;
import com.alipay.pairhub.common.utils.LoggerUtil;
import com.alipay.pairhub.common.utils.exception.BizErrEnum;
import com.alipay.pairhub.core.model.activity.UserCheckInRecordDO;
import com.alipay.pairhub.core.model.activity.vo.ActivityInfoVO;
import com.alipay.pairhub.core.model.prize.UserPrizeInfoDO;
import com.alipay.pairhub.core.service.activity.ActivityService;
import com.alipay.pairhub.core.service.activity.UserCheckInRecordService;
import com.alipay.pairhub.core.service.impl.activity.checkinprocessor.CheckInPlayProcessor;
import com.alipay.pairhub.core.service.trigger.ScenicChallengeTriggerService;
import com.alipay.pairhub.domain.activity.enums.CycleEnum;
import com.alipay.pairhub.domain.activity.utils.ActivityUtils;
import com.alipay.pairhubmng.facade.activity.model.DO.CheckInPlayInfoDO;
import io.fury.util.StringUtils;
import org.javers.common.collections.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version ScenicChallengeTriggerProcessor.java, v 0.1 2024年08月14日 下午4:57 sqg01890369
 */
@Component
public class ScenicChallengeTriggerProcessor implements BusinessProcessor<FunfairTriggerRequest, TriggerResponse> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ScenicChallengeTriggerProcessor.class);

    @Autowired
    private ActivityService activityService;
    @Autowired
    private UserCheckInRecordService userCheckInRecordService;
    @Autowired
    private ScenicChallengeTriggerService scenicChallengeTriggerService;
    @Autowired
    private CheckInPlayProcessor checkInPlayProcessor;

    /**
     * 业务逻辑
     * <p>
     * 景区挑战赛活动单用户线上只有一次抽奖机会， record_biz_no = 前缀+"_"+activityCode（唯一）
     * 只有当前用户打卡次数(不同打卡点)达到最小点数及以上才可进行抽奖
     * 本次活动兜底奖品均在海豚配置，无未中奖情况
     * 海豚抽奖使用activityCode进行幂等
     * <p>
     * 代码逻辑：
     * 1 参数校验
     * 1.1 校验活动有效性，活动不存在返回报错
     * 1.2 查询用户打卡表是否达到最小点数可抽奖，不可抽奖返回报错
     * 2 判断用户是否已参与活动
     * 2.1 存在中奖信息数据(状态为已中奖)，构建中奖信息返回
     * 2.2 存在中奖信息数据(状态为未中奖)，构建未中奖信息返回（景区挑战赛必定中奖）
     * 2.3 存在中奖信息数据(状态为进行中)，根据幂等号查询海豚
     * 2.3.1 查询不到海豚中奖信息，进行抽奖(景区挑战赛必定中奖)
     * 2.3.2 查询到海豚中奖信息，构建中奖处理；(若查询到多个奖品信息，则打印错误日志，取第一个奖品进行中奖处理)
     * 2.4 不存在中奖信息数据,初始化一条中奖信息，进行抽奖(景区挑战赛必定中奖)
     * 3 抽奖
     * 3.1 海豚接口调用失败(超时)，打印错误日志并报错"抽奖中，请稍后"，不更新抽奖凭证信息
     * 3.2 海豚接口调用成功
     * 3.2.1 未中奖，更新抽奖凭证信息为未中奖，返回未中奖信息;（盲盒玩法必定中奖，除非海豚异常）
     * 3.2.2 中奖，进行中奖处理
     * <p>
     * 进行中奖处理：查询并补充权益信息，组装中奖信息，更新抽奖凭证信息为已中奖，返回中奖信息
     * 1 查询不到权益信息，打印错误日志并报错，不更新用户权益信息和抽奖凭证信息
     * 2 查询到权益信息，补充权益信息，组装中奖信息，更新用户权益信息和抽奖凭证信息，返回中奖信息
     * <p>
     * 未中奖处理：更新用户中奖信息为未中奖(用户权益信息)，更新抽奖凭证信息为已使用，返回未中奖信息
     * <p>
     *
     * @param request 业务请求
     * @return 业务响应
     */
    @Override
    public TriggerResponse bizProcess(FunfairTriggerRequest request) {
        String userId = request.getCurrentUserId();
        String activeCode = request.getActiveCode();
        String playCode = request.getPlayCode();

        //校验活动有效性，活动不存在返回报错
        ActivityQueryRequest activityQueryRequest = new ActivityQueryRequest();
        activityQueryRequest.setActivityCode(activeCode);
        activityQueryRequest.setCurrentUserId(userId);
        ActivityInfoVO activityInfo = activityService.getActivityInfo(activityQueryRequest);
        if (activityInfo == null || !ActivityStatusEnum.PROCESSING.getCode().equalsIgnoreCase(activityInfo.getActivityStatus())) {
            LoggerUtil.error(LOGGER, "[ScenicChallengeTriggerProcessor] 该活动不存在, userId={0},activeCode={1}", userId, activeCode);
            throw new BizException(BizErrEnum.ACTIVITY_NOT_EXIST, "该活动不存在", true);
        }

        // 根据玩法code获取玩法信息
        CheckInPlayInfoDO checkInPlayInfoDO = checkInPlayProcessor.doGetBasePlayInfo(playCode);
        if (checkInPlayInfoDO == null) {
            LoggerUtil.error(LOGGER, "[ScenicChallengeTriggerProcessor] 该玩法不存在, userId={0},activeCode={1},playCode={2}", userId,
                    activeCode, playCode);
            throw new BizException(BizErrEnum.ACTIVITY_NOT_EXIST);
        }

        // 获取玩法配置的打卡点数, 为null，则报错
        if(checkInPlayInfoDO.getMinCheckInPoint() == null){
            throw new BizException(BizErrEnum.CONFIGURATION_ERROR, "玩法配置错误，未配置最小打卡点数", true);
        }
        int checkMinNum = checkInPlayInfoDO.getMinCheckInPoint(); // 玩法配置的最小打卡点数

        //查询用户打卡
        List<UserCheckInRecordDO> userCheckInRecordDOS;

        // 从玩法信息里获取玩法周期
        String playCycle = checkInPlayInfoDO.getPlayCycle();
        String recordBizNo; // 打卡记录的幂等号
        // 打卡记录不为空，且玩法周期不为空，则提取出当前周期内的打卡记录
        if (StringUtils.isNotBlank(playCycle)){
            // 根据玩法周期code获取玩法周期枚举
            CycleEnum cycleEnum = CycleEnum.getByCode(playCycle);
            if (cycleEnum == null) {
                throw new BizException(BizErrEnum.CONFIGURATION_ERROR, "玩法信息配置错误，玩法周期不存在");
            }

            // 根据玩法周期枚举获取当前玩法周期的开始和结束日期
            Date cycleStartDate = DateFormatUtil.getCycleStartDate(cycleEnum); // 获取当前玩法周期的开始日期
            Date cycleEndDate = DateFormatUtil.getCycleEndDate(cycleEnum); // 获取当前玩法周期的结束日期
            if (!ObjectUtil.isAllNotEmpty(cycleStartDate, cycleEndDate)) {
                throw new BizException(BizErrEnum.CONFIGURATION_ERROR, "玩法信息配置错误，玩法周期配置错误");
            }
            // 查询当前用户在本次活动玩法里本周期的打卡记录
            userCheckInRecordDOS = userCheckInRecordService.queryCheckInRecordByCycleDate(userId, activeCode, playCode, cycleStartDate, cycleEndDate);

            // 生成幂等号: 前缀SCENIC_CHALLENGE_RANK+uid+playCode+周期(这里查的是本周期)
            recordBizNo = ActivityUtils.buildCycleRecordBizNo(ActivityConstant.SCENIC_CHALLENGE, userId, playCode, cycleEnum, "0");
        }else { // 否则查询当前用户在本次活动玩法里的所有打卡记录
            userCheckInRecordDOS = userCheckInRecordService.queryCheckInRecord(userId, activeCode, playCode);

            // 生成幂等号
            recordBizNo = ActivityConstant.SCENIC_CHALLENGE + userId + '_' + activeCode;
        }

        //打卡记录为空报错
        if (CollectionUtil.isEmpty(userCheckInRecordDOS)) {
            LoggerUtil.info(LOGGER, "[ScenicChallengeTriggerProcessor],不存在打卡记录 userId={0},activeCode={1}", userId,
                    activeCode);
            throw new BizException(BizErrEnum.CHECKIN_RECORD_NOT_EXIST, "不存在打卡次数/打卡次数不满足抽奖条件", true);
        }

        // 提取已打卡点数
        long checkPointNum = userCheckInRecordDOS.stream().map(UserCheckInRecordDO::getCheckPointCode).distinct().count();
        // 最小打卡点数大于用户已打卡点数，则报错
        if (checkMinNum > checkPointNum) {
            LoggerUtil.warn(LOGGER, "[ScenicChallengeTriggerProcessor],打卡次数不满足抽奖条件,当前打卡次数={0},最低打卡次数={1},userId={2},activeCode={3},playCode={4}",
                    checkPointNum,checkMinNum,userId, activeCode, playCode);
            throw new BizException(BizErrEnum.CHECKIN_RECORD_NOT_EXIST, "打卡次数不满足抽奖条件", true);
        }

        // 设置幂等号
        request.setBizNo(recordBizNo);

        LoggerUtil.info(LOGGER, "[ScenicChallengeTriggerProcessor] 打卡次数满足抽奖条件, 开始抽奖, 当前打卡次数={0}, 最低打卡次数={1}, "
                        + "userId={2}, activeCode={3}, playCode={4}", checkPointNum, checkMinNum, userId, activeCode, playCode);
        //查询中奖记录表
        Pair<Boolean, UserPrizeInfoDO> prizeInfoDOPair = scenicChallengeTriggerService.queryAndInitUserPrizeInfo(request,
                PrizeRelationType.ACTIVITY.getCode(), activityInfo.getCampId(), userCheckInRecordDOS,checkMinNum);
        TriggerResponse triggerResponse;
        //非新增数据，组装返回/查询海豚中奖信息
        if (!prizeInfoDOPair.left()) {
            triggerResponse = scenicChallengeTriggerService.buildResponse(prizeInfoDOPair.right(), request);
        } else {
            triggerResponse = scenicChallengeTriggerService.trigger(prizeInfoDOPair.right(), request);
        }
        return triggerResponse;
    }

    @Override
    public void setBusinessType(FunfairTriggerRequest request) {
        request.setBusinessTypeEnum(BusinessTypeEnum.ACTIVITY);
    }

    @Override
    public void setOperationType(FunfairTriggerRequest request) {
        request.setOperationType(SampleOperationType.SCENIC_CHALLENGE_ACTIVITY_TRIGGER);
    }

    @Override
    public String buildLogInfo(FunfairTriggerRequest request, TriggerResponse data) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(request.getCurrentUserId()).append(",");
        stringBuilder.append(request.getCheckPointId()).append(",");
        stringBuilder.append(request.getActiveCode()).append(",");
        stringBuilder.append(request.getPlayCode()).append(",");

        if (data != null) {
            stringBuilder.append(data.getNeedRetry()).append(",");
            stringBuilder.append(data.getBizNo()).append(",");
            stringBuilder.append(data.getUserPrizeNo()).append(",");
            stringBuilder.append(JSON.toJSONString(data.getPrizeOrderList())).append(",");
        }
        return stringBuilder.toString();
    }
}
"#;

    #[test]
    fn test() {
        let r = Regex::new(CLASS_REGEX).unwrap().captures(&content);
        match r {
            None => {
                println!("not match")
            }
            Some(data) => {
                println!("{}", data.get(2).unwrap().as_str())
            }
        }
    }
}

//     use crate::function::codecomplete_strategy::CodeFuseCodeCompleteStrategy;
//     use agent_common_service::model::code_complete_model::{CodeEmbeddingRequestBean, QueryJarInfoRequestBean};
//     use agent_common_service::service::code_complete::CodeCompleteStrategy;
//     use agent_common_service::tools::xml_tools::{get_dependency_list, get_pom_content_without_variable};
//     use std::path::Path;
//     #[tokio::test]
//     async fn test2(){
//         let mut code_vec = vec![];
//         code_vec.push("    ;
//     }".to_string());
//
//         let req = CodeEmbeddingRequestBean {
//             userToken: "dfde8495-4979-472f-9763-acf971e05b9e".to_string(),
//             productType: "IDEA".to_string(),
//             codeList: code_vec,
//         };
//         let r = CodeFuseCodeCompleteStrategy::code_embedding(req).await;
//     println!("{:#?}", r);
//     }
//
//
//     #[tokio::test]
//     async fn test1() {
//         let file_url = "/Users/<USER>/workspace/codegenerator/backup/caselike/pom.xml";
// let path = Path::new(file_url);
//         let content = get_pom_content_without_variable(&path).unwrap();
//
//         let result = get_dependency_list(&content).unwrap();
//
//         for dependency in result {
//             let q = QueryJarInfoRequestBean {
//                 groupId: dependency.groupId,
//                 artifactId: dependency.artifactId,
//                 version: dependency.version,
//                 pageNo: 1,
//                 pageSize: 10,
//             };
//             if q.version.is_none() {
//                 continue;
//             }
//             let a = CodeFuseCodeCompleteStrategy::query_dependency_info(q).await;
//             prin

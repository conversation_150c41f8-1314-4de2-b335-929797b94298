use crate::ast::java::strategy::CodeFuseJavaAstStrategy;
use crate::ast::js::strategy::CodeFuseJsAstStrategy;
use agent_common_service::model::code_complete_model::CodeCompletionRequestBean;
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_common_service::service::abstract_similarity::{AssembleComponentHandler, Similarity};
use agent_common_service::service::code_complete_cache::CodeCompleteCacheStrategy;
use agent_common_service::service::similarity::current_package_handler::CurrentPackageComponentHandler;
use agent_common_service::service::similarity::similarity_import_handler::SimilarityImportHandler;
use agent_common_service::service::similarity::similarity_name_handler::SimilarityNameHandler;
use agent_common_service::tools::related_module_score::CacheRelatedModule;
use agent_db::domain::code_kv_index_domain::ScanFileRecord;
use std::collections::HashSet;

pub struct CodeFuseCodeCompleteCacheStrategy;

///代码补全过程中缓存处理策略
impl CodeCompleteCacheStrategy for CodeFuseCodeCompleteCacheStrategy {
    ///构建相关性缓存
    fn build_related_module_score(&self, code_complete_request: &CodeCompletionRequestBean, current_context: &PromptDeclaration, file_record: &ScanFileRecord) -> Option<CacheRelatedModule> {
        match code_complete_request.language.as_str() {
            "java" => CodeFuseJavaAstStrategy::build_related_module_score(code_complete_request, current_context, file_record),
            _ => None
        }
    }

    fn build_related_module_scores(&self, code_complete_request: &CodeCompletionRequestBean, current_context: &PromptDeclaration, file_record: &ScanFileRecord) -> Option<Vec<CacheRelatedModule>> {
        match code_complete_request.language.as_str() {
            "java" => CodeFuseJavaAstStrategy::build_related_module_score(code_complete_request, current_context, file_record).map(|item| vec![item]),
            "typescript" | "typescriptreact" | "javascript" | "javascriptreact" => Some(CodeFuseJsAstStrategy::build_related_module_scores(code_complete_request, current_context, file_record)),
            _ => None
        }
    }



    fn get_target_similarity_score_data(&self,scan_file_record: &ScanFileRecord, cur_score_data: &(HashSet<u32>, HashSet<u32>, HashSet<u32>)) -> (Option<Similarity<Option<String>>>, Option<Similarity<Option<String>>>, Option<Similarity<Option<String>>>) {
        //构建文件级别的相似度对比数据，用于后续文件级别排序，进行第一层数据过滤
        let current_package_opt = CurrentPackageComponentHandler::get_file_level_similarity(&scan_file_record, &cur_score_data.0);
        let similarity_import_opt = SimilarityImportHandler::get_file_level_similarity(&scan_file_record, &cur_score_data.1);
        let similarity_name_opt = SimilarityNameHandler::get_file_level_similarity(&scan_file_record, &cur_score_data.2);
        (current_package_opt, similarity_import_opt, similarity_name_opt)
    }

    fn is_need_build_related(&self,file_suffix:&String,current_data: &PromptDeclaration) -> bool {
       true
    }
}
use std::collections::HashMap;
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::remote_client::IndexTypeEnum::CHUNK_VECTOR;
use agent_db::dal::vector_client::{VectorItem, VECTOR_CLIENT};
use agent_db::domain::code_chat_domain::CodefuseChunk;
use agent_db::remote::http_client::{post_request, post_request_with_header, DEFAULT_TIMEOUT_MILLIS};
use agent_db::remote::rpc_model::BaseResponse;
use agent_db::tools::common_tools::LINE_ENDING;
use log::info;
use serde_json::Value;
use crate::ast_chunk::ast_chunk::{AIKnowledgeRequestBean, AIKnowledgeResponseBean, AstChunkRequestData, AstChunkResponseData, AstChunkResponseMetaData,  SyncDataFromRemoteRequestBean, SyncDataFromRemoteResponseBean};
use crate::deepsearchV2::searchrouter_data::ChatMessageModel;
use crate::deepsearchV2::searchrouter_net::requestAisServer_v2;
use crate::deepsearchV2::searchrouter_prompt::{system_prompt, user_prompt};
use crate::dialogue::codefuse_index_repository::CHUNK_CLIENT;
use crate::dialogue::misc_util::create_chunk_id;

//ast_chunk的server， 原始的服务
pub const AST_CHUNK_SERVER_URL: &str = "https://aimsalgo.alipay.com/api/codefuse/ast_chunk";
//请求cge embeding
pub const CGE_EMBEDING_SERVER_URL: &str = "https://cfsearch-pre.antgroup-inc.cn/openapi/embedding/batch";
//从远端同步数据, 批量请求ast chunk数据
pub const SYNC_DATA_SERVER_URL: &str = "https://cfsearch.antgroup-inc.cn/openapi/chunk/codeChunkSearch";
//ai知识库检索
pub const AI_KNOWLEDGE_SERVER_URL: &str = "https://cfsearch.antgroup-inc.cn/openapi/knowledgeBase/query";
pub const AST_CHUNK_SIZE: usize = 2048;


//向远端服务请求chunk数据
pub async fn request_ast_chunk_data(git_repo_url: &String, file_path: &String, content: &String) -> Option<Vec<AstChunkResponseData>>{
    //todo 这里临时注释，远程服务器压力扛不住，先本地切分
    // let file_suffix = Path::new(file_path).extension().and_then(|ext| ext.to_str()).unwrap_or("");
    // let ast_chunk_request_data = AstChunkRequestData {
    //     repoName: git_repo_url.to_string(),
    //     filePath: file_path.to_string(),
    //     sourceCode: content.to_string(),
    //     language: file_suffix.to_string(),
    //     chunkSize: AST_CHUNK_SIZE,
    // };
    //
    // let m = HashMap::new();
    // let request_ast_chunk_opt = post_request_with_header::<Value, _>(
    //     AST_CHUNK_SERVER_URL,
    //     &ast_chunk_request_data,
    //     m,
    //     DEFAULT_TIMEOUT_MILLIS,
    //     "request ast chunk fail"
    // ).await;
    // match request_ast_chunk_opt {
    //     Some(ast_chunk_response) => {
    //         let ast_chunk_response_data: Vec<AstChunkResponseData> = serde_json::from_value(ast_chunk_response).unwrap_or(Vec::new());
    //         Some(ast_chunk_response_data)
    //     }
    //     None => {None}
    // }
    let lines: Vec<String> = content.split(LINE_ENDING).map(|s| s.to_string()).collect();
    let mut chunk_index: usize = 1;
    let mut container: Vec<AstChunkResponseData> = Vec::new();
    for window_start in (0..lines.len()).step_by(AGENT_CONFIG.index_chunk_slide_size as usize) {
        let window_end = usize::min(window_start + AGENT_CONFIG.index_chunk_window_size as usize, lines.len());
        let content_arr = &lines[window_start..window_end];
        let code_snippet = content_arr.join(LINE_ENDING);
        if code_snippet.trim().len() == 0 {
            continue;
        }
        let mock_metadata = AstChunkResponseMetaData {
            author: "".to_string(),
            chunk_ancestors: vec![],
            chunk_size: 0,
            end_line_no: (window_end - 1),
            filepath: "".to_string(),
            language: "".to_string(),
            line_count: 0,
            node_count: 0,
            repo_name: "".to_string(),
            start_line_no: window_start,
        };
        let mock_result = AstChunkResponseData{
            content: code_snippet,
            metadata: mock_metadata,
        };
        container.push(mock_result);
        chunk_index = chunk_index + 1;
    }
    Some(container)
}


//从远端同步数据, 批量同步数据， file_path要求是相对路径
pub async fn sync_batch_file_data_from_remote(git_repo_url: &str, branch: &str, file_path_vec: Vec<String>) -> Option<Vec<SyncDataFromRemoteResponseBean>>{
    let formatted_git_repo_url = format_git_repo_url(git_repo_url);
    let sync_data_from_remote_request_bean = SyncDataFromRemoteRequestBean {
        repoRef: formatted_git_repo_url,
        branches: branch.to_string(),
        relativePath: file_path_vec,
    };
    let mut headers = HashMap::new();
    headers.insert("Content-Type", "application/json");
    headers.insert("codefusesearch_token", "b1eHTFGKRgOQmNI9C0tdYQ");
    headers.insert("codefusesearch_user", "test");
    let sync_data_from_remote_response_opt = post_request_with_header::<BaseResponse<Vec<SyncDataFromRemoteResponseBean>>, _>(
        SYNC_DATA_SERVER_URL,
        &sync_data_from_remote_request_bean,
        headers,
        DEFAULT_TIMEOUT_MILLIS,
        "sync_file_data_from_remote fail"
    ).await;
    match sync_data_from_remote_response_opt {
        Some(sync_data_from_remote_response) => {
            let data_result_opt = sync_data_from_remote_response.data;
            data_result_opt
        }
        None => {None}
    }
}

//git_repo_url的格式， 去掉前缀的协议git@｜https://， 以及后缀.git
//*******************:code-generator-group/agent_db.git -> code.alipay.com:code-generator-group/agent_db
//https://code.alipay.com/code-generator-group/agent_db.git -> code.alipay.com/code-generator-group/agent_db
pub fn format_git_repo_url(git_repo_url: &str) -> String {
    let git_repo_url = git_repo_url.trim();
    let mut format_git_repo_url = String::new();
    if git_repo_url.starts_with("git@") {
        format_git_repo_url = git_repo_url.replace("git@", "");
    }else if  git_repo_url.starts_with("https://"){
        format_git_repo_url = git_repo_url.replace("https://", "");
    }
    format_git_repo_url = format_git_repo_url.replace(".git", "");
    format_git_repo_url
}
//ai_knowledge的仓库格式
//*******************:code-generator-group/agent_db.git -> code-generator-group/agent_db
//https://code.alipay.com/code-generator-group/agent_db.git -> code-generator-group/agent_db
pub fn format_git_repo_url_ai_knowledge(git_repo_url: &str) -> Option<String> {
    if git_repo_url.is_empty() {
        return None;
    }
    // 移除可能的结尾 .git
    let url = git_repo_url.trim_end_matches(".git");
    // 处理 git@ 格式
    if let Some(ssh_path) = url.strip_prefix("git@") {
        return Some(ssh_path.split(':').nth(1)?.to_string());
    }
    // 处理 https:// 格式
    if let Some(https_path) = url.strip_prefix("https://") {
        return Some(https_path.split('/').skip(1).collect::<Vec<&str>>().join("/"));
    }
    None
}
//ai知识库搜索
pub async fn request_content_from_ai_knowledge(query: &String, repo_git_url: &String, need_summary: bool) -> Option<AIKnowledgeResponseBean>{
    let ai_knowledge_request_bean = AIKnowledgeRequestBean {
        question: query.to_string(),
        repoGroup: format_git_repo_url_ai_knowledge(&repo_git_url).unwrap_or("".to_string()),
        needAnswer: need_summary,
    };
    info!("request_content_from_ai_knowledge query: {}", serde_json::to_string_pretty(&ai_knowledge_request_bean).unwrap_or("".to_string()));

    let mut headers = HashMap::new();
    headers.insert("Content-Type", "application/json");
    headers.insert("codefusesearch_token", "b1eHTFGKRgOQmNI9C0tdYQ");
    headers.insert("codefusesearch_user", "test");

    let ai_knowledge_response_opt = post_request_with_header::<BaseResponse<AIKnowledgeResponseBean>, _>(
        AI_KNOWLEDGE_SERVER_URL,
        &ai_knowledge_request_bean,
        headers,
        DEFAULT_TIMEOUT_MILLIS,
        "sync_file_data_from_remote fail"
    ).await;
    match ai_knowledge_response_opt {
        Some(response_data) => {
            let data_result_opt = response_data.data;
            data_result_opt
        }
        None => {None}
    }

}
//对conten进行提取总结, 模型用的是Kimi-K2-Instruct
//这里对web页面以及ai知识库进行了提取总结,
pub async fn extract_summary_from_content(query: &String, content_vec: Vec<&String>) -> Option<(String, u128)> {
    info!("extract_summary_from_content query: {}", query);
    let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    let mut formatted_content = String::new();
    for (index, content) in content_vec.into_iter().enumerate() {
        formatted_content.push_str(format!("文章{}", index + 1).as_str());
        formatted_content.push_str(LINE_ENDING);
        formatted_content.push_str(content);
        formatted_content.push_str(LINE_ENDING);
    }
    let user_formatted_prompt = user_prompt.replace("{{query}}", query).replace("{{content}}", formatted_content.as_str());

    let mut messageList = Vec::new();
    let chatMessageModel = ChatMessageModel {
        role: "user".to_string(),
        content: user_formatted_prompt,
    };
    messageList.push(chatMessageModel);
    let system_model = ChatMessageModel {
        role: "system".to_string(),
        content: system_prompt.to_string()
    };
    messageList.push(system_model);

    //内容提取默认用kimi 2, 耗时，效果比deepseek-V3要好
    let llm_name = "Kimi-K2-Instruct".to_string();
    let extract_summary_opt = requestAisServer_v2(messageList, &llm_name).await;
    match extract_summary_opt {
        None => {
            info!("extract_summary_from_content , none ress");
        }
        Some(extract_summary) => {
            info!("extract_summary_from_content : {}", extract_summary.to_string());
            if let Some(content_str) = extract_summary.get("choices")
                .and_then(|choices| choices.get(0))
                .and_then(|choice| choice.get("message"))
                .and_then(|message| message.get("content"))
                .and_then(Value::as_str) {
                info!("filter_upstream_search_result content_str: {}", content_str);
                let content_json = content_str.replace("```", "").replace("json\n", "");
                info!("content_json {}", content_json);
                let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                return Some((content_json, end_time - start_time));
            }
        }
    }
    None
}

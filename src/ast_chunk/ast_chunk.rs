use crate::ast_chunk::ast_chunk_net::{request_ast_chunk_data, sync_batch_file_data_from_remote};
use crate::dialogue::codefuse_index_repository::CHUNK_CLIENT;
use crate::dialogue::misc_util::{autonomous_sleep_no_bolck, calculate_content_hash, create_chunk_id, doc_to_chunk, get_relative_path};
use crate::dialogue::repo_index_operator::{get_repo_build_status, is_need_interrupt_repo_build, save_repo_build_status};
use crate::utils::file_encoding::{read_file_smart, read_file_smart_sync};
use crate::utils::parallel_file_scan::MAX_NUM;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::remote_client::{cge_embedding, CgeEmbeddingRequestBean, IndexTypeEnum, EMBEDDING_BUILD};
use agent_db::dal::vector_client::{VectorItem, VECTOR_CLIENT};
use agent_db::domain::code_chat_domain::{CodefuseChunk, CodefuseFile};
use futures::future::join_all;
use futures::{stream, StreamExt};
use ignore::DirEntry;
use log::{error, info};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::path::Path;
use tokio::fs;


#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AstChunkRequestData {
    pub repoName: String,
    pub filePath: String,
    pub sourceCode: String,
    pub language: String,
    pub chunkSize: usize,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AstChunkResponseData {
    pub content: String,
    pub metadata: AstChunkResponseMetaData,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AstChunkResponseMetaData {
    pub author: String,
    pub chunk_ancestors: Vec<String>,
    pub chunk_size: usize,
    pub end_line_no: usize,
    pub filepath: String,
    pub language: String,
    pub line_count: usize,
    pub node_count: usize,
    pub repo_name: String,
    pub start_line_no: usize,
}

//批量从远端同步数据
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SyncDataFromRemoteRequestBean {
    pub repoRef: String,
    pub branches: String,
    pub relativePath: Vec<String>,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SyncDataFromRemoteResponseBean {
    pub snippet: Option<String>,
    pub snippetVector: Option<Vec<f32>>,
    pub startLine: Option<usize>,
    pub endLine: Option<usize>,
    pub relativePath: Option<String>,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AIKnowledgeRequestBean {
    pub question: String,
    pub repoGroup: String,
    pub needAnswer: bool,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AIKnowledgeResponseItem {
    pub chunkId: Option<String>,
    pub contentType: Option<String>,
    pub documentId: Option<String>,
    pub duration: Option<usize>,
    pub gmtCreate: Option<u128>,
    pub gmtModified: Option<u128>,
    pub index: Option<usize>,
    pub meta: Option<String>,
    pub productId: Option<String>,
    pub productName: Option<String>,
    pub sourceId: Option<String>,
    pub sourceType: Option<String>,
    pub text: Option<String>,
    pub title: Option<String>,
    pub url: Option<String>,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AIKnowledgeResponseBean {
    pub llmContent: String,
    pub searchResults: Option<Vec<AIKnowledgeResponseItem>>,
}

pub async fn execute_scan_for_index_repository_v2(data_vec: &Vec<DirEntry>, project_url: &String, branch: &String, token: &String, product_type: &String, mill_sleep_time: usize, index_type_set: &HashSet<IndexTypeEnum>, task_type: u8, git_repo_url: &String) {
    if get_repo_build_status(project_url, branch).await {
        info!("execute_scan_for_index_repository index_build is running , return");
        return;
    } else {
        info!("execute_scan_for_index_repository index_build is false , continue");
        save_repo_build_status(project_url, branch, true).await;
    }

    stream::iter(data_vec)
        .chunks(20)
        .for_each_concurrent(20, |chunk_arr| {
            async move {
                let file_path_vec: Vec<String> = chunk_arr.iter().map(|&entry| entry.path().to_string_lossy().to_string()).collect();
                let rst = sync_data_from_remote_to_local(git_repo_url, project_url, branch, &file_path_vec, task_type).await;
                if rst.is_err() {
                    error!("sync_data_from_remote_to_local error: {:?}", rst);
                }
            }
        }).await;
}

//differ_data_vec, 假设是绝对路径
pub async fn update_index_by_walkthrough_v2(diff_data_vec: &Vec<DirEntry>, project_url: &String, git_repo_url: &String, branch: &String, mill_sleep_time: usize, task_type: u8) {
    if get_repo_build_status(project_url, branch).await {
        info!("execute_scan_for_index_repository index_build is running , return");
        return;
    } else {
        info!("execute_scan_for_index_repository index_build is false , continue");
        save_repo_build_status(project_url, branch, true).await;
    }

    stream::iter(diff_data_vec)
        .chunks(10)
        .for_each_concurrent(20, |chunk_arr| {
            async move {
                let data_to_handle: Vec<String> = chunk_arr.iter().map(|&entry| entry.path().to_string_lossy().to_string().clone()).collect();
                //删除存量数据
                let mut delete_chunk_vec = Vec::new();
                for item in data_to_handle.clone() {
                    let need_interrupt_build = is_need_interrupt_repo_build(&project_url.clone().to_string(), &branch.clone().to_string(), task_type).await;
                    if need_interrupt_build {
                        info!("sync_data_from_remote_to_local, need interrupt, end the process");
                        return;
                    }

                    let mut param = HashMap::new();
                    param.insert("project_url".to_string(), project_url.to_string());
                    param.insert("file_url".to_string(), item);
                    let query_result_rst = CHUNK_CLIENT.query(&param, 200);
                    if let Ok(query_result) = query_result_rst {
                        for (score, doc) in query_result {
                            let chunk = doc_to_chunk(score, doc);
                            delete_chunk_vec.push(chunk.data)
                        }
                    }
                }
                CHUNK_CLIENT.delete_batch(delete_chunk_vec.as_slice()).unwrap();
                //重新建设数据
                let mut no_chunk_vec = Vec::new();
                let mut save_chunk_vec = Vec::new();
                for path in data_to_handle {
                    // 智能读取文件内容，支持多种编码（UTF-8, GBK, GB2312, GB18030等）
                    let content = read_file_smart(&path).await.unwrap_or_else(|e| {
                        error!("读取文件失败: {} - {}", path, e);
                        "".to_string()
                    });
                    let ast_chunk_data = request_ast_chunk_data(git_repo_url, &path, &content).await.unwrap_or(Vec::new());

                    if ast_chunk_data.len() == 0 {
                        no_chunk_vec.push(path.clone());
                    }

                    let codefuse_chunk_vec = convert_to_codefusechunk_from_ast_chunk(ast_chunk_data, project_url, branch, &path);
                    save_chunk_vec.extend(codefuse_chunk_vec);
                }
                info!("no_chunk_vec len {}", no_chunk_vec.len());
                CHUNK_CLIENT.save_batch(save_chunk_vec.clone().as_slice()).unwrap();
                //请求向量
                if save_chunk_vec.len() > AGENT_CONFIG.cge_batch_size {
                    //分块请求
                    let mut chunk_with_vector_vec = Vec::new();
                    let chunked_content_list: Vec<Vec<CodefuseChunk>> = save_chunk_vec.chunks(AGENT_CONFIG.cge_batch_size).map(|chunk| chunk.to_vec()).collect();
                    let mut futures = Vec::new();
                    for chunk_arr in chunked_content_list.iter() {
                        let chunk_content_list = chunk_arr.iter().map(|item| item.content.clone()).collect();
                        let req = CgeEmbeddingRequestBean { codeList: chunk_content_list, r#type: EMBEDDING_BUILD.to_string() };
                        futures.push(async move { cge_embedding(&req,AGENT_CONFIG.embedding_build_timeout).await });
                    }
                    let results = join_all(futures).await;
                    //保存向量
                    let mut vector_item_vec = Vec::new();


                    for index in 0..chunked_content_list.len() {
                        let item = &results[index];
                        if item.errorCode == 0 {
                            let data = &item.data;
                            if let Some(cge_result) = data {
                                let vector_vec = cge_result.result.clone();
                                if vector_vec.len() > 0 && vector_vec.len() ==chunked_content_list[index].len()  {
                                    for (sub_index, codefusechunk) in chunked_content_list[index].iter().enumerate() {
                                        let vector_item = VectorItem::new(codefusechunk.clone().id, vector_vec[sub_index].clone(), codefusechunk.project_url.clone());
                                        vector_item_vec.push(vector_item);
                                        chunk_with_vector_vec.push(codefusechunk.clone());
                                    }
                                }
                            }
                        }
                    }
                    //保存向量，并同步更新chunk状态
                    save_vectoritem_update_chunk_status(vector_item_vec, chunk_with_vector_vec).await;
                } else {
                    let mut vector_item_vec = Vec::new();
                    let content_list: Vec<String> = save_chunk_vec.iter().map(|item| item.content.clone()).collect();
                    let req = CgeEmbeddingRequestBean { codeList: content_list, r#type: EMBEDDING_BUILD.to_string() };
                    let response = cge_embedding(&req,AGENT_CONFIG.embedding_build_timeout).await;
                    if response.errorCode == 0 {
                        let data = response.data;
                        if let Some(cge_result) = data {
                            if cge_result.result.len() > 0 && cge_result.result.len() == save_chunk_vec.len() {
                                for (index, codefusechunk) in save_chunk_vec.iter().enumerate() {
                                    vector_item_vec.push(compose_to_vectoritem(codefusechunk.clone(), cge_result.result[index].clone()));
                                }
                            }
                            //保存向量，并同步更新chunk状态
                            save_vectoritem_update_chunk_status(vector_item_vec, save_chunk_vec).await;
                        }
                    }
                }
                autonomous_sleep_no_bolck(mill_sleep_time).await;
            }
        }).await;
    let commit_rst = CHUNK_CLIENT.commit();
    if commit_rst.is_err() {
        error!("commit chunk error: {:?}", commit_rst)
    }
}

//从远端同步数据,file_paths是绝对路径，
//按文件更新到本地存储
pub async fn sync_data_from_remote_to_local(git_repo_url: &str, project_url: &String, branch: &str, file_paths: &Vec<String>, task_type: u8) -> Result<(), anyhow::Error> {
    let need_interrupt_build = is_need_interrupt_repo_build(&project_url.clone().to_string(), &branch.clone().to_string(), task_type).await;
    if need_interrupt_build {
        info!("sync_data_from_remote_to_local, need interrupt, end the process");
        return Ok(());
    }
    let relative_path_vec = file_paths.iter().map(|item| get_relative_path(project_url, &item.to_string())).collect();
    let remote_data_opt = sync_batch_file_data_from_remote(git_repo_url, branch, relative_path_vec).await;
    if let Some(remote_data_vec) = remote_data_opt {
        let codefuse_chunk_data = convert_to_codefusechunk_and_vectoritem_from_remote_chunk_data(remote_data_vec, project_url, &branch.to_string());
        //保存到索引库
        let _ = CHUNK_CLIENT.save_batch(codefuse_chunk_data.0.as_slice());
        //保存向量，并同步更新chunk状态
        save_vectoritem_update_chunk_status(codefuse_chunk_data.1, codefuse_chunk_data.0).await;
    }

    Ok(())
}

//把从ast_chunk请求的数据转换成codefusechunk
pub fn convert_to_codefusechunk_from_ast_chunk(ast_chunk_vec: Vec<AstChunkResponseData>,
                                               project_url: &String,
                                               branch: &String,
                                               file_path: &String) -> Vec<CodefuseChunk> {
    let mut container = Vec::new();
    for item in ast_chunk_vec.iter().enumerate() {
        let index = item.0;
        let ast_chunk_data = item.1.clone();

        let mut codefuse_chunk = CodefuseChunk::default();
        codefuse_chunk.id = create_chunk_id(file_path.clone(), index);
        codefuse_chunk.file_url = file_path.clone();
        codefuse_chunk.project_url = project_url.clone();
        codefuse_chunk.branch = branch.clone();
        codefuse_chunk.content = ast_chunk_data.content;
        codefuse_chunk.index = index as u64;
        codefuse_chunk.start_line = ast_chunk_data.metadata.start_line_no as u64;
        codefuse_chunk.end_line = ast_chunk_data.metadata.end_line_no as u64;

        container.push(codefuse_chunk);
    }
    container
}

//把从远端同步的数据转换成codefusechunk
pub fn convert_to_codefusechunk_and_vectoritem_from_remote_chunk_data(remote_data: Vec<SyncDataFromRemoteResponseBean>,
                                                                      project_url: &String,
                                                                      branch: &String) -> (Vec<CodefuseChunk>, Vec<VectorItem>) {
    let mut chunk_container = Vec::new();
    let mut vectoritem_container = Vec::new();

    let mut path_data_map: HashMap<String, Vec<SyncDataFromRemoteResponseBean>> = HashMap::new();
    for item in remote_data.iter() {
        let relative_path = item.relativePath.clone().unwrap_or_default();
        path_data_map.entry(relative_path).or_insert(Vec::new()).push(item.clone());
    }

    for item in path_data_map {
        let relative_path = item.0;
        let ast_chunk_data = item.1.clone();
        let absolute_path = Path::new(project_url).join(Path::new(&relative_path)).to_string_lossy().to_string();

        for index_data in ast_chunk_data.iter().enumerate() {
            let index = index_data.0;
            let ast_chunk_data = index_data.1.clone();

            let mut codefuse_chunk = CodefuseChunk::default();
            codefuse_chunk.id = create_chunk_id(absolute_path.clone(), index);
            codefuse_chunk.file_url = relative_path.clone();
            codefuse_chunk.project_url = project_url.clone();
            codefuse_chunk.branch = branch.clone();
            codefuse_chunk.content = ast_chunk_data.snippet.unwrap_or("".to_string());
            codefuse_chunk.index = index as u64;
            codefuse_chunk.start_line = ast_chunk_data.startLine.unwrap_or(0) as u64;
            codefuse_chunk.end_line = ast_chunk_data.endLine.unwrap_or(0) as u64;

            let vector_item = VectorItem::new(codefuse_chunk.id.clone(), ast_chunk_data.snippetVector.unwrap_or(Vec::new()), project_url.clone());

            chunk_container.push(codefuse_chunk);
            vectoritem_container.push(vector_item);
        }
    }
    (chunk_container, vectoritem_container)
}
//把codefusechunk转成vectoritem
async fn convert_to_vectoritem_from_codefusechunk(codefusechunk_data: Vec<CodefuseChunk>) -> Vec<VectorItem> {
    let mut vector_item_vec = Vec::new();
    let codefusechunk_data_length = codefusechunk_data.len();
    let content_vec: Vec<String> = codefusechunk_data.clone().into_iter().map(|item| {
        item.content.clone()
    }).collect();
let r = CgeEmbeddingRequestBean{
    codeList: content_vec,
    r#type: EMBEDDING_BUILD.to_string(),
};
   let result =  cge_embedding(&r,AGENT_CONFIG.embedding_build_timeout).await;
    if result.errorCode ==0{
        let reponse = result.data;
        if let Some(cge_result) = reponse{
            let vector_vec = cge_result.result;
            if vector_vec.len() == codefusechunk_data_length {
                for (index, codefusechunk) in codefusechunk_data.iter().enumerate() {
                    let vector_item = VectorItem::new(codefusechunk.clone().id, vector_vec.get(index).unwrap().clone(), codefusechunk.project_url.clone());
                    vector_item_vec.push(vector_item);
                }
            }
        }
    }
    vector_item_vec
}
//组合codefusechunk和Vec<f32>成VectorItem
fn compose_to_vectoritem(chunk: CodefuseChunk, vector: Vec<f32>) -> VectorItem {
    VectorItem::new(chunk.id, vector, chunk.project_url)
}
//保存向量，同步更新es库里的状态
async fn save_vectoritem_update_chunk_status(vector_item_vec: Vec<VectorItem>, chunk_vec_to_update: Vec<CodefuseChunk>) {
    if vector_item_vec.is_empty() {
        return;
    }
    //保存向量数据
    let _ = VECTOR_CLIENT.batch_upsert(vector_item_vec).await;
    // 更新chunk状态
    let mut chunk_container = Vec::new();
    for mut chunk in chunk_vec_to_update {
        chunk.has_content_vector = 1;
        chunk_container.push(chunk.clone());
    }
    let _ = CHUNK_CLIENT.update_batch(chunk_container.as_slice());
}

//ast_chunk场景下的CodefuseFile，
// 1. es不需要保存content， 节省空间
// 2. hash值是根据content计算的，
fn build_file_index_for_ast_chunk(dir: &Path, source_content: &String, project_url: &str, branch: &str) -> anyhow::Result<CodefuseFile> {
    let mut index_file = CodefuseFile::default();
    index_file.id = dir.to_str().unwrap().to_string();
    index_file.content = "".to_string();
    index_file.project_url = project_url.to_string();
    index_file.branch = branch.to_string();
    index_file.hash = calculate_content_hash(source_content);

    Ok(index_file)
}


#[cfg(test)]
mod tests {
    use crate::ast_chunk::ast_chunk::{execute_scan_for_index_repository_v2, update_index_by_walkthrough_v2, AIKnowledgeRequestBean, AstChunkRequestData, AstChunkResponseData};
    use crate::ast_chunk::ast_chunk_net::{format_git_repo_url, format_git_repo_url_ai_knowledge, request_ast_chunk_data,  request_content_from_ai_knowledge, AST_CHUNK_SERVER_URL};
    use crate::deepsearchV2::searchrouter_data::{ChatMessageModel, ChatTemplateKwargs, QeuryAndRetrievalTool, SearchRouterRequestLLMData};
    use crate::deepsearchV2::searchrouter_net::{requestAisServer, DEEPSEEK_V3, MIDDLE_TIMEOUT_MILLIS, ORIGINAL_QUERY};
    use crate::deepsearchV2::searchrouter_prompt::{query_split_with_wiki, query_split_wo_wiki, system_prompt, user_prompt};
    use crate::deepsearchV2::searchrouter_util::{HEAD_TOKEN, SEARCH_URL};
    use crate::utils::path::get_all_file_by_url;
    use agent_db::config::runtime_config::AGENT_CONFIG;
    use agent_db::dal::remote_client::{query_repo_wiki_entry, IndexTypeEnum};
    use agent_db::remote::http_client::{post_request, post_request_with_header, DEFAULT_TIMEOUT_MILLIS};
    use ignore::DirEntry;
    use oxc_resolver::AliasValue::Path;
    use serde_json::Value;
    use std::collections::{HashMap, HashSet};
    use std::fs;
    use std::time::{SystemTime, UNIX_EPOCH};
    use uuid::Uuid;

    //请求ast_chunk_server
    #[test]
    fn test_ast_chunk_server() {
        let git_repo_url = "";
        let file_path = "";
        let content = "";
        let dest_path = "/Users/<USER>/Downloads/alispace/xiaozhi/xiaozhi/server.py";
        let content = fs::read_to_string(dest_path).unwrap();
        // ()
        let runtime = tokio::runtime::Runtime::new().unwrap();
        let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let ast_chunk_data = runtime.block_on(request_ast_chunk_data(&git_repo_url.to_string(),
                                                                     &file_path.to_string(),
                                                                     &content));
        println!("ast_chunk_data {}", serde_json::to_string_pretty(&ast_chunk_data.unwrap()).unwrap());
        let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        println!("request_ast_chunk_data cost end_time - start_time {}", end_time - start_time);
    }

    //ai知识库的请求和耗时
    #[test]
    fn test_ai_knowledge_cost() {
        //***测试ai知识库的检索质量和耗时

        let query = "查询代码补全功能在哪里".to_string();
        let git_repo_url = "https://code.alipay.com/business_release/caselike.git";
        let runtime = tokio::runtime::Runtime::new().unwrap();
        let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let ai_knowledge_response_opt = runtime.block_on(request_content_from_ai_knowledge(&query, &git_repo_url.to_string(), false));
        let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        println!("ai_knowledge_response cost end_time - start_time {}", end_time - start_time);
        let ai_knowledge_response = ai_knowledge_response_opt.unwrap();
        println!("ai_knowledge_response {}", serde_json::to_string_pretty(&ai_knowledge_response).unwrap());
    }

    //带wiki的问题拆解
    #[test]
    fn test_query_split_with_wiki() {
        let query = "查询代码补全功能在哪里".to_string();
        let git_repo_url = "https://code.alipay.com/business_release/caselike.git";

        //强制执行
        if AGENT_CONFIG.use_repo_wiki == 0 {
            //***测试带wiki的问题拆分
            let formatted_prompt = query_split_with_wiki.replace(ORIGINAL_QUERY, &query).replace("{repo_info}", "这是蚂蚁集团的代码补全服务仓库");
            let runtime = tokio::runtime::Runtime::new().unwrap();
            let split_query_res = runtime.block_on(requestAisServer(&formatted_prompt, &DEEPSEEK_V3.to_string(), MIDDLE_TIMEOUT_MILLIS));
            match split_query_res {
                None => {
                    println!("codefuse deepsearch check_result_relative , none res");
                }
                Some(query_res) => {
                    println!("searchrooter split query : {}", query_res.to_string());
                    if let Some(content_str) = query_res.get("choices")
                        .and_then(|choices| choices.get(0))
                        .and_then(|choice| choice.get("message"))
                        .and_then(|message| message.get("content"))
                        .and_then(Value::as_str) {
                        println!("searchrooter split content_str: {}", content_str);
                        let content_json = content_str.replace("```", "").replace("json\n", "");
                        println!("content_json {}", content_json);
                        let request_split_data: Vec<QeuryAndRetrievalTool> = match serde_json::from_str(&content_json) {
                            Ok(parsed_data) => parsed_data,
                            Err(e) => {
                                println!("content_str Failed to parse JSON: {}", e);
                                Vec::new()
                            }
                        };
                        println!("request_split_data {}", serde_json::to_string_pretty(&request_split_data).unwrap());
                    }
                }
            }
        }
    }

    //数据提炼
    #[test]
    fn test_ai_knowledge_extract() {
        // requestAisServer()
        let query = "仓库的核心功能是什么";
        let content = "文章1\n文档标签：loresage\n文档标题：Sofa框架技术规范\n#### **一、SOFA框架介绍**\n01、SOFA分层结构：- MVC 分层：传统三层模型的分层模型，代码库里主要包含bootstrap层/web层/facade层/service层/model层/dal层/utils层\n- DDD 分层：面向领域建模的分层模型，代码库里主要包含bootstrap层/web层/facade层/application层/domain层/infrastructure层- SOFA 分层：蚂蚁传统分层模型，主要包含test 层（测试层）/bootstrap 层（启动层-SOFA）/web 层（展现层）/biz 层（业务应用层）/core 层（核心领域层）/common 层（基础结构层）\n02、SOFA各模块(层)能力项说明：- xxx-bootstrap层：该层是 SOFABoot 项目中的启动模块，其中包含 SOFABoot 应用的启动类、配置文件、打包插件等，其测试目录中还提供了集成测试的基类，可支持继承和扩展。该模块可通过直接或间接依赖引用其他各模块的代码。\n- xxx-web层：应用对外暴露的的Http接口/OpenApi等接口写在web层，通常的业务逻辑调用关系为DemoController-->Biz-xxx层(如果用户有自定义这一层)-->Service 层-->Dal层来实现相关业务逻辑的处理；- xxx-common-service-facade层：通常应用对外暴露的RPC(TR)服务会写在这一层，这一层里会定义相关Facade(RPC\\TR)接口(常见的接口命名方式为*Facade)，以及涉及到的request、response等相关类，相关的业务逻辑实现类XxxxImpl会写在 biz-service-impl (或者application)这一层里，最后该模块会打包成一个单独的JAR包，然后发布到mvn仓库给外部的系统去引用；- application 层（应用层）：application 层为领域驱动设计中的应用层，位于领域层之上，可以调用领域服务、使用领域模型。该层更专注于具体应用所需要的逻辑处理，为领域层需要协作的各个领域服务协调任务、委派工作而不包含核心业务规则。当存在 facade 层时，application 层依赖 facade 层，提供 facade 层接口的具体实现逻辑。\n- domain 层（核心领域层）：该模块主要用来定义仓库内用的所有领域模型等结构，会被其他各个模块所引用；- infrastructure 层（基础设施层）:infrastructure 层为系统提供了各类基础设施，包含 RPC、Message、DB、Cache 等三方系统的依赖，流程引擎、规则引擎等 service 层可能用到的编排工具。- xxx-biz-service-impl模块：biz-service-impl 模块中封装了对外发布的服务接口的具体实现，接口定义放在 common 层的 common-service-facade 模块，该模块的业务可能会引用 biz-shared、biz-xxx、biz-xxx 等模块中的服务，因此它可以依赖 biz 层的所有模块。\n- xxx-biz-shared模块：用于封装公用的应用逻辑。- xxx-common-service模块：该模块主要实现对一些简单业务逻辑的封装，可以称之为仓储层，通常会在这个里面定义XxxRepository 来实现对DAO层的调用，更复杂的业务逻辑会由用户自定义的bundle层来实现，会被biz层依赖，同时又依赖core-model层，比如会在biz-xxx层来实现对core-service层的调用；\n- xxx-core-model模块：model 层用于存放实体类，即定义业务逻辑的领域对象，包含领域层各个模型对象，会被其他各个模块所引用；- xxx-common-dal模块：封装了对数据库的访问逻辑，向上暴露 DAO 服务，主要是用于访问DB层，即编写对数据库的增删改查的 sql 语句实现，简称为DAO层；common-dal 模块位于依赖链的最底层，所有模块都会直接或间接的依赖它，使用其 DAO 服务。\n- xxx-common-utils模块：utils 层提供了一些公共的工具库类，这些类可以被应用中的所有类使用，提供了基础的公用的工具服务。- xxx-common-service-integration模块：该模块主要用来实现应用调用外部服务的接入，比如应用需要实现对外部应用a的调用，则会将相关逻辑写到这个模块下，通常会定义一个XxxClient接口，实现类XxxClientImpl等，具体会被其他bundle比如biz-xxx等模块调用；";

        let formatted_prompt = user_prompt.replace("{{query}}", query).replace("{{content}}", content);

        let mut messageList = Vec::new();
        let chatMessageModel = ChatMessageModel {
            role: "user".to_string(),
            content: formatted_prompt,
        };
        messageList.push(chatMessageModel);
        let system_model = ChatMessageModel {
            role: "system".to_string(),
            content: system_prompt.to_string(),
        };
        messageList.push(system_model);

        let params = SearchRouterRequestLLMData {
            model: "DeepSeek-V3".to_string(),
            // model: "Kimi-K2-Instruct".to_string(),
            messages: messageList,
            stream: false,
            top_p: 0.8,
            top_k: 20,
            temperature: 0.7,
            chat_template_kwargs: ChatTemplateKwargs { enable_thinking: false },
        };

        let trace_id = Uuid::new_v4().simple().to_string();
        let mut headers = HashMap::new();
        headers.insert("Content-Type", "application/json");
        headers.insert("Authorization", HEAD_TOKEN);
        headers.insert("SOFA-TraceId", &trace_id);
        headers.insert("SOFA-RpcId", "0.1");

        let runtime = tokio::runtime::Runtime::new().unwrap();
        let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let file_data = runtime.block_on(post_request_with_header::<serde_json::Value, _>(SEARCH_URL,
                                                                                          &params,
                                                                                          headers,
                                                                                          DEFAULT_TIMEOUT_MILLIS * 2,
                                                                                          "searchrouter_split_query request fail"));
        let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        println!("cost {}", end_time - start_time);
        println!("data {}", serde_json::to_string_pretty(&file_data.unwrap()).unwrap());

        // let response_data = post_request_with_header::<serde_json::Value, _>(SEARCH_URL,
        //                                                                      &params,
        //                                                                      headers,
        //                                                                      DEFAULT_TIMEOUT_MILLIS*2,
        //                                                                      "searchrouter_split_query request fail").await;

    }
    #[test]
    fn test_ai_knowledge() {
        let git_repo_url = " https://code.alipay.com/business_release/caselike.git";
        let query = "仓库的职责和核心功能是什么?";

        let runtime = tokio::runtime::Runtime::new().unwrap();
        let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let file_data = runtime.block_on(request_content_from_ai_knowledge(&query.to_string(), &git_repo_url.to_string(), true));
        let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        println!("end_time - start_time {}", end_time - start_time);
        println!("file_data {}", serde_json::to_string_pretty(&file_data.unwrap()).unwrap())
    }

    #[test]
    fn test_file_data_sync() {
        // let git_repo_url = "https://code.alipay.com/business_release/caselike.git";
        // let branch = "master";
        // let file_path = "app/service/src/main/java/com/alipay/tsingyanprod/service/antlr/gen/python/Python3Parser.java";
        //
        // let runtime = tokio::runtime::Runtime::new().unwrap();
        // let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        // let file_data = runtime.block_on(sync_file_data_from_remote(git_repo_url, branch, file_path));
        // let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        // println!("end_time - start_time {}", end_time - start_time);
        // println!("file_data {}", serde_json::to_string_pretty(&file_data).unwrap())

        let project_url = "/Users/<USER>/Downloads/alispace/caselike".to_string();
        let branch = "master".to_string();
        let token = "".to_string();
        let product_type = "".to_string();
        let mill_sleep_time = 0;
        let index_type_set: HashSet<IndexTypeEnum> = HashSet::new();
        let task_type = 0;
        let git_repo_url = " https://code.alipay.com/business_release/caselike.git".to_string();

        let runtime = tokio::runtime::Runtime::new().unwrap();

        let rst = runtime.block_on(get_all_file_by_url(&project_url, false));

        // let data_vec = get_all_file_by_url(&project_url);

        runtime.block_on(update_index_by_walkthrough_v2(&rst.0,
                                                        &project_url,
                                                        &git_repo_url,
                                                        &branch,
                                                        mill_sleep_time,
                                                        task_type));
    }

    #[test]
    fn test_cge_embeding() {
        let data = vec!["hello".to_string(), "world".to_string()];

        let runtime = tokio::runtime::Runtime::new().unwrap();

        let ast_rst = runtime.block_on(request_cge_embedding(data));
    }
}
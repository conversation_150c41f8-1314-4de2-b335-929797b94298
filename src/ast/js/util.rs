use std::path::Path;

use agent_db::tools::common_tools::LINE_ENDING;
use once_cell::sync::Lazy;
use tree_sitter::{Language, Node, Parser, Query};

pub static LANGUAGE_TS: Lazy<Language> = Lazy::new(|| tree_sitter_typescript::language_typescript());
pub static LANGUAGE_TSX: Lazy<Language> = Lazy::new(|| tree_sitter_typescript::language_tsx());
pub static LANGUAGE_JS: Lazy<Language> = Lazy::new(|| tree_sitter_javascript::language());

pub fn get_file_ext(file_path: &Path) -> Option<String> {
    let ext = file_path.extension()?.to_string_lossy().to_ascii_lowercase();
    match ext.as_str() {
        "mts" | "cts" => Some("ts".into()),
        "mjs" | "cjs" | "jsx" => Some("js".into()),
        _ => Some(ext),
    }
}

pub fn set_parser_language(parser: &mut Parser, ext: &String) -> Option<()> {
    match ext.as_str() {
        "ts" => parser.set_language(&LANGUAGE_TS).ok(),
        "tsx" => parser.set_language(&LANGUAGE_TSX).ok(),
        "js" => parser.set_language(&LANGUAGE_JS).ok(),
        _ => return None,
    }
}

pub fn get_node_text(node: Option<&Node>, source: &[u8]) -> Option<String> {
    node?.utf8_text(source).ok().map(|text| text.to_owned())
}

pub fn get_comment(node: &Node, source_code: &str) -> Option<(String, usize, usize)> {
    let mut start_byte = 0usize;
    let mut start_line = 0usize;
    let mut comments = vec![];

    let mut prev_sibling_node = node.prev_named_sibling();
    while let Some(current_node) = prev_sibling_node {
        if current_node.kind() == "comment" {
            start_byte = current_node.start_byte();
            start_line = current_node.start_position().row + 1;
            comments.push(current_node.utf8_text(source_code.as_bytes()).ok()?);
            prev_sibling_node = current_node.prev_named_sibling();
        } else {
            break;
        }
    }

    if !comments.is_empty() {
        let normal_comments: Vec<&str> = comments.into_iter().rev().collect();
        return Some((
            normal_comments.join(LINE_ENDING),
            start_byte,
            start_line,
        ))
    }
    return None
}

// 去除在语句块以及作为参数的函数
pub fn ignore_method(node: &Node) -> bool {
    let mut parent_node = node.parent();
    while let Some(current_node) = parent_node {
        match current_node.kind() {
            "arguments" | "statement_block" => return true,
            _ => { parent_node = current_node.parent() }
        }
    }
    return false
}

// 调试方法
pub fn collect_matches<'a>(
    matches: impl Iterator<Item = tree_sitter::QueryMatch<'a, 'a>>,
    query: &'a Query,
    source: &'a str,
) -> Vec<(usize, Vec<(&'a str, &'a str)>)> {
    matches
        .map(|m| {
            (
                m.pattern_index,
                format_captures(m.captures.iter().copied(), query, source),
            )
        })
        .collect()
}

pub fn collect_captures<'a>(
    captures: impl Iterator<Item = (tree_sitter::QueryMatch<'a, 'a>, usize)>,
    query: &'a Query,
    source: &'a str,
) -> Vec<(&'a str, &'a str)> {
    format_captures(captures.map(|(m, i)| m.captures[i]), query, source)
}

fn format_captures<'a>(
    captures: impl Iterator<Item = tree_sitter::QueryCapture<'a>>,
    query: &'a Query,
    source: &'a str,
) -> Vec<(&'a str, &'a str)> {
    captures
        .map(|capture| {
            (
                query.capture_names()[capture.index as usize],
                capture.node.utf8_text(source.as_bytes()).unwrap(),
            )
        })
        .collect()
}

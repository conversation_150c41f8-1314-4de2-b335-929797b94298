use std::collections::HashMap;
use std::path::Path;

use agent_common_service::model::code_complete_model::ContextAwareInfo;
use agent_common_service::tools::common_tools::CacheRelatedModuleEnum;
use agent_common_service::tools::related_module_score::CacheRelatedModule;
use agent_common_service::{model::{code_complete_model::CodeCompletionRequestBean, prompt_declaration::PromptDeclaration}, service::code_ast_analysis::AstStrategy};
use agent_db::domain::code_chat_domain::{CodefuseFile, CodefuseMethod};
use agent_db::domain::code_kv_index_domain::CodeInfo;
use agent_db::domain::code_kv_index_domain::ScanFileRecord;

use super::chat::{extract_file_chat_index, extract_method_chat_index};
use super::complete::{extract_code_info, get_prompt_declaration};
use super::related_module::{build_related_module_scores, get_context_aware_from_cache};

pub struct CodeFuseJsAstStrategy;

impl AstStrategy for CodeFuseJsAstStrategy {
    ///抽取通用代码结构
    fn extra_code_info(_code_content: &String) -> Option<CodeInfo> {
        None
    }

    fn get_prompt_declaration(bean: &CodeCompletionRequestBean) -> Option<PromptDeclaration> {
        get_prompt_declaration(bean)
    }

    fn extra_method_chat_index(dir: &Path, source_code: &String, project_url: &String, branch: &String) -> Option<Vec<CodefuseMethod>> {
        extract_method_chat_index(dir, source_code, project_url, branch)
    }
}

impl CodeFuseJsAstStrategy {
    pub fn extract_code_info(project_path: &String, file_path: &Path, source_code: &String) -> Option<CodeInfo> {
        extract_code_info(project_path, file_path, source_code)
    }

    // 目前没有用到
    pub fn extra_file_chat_index(dir: &Path, source_code: &String, project_url: &String, branch: &String) -> Option<CodefuseFile> {
        extract_file_chat_index(dir, source_code, project_url, branch)
    }

    pub fn build_related_module_scores(_: &CodeCompletionRequestBean, current_context: &PromptDeclaration, file_record: &ScanFileRecord) -> Vec<CacheRelatedModule> {
        build_related_module_scores(current_context, file_record)
    }

    pub fn get_context_aware_from_cache(
        related_module_enum: CacheRelatedModuleEnum,
        cache_module_lated_map: &HashMap<CacheRelatedModuleEnum, Vec<ContextAwareInfo>>,
        cache_context: &PromptDeclaration,
        current_context: &PromptDeclaration
    ) -> (bool, Option<Vec<ContextAwareInfo>>) {
        get_context_aware_from_cache(related_module_enum, cache_module_lated_map, cache_context, current_context)
    }
}

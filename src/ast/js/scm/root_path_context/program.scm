(import_statement
  (import_clause
    (named_imports
      (import_specifier
        name: (identifier) @import_specifier_name
        alias: (identifier)* @import_specifier_alias)))
  source: (_ (string_fragment) @import_source))

(import_statement
  (import_clause (identifier) @import_default)
  source: (_ (string_fragment) @import_source))

(import_statement
  (import_clause (namespace_import))
  source: (_ (string_fragment) @import_source))

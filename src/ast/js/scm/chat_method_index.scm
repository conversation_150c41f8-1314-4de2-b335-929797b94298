; 提取文件顶部所有函数或方法

; 方法申明
; ------------------------------
(class_body
  (_)*
  (comment)* @comment
  .
  (decorator)* @decorator
  .
  (method_definition) @function)

; 函数申明
; function fn() {}
; function* fn() {}
; ------------------------------
(program
  (_)*
  ((comment)* @comment
  .
  [
    (function_declaration)
    (generator_function_declaration)
  ] @function))

; 函数表达式
; let fn = () => {}
; let fn = function() {}
; let fn = function*() {}
; ------------------------------
(program
  (_)*
  (comment)* @comment
  .
  (lexical_declaration
    (variable_declarator
      [
        (arrow_function)
        (function_expression)
        (generator_function)
      ] @function)))

; 函数导出申明
; export function fn() {}
; export function* fn() {}
; export default function fn() {}
; export default function* fn() {}
; export default () => {}
; ------------------------------
(program
  (_)*
  (comment)* @comment
  .
  (export_statement
    [
      (function_declaration)
      (function_expression)
      (generator_function_declaration)
      (generator_function)
      (arrow_function)
    ] @function))

; 函数导出申明
; export const a = () => {}
; ------------------------------
(program
  (_)*
  (comment)* @comment
  .
  (export_statement
    (lexical_declaration
      (variable_declarator
        [
          (arrow_function)
          (function_expression)
          (generator_function)
        ])) @function))

; 函数赋值导出
; module.exports = () => {}
; exports.fn = () => {}
(program
  (_)*
  (comment)* @comment
  .
  (expression_statement
    (assignment_expression
      right: [
        (arrow_function)
        (function_expression)
        (generator_function)
      ]) @function))

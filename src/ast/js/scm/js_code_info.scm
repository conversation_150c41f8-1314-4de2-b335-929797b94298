; 代码结构信息，提取 import 以及 export 类/函数/类型

(import_statement source: (_ (string_fragment) @import.source))

(export_statement
  [
    (class_declaration (identifier) @identifier) @declaration
    (function_declaration (identifier) @identifier) @declaration
    (generator_function_declaration (identifier) @identifier) @declaration
    (lexical_declaration (variable_declarator (identifier) @identifier [(arrow_function) (function_expression) (generator_function)])) @declaration
    (variable_declaration (variable_declarator (identifier) @identifier [(arrow_function) (function_expression) (generator_function)])) @declaration
    (class) @declaration
    (arrow_function) @declaration
    (function_expression) @declaration
    (generator_function) @declaration
  ])

(program
  [
    (class_declaration (identifier) @identifier) @declaration
    (function_declaration (identifier) @identifier) @declaration
    (generator_function_declaration (identifier) @identifier) @declaration
    (lexical_declaration (variable_declarator (identifier) @identifier [(arrow_function) (function_expression) (generator_function)])) @declaration
    (variable_declaration (variable_declarator (identifier) @identifier [(arrow_function) (function_expression) (generator_function)])) @declaration
  ]
  [
    (export_statement "default" @export_default (identifier) @export_identifier)
    (export_statement (export_clause (export_specifier (identifier) @export_identifier)) !source)
  ]
  (#eq? @identifier @export_identifier))

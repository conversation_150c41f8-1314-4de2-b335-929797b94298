{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "esModuleInterop": true,
    "sourceMap": true,
    "baseUrl": "../../",
    "strict": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@@/*": [
        "src/.umi/*"
      ],
    }
  },
  "include": [
    "../../.umirc.ts",
    "../../.umirc.*.ts",
    "../../**/*.d.ts",
    "../../**/*.ts",
    "../../**/*.tsx"
  ]
}
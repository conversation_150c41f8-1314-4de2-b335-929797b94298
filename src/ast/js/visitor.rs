use std::time::{Duration, SystemTime};
use tree_sitter::Node;

pub fn visit_with_timeout<F>(node: &Node, mut enter: F, timeout: Option<Duration>)
where
    F: FnMut(Node) -> bool,
{
    let mut has_next = true;
    let mut cursor = node.walk();
    let start_time = timeout.map(|_| SystemTime::now());

    while has_next {
        if let (Some(start_time), Some(timeout)) = (start_time, timeout) {
            if let Ok(duration) = start_time.elapsed() {
                if duration > timeout {
                    break;
                }
            }
        }

        let node = cursor.node();
        let goto_child = enter(node);
        has_next = if goto_child {
            cursor.goto_first_child()
        } else {
            false
        };

        if !has_next {
            has_next = cursor.goto_next_sibling();
            while !has_next && cursor.goto_parent() {
                has_next = cursor.goto_next_sibling();
            }
        }
    }
}

pub fn visit<F>(node: &Node, enter: F)
where
    F: FnMut(Node) -> bool,
{
    visit_with_timeout(node, enter, None);
}

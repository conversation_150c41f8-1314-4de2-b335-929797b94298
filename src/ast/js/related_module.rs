use std::collections::{HashMap, HashSet};

use agent_common_service::{model::{code_complete_model::ContextAwareInfo, prompt_declaration::PromptDeclaration}, tools::{common_tools::CacheRelatedModuleEnum, related_module_score::CacheRelatedModule}};
use agent_db::{domain::code_kv_index_domain::{CodeInfo, ScanFileRecord}, tools::common_tools::LINE_ENDING};

// 查找关联文件片段
// 首先需要 file_record.file_url 在 import 列表里，这样可保证强相关，否则获取到的可能是无关的数据，导致采纳率变低
pub fn build_related_module_scores(current_context: &PromptDeclaration, file_record: &ScanFileRecord) -> Vec<CacheRelatedModule> {
    let file_path = &file_record.file_url;
    let code_info = match &file_record.code_info {
        Some(v) => v,
        _ => return vec![],
    };

    let mut import_declaration = None;
    if let Some(ts_prompt) = &current_context.extend_typescript {
        if let Some(import_vec) = &ts_prompt.import_vec {
            for item in import_vec.iter() {
                if item.source == file_record.file_url {
                    import_declaration = Some(item);
                }
            }
        }
    }
    let name_vec = match import_declaration {
        Some(v) => &v.name_vec,
        _ => return vec![],
    };

    // name 为空，则为 * 导入，只直接返回文件结构
    if name_vec.is_empty() {
        let info = ContextAwareInfo {
            filePath: file_path.clone(),
            content: get_file_code_struct(code_info),
            score: 0.1,
            extraData: Some("importClass".to_owned()),
        };
        return vec![CacheRelatedModule{ module_type: CacheRelatedModuleEnum::IMPORT, info }];
    }

    // let name_set: HashSet<&String> = name_vec.iter().collect();

    let mut context_map: HashMap<String, (CacheRelatedModuleEnum, String, f64)> = HashMap::new();

    if let Some(ts_prompt) = &current_context.extend_typescript {
        if let Some(function_declaration) = &ts_prompt.function_declaration {
            // 解析参数类型
            if let Some(param_type_vec) = &function_declaration.param_type_vec {
                set_context_info(&mut context_map, &CacheRelatedModuleEnum::METHOD, "methodParam", &name_vec, param_type_vec, 0.9, false);
            }
            // 解析函数体
            if let Some(local_var_type_vec) = &function_declaration.local_var_type_vec {
                //衰减标识为true，离光标位置越近越好
                set_context_info(&mut context_map, &CacheRelatedModuleEnum::METHOD,"methodInnerClass", &name_vec, local_var_type_vec, 0.8, true);
            }
            // 解析返回值类型
            if let Some(return_type) = &function_declaration.return_type {
                let return_vec = vec![return_type.clone()];
                set_context_info(&mut context_map, &CacheRelatedModuleEnum::METHOD, "returnClass", &name_vec, &return_vec, 0.7, false);
            }
        }

        // 解析 scope 函数参数
        if let Some(function_param_type_vec) = &ts_prompt.function_param_type_vec {
            for param_type_vec in function_param_type_vec.iter().rev() {
                set_context_info(&mut context_map, &CacheRelatedModuleEnum::METHOD, "methodParam", &name_vec, &param_type_vec, 0.6, false);
            }
        }
    }

    // step5 解析类变量
    if let Some(field_set) = &current_context.field_set {
        let field_vec: Vec<String> = field_set.iter().cloned().collect();
        set_context_info(&mut context_map, &CacheRelatedModuleEnum::FIELD, "classField", &name_vec, &field_vec, 0.5, false);
    }

    // 解析类变量
    if let Some(extends_class) = &current_context.extends_class {
        let extends_vec = vec![extends_class.to_owned()];
        set_context_info(&mut context_map, &CacheRelatedModuleEnum::EXTENDS, "extendClass", &name_vec, &extends_vec, 0.4, false);
    }

    // 解析import信息
    for (name, _) in name_vec {
        if !context_map.contains_key(name) {
            context_map.insert(name.clone(), (CacheRelatedModuleEnum::IMPORT, "importClass".to_owned(), 0.1));
        }
    }

    context_map.into_iter()
        .filter_map(|(key, value)| {
            get_type_code_struct(code_info, &key).map(|content| {
                CacheRelatedModule {
                    module_type: value.0,
                    info: ContextAwareInfo {
                        filePath: file_path.clone(),
                        content,
                        score: value.2,
                        extraData: Some(value.1),
                    }
                }
            })
        })
        .collect()
}

pub fn build_related_module_score_legacy(current_context: &PromptDeclaration, file_record: &ScanFileRecord) -> (Option<CacheRelatedModuleEnum>, Option<ContextAwareInfo>) {
    let file_path = &file_record.file_url;
    let code_info = match &file_record.code_info {
        Some(v) => v,
        _ => return (None, None),
    };

    // 如果 file_record 文件路径在 import 列表里，说明强相关，否则弱相关
    // TODO: 需要建立类似 LSP 的文件索引图，以便更精确查找关联文件片段，目前先按类型名一致判断
    let mut file_related_factor = 0.5;
    if let Some(ts_prompt) = &current_context.extend_typescript {
        if let Some(import_vec) = &ts_prompt.import_vec {
            for item in import_vec.iter() {
                if item.source == file_record.file_url {
                    file_related_factor = 1 as f64;
                }
            }
        }
    }

    if let Some(ts_prompt) = &current_context.extend_typescript {
        if let Some(function_declaration) = &ts_prompt.function_declaration {
            // step1 解析参数类型，默认得分6
            if let Some(param_type_vec) = &function_declaration.param_type_vec {
                let aware_info = get_context_aware_info("methodParam", file_path, code_info, param_type_vec, 0.6 * file_related_factor, false);
                if let Some(aware_info) = aware_info {
                    return (Some(CacheRelatedModuleEnum::METHOD), Some(aware_info));
                }
            }
            // step2 解析函数体,默认得分5
            if let Some(local_var_type_vec) = &function_declaration.local_var_type_vec {
                //衰减标识为true，离光标位置越近越好
                let aware_info = get_context_aware_info("methodInnerClass", file_path, code_info, local_var_type_vec, 0.5 * file_related_factor, true);
                if let Some(aware_info) = aware_info {
                    return (Some(CacheRelatedModuleEnum::METHOD), Some(aware_info));
                }
            }
            // step3 解析返回值类型，默认得分4
            if let Some(return_type) = &function_declaration.return_type {
                let return_vec = vec![return_type.clone()];
                let aware_info = get_context_aware_info("returnClass", file_path, code_info, &return_vec, 0.4 * file_related_factor, false);
                if let Some(aware_info) = aware_info {
                    return (Some(CacheRelatedModuleEnum::METHOD), Some(aware_info));
                }
            }
        }

        // step4 解析 scope 函数参数
        if let Some(function_param_type_vec) = &ts_prompt.function_param_type_vec {
            for param_type_vec in function_param_type_vec.iter().rev() {
                let aware_info = get_context_aware_info("methodParam", file_path, code_info, param_type_vec, 0.3 * file_related_factor, false);
                if let Some(aware_info) = aware_info {
                    return (Some(CacheRelatedModuleEnum::METHOD), Some(aware_info));
                }
            }
        }
    }

    // step5 解析类变量，默认得分2
    if let Some(field_set) = &current_context.field_set {
        let field_vec: Vec<String> = field_set.iter().cloned().collect();
        let aware_data_opt = get_context_aware_info("classField", file_path, &code_info, &field_vec, 0.3 * file_related_factor, false);
        if let Some(aware_data) = aware_data_opt {
            return (Some(CacheRelatedModuleEnum::FIELD), Some(aware_data));
        }
    }

    // step6 解析类变量，默认得分2
    if let Some(extends_class) = &current_context.extends_class {
        let extends_vec = vec![extends_class.to_owned()];
        let aware_data_opt = get_context_aware_info("extendClass", file_path, code_info, &extends_vec, 0.2 * file_related_factor, false);
        if let Some(aware_data) = aware_data_opt {
            return (Some(CacheRelatedModuleEnum::EXTENDS), Some(aware_data));
        }
    }

    // step7 解析import信息 解析类变量，默认得分2
    if let Some(ts_prompt) = &current_context.extend_typescript {
        if let Some(import_vec) = &ts_prompt.import_vec {
            for import_declaration in import_vec.iter() {
                // 如果使用命名导出，则提取对应的代码，否则提取全部的文件结构
                if !import_declaration.name_vec.is_empty() {
                    let aware_data_opt = get_context_aware_info("importClass", file_path, code_info, &import_declaration.name_vec.iter().map(|item| item.1.clone()).collect(), 0.1 * file_related_factor, false);
                    if let Some(aware_data) = aware_data_opt {
                        return (Some(CacheRelatedModuleEnum::IMPORT), Some(aware_data));
                    }
                } else {
                    let item = ContextAwareInfo {
                        filePath: file_path.clone(),
                        content: get_file_code_struct(code_info),
                        score: 0.1,
                        extraData: Some("importClass".to_owned()),
                    };
                    return (Some(CacheRelatedModuleEnum::IMPORT), Some(item));
                }
            }
        }
    }

    return (None, None);
}

pub fn get_context_aware_from_cache(related_module_enum: CacheRelatedModuleEnum,
    cache_module_lated_map: &HashMap<CacheRelatedModuleEnum, Vec<ContextAwareInfo>>,
    cache_context: &PromptDeclaration,
    current_context: &PromptDeclaration) -> (bool, Option<Vec<ContextAwareInfo>>) {

    //true:没有修改 false：有修改
    let change_flag: bool = match related_module_enum {
        CacheRelatedModuleEnum::IMPORT => {
            // 比较导入的文件
            let source1: Option<HashSet<&String>> = current_context.extend_typescript.as_ref()
                .and_then(|item| item.import_vec.as_ref())
                .map(|item| item.iter().map(|item| &item.source).collect());

            let source2: Option<HashSet<&String>> = cache_context.extend_typescript.as_ref()
                .and_then(|item| item.import_vec.as_ref())
                .map(|item| item.iter().map(|item| &item.source).collect());

            match (source1, source2) {
                (Some(set1), Some(set2)) => set1.len() == set2.len() && set1.is_subset(&set2),
                (None, None) => true,
                _ => false,
            }
        }
        CacheRelatedModuleEnum::FIELD => {
            let (current_set, cache_set) = match related_module_enum {
                CacheRelatedModuleEnum::IMPORT => (&current_context.import_set, &cache_context.import_set),
                CacheRelatedModuleEnum::FIELD => (&current_context.field_set, &cache_context.field_set),
                _ => unreachable!(),
            };
            //如果两者都为空，没有代码更新，但是返回空值
            //如果两者都不为空，判断差集，如果有差集，那么说明有代码更新，
            //如果一个为空，另一个不为空。。直接判断有代码更新
            match (current_set, cache_set) {
                (Some(set_a), Some(set_b)) => (set_a.len() == set_b.len()) && set_a.is_subset(set_b),
                (None, None) => true,
                _ => false,
            }
        }
        CacheRelatedModuleEnum::METHOD => {
            // 比较参数类型
            let param1: Option<HashSet<&String>> = current_context.extend_typescript.as_ref()
                .and_then(|item| item.function_declaration.as_ref())
                .and_then(|item| item.param_type_vec.as_ref())
                .map(|item| item.iter().collect());

            let param2: Option<HashSet<&String>> = cache_context.extend_typescript.as_ref()
                .and_then(|item| item.function_declaration.as_ref())
                .and_then(|item| item.param_type_vec.as_ref())
                .map(|item| item.iter().collect());

            match (param1, param2) {
                (Some(set1), Some(set2)) => set1.len() == set2.len() && set1.is_subset(&set2),
                (None, None) => true,
                _ => false,
            }
        }
        CacheRelatedModuleEnum::EXTENDS => {
            match (&current_context.extends_class, &cache_context.extends_class) {
                (Some(val_a), Some(val_b)) => val_a == val_b,
                (None, None) => true,
                _ => false,
            }
        }
    };
    //如果更新过代码
    if !change_flag {
        return (true, None);
    }
    let related_value_result = cache_module_lated_map.get(&related_module_enum);
    match related_value_result {
        Some(related_value) => {
            if related_value.len() == 0 {
                return (false, None);
            }
            return (false, Some(related_value.clone()));
        }
        None => {
            return (false, None);
        }
    }
}

fn get_context_aware_info(
    desc_str: &str,
    file_path: &String,
    code_info: &CodeInfo,
    type_identity_vec: &Vec<String>,
    mut score: f64,
    decay_flag: bool,
) -> Option<ContextAwareInfo> {
    if let Some((code_struct, index)) = find_by_type(type_identity_vec, code_info) {
        if decay_flag {
            score = score + 0.1 * index as f64
        }
        Some(ContextAwareInfo {
            filePath: file_path.clone(),
            content: code_struct,
            score,
            extraData: Some(desc_str.to_owned()),
        })
    } else {
        None
    }
}

fn set_context_info(
    map: &mut HashMap<String, (CacheRelatedModuleEnum, String, f64)>,
    module_enum: &CacheRelatedModuleEnum,
    desc: &str,
    import_name_vec: &Vec<(String, String)>,
    type_identity_vec: &Vec<String>,
    mut score: f64,
    decay_flag: bool,
) {
    for (index, type_name) in type_identity_vec.iter().enumerate() {
        let specifier = import_name_vec.iter().find(|item| item.1 == *type_name);
        if let Some((name, _)) = specifier {
            if !map.contains_key(name) {
                if decay_flag {
                    score = score + 0.1 * (index as f64 / type_identity_vec.len() as f64)
                }
                map.insert(name.clone(), (clone_module_enum(module_enum), desc.to_owned(), score));
            }
        }
    }
}

fn clone_module_enum(module_enum: &CacheRelatedModuleEnum) -> CacheRelatedModuleEnum {
    match module_enum {
        CacheRelatedModuleEnum::METHOD => CacheRelatedModuleEnum::METHOD,
        CacheRelatedModuleEnum::FIELD => CacheRelatedModuleEnum::FIELD,
        CacheRelatedModuleEnum::EXTENDS => CacheRelatedModuleEnum::EXTENDS,
        CacheRelatedModuleEnum::IMPORT => CacheRelatedModuleEnum::IMPORT,
    }
}

fn find_by_type(
    type_identity_vec: &Vec<String>,
    code_info: &CodeInfo,
) -> Option<(String, usize)> {
    for (index, type_name) in type_identity_vec.iter().enumerate() {
        if let Some(code_struct) = get_type_code_struct(code_info, type_name) {
            return Some((code_struct, index))
        }
    }
    None
}

fn get_type_code_struct(code_info: &CodeInfo, type_name: &String) -> Option<String> {
    if let Some(class_list) = &code_info.class_list {
        for class_info in class_list.iter() {
            if class_info.class_name == *type_name {
                return Some(class_info.code_struct.clone())
            }
        }
    }

    if let Some(ts_info) = &code_info.extend_typescript {
        if let Some(type_vec) = &ts_info.type_vec {
            for declaration in type_vec.iter() {
                if declaration.name == *type_name {
                    return Some(declaration.code_struct.clone())
                }
            }
        }

        if let Some(function_vec) = &ts_info.function_vec {
            for declaration in function_vec.iter() {
                if declaration.name == *type_name {
                    return Some(declaration.code_struct.clone())
                }
            }
        }
    }

    None
}

fn get_file_code_struct(code_info: &CodeInfo) -> String {
    let mut code_struct_list = vec![];

    if let Some(class_list) = &code_info.class_list {
        for class_info in class_list.iter() {
            code_struct_list.push(class_info.code_struct.as_str());
        }
    }

    if let Some(ts_info) = &code_info.extend_typescript {
        if let Some(type_vec) = &ts_info.type_vec {
            for declaration in type_vec.iter() {
                code_struct_list.push(declaration.code_struct.as_str());
            }
        }

        if let Some(function_vec) = &ts_info.function_vec {
            for declaration in function_vec.iter() {
                code_struct_list.push(declaration.code_struct.as_str());
            }
        }
    }

    code_struct_list.join(LINE_ENDING)
}

#[cfg(test)]
mod tests {
    use std::collections::{HashMap, HashSet};

    use agent_common_service::model::prompt_declaration::{TypescriptFunctionDeclaration, TypescriptImportStatement, TypescriptPromptDeclaration};
    use agent_db::domain::code_kv_index_domain::{ClassInfo, TypeScriptDeclaration, TypeScriptInfo};

    use super::*;

    #[test]
    fn test_build_related_module_score() {
        let current_context = PromptDeclaration {
            import_set: None,
            field_set: Some(HashSet::from([
                "ILogger".into()
            ])),
            extends_class: Some(
                "BaseUtil".into()
            ),
            method_declaration: None,
            implements_class_name_set: Some(HashSet::from([
                "IUtil".into(),
            ])),
            package_name: None,
            extend_typescript: Some(
                TypescriptPromptDeclaration {
                    import_vec: Some(
                        vec![
                            TypescriptImportStatement {
                                source: "/fixtures/src/app.ts".into(),
                                name_vec: vec![],
                            },
                            TypescriptImportStatement {
                                source: "/fixtures/src/pages/home/<USER>".into(),
                                name_vec: vec![
                                    ("default".into(), "Home".into()),
                                    ("IProps".into(), "IHomeProps".into()),
                                    ("INode".into(), "INode".into()),
                                    ("ILogger".into(), "ILogger".into()),
                                    ("IUtil".into(), "IUtil".into()),
                                    ("BaseUtil".into(), "BaseUtil".into()),
                                    ("f1".into(), "f1".into()),
                                ],
                            },
                            TypescriptImportStatement {
                                source: "/fixtures/src/pages/home/<USER>".into(),
                                name_vec: vec![
                                    ("log".into(), "log".into()),
                                    ("sum".into(), "sum".into()),
                                ],
                            },
                        ],
                    ),
                    function_declaration: Some(
                        TypescriptFunctionDeclaration {
                            param_type_vec: Some(
                                vec![
                                    "Home".into(),
                                ],
                            ),
                            return_type: Some(
                                "IHomeProps".into(),
                            ),
                            local_var_type_vec: Some(
                                vec![
                                    "IManager".into(),
                                    "ICache".into(),
                                    "INode".into(),
                                ],
                            ),
                        },
                    ),
                    function_param_type_vec: Some(
                        vec![
                            vec![
                                "View".into(),
                            ],
                        ],
                    ),
                },
            ),
        };

        let file_record = ScanFileRecord {
            file_url: "/fixtures/src/pages/home/<USER>".into(),
            file_name_suffix: "ts".into(),
            total_snippet_num: 0,
            code_info: Some(CodeInfo{
                code_struct: "".into(),
                full_qualified_name: None,
                import_set: None,
                class_type: None,
                class_name: None,
                extend_class_name: None,
                implements_class_name_set: None,
                fild_name_class_map: None,
                class_list: Some(
                    vec![
                        ClassInfo {
                            class_type: "class".into(),
                            class_name: "View1".into(),
                            extend_class_name: None,
                            implements_class_name_set: None,
                            field_name_class_map: Some(HashMap::from([
                                ("v1".into(), "INode".into())
                            ])),
                            code_struct: "class View extends BaseView {\n  v1: INode;\n  v2 = 2;  \n  get fn1(): number { }\n  fn2(a: string) { }\n}".into(),
                        },
                        ClassInfo {
                            class_type: "class".into(),
                            class_name: "BaseUtil".into(),
                            extend_class_name: None,
                            implements_class_name_set: Some(HashSet::from([
                                "BaseUtil".into(),
                            ])),
                            field_name_class_map: None,
                            code_struct: "abstract class BaseUtil implements IBaseUtil {\n  fn() { }\n}".into(),
                        },
                    ],
                ),
                extend_typescript: Some(
                    TypeScriptInfo {
                        import_source_set: None,
                        type_vec: Some(
                            vec![
                                TypeScriptDeclaration {
                                    name: "IUtil".into(),
                                    code_struct: "interface IUtil {}".into(),
                                },
                                TypeScriptDeclaration {
                                    name: "ILogger".into(),
                                    code_struct: "interface ILogger {}".into(),
                                },
                                TypeScriptDeclaration {
                                    name: "INode".into(),
                                    code_struct: "interface INode {\n  val: string,\n  next?: INode,\n}".into(),
                                },
                                TypeScriptDeclaration {
                                    name: "IProps".into(),
                                    code_struct: "type IProps = Record<string, any>".into(),
                                },
                            ],
                        ),
                        function_vec: Some(
                            vec![
                                TypeScriptDeclaration {
                                    name: "f1".into(),
                                    code_struct: "function f1(): string { }".into(),
                                },
                                TypeScriptDeclaration {
                                    name: "default".into(),
                                    code_struct: "(props: Props) => { }".into(),
                                },
                            ],
                        ),
                    },
                ),
            })
        };

        let result = build_related_module_scores(&current_context, &file_record);
        let list: Vec<(CacheRelatedModuleEnum, ContextAwareInfo)> = result.into_iter().map(|item| (item.module_type, item.info)).collect();
        dbg!(list);
    }
}

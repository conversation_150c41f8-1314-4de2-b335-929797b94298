use std::collections::{HashMap, VecDeque};
use std::path::{Path, PathBuf};
use std::sync::{Arc, RwLock};

use once_cell::sync::Lazy;
use oxc_resolver::{ResolveOptions, Resolver, TsconfigOptions, TsconfigReferences};

use super::constant::JS_VALID_EXTENSIONS;

pub struct ProjectContext {
    pub project_path: String,
    pub resolver: Resolver,
}

struct ProjectContextCache {
    capacity: usize,
    map: HashMap<String, Arc<ProjectContext>>,
    deq: VecDeque<String>,
}

static PROJECT_CONTEXT_CACHE: Lazy<RwLock<ProjectContextCache>> = Lazy::new(|| {
    RwLock::new(ProjectContextCache {
        capacity: 5,
        map: HashMap::new(),
        deq: VecDeque::new(),
    })
});

fn create_project_context(project_path: &String) -> Option<Arc<ProjectContext>> {
    let mut guard = PROJECT_CONTEXT_CACHE.write().ok()?;
    if guard.deq.len() == guard.capacity {
        if let Some(key) = guard.deq.pop_back()  {
            guard.map.remove(&key);
        }
    }

    let tsconfig_path = Path::new(project_path.as_str()).join("tsconfig.json");
    let resolver = get_resolver(tsconfig_path);
    let context = Arc::new(ProjectContext {
        project_path: project_path.clone(),
        resolver,
    });
    guard.deq.push_front(project_path.clone());
    guard.map.insert(project_path.clone(), context.clone());
    Some(context)
}

fn get_project_context(project_path: &String) -> Option<Arc<ProjectContext>> {
    let cache = PROJECT_CONTEXT_CACHE.read().ok()?;
    let context = cache.map.get(project_path);
    Some(context?.clone())
}

pub fn ensure_project_context(project_path: &String) -> Option<Arc<ProjectContext>> {
    if let Some(context) = get_project_context(project_path) {
        Some(context)
    } else {
        create_project_context(project_path)
    }
}

fn get_resolver(tsconfig_path: PathBuf) -> Resolver {
    let tsconfig = if tsconfig_path.is_file() {
        Some(TsconfigOptions {
            config_file: tsconfig_path,
            references: TsconfigReferences::Auto,
        })
    } else {
        None
    };

    Resolver::new(ResolveOptions {
        extensions: JS_VALID_EXTENSIONS
            .iter()
            .map(|ext| format!(".{ext}"))
            .collect(),
        condition_names: vec!["default".into()],
        // disable node_modules resolution
        modules: vec![],
        tsconfig,
        ..ResolveOptions::default()
    })
}

use std::str;
use std::{collections::HashMap, path::Path};

use super::util::{get_file_ext, set_parser_language, LANGUAGE_JS, LANGUAGE_TS, LANGUAGE_TSX};
use once_cell::sync::Lazy;
use tree_sitter::{Parser, Query, QueryCursor};

use crate::dialogue::misc_util::calculate_content_hash;
use agent_db::domain::code_chat_domain::{CodefuseFile, CodefuseMethod};
use agent_db::tools::common_tools::V_M_PREFIX;

static CHAT_QUERY_MAP: Lazy<HashMap<&str, Query>> = Lazy::new(|| {
    let file_index_scm = include_str!("scm/chat_file_index.scm");
    let method_index_scm = include_str!("scm/chat_method_index.scm");
    HashMap::from([
        ("ts/chat_file_index", Query::new(&LANGUAGE_TS, file_index_scm).unwrap()),
        ("tsx/chat_file_index", Query::new(&LANGUAGE_TSX, file_index_scm).unwrap()),
        ("js/chat_file_index", Query::new(&LANGUAGE_JS, file_index_scm).unwrap()),
        ("ts/chat_method_index", Query::new(&LANGUAGE_TS, method_index_scm).unwrap()),
        ("tsx/chat_method_index", Query::new(&LANGUAGE_TSX, method_index_scm).unwrap()),
        ("js/chat_method_index", Query::new(&LANGUAGE_JS, method_index_scm).unwrap()),
    ])
});

pub fn extract_file_chat_index(dir: &Path, source_code: &String, project_url: &String, branch: &String) -> Option<CodefuseFile> {
    let ext = get_file_ext(dir)?;
    let mut parser = Parser::new();
    set_parser_language(&mut parser, &ext)?;

    let tree = parser.parse(source_code, None)?;
    let query = CHAT_QUERY_MAP.get(format!("{}/{}", &ext, "chat_file_index").as_str())?;
    let mut query_cursor = QueryCursor::new();
    let captures = query_cursor.captures(query, tree.root_node(), source_code.as_bytes());
    let node: Vec<&str> = captures.map(|(m, i)| m.captures[i].node.utf8_text(source_code.as_bytes()).unwrap_or_default()).collect();

    let index_file = CodefuseFile {
        id: dir.to_str()?.to_string(),
        content: source_code.clone(),
        project_url: project_url.clone(),
        branch: branch.clone(),
        hash: calculate_content_hash(&source_code),
        annotate: node.join("\n"),
        ..Default::default()
    };

    Some(index_file)
}

pub fn extract_method_chat_index(dir: &Path, source_code: &String, project_url: &String, branch: &String) -> Option<Vec<CodefuseMethod>> {
    let ext = get_file_ext(dir)?;
    let mut parser = Parser::new();
    set_parser_language(&mut parser, &ext)?;

    let tree = parser.parse(source_code, None)?;
    let query = CHAT_QUERY_MAP.get(format!("{}/{}", &ext, "chat_method_index").as_str())?;
    let mut query_cursor = QueryCursor::new();

    let source_code_bytes = source_code.as_bytes();
    let file_url = dir.to_str()?;
    let mut methods = vec![];

    let matches = query_cursor.matches(query, tree.root_node(), source_code.as_bytes());
    matches.enumerate().for_each(|(match_index, m)| {
        let captures = m.captures;
        if captures.is_empty() {
            return;
        }
        let first_node = captures[0].node;
        let mut comments_node = vec![];
        let mut function_node = None;
        captures.iter().for_each(|capture| {
            let name = query.capture_names()[capture.index as usize];
            match name {
                "comment" => {
                    comments_node.push(capture.node)
                },
                "function" => {
                    function_node = Some(capture.node)
                },
                _ => (),
            }
        });

        let function_node = match function_node {
            Some(v) => v,
            None => return,
        };
        let mut codefuse_method = CodefuseMethod::default();
        let mut func_name = function_node.child_by_field_name("name").map(|node| node.utf8_text(source_code_bytes).unwrap_or_default()).unwrap_or_default();
        if func_name.is_empty() {
            func_name = "anonymous";
        }
        codefuse_method.id = format!("{}{}#{}#{}", V_M_PREFIX, file_url, func_name, match_index);
        codefuse_method.project_url = project_url.clone();
        codefuse_method.branch = branch.clone();
        codefuse_method.file_url = file_url.to_string();
        codefuse_method.start_line = (first_node.start_position().row + 1) as u64;
        codefuse_method.end_line = (first_node.end_position().row + 1) as u64;

        codefuse_method.content = str::from_utf8(&source_code_bytes[first_node.start_byte()..function_node.end_byte()]).unwrap_or_default().into();

        let comment: Vec<_> = comments_node.iter().map(|node| {
            node.utf8_text(source_code_bytes).ok().unwrap_or_default()
        }).collect();
        if !comment.is_empty() {
            codefuse_method.annotate = comment.join("\n");
        }
        codefuse_method.hash = calculate_content_hash(codefuse_method.content.as_str());
        methods.push(codefuse_method);
    });

    if methods.is_empty() {
        None
    } else {
        Some(methods)
    }
}

#[cfg(test)]
mod tests {
    use std::{sync::mpsc, thread};

    use super::*;

    #[test]
    fn test_extra_file_chat_index() {
        let branch = "master";
        let project_url = "/workspace";

        let source = vec!(
            (
                "/workspace/a.ts",
                r#"
/**
 * 实现文件缓存
 */
class Cache {
  // 获取缓存值
  getValue(): string {}
}
                "#.trim(),
                r#"
/**
 * 实现文件缓存
 */
                "#.trim()
            ),
            (
                "/workspace/b.tsx",
                r#"
/**
 * 页面布局
 */
 
// 布局组件
export const Layout() {
  // 获取缓存值
  return <div>layout<div>
}
                "#.trim(),
                r#"
/**
 * 页面布局
 */
 
// 布局组件
                "#.trim()
            ),
            (
                "/workspace/c.js",
                r#"
// inc fn
export const inc = (x) => x + 1
// add fn
export const add = (x, y) => x + y
                "#.trim(),
                r#"
// inc fn
// add fn
                "#.trim()
            )
        );

        let (tx, rx) = mpsc::channel();

        let mut files = vec![];
        for (dir, source_code, ..) in source.clone() {
            let tx = tx.clone();
            thread::spawn(move || {
                let file = extract_file_chat_index(Path::new(dir), &source_code.to_string(), &project_url.to_string(), &branch.to_string());
                if let Some(file) = file {
                    tx.send(file).unwrap();
                }
            });
        }

        drop(tx);
        for received in rx {
            files.push(received);
        }

        let actual: Vec<(&str, &str, &str)> = files.iter().map(|file| (file.id.as_str(), file.content.as_str(), file.annotate.as_str())).collect();

        assert_eq!(actual.clone().sort_by(|a, b| a.0.cmp(b.0)), source.clone().sort_by(|a, b| a.0.cmp(b.0)));
    }

    #[test]
    fn test_extra_method_chat_index() {
        let branch = "master";
        let project_url = "/workspace";
        let dir = "/workspace/a.tsx";
        let source_code = r#"
export class C1 extends C2 implements T {
    // get
	get f1() {}

    // set
	set f2() {}

    // fn3
	fn3() {}

    // async f4
	async fn4() {}

    // decorator
    @fn
    fn5() {}

    // property
	fn6 = () => {}
}

modules.exports = class C2 {
  fn() {}
}

// f1
function f1() {
  // code
}

// f2
function* f2() {}

// f3
// f3
const f3 = () => {}

// f4
export function f4() {}

// f5
export function f5() {}

// f6
export const f6 = () => {}

// f7
export default function f7() {}

// f8
export default function() {}

// f9
export default function* f9() {}

// f10
export default function*() {}

// f11
export default () => {}

// exports1
module.exports = () => {}

// exports2
exports.f = () => {}

// export tsx
export default (props: any) => {
  return <div></div>
}
"#.trim();

        let methods = extract_method_chat_index(Path::new(dir), &source_code.to_string(), &project_url.to_string(), &branch.to_string()).unwrap();

        let expect = vec![
            (
                ("// get\n\tget f1() {}"),
                ("// get")
            ),
            (
                ("// set\n\tset f2() {}"),
                ("// set"),
            ),
            (
                ("// fn3\n\tfn3() {}"),
                ("// fn3"),
            ),
            (
                ("// async f4\n\tasync fn4() {}"),
                ("// async f4"),
            ),
            (
                ("// decorator\n    @fn\n    fn5() {}"),
                ("// decorator"),
            ),
            (
                ("fn() {}"),
                (""),
            ),
            (
                ("// f1\nfunction f1() {\n  // code\n}"),
                ("// f1"),
            ),
            (
                ("// f2\nfunction* f2() {}"),
                ("// f2"),
            ),
            (
                ("// f3\n// f3\nconst f3 = () => {}"),
                ("// f3\n// f3"),
            ),
            (
                ("// f4\nexport function f4() {}"),
                ("// f4"),
            ),
            (
                ("// f5\nexport function f5() {}"),
                ("// f5"),
            ),
            (
                ("// f6\nexport const f6 = () => {}"),
                ("// f6"),
            ),
            (
                ("// f7\nexport default function f7() {}"),
                ("// f7"),
            ),
            (
                ("// f8\nexport default function() {}"),
                ("// f8"),
            ),
            (
                ("// f9\nexport default function* f9() {}"),
                ("// f9"),
            ),
            (
                ("// f10\nexport default function*() {}"),
                ("// f10"),
            ),
            (
                ("// f11\nexport default () => {}"),
                ("// f11"),
            ),
            (
                ("// exports1\nmodule.exports = () => {}"),
                ("// exports1"),
            ),
            (
                ("// exports2\nexports.f = () => {}"),
                ("// exports2"),
            ),
            (
                ("// export tsx\nexport default (props: any) => {\n  return <div></div>\n}"),
                ("// export tsx"),
            )
        ];

        let actual: Vec<_> = methods.iter().map(|method| (method.content.as_ref(), method.annotate.as_ref())).collect();

        assert_eq!(actual, expect);
    }
}

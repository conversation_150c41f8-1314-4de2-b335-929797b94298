use std::collections::{HashMap, HashSet};

use tree_sitter::Node;

use agent_db::domain::code_kv_index_domain::{CodeInfo, MethodDeclaration};
use agent_db::tools::common_tools::LINE_ENDING;

pub const IMPORT_PREFIX: &str = "import ";
pub const PACKAGE_PREFIX: &str = "package ";
pub const STATIC_PREFIX: &str = "static ";


/// 抽取class级别的字段类型
/// 注意： 抽取时候只会关注对象级别，类似int类型不会抽取
pub fn extract_class_field_map(class_field_map: &mut HashMap<String, String>, code_content: &&String, node: &Node) {
    let mut cursor = node.walk();
    let field_type_node_opt = node.child_by_field_name("type");
    if let Some(field_type_node) = field_type_node_opt {
        let file_type = &code_content[field_type_node.start_byte()..field_type_node.end_byte()];
        for field_node in node.children(&mut cursor) {
            match field_node.kind() {
                "variable_declarator" => {
                    let mut variable_cursor = field_node.walk();
                    for variable_node in field_node.children(&mut variable_cursor) {
                        if variable_node.kind() == "identifier" {
                            let field_name = &code_content[variable_node.start_byte()..variable_node.end_byte()];
                            class_field_map.insert(field_name.to_string(), file_type.to_string());
                        }
                    }
                }
                _ => {}
            }
        }
    }
}

///class基础信息
pub fn extract_class_base_model(code_content: &&String, code_info: &mut CodeInfo, node: Node) {
    let class_type = match node.kind() {
        "class_declaration" => "class",
        "enum_declaration" => "enum",
        "interface_declaration" => "interface",
        "annotation_type_declaration" => "annotation",
        _ => "unknown"
    };
    let mut modifier_identifier: (Option<String>, Option<String>) = (None, None);
    // 从当前节点开始遍历其子节点,查找
    let mut child_cursor = node.walk();
    for child in node.children(&mut child_cursor) {
        match child.kind() {
            "modifiers" => {
                let modifiers_str = &code_content[child.start_byte()..child.end_byte()];
                let modifier_value = match modifiers_str.rsplit_once(LINE_ENDING) {
                    Some((_, last)) => last,
                    None => modifiers_str
                };
                modifier_identifier.0 = Some(modifier_value.to_string())
            }
            "identifier" => {
                let identifier_str = &code_content[child.start_byte()..child.end_byte()];
                modifier_identifier.1 = Some(identifier_str.to_string())
            }
            _ => {}
        }
    }

    if let (Some(modifier), Some(identifier)) = (modifier_identifier.0, modifier_identifier.1) {
        //todo ,一个文件内可能有多个class,目前把 public 修饰的当作最外层class
        if modifier.starts_with("public") {
            code_info.class_type = Some(class_type.to_string());
            code_info.class_name = Some(identifier)
        }
    }
}

///抽取未写完的java方法，如果写完的代码不用管
/// 主要用在补全场景，函数体内的补全时对依赖代码做排序
pub fn extract_unfinish_method(code_content: &&String, node: &Node) -> Option<MethodDeclaration> {
    let mut result = MethodDeclaration::default();
    let mut return_class: String = String::new();
    let mut param_class_vec: Vec<String> = Vec::new();
    let mut method_name: String = String::new();
    let mut method_inner_variable_class_vec: Vec<String> = Vec::new();
    let mut cursor = node.walk();
    for method_node in node.children(&mut cursor) {
        match method_node.kind() {
            "type_identifier" => {
                //返回值
                let node_value = &code_content[method_node.start_byte()..method_node.end_byte()];
                return_class = node_value.to_string();
            }
            "identifier" => {
                //解析方法名
                let node_value = &code_content[method_node.start_byte()..method_node.end_byte()];
                method_name = node_value.to_string();
            }
            "formal_parameters" => {
                //解析方法参数
                let mut param_cursor = method_node.walk();
                for param_node in method_node.children(&mut param_cursor) {
                    match param_node.kind() {
                        "formal_parameter" => {
                            let field_type_node_opt = param_node.child_by_field_name("type");
                            if let Some(field_type_node) = field_type_node_opt {
                                let file_type = &code_content[field_type_node.start_byte()..field_type_node.end_byte()];
                                param_class_vec.push(file_type.to_string());
                            }
                        }
                        _ => {}
                    }
                }
            }
            "block" => {
                // let method_last_char = &code_content[method_node.start_byte()..method_node.end_byte()].chars().last();
                // if let Some(last_char) = method_last_char {
                    // if *last_char != '}' {
                        //如果不是以}结尾，才是在函数体内补全。那么解析参数，返回值，函数体内数据
                        let mut method_inner_cursor = method_node.walk();
                        for method_item_node in method_node.children(&mut method_inner_cursor) {
                            // let test_value = &code_content[method_item_node.start_byte()..method_item_node.end_byte()];
                            // info!("node_type: {} , test_value:{}",method_item_node.kind(),test_value);
                            match method_item_node.kind() {
                                "local_variable_declaration" => {
                                    let mut method_variable_cursor = method_item_node.walk();
                                    for method_variable_node in method_item_node.children(&mut method_variable_cursor) {
                                        match method_variable_node.kind() {
                                            "type_identifier" => {
                                                let variable_name = &code_content[method_variable_node.start_byte()..method_variable_node.end_byte()];
                                                //todo 注意这里的顺序，需要和build_cache_data里面的build_related_module_score对应，内部类有个递进的得分机制
                                                method_inner_variable_class_vec.push(variable_name.to_string());
                                            }
                                            _ => {}
                                        }
                                    }
                                }
                                _ => {}
                            }
                            if method_inner_variable_class_vec.len() > 0 {
                                result.inner_class_declaration = Some(method_inner_variable_class_vec.clone());
                            }
                        }
                    // }
                // }
            }
            _ => {}
        }
    }
    if method_name.len() > 0 {
        result.method_name = method_name;
    }
    if param_class_vec.len() > 0 {
        result.param_class_vec = Some(param_class_vec);
    }
    if return_class.len() > 0 {
        result.return_class = Some(return_class);
    }
    //当method_name不为空，并且有参数或者有返回值或者方法体内有类变量，返回result，否则返回None(如果仅有method_name)，没有其他信息，返回上游也没啥用
    if result.method_name.len() > 0 && (result.param_class_vec.is_some() || result.return_class.is_some() || result.inner_class_declaration.is_some()) {
        Some(result)
    } else {
        None
    }
}


///抽取import信息
pub fn extract_import_set(code_content: &&String, import_set: &mut HashSet<String>, node: &Node) {
    let import_str = &code_content[node.start_byte()..node.end_byte()];
    //如果包含*,一般代表引入了某个package 暂不处理
    if !import_str.contains("*") {
        let last_char_opt = import_str.find(";");
        if let Some(last_char) = last_char_opt {
            // 正常情况下肯定是以import 开头, 以; 结尾, 如果不是, 那么跳过
            if import_str.starts_with(IMPORT_PREFIX) {
                let mut import_str = import_str[IMPORT_PREFIX.len()..last_char].trim();
                //如果引入的是静态x,做特殊处理
                if import_str.starts_with(STATIC_PREFIX) {
                    import_str = &import_str[STATIC_PREFIX.len()..].trim();
                } else {
                    import_set.insert(import_str.to_string());
                }
            }
        }
    }
}

///抽取继承class
pub fn extract_extends_class(code_content: &&String,  node: &Node)->Option<String> {
    let mut cursor = node.walk();
    for extends_node in node.children(&mut cursor) {
        match extends_node.kind() {
            "type_identifier" => {
                let extends_class = &code_content[extends_node.start_byte()..extends_node.end_byte()];
                return Some(extends_class.to_string());
            }
            "generic_type" => {
                //范型
                let mut child_node_cursor = extends_node.walk();
                for child_node in extends_node.children(&mut child_node_cursor) {
                    if child_node.kind() == "type_identifier" {
                        let child_node_class = &code_content[child_node.start_byte()..child_node.end_byte()];
                        return Some(child_node_class.to_string());
                    }
                }
            }
            _ => {}
        }
    }
    return None;
}

///抽取implements实现接口set
pub fn extract_implements_class(code_content: &&String, code_info: &mut CodeInfo, node: &Node) {
    let mut cursor = node.walk();
    for implements_node in node.children(&mut cursor) {
        if implements_node.kind() == "type_list" {
            let mut implements_class_set: HashSet<String> = HashSet::new();
            let mut child_cursor = implements_node.walk();
            for child_node in implements_node.children(&mut child_cursor) {
                match child_node.kind() {
                    "type_identifier" => {
                        let type_identifier_str = &code_content[child_node.start_byte()..child_node.end_byte()];
                        implements_class_set.insert(type_identifier_str.to_string());
                    }
                    "generic_type" => {
                        //范型
                        let mut child_node_cursor = child_node.walk();
                        for child_node in child_node.children(&mut child_node_cursor) {
                            if child_node.kind() == "type_identifier" {
                                let child_node_class = &code_content[child_node.start_byte()..child_node.end_byte()];
                                implements_class_set.insert(child_node_class.to_string());
                            }
                        }
                    }
                    _ => {}
                }
            }
            if implements_class_set.len() > 0 {
                code_info.implements_class_name_set = Some(implements_class_set)
            }
        }
    }
}

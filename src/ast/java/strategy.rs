use agent_common_service::model::code_complete_model::{CodeCompletionRequestBean, ContextAwareInfo};
use log::{error, info};
use once_cell::sync::Lazy;
use std::collections::{HashMap, HashSet};
use std::path::Path;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tree_sitter::{Node, Parser, Point, TreeCursor};

use crate::ast::constants::MAX_LOOP_TIME;
use crate::ast::java::extract_utils::{extract_class_base_model, extract_class_field_map, extract_extends_class, extract_implements_class, extract_import_set, extract_unfinish_method, IMPORT_PREFIX, PACKAGE_PREFIX};
use crate::dialogue::codefuse_index_repository::{build_method_index_exetute, JavaParser};
use crate::dialogue::instance_vec::SharedVec;
use crate::function::build_cache_data::build_related_module_score;
use agent_common_service::model::code_scan_model::ScanConfig;
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_common_service::service::code_ast_analysis::AstStrategy;
use agent_common_service::service::code_scan::CodeScanStrategy;
use agent_common_service::tools::related_module_score::CacheRelatedModule;
use agent_db::domain::code_chat_domain::CodefuseMethod;
use agent_db::domain::code_kv_index_domain::{CodeInfo, ScanFileRecord};
use agent_db::tools::common_tools::LINE_ENDING;


pub struct CodeFuseJavaAstStrategy;

impl AstStrategy for CodeFuseJavaAstStrategy {
    ///抽取通用代码结构
    fn extra_code_info(code_content: &String) -> Option<CodeInfo> {
        let mut result = code_content.clone();
        let mut parser = Parser::new();
        parser.set_language(&tree_sitter_java::language()).unwrap();
        let tree = parser.parse(code_content, None).unwrap();
        let mut cursor = tree.walk();
        let mut to_remove: Vec<(usize, usize)> = Vec::new();
        let mut code_info = CodeInfo::default();
        let mut import_set: HashSet<String> = HashSet::new();
        let mut package_opt: Option<String> = None;
        let mut class_field_map = HashMap::new();
        let start_millis = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        //循环便利
        loop {
            let end_millis = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
            //兜底策略,防止死循环导致agent卡死
            if end_millis - start_millis > MAX_LOOP_TIME {
                break;
            }
            let node = cursor.node();

            match node.kind() {
                "superclass" => {
                    let extend_class_opt = extract_extends_class(&code_content, &node);
                    if let Some(extend_class) = extend_class_opt {
                        code_info.extend_class_name = Some(extend_class);
                    }
                }
                "super_interfaces" => {
                    extract_implements_class(&code_content, &mut code_info, &node);
                }
                "field_declaration" => {
                    extract_class_field_map(&mut class_field_map, &code_content, &node)
                }
                "class_declaration" | "enum_declaration" | "interface_declaration" | "annotation_type_declaration" => {
                    extract_class_base_model(&code_content, &mut code_info, node);
                }
                "method_declaration" | "constructor_declaration" => {
                    if let Some(block) = node.child_by_field_name("body") {
                        // 获取要删除的范围
                        to_remove.push((block.start_byte(), block.end_byte()));
                    }
                }
                "import_declaration" => {
                    extract_import_set(&code_content, &mut import_set, &node);
                }
                "package_declaration" => {
                    let package_str = &code_content[node.start_byte()..node.end_byte()];
                    let last_char_opt = package_str.find(";");
                    if let Some(last_char) = last_char_opt {
                        // 正常情况下肯定是以import 开头, 以; 结尾, 如果不是, 那么跳过
                        if package_str.starts_with(PACKAGE_PREFIX) {
                            let package_str = package_str[IMPORT_PREFIX.len()..last_char].trim();
                            package_opt = Some(package_str.to_string());
                        }
                    }
                }
                _ => {}
            }
            //移动下一个子节点,如果没有子节点返回false
            if !cursor.goto_first_child() {
                //移动下一个兄弟节点,如果没有返回false
                while !cursor.goto_next_sibling() {
                    //遍历所有兄弟节点, 游标返回父节点
                    if !cursor.goto_parent() {
                        // 已经遍历了整棵树
                        break;
                    }
                }
            }
            if cursor.node() == tree.root_node() {
                // 遍历结束
                break;
            }
        }

        // 逆序移除范围，这样索引就不会发生变化
        to_remove.reverse();
        let mut code_struct = result.to_string();
        for &(mut start, mut end) in &to_remove {
            // code_struct.replace_range(start..end, "{ }");
            // 确保start和end在字符边界上
            while !code_struct.is_char_boundary(start) && start > 0 {
                start -= 1;
            }
            while !code_struct.is_char_boundary(end) && end < code_struct.len() {
                end += 1;
            }
            if end > code_struct.len() {
                end = code_struct.len();
            }
            // 检查一下start和end的有效性，确保它们在字符串范围内
            if start <= end && end <= code_struct.len() {
                code_struct.replace_range(start..end, "{ }");
            } else {
                error!("Invalid range: {:?}..{:?} , content: {}", start, end,code_struct);
                return None;
            }
        }
        code_info.code_struct = code_struct;
        if let (Some(class_name), Some(class_type), Some(package_value)) = (&code_info.class_name, &code_info.class_type, package_opt) {
            if class_name.trim().len() > 0 && class_type.len() > 0 && package_value.len() > 0 {
                code_info.full_qualified_name = Some(format!("{}.{}", package_value, class_name));
                if import_set.len() > 0 {
                    code_info.import_set = Some(import_set);
                }
                if class_field_map.len() > 0 {
                    code_info.fild_name_class_map = Some(class_field_map);
                }
                return Some(code_info);
            }
        }
        return None;
    }
    fn get_prompt_declaration(bean: &CodeCompletionRequestBean) -> Option<PromptDeclaration> {
        get_current_detail_from_prompt(&bean.prompt)
    }

    fn extra_method_chat_index(dir: &Path, source_code: &String, project_url:&String,branch:&String) -> Option<Vec<CodefuseMethod>> {
        info!("extra_method_chat_index start, project_url:{},branch:{}",project_url,branch);
        // 解析源代码
        let mut java_parser = JavaParser::new();
        // let tree = java_parser.parser.parse(&source_code, None).expect("Unable to parse source code");
        match java_parser.parser.parse(&source_code, None) {
            Some(tree) => {
                let root_node = tree.root_node();
                let mut share_vec = SharedVec::new();
                let mut container: Vec<CodefuseMethod> = Vec::new();
                let _ = build_method_index_exetute(&root_node, &source_code, project_url, branch, &mut share_vec, &mut container, dir, None);
                Some(container)
            }
            None => Some(Vec::new())
        }
    }
}

impl CodeFuseJavaAstStrategy {
    pub(crate) fn build_related_module_score(_: &CodeCompletionRequestBean, current_context: &PromptDeclaration, file_record: &ScanFileRecord) -> Option<CacheRelatedModule> {
        let result = build_related_module_score(current_context, file_record);
        CacheRelatedModule::from_tuple(result)
    }
}

const MIN_CONTENT_LEN: usize = 20;

///从当前代码上文，抽取出import，所在函数体等详情信息
fn get_current_detail_from_prompt(prompt: &String) -> Option<PromptDeclaration> {
    let mut lines: Vec<&str> = prompt.split(LINE_ENDING).collect();
    if lines.len() < MIN_CONTENT_LEN {
        //如果prompt行数小于20，直接返回，无需更新缓存数据，暂时不处理
        return None;
    }
    //去掉最后一行
    lines.pop();
    let del_last_line_content = lines.join(LINE_ENDING);
    get_relate_item_from_prompt(&del_last_line_content)
}

/// 备注： 因为是针对java语言，所以放在CodeFuseJavaAstStrategy外面了
/// 获取依赖class信息，包含import，field（对象类型），返回值，函数参数，import，extends
/// 注意：由于写一半的代码会影响解析，所以调用此函数前要对prompt进行截断。比如
///        User u = new User();
///        u
/// 截断成
///        User u = new User();
pub fn get_relate_item_from_prompt(code_content: &String) -> Option<PromptDeclaration> {
    let mut result = PromptDeclaration::new();
    let mut parser = Parser::new();
    parser.set_language(&tree_sitter_java::language()).unwrap();
    let tree = parser.parse(code_content, None).unwrap();
    let mut cursor = tree.walk();
    let mut class_field_map = HashMap::new();
    let mut import_set: HashSet<String> = HashSet::new();
    let start_millis = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    //循环便利
    loop {
        let end_millis = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        //兜底策略,防止死循环导致agent卡死
        if end_millis - start_millis > MAX_LOOP_TIME {
            break;
        }
        let node = cursor.node();
        match node.kind() {
            "import_declaration" => {
                extract_import_set(&code_content, &mut import_set, &node);
            }
            "superclass" => {
                let extend_class_opt = extract_extends_class(&code_content, &node);
                if let Some(extend_class) = extend_class_opt {
                    result.extends_class = Some(extend_class);
                }
            }
            "field_declaration" => {
                extract_class_field_map(&mut class_field_map, &code_content, &node);
            }
            "method_declaration" => {
                let java_method_declaration_opt = extract_unfinish_method(&code_content, &node);
                if let Some(java_method_declaration) = java_method_declaration_opt {
                    result.method_declaration = Some(java_method_declaration);
                }
            }
            "package_declaration" => {
                let package_str = &code_content[node.start_byte()..node.end_byte()];
                let last_char_opt = package_str.find(";");
                if let Some(last_char) = last_char_opt {
                    // 正常情况下肯定是以import 开头, 以; 结尾, 如果不是, 那么跳过
                    if package_str.starts_with(PACKAGE_PREFIX) {
                        let package_str = package_str[IMPORT_PREFIX.len()..last_char].trim();
                        result.package_name = Some(package_str.to_string());
                    }
                }
            }
            _ => {}
        }
        //移动下一个子节点,如果没有子节点返回false
        if !cursor.goto_first_child() {
            //移动下一个兄弟节点,如果没有返回false
            while !cursor.goto_next_sibling() {
                //遍历所有兄弟节点, 游标返回父节点
                if !cursor.goto_parent() {
                    // 已经遍历了整棵树
                    break;
                }
            }
        }
        if cursor.node() == tree.root_node() {
            // 遍历结束
            break;
        }
    }
    if import_set.len() > 0 {
        result.import_set = Some(import_set);
    }
    if class_field_map.len() > 0 {
        let field_class_set: HashSet<String> = class_field_map.values().cloned().collect();
        result.field_set = Some(field_class_set);
    }
    if result.method_declaration.is_some()||result.field_set.is_some()||result.import_set.is_some()||result.extends_class.is_some() {
        Some(result)
    }else {
        None
    }
}

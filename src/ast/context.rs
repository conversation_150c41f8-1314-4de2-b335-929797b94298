use crate::ast::typescript::parsers::context::TsAstContext;
use std::cell::OnceCell;
use std::rc::Rc;

pub struct AstContext {
    pub project_path: String,
    ts_ast_context: OnceCell<Rc<TsAstContext>>,
}

/// AstContext is a context that holds the project path and the ts ast context.
/// 在解析整个仓库的过程中只需要初始化一次，它内部维护了仓库依赖关系
impl AstContext {
    pub fn new(project_path: &String) -> Self {
        Self {
            project_path: project_path.clone(),
            ts_ast_context: OnceCell::new(),
        }
    }

    pub fn ts_ast_context(&self) -> &Rc<TsAstContext> {
        self.ts_ast_context
            .get_or_init(|| Rc::new(TsAstContext::new(&self.project_path)))
    }
}

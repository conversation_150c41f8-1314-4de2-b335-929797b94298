use std::time::Instant;

// 定义一个包含 `now` 字段的基础结构体
#[derive(Debug)]
pub struct TimeTracker {
    start: Instant,
}

impl TimeTracker {
    // 检查是否超过最大循环时间
   pub fn exceed_time(&self) -> bool {
        self.start.elapsed().as_millis() > MAX_LOOP_TIME
    }
}

impl Default for TimeTracker {
    fn default() -> Self {
        Self {
            start: Instant::now(),
        }
    }
}


//循环占用时间最大为100ms
#[cfg(test)]
pub const MAX_LOOP_TIME: u128 = 100000000;
#[cfg(not(test))]
pub const MAX_LOOP_TIME: u128 = 300;


/// 如果prompt行数小于该值，暂时不处理此种情况
pub const MIN_PROMPT_LINES_LEN: usize = 20;

pub const METHOD_PARAM: &str = "methodParam";
pub const EXTEND_CLASS: &str = "extendClass";
pub const METHOD_INNER_CLASS: &str = "methodInnerClass";
pub const RETURN_CLASS: &str = "returnClass";
pub const CLASS_FIELD: &str = "classField";
pub const IMPORT_CLASS: &str = "importClass";
use agent_db::tools::common_tools::LINE_ENDING;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{ScanProjectRecord, PROJECT_DATA_KEY};
use ignore::<PERSON>r<PERSON><PERSON><PERSON>;
use log::{error, info};
use std::fs;
use std::fs::File;
use std::io::{BufRead, BufReader};
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};
use tree_sitter::Node;

//循环占用时间最大为100ms
#[cfg(test)]
pub const MAX_LOOP_TIME: u128 = 100000000;
#[cfg(not(test))]
pub const MAX_LOOP_TIME: u128 = 10000000;

///判断是否跳过指定文件
/// 返回值（true:跳过，false:不跳过，file_name_suffix:文件后缀名，lines:文件内容）
pub fn skip_file(dir: &Path, file_content_opt: Option<String>) -> (bool, Option<String>, Option<Vec<String>>) {
    if !dir.exists() {
        return (true, None, None);
    }
    let extension_opt = dir.extension();
    if let None = extension_opt {
        //没有后缀名
        return (true, None, None);
    }
    let short_file_name_opt = dir.file_stem();
    if short_file_name_opt.is_none() {
        //文件名是空
        return (true, None, None);
    }
    let short_file_name = short_file_name_opt.unwrap().to_str().unwrap();
    if AGENT_CONFIG.scan_skip_test_flag == 1 && short_file_name.ends_with("Test") {
        //跳过测试文件
        return (true, None, None);
    }
    let file_name_suffix = extension_opt.unwrap().to_string_lossy().to_string();
    if !(AGENT_CONFIG.similarity_suffix_arr.contains(&file_name_suffix) || AGENT_CONFIG.related_suffix_arr.contains(&file_name_suffix)) {
        return (true, None, None);
    }
    let file_size = fs::metadata(dir).unwrap().len();
    if file_size > AGENT_CONFIG.scan_skip_file_size {
        return (true, None, None);
    }
    let file_result = File::open(dir);
    if let Err(error) = file_result {
        //有可能扫描过程中用户删除文件
        error!("open file error: {:?} , url: {:?}",error,dir);
        return (true, None, None);
    }

    let mut lines: Vec<String> = vec![];
    if file_content_opt.is_none() {
        let file = file_result.unwrap();
        let reader = BufReader::new(file);
        lines = reader.lines().collect::<Result<_, _>>().unwrap_or(vec![]);
    } else {
        lines = file_content_opt.unwrap().split(LINE_ENDING).map(|s| s.to_string()).collect();
    }

    if lines.len() > AGENT_CONFIG.scan_skip_file_max_len || lines.len() < AGENT_CONFIG.scan_skip_file_min_len {
        return (true, None, None);
    }
    return (false, Some(file_name_suffix), Some(lines));
}

pub fn is_offset_in_node(offset: usize, node: &Node) -> bool {
    return offset >= node.start_byte() && offset < node.end_byte();
}

pub fn get_real_class_name(class_name: &String) -> String {
    if let Some(left_index) = class_name.find("<") {
        if let Some(right_index) = class_name.find(">") {
            return class_name[left_index + 1..right_index].to_string();
        }
    }
    return class_name.clone();
}

pub fn is_same_class_name(class_name1: &String, class_name2: &String) -> bool {
    return get_real_class_name(class_name1) == get_real_class_name(class_name2);
}
use serde::{Deserialize, Serialize};
use std::sync::atomic::AtomicUsize;

#[derive(Debug, Serialize, Deserialize)]
pub struct RepositoryCounter {
    pub method_count : AtomicUsize,
    pub method_has_summary_count : AtomicUsize,
    pub method_has_vector_count : AtomicUsize,

    pub chunk_count : AtomicUsize,
    pub chunk_has_summary_count : AtomicUsize,
    pub chunk_has_vector_count : AtomicUsize,

    pub file_count : AtomicUsize,
    pub file_has_summary_count : AtomicUsize,

    //chunk summary有vector的数量
    pub chunk_has_summary_vector_count: AtomicUsize,
    //method summary有vector的数量
    pub method_has_summary_vector_count: AtomicUsize,
    //file summary有vector的数量
    pub file_has_summary_vector_count: AtomicUsize,
}

impl Default for RepositoryCounter {
    fn default() -> Self {
        RepositoryCounter{
            method_count: AtomicUsize::new(0),
            method_has_summary_count: AtomicUsize::new(0),
            method_has_vector_count: AtomicUsize::new(0),

            chunk_count: AtomicUsize::new(0),
            chunk_has_summary_count: AtomicUsize::new(0),
            chunk_has_vector_count: AtomicUsize::new(0),

            file_count: AtomicUsize::new(0),
            file_has_summary_count: AtomicUsize::new(0),

            chunk_has_summary_vector_count: Default::default(),
            method_has_summary_vector_count: Default::default(),
            file_has_summary_vector_count: Default::default(),
        }
    }
}
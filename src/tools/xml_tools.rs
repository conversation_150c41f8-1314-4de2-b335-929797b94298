use log::error;
use quick_xml::de::from_str;
use regex::Regex;
use serde::Deserialize;
use std::fs::File;
use std::io::{BufRead, Read};
use std::path::Path;
use std::{fs, io};

#[derive(Debug, Deserialize)]
pub struct Project {
    #[serde(rename = "dependencyManagement")]
    pub dependency_management: Option<DependencyManagement>,
}

#[derive(Debug, Deserialize)]
pub struct DependencyManagement {
    #[serde(rename = "dependencies")]
    pub  dependencies: Option<Dependencies>,
}

#[derive(Debug, Deserialize)]
pub struct Dependencies {
    #[serde(rename = "dependency")]
    pub  dependencies: Option<Vec<Dependency>>,
}

#[derive(Debug, Deserialize, <PERSON><PERSON>, Default)]
pub struct Dependency {
    pub   groupId: String,
    pub artifactId: String,
    pub version: Option<String>,
}

//pom文件内容，并且替换掉pom中的变量
pub fn get_pom_content_without_variable(file_url: &Path) -> Option<String> {
    let file_result = File::open(file_url); // 打开文件
    if file_result.is_err() {
        error!("Error opening file {:?}", file_url.to_str());
        return None;
    }
    let mut file = file_result.unwrap();
    let mut content = String::new();
    let read_result = file.read_to_string(&mut content);
    if read_result.is_err() {
        error!("Error reading file {:?}", file_url.to_str());
        return None;
    }
    let reader = io::BufReader::new(file);
    //找到所有properties
    let mut in_properties = false;
    let property_regex = Regex::new(r"<([^>]+)>([^<]+)</[^>]+>").unwrap();
    for line in reader.lines() {
        let line = line.unwrap();
        let trimmed = line.trim();

        if trimmed == "<properties>" {
            in_properties = true;
            continue;
        }

        if trimmed == "</properties>" {
            in_properties = false;
            break;
        }

        if in_properties {
            if let Some(captures) = property_regex.captures(trimmed) {
                let key = captures.get(1).unwrap().as_str();
                let value = captures.get(2).unwrap().as_str();
                //如果找到properties信息，替换pom中${properties.key}的变量
                let placeholder = format!("${{{}}}", key);
                content = content.replace(&placeholder, value)
            }
        }
    }
    Some(content)
}


//获取pom中的依赖列表
pub fn get_dependency_list(content: &String) -> Option<Vec<Dependency>> {
    let project_result = from_str(content);
    if project_result.is_err() {
        error!("Error deserializing project");
        return None;
    }
    let project: Project = project_result.unwrap();

    project.dependency_management.as_ref()?.dependencies.as_ref()?.dependencies.as_ref().cloned()
}


#[cfg(test)]
mod tests {
    use std::path::Path;

    #[test]
    fn test1() {
        let file_url = "/Users/<USER>/workspace/codegenerator/backup/caselike/pom11.xml";


        let p = Path::new(file_url);
        println!("{:?}", p.is_file());

        // let content = get_pom_content_without_variable(file_url).unwrap();
        //
        // let dependency_opt = get_dependency_list(&content);
        // let dependency_list = dependency_opt.unwrap();
        // for dependency in dependency_list {
        //     println!("{:?}", dependency);
        // }
    }
}
use std::str::FromStr;
use std::sync::Arc;

use anyhow::Result;
use once_cell::sync::Lazy;
use tokenizers::Tokenizer;

use agent_db::domain::code_kv_index_domain::ScanSnippetRecord;

///token解析器的客户端对象
struct TokenClient {
    //Arc 确保了不同线程可以共享 Mutex 的所有权而不会造成数据竞争。
    //Mutex 确保了同一时间只有一个线程可以访问受保护的数据，避免了不一致状态和竞态条件。
    tokenizer: Arc<Tokenizer>,
}


//初始化执行一次
static TOKEN_CLIENT: Lazy<TokenClient> = Lazy::new(|| {
    let content = include_str!("../../resources/tokenizer.json");

    let tokenizer = Tokenizer::from_str(content).expect("Failed to load tokenizer");

    TokenClient {
        tokenizer: Arc::new(tokenizer),
    }
});

///代码分词,输入一个代码片段,返回这个代码片段的分词id列表+token列表
pub fn code_snippet_tokenizer(code_snippet: String, start_no: usize, end_no: usize, index: usize, file_url: &str,file_name_suffix: &String) -> Result<ScanSnippetRecord> {
    let current_tokenizer = &TOKEN_CLIENT.tokenizer;
    // 对代码片段进行编码
    let encoding_result = current_tokenizer.encode(code_snippet, false).unwrap();
    // let token_arr = encoding_result.get_tokens().to_vec();
    let id_vec = encoding_result.get_ids().to_vec();
    // 返回tokenization结果
    Ok(ScanSnippetRecord {
        file_url: file_url.to_string(),
        file_name_suffix:file_name_suffix.clone(),
        id_vec: id_vec,
        start_no: start_no,
        end_no: end_no,
        index: index,
        next_key: None,
    })
}

///将token id列表转换为代码片段
pub fn token_to_code(token_id_vec: &Vec<u32>) -> Result<String> {
    let current_tokenizer = &TOKEN_CLIENT.tokenizer;
    let code_content = current_tokenizer.decode(token_id_vec.as_slice(), false).unwrap();
    Ok(code_content)
}
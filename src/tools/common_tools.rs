use std::env;
use std::path::PathBuf;

use crate::dal::remote_client::DeviceInfo;
use dirs_next::home_dir;

#[cfg(windows)]
pub const LINE_ENDING: &str = "\r\n";
#[cfg(not(windows))]
pub const LINE_ENDING: &str = "\n";

pub const SEPARATORSIMPLE: &str = "#";

pub const FILE_PREFIX: &str = "F_";
pub const SNIPPET_PREFIX: &str = "S_";
//二/三方jar依赖信息，key：project_url#二方包class全限定名
pub const R_PREFIX: &str = "R_";
//chunk级的id前缀
pub const V_C_PREFIX: &str = "VC_";
//method级的id前缀
pub const V_M_PREFIX: &str = "VM_";


///抽取用户目录
pub fn expand_user_home(path: &str) -> Option<PathBuf> {
    if path.starts_with('~') {
        if let Some(home_dir) = home_dir() {
            // 在 Windows 上，'~/' 和 '~\' 都应该被替换成用户主目录
            let rest_of_path = path
                .strip_prefix("~/")
                .unwrap_or_else(|| path.strip_prefix("~\\").unwrap_or(path));
            return Some(home_dir.join(rest_of_path));
        } else {
            return None; // 无法获取主目录
        }
    } else {
        // 对于 Windows，我们还需要处理可能存在的 '\\' 分隔符
        let normalized_path = path.replace("\\", "/");
        Some(PathBuf::from(normalized_path))
    }
}

///收集设备信息
pub fn collect_device_info() -> DeviceInfo {
    let mut device_info = DeviceInfo::default();
    device_info.os_type = env::consts::OS.to_string();
    device_info.cpu_arch = env::consts::ARCH.to_string();
    device_info.os_name = sys_info::os_type().unwrap_or_else(|e| "".to_string());
    device_info.os_version = sys_info::os_release().unwrap_or_else(|e| "".to_string());
    device_info.cpu_num = sys_info::cpu_num().unwrap_or_else(|e| 0) as usize;

    match sys_info::mem_info() {
        Ok(mem_info) => {
            device_info.total_mem = format!("{}{}", mem_info.total / 1024, "M");
            device_info.free_mem = format!("{}{}", mem_info.free / 1024, "M");
        }
        Err(error) => {
            device_info.total_mem = format!("{}", "unknown");
            device_info.free_mem = format!("{}", "unknown");
        }
    }
    device_info
}

use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::path::Path;
use log::info;
use crate::model::chat_model::ChatRelatedRequestBean;

///缓存类型
#[derive(Hash, Eq, PartialEq, Debug)]
pub enum CacheTypeEnum {
    SIMILARITY,
    RELATED,
}

///相关缓存数据模块类型
#[derive(Hash, Eq, PartialEq, Debug)]
pub enum CacheRelatedModuleEnum {
    IMPORT,
    FIELD,
    EXTENDS,
    METHOD,
}

///相似片段结构
pub struct SimilarityCodeInfo {
    //代码片段token id集合
    pub token_id_set: HashSet<u32>,
    //如果AgentConfig.similarity_cal_type为0：保存当前token_id_set对应的代码片段
    //如果AgentConfig.similarity_cal_type为1：保存下滑code_complation_slide_size行后的代码片段
    pub code_snippet: String,
    //文件地址
    pub file_url: String,
}

///二方jar信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AntJarCodeInfo {
    //二方包类名
    pub fullyQualifiedName:String,
    //二方包类结构
    pub codeStruct:String,
}


impl SimilarityCodeInfo {
    pub fn new(file_url: String) -> Self {
        SimilarityCodeInfo {
            token_id_set: Default::default(),
            code_snippet: "".to_string(),
            file_url,
        }
    }
}

///记录对话状态日志
pub fn log_chat_step(method_name:&str,chat_related_request: &ChatRelatedRequestBean,value:HashMap<&str,String>){
    info!("method: {:?}, question_id: {:?}, query: {:?}, project_url: {:?}, branch: {:?},value:{:?}",
        method_name,
        chat_related_request.questionUid,
        chat_related_request.query,
        chat_related_request.projectUrl,
        chat_related_request.branch,
        value
    );
}


///获取父目录, 如果输入文件路径, 则是当前文件所在目录
pub fn get_parent_directory<P: AsRef<Path>>(path: P) -> Option<String> {
    path.as_ref()
        .parent()
        .and_then(|p| p.to_str())
        .map(|s| s.to_owned())
}
///获取文件名
pub fn get_file_name<P: AsRef<Path>>(path: P) -> Option<String>  {
    path.as_ref()
        .file_name() // 获取文件名部分
        .and_then(|name| name.to_str()) // 将 OsStr 转换为 &str
        .map(|s| s.to_string()) // 将 &str 转换为 String
}

/// 根据文件路径推测编程语言
///
/// # Arguments
///
/// * `file_path` - 文件路径
///
/// # Returns
///
/// 返回推测的编程语言，如果无法推测则返回空字符串
pub fn detect_language(file_path: &String) -> String {
    let path = Path::new(file_path);

    // 获取文件扩展名
    match path.extension() {
        Some(ext) => {
            match ext.to_str().unwrap_or("").to_lowercase().as_str() {
                // 常见编程语言文件扩展名映射
                "py" => "python",
                "js" => "javascript",
                "ts" => "typescript",
                "java" => "java",
                "cpp" | "cc" | "cxx" => "c++",
                "c" => "c",
                "cs" => "c#",
                "go" => "go",
                "rs" => "rust",
                "rb" => "ruby",
                "php" => "php",
                "swift" => "swift",
                "kt" | "kts" => "kotlin",
                "scala" => "scala",
                "html" => "html",
                "css" => "css",
                "sh" => "shell",
                "pl" => "perl",
                "r" => "r",
                "lua" => "lua",
                "sql" => "sql",
                "m" => "objective-c",
                "mm" => "objective-c++",
                "dart" => "dart",
                "f" | "f90" | "f95" => "fortran",
                "jl" => "julia",
                "elm" => "elm",
                "ex" | "exs" => "elixir",
                "erl" => "erlang",
                "hs" => "haskell",
                "vue" => "vue",
                "tsx" => "react",
                "jsx" => "react",
                "xml" => "xml",
                "yaml" | "yml" => "yaml",
                "json" => "json",
                "md" => "markdown",
                // 如果没有匹配到，返回空字符串
                _ => "",
            }.to_string()
        },
        None => "".to_string()
    }
}
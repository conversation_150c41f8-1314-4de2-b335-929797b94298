use std::path::Path;
use std::collections::HashSet;
use std::env;
use anyhow::{anyhow, Result};
use git2::{Repository, Oid, CredentialType, Cred};
use log::{debug, info, warn, error};
use agent_db::config::runtime_config::AGENT_CONFIG;

/// Get the Git repository URL of the current working directory
pub fn get_repository_url<P: AsRef<Path>>(repo_path: P) -> Result<String> {
    let repo = Repository::open(repo_path)?;
    
    // Try to get the remote origin URL
    let remote = repo.find_remote("origin")
        .map_err(|_| anyhow!("No 'origin' remote found in repository"))?;
    
    let url = remote.url()
        .ok_or_else(|| anyhow!("Remote origin URL is not valid UTF-8"))?;
    
    Ok(url.to_string())
}

/// Get a list of files that differ between a remote commit ID and the local repository state
/// 
/// This function:
/// 1. Gets the repository URL
/// 2. Fetches the remote commit if necessary
/// 3. Compares the remote commit with local HEAD and working directory
/// 4. Returns a list of file paths that have differences
pub fn get_diff_files(commit_id: &str) -> Result<Vec<String>> {
    get_diff_files_from_path(".", commit_id)
}

/// Get a list of files that differ between a remote commit ID and the local repository state
/// from a specific repository path
pub fn get_diff_files_from_path<P: AsRef<Path>>(repo_path: P, commit_id: &str) -> Result<Vec<String>> {
    let repo_path = repo_path.as_ref();
    
    // Open the repository
    let repo = Repository::open(repo_path)
        .map_err(|e| anyhow!("Failed to open Git repository at {:?}: {}", repo_path, e))?;
    
    info!("Opened Git repository at: {:?}", repo_path);
    
    // Get repository URL for logging
    match get_repository_url(repo_path) {
        Ok(url) => info!("Repository URL: {}", url),
        Err(e) => warn!("Could not get repository URL: {}", e),
    }
    
    // Parse the target commit ID
    let target_oid = Oid::from_str(commit_id)
        .map_err(|e| anyhow!("Invalid commit ID '{}': {}", commit_id, e))?;
    
    // Try to find the commit locally first
    let target_commit = match repo.find_commit(target_oid) {
        Ok(commit) => {
            info!("Found target commit {} locally", commit_id);
            commit
        }
        Err(_) => {
            // Commit not found locally, try to fetch from remote
            info!("Commit {} not found locally, attempting to fetch from remote", commit_id);
            fetch_from_remote(&repo)?;
            
            // Try again after fetch
            repo.find_commit(target_oid)
                .map_err(|e| anyhow!("Commit '{}' not found even after fetching: {}", commit_id, e))?
        }
    };
    
    let mut diff_files = HashSet::new();
    
    // Get HEAD commit for comparison
    let head_ref = repo.head()
        .map_err(|e| anyhow!("Failed to get HEAD reference: {}", e))?;
    let head_commit = head_ref.peel_to_commit()
        .map_err(|e| anyhow!("Failed to get HEAD commit: {}", e))?;
    
    info!("Comparing target commit {} with HEAD commit {}", 
          target_commit.id(), head_commit.id());
    
    // Compare target commit with HEAD commit
    let target_tree = target_commit.tree()
        .map_err(|e| anyhow!("Failed to get target commit tree: {}", e))?;
    let head_tree = head_commit.tree()
        .map_err(|e| anyhow!("Failed to get HEAD commit tree: {}", e))?;
    
    let diff = repo.diff_tree_to_tree(Some(&target_tree), Some(&head_tree), None)
        .map_err(|e| anyhow!("Failed to create diff between commits: {}", e))?;
    
    // Collect files from commit diff
    diff.foreach(
        &mut |delta, _progress| {
            if let Some(old_file) = delta.old_file().path() {
                if let Some(path_str) = old_file.to_str() {
                    diff_files.insert(path_str.to_string());
                }
            }
            if let Some(new_file) = delta.new_file().path() {
                if let Some(path_str) = new_file.to_str() {
                    diff_files.insert(path_str.to_string());
                }
            }
            true
        },
        None,
        None,
        None,
    ).map_err(|e| anyhow!("Failed to process diff: {}", e))?;
    
    // Also check for working directory changes (staged and unstaged)
    let workdir_diff = repo.diff_index_to_workdir(None, None)
        .map_err(|e| anyhow!("Failed to create working directory diff: {}", e))?;
    
    workdir_diff.foreach(
        &mut |delta, _progress| {
            if let Some(old_file) = delta.old_file().path() {
                if let Some(path_str) = old_file.to_str() {
                    diff_files.insert(path_str.to_string());
                }
            }
            if let Some(new_file) = delta.new_file().path() {
                if let Some(path_str) = new_file.to_str() {
                    diff_files.insert(path_str.to_string());
                }
            }
            true
        },
        None,
        None,
        None,
    ).map_err(|e| anyhow!("Failed to process working directory diff: {}", e))?;
    
    // Check for staged changes
    let head_tree = head_commit.tree()?;
    let mut index = repo.index()?;
    let index_tree = repo.find_tree(index.write_tree()?)?;
    
    let staged_diff = repo.diff_tree_to_tree(Some(&head_tree), Some(&index_tree), None)
        .map_err(|e| anyhow!("Failed to create staged diff: {}", e))?;
    
    staged_diff.foreach(
        &mut |delta, _progress| {
            if let Some(old_file) = delta.old_file().path() {
                if let Some(path_str) = old_file.to_str() {
                    diff_files.insert(path_str.to_string());
                }
            }
            if let Some(new_file) = delta.new_file().path() {
                if let Some(path_str) = new_file.to_str() {
                    diff_files.insert(path_str.to_string());
                }
            }
            true
        },
        None,
        None,
        None,
    ).map_err(|e| anyhow!("Failed to process staged diff: {}", e))?;

    // Check for untracked files (new files not in git)
    let mut status_options = git2::StatusOptions::new();
    status_options.include_untracked(true);
    status_options.include_ignored(false);

    let statuses = repo.statuses(Some(&mut status_options))
        .map_err(|e| anyhow!("Failed to get repository status: {}", e))?;

    for entry in statuses.iter() {
        let status = entry.status();
        let file_path = entry.path().unwrap_or("");

        // Include untracked files and any other changes
        if status.is_wt_new() || status.is_index_new() ||
           status.is_wt_modified() || status.is_index_modified() ||
           status.is_wt_deleted() || status.is_index_deleted() ||
           status.is_wt_renamed() || status.is_index_renamed() {
            diff_files.insert(file_path.to_string());
            debug!("Found status change: {} - {:?}", file_path, status);
        }
    }

    // Filter out directories, only keep files
    let repo_workdir = repo.workdir()
        .ok_or_else(|| anyhow!("Repository has no working directory"))?;

    let mut result: Vec<String> = diff_files.into_iter()
        .filter(|path| {
            let full_path = repo_workdir.join(path);
            // Check if it's a file (not a directory)
            let is_file = full_path.is_file();
            if !is_file {
                debug!("Filtering out directory: {}", path);
            }
            is_file
        })
        .collect();

    result.sort();

    info!("Found {} files with differences (directories filtered out)", result.len());
    debug!("Differing files: {:?}", result);

    Ok(result)
}

/// Attempt to fetch from the remote repository using system git command as fallback
fn fetch_from_remote(repo: &Repository) -> Result<()> {
    let remote_url = repo.find_remote("origin")
        .map_err(|e| anyhow!("Failed to find 'origin' remote: {}", e))?
        .url()
        .ok_or_else(|| anyhow!("Remote origin URL is not valid UTF-8"))?
        .to_string();

    info!("Fetching from remote origin: {}", remote_url);

    // First try using git2 library
    match try_git2_fetch(repo) {
        Ok(_) => {
            println!("✅ Successfully fetched using git2 library");
            return Ok(());
        }
        Err(e) => {
            println!("❌ git2 fetch failed: {}", e);
            println!("🔄 Falling back to system git command...");
        }
    }

    // Fallback to system git command
    try_system_git_fetch(repo)
}

/// Try fetching using git2 library with comprehensive authentication
fn try_git2_fetch(repo: &Repository) -> Result<()> {
    let mut remote = repo.find_remote("origin")?;
    let remote_url = remote.url().unwrap_or("").to_string();

    let is_ssh = remote_url.starts_with("git@") || remote_url.starts_with("ssh://");
    let is_https = remote_url.starts_with("https://");

    let mut fetch_options = git2::FetchOptions::new();
    let mut callbacks = git2::RemoteCallbacks::new();

    callbacks.credentials(|url, username_from_url, allowed_types| {
        println!("🔐 git2: Authentication requested for URL: {}", url);

        // Try credential helper first
        if allowed_types.is_user_pass_plaintext() {
            if let Ok(config) = git2::Config::open_default() {
                if let Ok(cred) = git2::Cred::credential_helper(&config, url, username_from_url) {
                    println!("✅ git2: Got credentials from git credential helper");
                    return Ok(cred);
                }
            }
        }

        // Try SSH key for SSH URLs
        if is_ssh && allowed_types.is_ssh_key() {
            let username = username_from_url.unwrap_or("git");
            if let Ok(cred) = git2::Cred::ssh_key_from_agent(username) {
                println!("✅ git2: Got SSH key from agent");
                return Ok(cred);
            }
        }

        // Try environment variables
        if allowed_types.is_user_pass_plaintext() {
            let env_username = std::env::var("GIT_USERNAME").unwrap_or_default();
            let env_password = std::env::var("GIT_PASSWORD").unwrap_or_default();

            if !env_username.is_empty() || !env_password.is_empty() {
                println!("✅ git2: Using environment variables");
                return git2::Cred::userpass_plaintext(&env_username, &env_password);
            }
        }

        Err(git2::Error::from_str("No suitable authentication method"))
    });

    fetch_options.remote_callbacks(callbacks);

    remote.fetch(&[] as &[&str], Some(&mut fetch_options), None)
        .map_err(|e| anyhow!("git2 fetch failed: {}", e))
}

/// Try fetching using system git command (uses system's configured authentication)
fn try_system_git_fetch(repo: &Repository) -> Result<()> {
    let repo_path = repo.path().parent()
        .ok_or_else(|| anyhow!("Could not get repository working directory"))?;

    println!("🔧 Using system git command in directory: {:?}", repo_path);

    // Use system git command to fetch with non-interactive mode and timeout
    let output = if cfg!(target_os = "windows") {
        // Windows: 使用PowerShell的timeout功能
        std::process::Command::new("powershell")
            .arg("-Command")
            .arg(&format!("& {{ $job = Start-Job -ScriptBlock {{ git fetch origin --quiet }}; if (Wait-Job $job -Timeout {}) {{ Receive-Job $job }} else {{ Remove-Job $job -Force; throw 'Timeout' }} }}", AGENT_CONFIG.git_fetch_timeout_secs))
            .current_dir(repo_path)
            .env("GIT_TERMINAL_PROMPT", "0")  // 禁用终端提示
            .env("GIT_ASKPASS", "echo")       // 设置空的askpass程序
            .env("SSH_ASKPASS", "echo")       // 设置空的SSH askpass程序
            .stdin(std::process::Stdio::null()) // 禁用stdin输入
            .output()
            .map_err(|e| anyhow!("Failed to execute git command: {}", e))?
    } else {
        // Unix/Linux/macOS: 使用timeout命令
        std::process::Command::new("timeout")
            .arg(AGENT_CONFIG.git_fetch_timeout_secs.to_string())  // 使用配置的超时时间
            .arg("git")
            .arg("fetch")
            .arg("origin")
            .arg("--quiet")  // 减少输出
            .current_dir(repo_path)
            .env("GIT_TERMINAL_PROMPT", "0")  // 禁用终端提示
            .env("GIT_ASKPASS", "echo")       // 设置空的askpass程序
            .env("SSH_ASKPASS", "echo")       // 设置空的SSH askpass程序
            .stdin(std::process::Stdio::null()) // 禁用stdin输入
            .output()
            .map_err(|e| anyhow!("Failed to execute git command: {}", e))?
    };

    if output.status.success() {
        println!("✅ Successfully fetched using system git command");
        Ok(())
    } else {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);

        println!("❌ System git fetch failed:");
        println!("stdout: {}", stdout);
        println!("stderr: {}", stderr);

        // Check if it's a timeout error (exit code 124 from timeout command)
        if let Some(exit_code) = output.status.code() {
            if exit_code == 124 {
                warn!("Git fetch timeout ({}s), will treat all files as diff files. Error: {}", AGENT_CONFIG.git_fetch_timeout_secs, stderr);
                return Err(anyhow!("Git fetch timeout: {}", stderr));
            }
        }

        // Check if it's an authentication error
        if stderr.contains("Authentication") || stderr.contains("authentication") ||
           stderr.contains("Permission denied") || stderr.contains("access denied") ||
           stderr.contains("Username for") || stderr.contains("Password for") {
            // 对于认证错误，直接返回错误，不要求用户输入
            warn!("Git authentication failed, will treat all files as diff files. Error: {}", stderr);
            Err(anyhow!("Git authentication failed: {}", stderr))
        } else {
            warn!("Git fetch failed, will treat all files as diff files. Error: {}", stderr);
            Err(anyhow!("Git fetch failed: {}", stderr))
        }
    }
}

/// Try different authentication methods based on the context
fn try_authentication(
    url: &str,
    username_from_url: Option<&str>,
    allowed_types: CredentialType,
    is_ssh: bool,
    is_https: bool,
    attempt: i32,
) -> Result<Cred, git2::Error> {
    debug!("Trying authentication for attempt #{}", attempt);

    // For SSH URLs
    if is_ssh {
        return try_ssh_authentication(username_from_url, allowed_types, attempt);
    }

    // For HTTPS URLs
    if is_https {
        return try_https_authentication(url, username_from_url, allowed_types, attempt);
    }

    // Fallback for other URL types
    try_fallback_authentication(username_from_url, allowed_types)
}

/// Try SSH-specific authentication methods
fn try_ssh_authentication(
    username_from_url: Option<&str>,
    allowed_types: CredentialType,
    attempt: i32,
) -> Result<Cred, git2::Error> {
    let username = username_from_url.unwrap_or("git");

    match attempt {
        1 => {
            // First attempt: SSH agent
            if allowed_types.is_ssh_key() {
                debug!("Attempting SSH agent authentication for user: {}", username);
                return Cred::ssh_key_from_agent(username);
            }
        }
        2 => {
            // Second attempt: SSH key from default locations
            if allowed_types.is_ssh_key() {
                debug!("Attempting SSH key from default locations for user: {}", username);
                return try_ssh_key_from_files(username);
            }
        }
        3 => {
            // Third attempt: SSH key with environment variable paths
            if allowed_types.is_ssh_key() {
                debug!("Attempting SSH key from environment variables for user: {}", username);
                return try_ssh_key_from_env(username);
            }
        }
        _ => {
            // Additional attempts: try username-only if allowed
            if allowed_types.is_username() {
                debug!("Attempting username-only authentication for user: {}", username);
                return Cred::username(username);
            }
        }
    }

    Err(git2::Error::from_str("No suitable SSH authentication method available"))
}

/// Try HTTPS-specific authentication methods
fn try_https_authentication(
    url: &str,
    username_from_url: Option<&str>,
    allowed_types: CredentialType,
    attempt: i32,
) -> Result<Cred, git2::Error> {
    match attempt {
        1 => {
            // First attempt: Environment variables
            if allowed_types.is_user_pass_plaintext() {
                debug!("Attempting HTTPS authentication from environment variables");
                if let Ok(cred) = try_env_credentials() {
                    return Ok(cred);
                }

                // If no environment variables, try anonymous access for public repos
                debug!("No environment credentials found, trying anonymous access");
                if let Ok(cred) = Cred::userpass_plaintext("", "") {
                    return Ok(cred);
                }
            }
        }
        2 => {
            // Second attempt: Git credential helper
            if allowed_types.is_user_pass_plaintext() {
                debug!("Attempting HTTPS authentication via credential helper");
                if let Ok(cred) = try_credential_helper(url, username_from_url) {
                    return Ok(cred);
                }
            }
        }
        3 => {
            // Third attempt: Default credentials (if configured)
            if allowed_types.is_default() {
                debug!("Attempting default credential authentication");
                return Cred::default();
            }
        }
        4 => {
            // Fourth attempt: Try anonymous access for public repositories
            if allowed_types.is_user_pass_plaintext() {
                debug!("Attempting anonymous HTTPS access");
                // Try with empty credentials for public repositories
                return Cred::userpass_plaintext("", "");
            }
        }
        5 => {
            // Fifth attempt: Try with git as username for some Git services
            if allowed_types.is_user_pass_plaintext() {
                debug!("Attempting HTTPS with 'git' username");
                return Cred::userpass_plaintext("git", "");
            }
        }
        _ => {
            // Additional attempts: try username-only if allowed
            if allowed_types.is_username() {
                if let Some(username) = username_from_url {
                    debug!("Attempting username-only authentication for user: {}", username);
                    return Cred::username(username);
                }
            }

            // Last resort: try to extract username from URL and use empty password
            if allowed_types.is_user_pass_plaintext() {
                if let Some(username) = username_from_url {
                    debug!("Last resort: trying username from URL with empty password");
                    return Cred::userpass_plaintext(username, "");
                }
            }
        }
    }

    Err(git2::Error::from_str("No suitable HTTPS authentication method available"))
}

/// Try fallback authentication methods for other URL types
fn try_fallback_authentication(
    username_from_url: Option<&str>,
    allowed_types: CredentialType,
) -> Result<Cred, git2::Error> {
    // Try default authentication first
    if allowed_types.is_default() {
        debug!("Attempting default authentication");
        if let Ok(cred) = Cred::default() {
            return Ok(cred);
        }
    }

    // Try username-only if available
    if allowed_types.is_username() {
        if let Some(username) = username_from_url {
            debug!("Attempting username-only authentication for user: {}", username);
            return Cred::username(username);
        }
    }

    Err(git2::Error::from_str("No suitable authentication method available"))
}

/// Try SSH key authentication from default file locations
fn try_ssh_key_from_files(username: &str) -> Result<Cred, git2::Error> {
    // Common SSH key locations
    let home_dir = env::var("HOME").or_else(|_| env::var("USERPROFILE"))
        .map_err(|_| git2::Error::from_str("Could not determine home directory"))?;

    let key_paths = [
        format!("{}/.ssh/id_rsa", home_dir),
        format!("{}/.ssh/id_ed25519", home_dir),
        format!("{}/.ssh/id_ecdsa", home_dir),
        format!("{}/.ssh/id_dsa", home_dir),
    ];

    for private_key_path in &key_paths {
        let public_key_path = format!("{}.pub", private_key_path);

        // Check if both private and public key files exist
        if Path::new(private_key_path).exists() {
            debug!("Trying SSH key: {}", private_key_path);

            let public_key = if Path::new(&public_key_path).exists() {
                Some(Path::new(&public_key_path))
            } else {
                None
            };

            // Try without passphrase first, then with empty passphrase
            match Cred::ssh_key(username, public_key, Path::new(private_key_path), None) {
                Ok(cred) => return Ok(cred),
                Err(_) => {
                    // Try with empty passphrase
                    match Cred::ssh_key(username, public_key, Path::new(private_key_path), Some("")) {
                        Ok(cred) => return Ok(cred),
                        Err(e) => debug!("SSH key {} failed: {}", private_key_path, e),
                    }
                }
            }
        }
    }

    Err(git2::Error::from_str("No usable SSH key found in default locations"))
}

/// Try SSH key authentication from environment variables
fn try_ssh_key_from_env(username: &str) -> Result<Cred, git2::Error> {
    // Check for SSH key paths in environment variables
    if let Ok(private_key_path) = env::var("GIT_SSH_KEY") {
        debug!("Trying SSH key from GIT_SSH_KEY: {}", private_key_path);

        let public_key_path = env::var("GIT_SSH_KEY_PUB").ok();
        let passphrase = env::var("GIT_SSH_PASSPHRASE").ok();

        let public_key = public_key_path.as_ref().map(|p| Path::new(p));

        return Cred::ssh_key(
            username,
            public_key,
            Path::new(&private_key_path),
            passphrase.as_deref(),
        );
    }

    // Check for SSH key content in environment variables
    if let Ok(private_key_content) = env::var("GIT_SSH_KEY_CONTENT") {
        debug!("Trying SSH key from GIT_SSH_KEY_CONTENT");

        let public_key_content = env::var("GIT_SSH_KEY_PUB_CONTENT").ok();
        let passphrase = env::var("GIT_SSH_PASSPHRASE").ok();

        return Cred::ssh_key_from_memory(
            username,
            public_key_content.as_deref(),
            &private_key_content,
            passphrase.as_deref(),
        );
    }

    Err(git2::Error::from_str("No SSH key found in environment variables"))
}

/// Try to get credentials from environment variables
fn try_env_credentials() -> Result<Cred, git2::Error> {
    // Try common environment variable combinations
    let username = env::var("GIT_USERNAME")
        .or_else(|_| env::var("GIT_USER"))
        .or_else(|_| env::var("GITHUB_USERNAME"))
        .or_else(|_| env::var("GITLAB_USERNAME"));

    let password = env::var("GIT_PASSWORD")
        .or_else(|_| env::var("GIT_TOKEN"))
        .or_else(|_| env::var("GITHUB_TOKEN"))
        .or_else(|_| env::var("GITLAB_TOKEN"));

    match (username, password) {
        (Ok(user), Ok(pass)) => {
            debug!("Using credentials from environment variables for user: {}", user);
            Cred::userpass_plaintext(&user, &pass)
        }
        _ => Err(git2::Error::from_str("No credentials found in environment variables"))
    }
}

/// Try to use git credential helper
fn try_credential_helper(url: &str, username_from_url: Option<&str>) -> Result<Cred, git2::Error> {
    // For public repositories or when no specific credentials are needed,
    // we can try some common approaches

    // Try to extract hostname and see if we can provide reasonable defaults
    if url.contains("github.com") || url.contains("gitlab.com") || url.contains("alipay.com") {
        debug!("Attempting credential helper for URL: {}", url);

        // For public repositories, try anonymous access first
        if let Ok(cred) = Cred::userpass_plaintext("", "") {
            debug!("Trying anonymous access for public repository");
            return Ok(cred);
        }

        // If there's a username in the URL, try with empty password
        if let Some(username) = username_from_url {
            debug!("Trying username '{}' with empty password", username);
            return Cred::userpass_plaintext(username, "");
        }

        // Try common git username
        debug!("Trying 'git' username with empty password");
        return Cred::userpass_plaintext("git", "");
    }

    Err(git2::Error::from_str("No suitable credential helper method available"))
}

/// Format a comprehensive error message based on the authentication failure
fn format_authentication_error(
    error: &git2::Error,
    remote_url: &str,
    is_ssh: bool,
    is_https: bool,
) -> String {
    let base_error = format!("Failed to fetch from remote '{}': {}", remote_url, error);

    let mut suggestions = Vec::new();

    if is_ssh {
        suggestions.push("SSH Authentication Troubleshooting:".to_string());
        suggestions.push("1. Ensure your SSH agent is running: `ssh-add -l`".to_string());
        suggestions.push("2. Add your SSH key to the agent: `ssh-add ~/.ssh/id_rsa`".to_string());
        suggestions.push("3. Test SSH connection: `ssh -T git@hostname`".to_string());
        suggestions.push("4. Set SSH key path: export GIT_SSH_KEY=/path/to/key".to_string());
        suggestions.push("5. Check SSH key permissions (should be 600)".to_string());
    } else if is_https {
        suggestions.push("HTTPS Authentication Troubleshooting:".to_string());
        suggestions.push("1. Set credentials: export GIT_USERNAME=your_username".to_string());
        suggestions.push("2. Set token/password: export GIT_PASSWORD=your_token".to_string());
        suggestions.push("3. Use personal access token instead of password".to_string());
        suggestions.push("4. Configure git credential helper: `git config credential.helper store`".to_string());
        suggestions.push("5. Check if 2FA is enabled (requires token, not password)".to_string());
    } else {
        suggestions.push("General Authentication Troubleshooting:".to_string());
        suggestions.push("1. Verify the remote URL is correct".to_string());
        suggestions.push("2. Check network connectivity".to_string());
        suggestions.push("3. Ensure you have access to the repository".to_string());
    }

    format!("{}\n\n{}", base_error, suggestions.join("\n"))
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;
    
    #[test]
    fn test_get_repository_url() {
        // This test will only work if run from within a Git repository
        if let Ok(current_dir) = env::current_dir() {
            match get_repository_url(&current_dir) {
                Ok(url) => {
                    println!("Repository URL: {}", url);
                    assert!(!url.is_empty());
                }
                Err(e) => {
                    println!("Not in a Git repository or no origin remote: {}", e);
                    // This is expected if not in a Git repo, so we don't fail the test
                }
            }
        }
    }
    
    #[test]
    fn test_get_diff_files_invalid_commit() {
        // Test with an invalid commit ID
        let result = get_diff_files("invalid_commit_id");
        assert!(result.is_err());
    }
    #[test]
    fn test_get_diff_files_valid_commit() {

        println!("🧪 Testing Git Utils");
        println!("{}", "=".repeat(50));

        // Test 1: Get repository URL
        println!("\n📡 Testing get_repository_url...");
        match get_repository_url(".") {
            Ok(url) => {
                println!("✅ Repository URL: {}", url);
            }
            Err(e) => {
                println!("❌ Failed to get repository URL: {}", e);
            }
        }

        // Test 2: Get diff files with a test commit ID
        println!("\n📊 Testing get_diff_files...");
let project_url = "/Users/<USER>/RustroverProjects/codefuse_local_agent/agent_common_servic";
        // Use the test commit ID from the requirements
        let test_commit_id = "291a05e718dd1b78826db6ed3efe75a85c33d380";

        match get_diff_files(test_commit_id) {
            Ok(files) => {
                println!("✅ Found {} files with differences:", files.len());
                for (i, file) in files.iter().enumerate() {
                    if i < 10 {  // Show first 10 files
                        println!("  - {}", file);
                    } else if i == 10 {
                        println!("  ... and {} more files", files.len() - 10);
                        break;
                    }
                }
            }
            Err(e) => {
                let error_msg = e.to_string();
                if error_msg.contains("authentication") || error_msg.contains("Auth") {
                    println!("🔐 Authentication required (expected for private repositories)");
                    println!("✅ Authentication system is working correctly!");
                    println!("📝 Error details: {}", e);
                    println!("\n💡 To test with real credentials, set:");
                    println!("   export GIT_USERNAME=your_username");
                    println!("   export GIT_PASSWORD=your_token");
                } else {
                    println!("❌ Failed to get diff files: {}", e);
                    println!("This might be expected if:");
                    println!("  - The commit ID doesn't exist in this repository");
                    println!("  - Network issues prevent fetching");
                }
            }
        }

        // Test 3: Test with an invalid commit ID
        println!("\n🔍 Testing with invalid commit ID...");
        match get_diff_files("invalid_commit_id_12345") {
            Ok(_) => {
                println!("❌ Unexpectedly succeeded with invalid commit ID");
            }
            Err(e) => {
                println!("✅ Correctly failed with invalid commit ID: {}", e);
            }
        }

        println!("\n🎉 Git utils testing completed!");


    }
}

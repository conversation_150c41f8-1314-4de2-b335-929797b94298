use crate::model::code_complete_model::ContextAwareInfo;
use crate::tools::common_tools::CacheRelatedModuleEnum;
use std::cmp::Ordering;

pub struct CacheRelatedModule {
    pub info: ContextAwareInfo,
    pub module_type: CacheRelatedModuleEnum,
}

impl CacheRelatedModule {
    pub fn new(info: ContextAwareInfo, module_type: CacheRelatedModuleEnum) -> CacheRelatedModule {
        CacheRelatedModule {
            info,
            module_type,
        }
    }

    pub fn from_tuple(result: (Option<CacheRelatedModuleEnum>, Option<ContextAwareInfo>)) -> Option<CacheRelatedModule> {
        match result {
            (Some(cache_module_type), Some(context)) => {
                Some(CacheRelatedModule {
                    info: context,
                    module_type: cache_module_type,
                })
            }
            _ => None
        }
    }
}
impl PartialEq for CacheRelatedModule {
    fn eq(&self, other: &Self) -> bool {
        self.info.score == other.info.score
    }
}

// Since Eq is just a marker trait that requires PartialEq, it can be empty
impl Eq for CacheRelatedModule {}

impl Ord for CacheRelatedModule {
    fn cmp(&self, other: &Self) -> Ordering {
        // Compare f64 directly, maintaining higher scores at the top of the heap
        self.info.score.partial_cmp(&other.info.score).unwrap_or(Ordering::Equal)
    }
}

impl PartialOrd for CacheRelatedModule {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

use std::collections::HashSet;

// 定义 Jaccard 相似度计算函数
pub fn jaccard_similarity(set1: &HashSet<u32>, set2: &HashSet<u32>) -> f64 {
    let intersection_size = set1.intersection(set2).count() as f64;
    let union_size = (set1.len() + set2.len() - intersection_size as usize) as f64;
    intersection_size / union_size
}

fn shingles(s: &str) -> HashSet<String> {
    let chars: Vec<_> = s.chars().collect();
    chars.windows(2).map(|w| w.iter().cloned().collect()).collect()
}

/// https://codereview.stackexchange.com/questions/109461/jaccard-distance-between-strings-in-rust
pub fn jaccard_distance(s1: &str, s2: &str) -> f64 {
    let s1_shingles = shingles(s1);
    let s2_shingles = shingles(s2);
    let inter = s1_shingles.intersection(&s2_shingles).count();
    let union = s1_shingles.union(&s2_shingles).count();
    (inter as f64) / (union as f64)
}

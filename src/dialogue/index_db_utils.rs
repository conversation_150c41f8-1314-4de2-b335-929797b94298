use crate::dialogue::codefuse_index_repository::{CHUNK_CLIENT, CHUNK_FIELDS, FILE_CLIET, FILE_FIELDS, METHOD_CLIENT, METHOD_FIELDS};
use crate::dialogue::file_index_operator::update_chunk_vector_async;
use crate::dialogue::misc_util::{accelerate_by_time, autonomous_sleep_no_bolck, autonomous_sleep_with_bolck, doc_to_file, get_parallel_num, get_project_chunk_vector_prefix};
use crate::dialogue::repo_index_operator::{is_need_interrupt_repo_build, VecWrapper};
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::dal::remote_client::{ IndexTypeEnum, QueryInProject};
use agent_db::dal::vector_client::{VECTOR_CLIENT, VECTOR_FIELD_PROJECT_URL};
use agent_db::domain::code_chat_domain::CodefuseFile;
use futures::{stream, StreamExt};
use log::{error, info};
use rayon::prelude::*;
use std::collections::HashSet;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tantivy::doc;
use tokio::sync::Mutex;
use tokio::task::block_in_place;

pub const PAGE_SIZE: usize = 50;        //分查询的时候每页的数据



///仓库级别更新chunk vector
pub async fn update_chunk_vector_by_project(project_url: &str, branch:&str, token: &str, product_type: &str, mill_sleep_time: usize, task_type: u8) {
    let mut query_in_project = QueryInProject::default();
    query_in_project.project_url = project_url.to_string();

    let chunk_no_content_vector_count_rst = CHUNK_CLIENT.query_no_content_vector_count_in_project(project_url).await;
    if let Ok(method_no_content_vector_count) = chunk_no_content_vector_count_rst {
        let remainder = method_no_content_vector_count % PAGE_SIZE;
        let mut page_size = method_no_content_vector_count / PAGE_SIZE;
        if remainder > 0 {
            page_size = page_size + 1;
        }
        let mut current_page = 1;

        query_in_project.page_size = PAGE_SIZE;
        while current_page <= page_size {
            query_in_project.page_number = 1;
            let query_data_result_rst = CHUNK_CLIENT.query_data_no_content_vector_in_project(&query_in_project).await;
            let mut codefuse_container = Vec::new();
            match query_data_result_rst {
                Ok(query_data_result) => {
                    for (score, doc) in query_data_result {
                        let condefuse_chunk = CHUNK_FIELDS.convert_to_chunk(doc);
                        codefuse_container.push(condefuse_chunk);
                    }
                    //报存请求失败的数据， todo
                    //let mut vec_wrapper = VecWrapper::new();
                    let max_concurrency = get_parallel_num(mill_sleep_time);
                    stream::iter(codefuse_container.clone())
                        .chunks(crate::utils::parallel_file_scan::MAX_VECTOR)
                        .for_each_concurrent(max_concurrency, |chunk_arr| {
                            async move {
                                let need_interrupt_build = is_need_interrupt_repo_build(&project_url.clone().to_string(), &branch.clone().to_string(), task_type).await;
                                if need_interrupt_build {
                                    info!("update_chunk_vector_by_project, current project and url has changed, end the process");
                                    return;
                                }
                                let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                                let mut chunk_mut = chunk_arr.clone();
                                update_chunk_vector_async(&mut chunk_mut, &token.to_string(), &product_type.to_string()).await;
                                let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                                info!("update_chunk_vector_by_project by stream {} ms", end_time - start_time);
                                autonomous_sleep_with_bolck(mill_sleep_time);
                            }
                        }).await;

                    //报存请求失败的数据， todo
                    CHUNK_CLIENT.commit();
                }
                Err(e) => {}
            }
            current_page = current_page + 1;
            autonomous_sleep_no_bolck(mill_sleep_time).await;
            ///检查当前工程是否发生变化,如果发生变化就返回
            let need_interrupt_build = is_need_interrupt_repo_build(&project_url.clone().to_string(), &branch.clone().to_string(), task_type).await;
            if need_interrupt_build {
                info!("update_chunk_vector_by_project, current project and url has changed, end the process");
                return;
            }
        }
    }
}


/// 获取仓库的所有file， 用分页查找， 并抹去了content
pub async fn get_all_codefile_in_project(project_url: &str, mill_sleep_time: usize) -> Option<Vec<CodefuseFile>> {
    let mut file_container = Vec::<CodefuseFile>::new();

    let mut query_in_project = QueryInProject::default();
    query_in_project.project_url = project_url.to_string();

    let all_count = FILE_CLIET.query_count_in_project(query_in_project.clone()).await;
    if let Ok(count) = all_count {
        let remainder = count % PAGE_SIZE;
        let mut page_size = count / PAGE_SIZE;
        if remainder > 0 {
            page_size = page_size + 1;
        }

        let mut current_page = 1;
        while current_page <= page_size {
            query_in_project.page_size = PAGE_SIZE;
            query_in_project.page_number = current_page;
            let query_result_rst = FILE_CLIET.query_data_by_page(query_in_project.clone()).await;
            if let Ok(query_result) = query_result_rst {
                let mut sub_file_container = Vec::<CodefuseFile>::new();
                for (score, doc) in query_result {
                    let mut codefuse_file = FILE_FIELDS.convert_to_file(doc);
                    //为了内存，抹掉content，
                    codefuse_file.content = "".to_string();
                    sub_file_container.push(codefuse_file);
                }
                file_container.extend(sub_file_container);
            }
            current_page = current_page + 1;
            autonomous_sleep_no_bolck(mill_sleep_time).await
        }
    }
    Some(file_container)
}

///删除一个project
pub async fn delete_project(project_url: &str) {
    info!("invoke delete project {}", project_url);
    //删除chunk
    let _ = delete_all_chunk_by_project(project_url, 0).await;
    //删除chunk vector
    let _ = delete_all_chunk_vector_by_project(project_url).await;
    let _ = CHUNK_CLIENT.commit();
}

///按仓库级别删除chunk
pub async fn delete_all_chunk_by_project(project_url: &str, mill_sleep_time: usize){
    let mut query_in_project = QueryInProject::default();
    query_in_project.project_url = project_url.to_string();
    let all_chunk_count_rst = CHUNK_CLIENT.query_count_in_project(query_in_project.clone()).await;
    if let Ok(all_chunk_count) = all_chunk_count_rst {
        let remainder = all_chunk_count % PAGE_SIZE;
        let mut page_size = all_chunk_count / PAGE_SIZE;
        if remainder > 0 {
            page_size = page_size + 1;
        }
        let mut current_page = 1;
        while current_page <= page_size {
            query_in_project.page_size = PAGE_SIZE;
            query_in_project.page_number = 1;
            let chunk_page_data = CHUNK_CLIENT.query_data_by_page(query_in_project.clone()).await.unwrap();
            let mut chunk_container = Vec::new();
            for (score, doc) in chunk_page_data {
                let mut codefuse_file = CHUNK_FIELDS.convert_to_chunk(doc);
                chunk_container.push(codefuse_file);
            }
            CHUNK_CLIENT.delete_batch(chunk_container.as_slice()).unwrap();
            if CHUNK_CLIENT.commit().is_err(){
                error!("Failed to commit chunk");
            }
            current_page = current_page + 1;
            autonomous_sleep_no_bolck(mill_sleep_time).await
        }
    }
}



///按仓库级别删除chunk的vector
pub async fn delete_all_chunk_vector_by_project(project_url: &str) {
    let _ = VECTOR_CLIENT.delete_with_key_and_value(VECTOR_FIELD_PROJECT_URL,project_url).await;
    info!("invoke manual_cleanup in delete_all_chunk_vector_by_project");
    let _ = VECTOR_CLIENT.manual_cleanup().await;
}

///把has_content_vector统一恢复到0， 用作数据状态的一致
/// 应用场景是，kv库偶现初始化失败的问题， 此时会把kv库的缓存目录清空，再重新初始化kv库，此时kv库里所有数据都会丢失，
/// 所有这里需要把对应的文档数据也进行重置
pub fn recover_chunk_has_content_vector_in_db() {
    let total_count = CHUNK_CLIENT.get_has_content_vector_data_count().unwrap();
    let mut query_in_project = QueryInProject::default();
    query_in_project.page_size = PAGE_SIZE;
    let remainder = total_count % PAGE_SIZE;
    let mut page_number = total_count / PAGE_SIZE;
    if remainder > 0 {
        page_number = page_number + 1;
    }
    let mut current_page_number = 1;
    while current_page_number <= page_number {
        query_in_project.page_number = 1;
        let querry_result = CHUNK_CLIENT.get_has_content_vector_data_by_page(query_in_project.clone()).unwrap();
        let mut chunks_data = Vec::new();
        for (score, doc) in querry_result {
            let mut codefuse_file = CHUNK_FIELDS.convert_to_chunk(doc);
            codefuse_file.has_content_vector = 0;
            chunks_data.push(codefuse_file);
            CHUNK_CLIENT.update_batch(chunks_data.as_slice()).unwrap();
        }
        current_page_number = current_page_number + 1;
    }
    if CHUNK_CLIENT.commit().is_err(){
        error!("Failed to commit chunk");
    }

}
///同上，这里是恢复method的has_content_vector=0
pub fn recover_method_has_content_vector_in_db() {
    let total_count = METHOD_CLIENT.get_has_content_vector_data_count().unwrap();
    let mut query_in_project = QueryInProject::default();

    query_in_project.page_size = PAGE_SIZE;
    let remainder = total_count % PAGE_SIZE;
    let mut page_number = total_count / PAGE_SIZE;
    if remainder > 0 {
        page_number = page_number + 1;
    }
    let mut current_page_number = 1;
    while current_page_number <= page_number {
        query_in_project.page_number = 1;
        let querry_result = METHOD_CLIENT.get_has_content_vector_data_by_page(query_in_project.clone()).unwrap();
        let mut methods_data = Vec::new();
        for (score, doc) in querry_result {
            let mut codefuse_file = METHOD_FIELDS.convert_to_method(doc);
            codefuse_file.has_content_vector = 0;
            methods_data.push(codefuse_file);
            METHOD_CLIENT.update_batch(methods_data.as_slice()).unwrap();
        }
        current_page_number = current_page_number + 1;
    }
    if METHOD_CLIENT.commit().is_err(){
        error!("Failed to commit method");
    }
}

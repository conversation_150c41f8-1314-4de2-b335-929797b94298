use agent_db::dal::remote_client::IndexTypeEnum;
use agent_db::domain::code_chat_domain::{CodefuseChunk, CodefuseFile, CodefuseMethod};
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use tantivy::schema::{Field, Schema, Value};
use tantivy::{doc, Document, TantivyDocument};

///建立文件chunk|method索引时的文件扫描记录
/// key是 F_${file_url}
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct ScanFileToIndexChunkRecord {
    //文件路径
    pub file_url: String,
    //文件后缀
    pub file_name_suffix: String,
    //文件切分数量
    pub total_chunk_num: usize,
    //branch
    pub branch: String,
}

///IndexFileChunkRecord
impl Default for ScanFileToIndexChunkRecord {
    fn default() -> Self {
        ScanFileToIndexChunkRecord {
            file_url: String::new(),
            file_name_suffix: String::new(),
            total_chunk_num: 0,
            branch: String::new(),
        }
    }
}

///检索数据结果
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryChatRelatedData {
    //文件索引库返回结果
    pub file_content_data: Option<Vec<QueryChatItemResult<CodefuseFile>>>,
    //函数索引库返回结果
    pub method_content_data: Option<Vec<QueryChatItemResult<CodefuseMethod>>>,
    //chunk索引库返回结果
    pub chunk_content_data: Option<Vec<QueryChatItemResult<CodefuseChunk>>>,
    //chunk向量检索结果
    pub chunk_vector_data: Option<Vec<QueryChatItemResult<CodefuseChunk>>>,
    pub search_time:Option<u128>
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryChatRelatedResponse {
    //查询的关键词集合
    pub query_key_word_set: HashSet<String>,
    //检索结果
    pub query_chat_related_data: Option<QueryChatRelatedData>,
    //查询的用户问题
    pub question_set: HashSet<String>,
}

///对话-检索数据结果
/// chunk 扩展 method+file
/// method 扩展 file
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryChatItemResult<T> {
    pub search_result: QueryResultItem<T>,
    pub extend_method_result: Option<Vec<CodefuseMethod>>,
    pub extend_file_result: Option<CodefuseFile>,
}

///检索对话索引的内部请求
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryIndexRequest {
    //关键词检索
    pub key_word_set: HashSet<String>,
    //每个索引库的topN
    pub top: usize,
    //检索类型
    pub index_type_set: HashSet<IndexTypeEnum>,
    //用户问题（中/英)
    pub question_set: HashSet<String>,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryResultItem<T> {
    pub score: f32,
    pub data: T,
}

pub struct CodefuseChunkFields {
    field_id: Field,
    field_file_url: Field,
    field_index: Field,
    field_content: Field,
    field_start_line: Field,
    field_end_line: Field,
    field_summary: Field,
    field_project_url: Field,
    field_branch: Field,
    field_hash: Field,
    field_feature: Field,
    field_summary_keyword: Field,
    field_has_summary: Field,
    field_has_content_vector: Field,
    field_has_summary_vector: Field,
}

impl CodefuseChunkFields {
    pub(crate) fn new(schema: &Schema) -> CodefuseChunkFields {
        Self {
            field_id: schema.get_field("id").unwrap(),
            field_file_url: schema.get_field("file_url").unwrap(),
            field_index: schema.get_field("index").unwrap(),
            field_content: schema.get_field("content").unwrap(),
            field_start_line: schema.get_field("start_line").unwrap(),
            field_end_line: schema.get_field("end_line").unwrap(),
            field_summary: schema.get_field("summary").unwrap(),
            field_project_url: schema.get_field("project_url").unwrap(),
            field_branch: schema.get_field("branch").unwrap(),
            field_hash: schema.get_field("hash").unwrap(),
            field_feature: schema.get_field("feature").unwrap(),
            field_summary_keyword: schema.get_field("summary_keyword").unwrap(),
            field_has_summary: schema.get_field("has_summary").unwrap(),
            field_has_content_vector: schema.get_field("has_content_vector").unwrap(),
            field_has_summary_vector: schema.get_field("has_summary_vector").unwrap(),
        }
    }

    pub fn convert_to_chunk(&self, tantivy_doc: TantivyDocument) -> CodefuseChunk {
        let id = tantivy_doc.get_first(self.field_id).unwrap();
        let file_url = tantivy_doc.get_first(self.field_file_url).unwrap();
        let index = tantivy_doc.get_first(self.field_index).unwrap();
        let content = tantivy_doc.get_first(self.field_content).unwrap();
        let start_line = tantivy_doc.get_first(self.field_start_line).unwrap();
        let end_line = tantivy_doc.get_first(self.field_end_line).unwrap();
        let summary = tantivy_doc.get_first(self.field_summary).unwrap();
        let project_url = tantivy_doc.get_first(self.field_project_url).unwrap();
        let branch = tantivy_doc.get_first(self.field_branch).unwrap();
        let hash = tantivy_doc.get_first(self.field_hash).unwrap();
        let feature = tantivy_doc.get_first(self.field_feature).unwrap();
        let summary_keyword = tantivy_doc.get_all(self.field_summary_keyword);
        let has_summary = tantivy_doc.get_first(self.field_has_summary).unwrap();
        let has_content_vector = tantivy_doc
            .get_first(self.field_has_content_vector)
            .unwrap();
        let has_summary_vector = tantivy_doc
            .get_first(self.field_has_summary_vector)
            .unwrap();

        let mut c_obj = CodefuseChunk::default();

        c_obj.id = id.as_str().unwrap_or("").to_string();
        c_obj.file_url = file_url.as_str().unwrap_or("").to_string();
        c_obj.index = index.as_u64().unwrap_or(0);
        c_obj.content = content.as_str().unwrap_or("").to_string();
        c_obj.start_line = start_line.as_u64().unwrap_or(0);
        c_obj.end_line = end_line.as_u64().unwrap_or(0);
        c_obj.summary = summary.as_str().unwrap_or("").to_string();
        c_obj.project_url = project_url.as_str().unwrap_or("").to_string();
        c_obj.branch = branch.as_str().unwrap_or("").to_string();
        c_obj.hash = hash.as_str().unwrap_or("").to_string();
        c_obj.feature = feature.as_str().unwrap_or("").to_string();
        c_obj.summary_keyword = summary_keyword
            .filter_map(|v| Some(v.as_str().unwrap_or("").to_string()))
            .collect::<Vec<String>>();
        c_obj.has_summary = has_summary.as_u64().unwrap_or(0);
        c_obj.has_content_vector = has_content_vector.as_u64().unwrap_or(0);
        c_obj.has_summary_vector = has_summary_vector.as_u64().unwrap_or(0);

        c_obj
    }
}

pub struct CodefuseFileFields {
    field_id: Field,
    field_content: Field,
    field_summary: Field,
    field_annotate: Field,
    field_project_url: Field,
    field_branch: Field,
    field_hash: Field,
    field_feature: Field,
    field_summary_keyword: Field,
    field_has_summary: Field,
    field_has_summary_vector: Field,
}

impl CodefuseFileFields {
    pub fn new(schema: &Schema) -> CodefuseFileFields {
        Self {
            field_id: schema.get_field("id").unwrap(),
            field_content: schema.get_field("content").unwrap(),
            field_summary: schema.get_field("summary").unwrap(),
            field_annotate: schema.get_field("annotate").unwrap(),
            field_project_url: schema.get_field("project_url").unwrap(),
            field_branch: schema.get_field("branch").unwrap(),
            field_hash: schema.get_field("hash").unwrap(),
            field_feature: schema.get_field("feature").unwrap(),
            field_summary_keyword: schema.get_field("summary_keyword").unwrap(),
            field_has_summary: schema.get_field("has_summary").unwrap(),
            field_has_summary_vector: schema.get_field("has_summary_vector").unwrap(),
        }
    }

    pub fn convert_to_file(&self, tantivy_doc: TantivyDocument) -> CodefuseFile {
        let mut m_obj = CodefuseFile::default();

        let file_url = tantivy_doc.get_first(self.field_id).unwrap();
        let content = tantivy_doc.get_first(self.field_content).unwrap();
        let summary = tantivy_doc.get_first(self.field_summary).unwrap();
        let annotate = tantivy_doc.get_first(self.field_annotate).unwrap();
        let project_url = tantivy_doc.get_first(self.field_project_url).unwrap();
        let branch = tantivy_doc.get_first(self.field_branch).unwrap();
        let hash = tantivy_doc.get_first(self.field_hash).unwrap();
        let feature = tantivy_doc.get_first(self.field_feature).unwrap();
        let summary_keyword = tantivy_doc.get_all(self.field_summary_keyword);
        let has_summary = tantivy_doc.get_first(self.field_has_summary).unwrap();
        let has_summary_vector = tantivy_doc
            .get_first(self.field_has_summary_vector)
            .unwrap();

        m_obj.id = file_url.as_str().unwrap_or("").to_string();
        m_obj.content = content.as_str().unwrap_or("").to_string();
        m_obj.summary = summary.as_str().unwrap_or("").to_string();
        m_obj.annotate = annotate.as_str().unwrap_or("").to_string();
        m_obj.project_url = project_url.as_str().unwrap_or("").to_string();
        m_obj.branch = branch.as_str().unwrap_or("").to_string();
        m_obj.hash = hash.as_str().unwrap_or("").to_string();
        m_obj.feature = feature.as_str().unwrap_or("").to_string();
        m_obj.summary_keyword = summary_keyword
            .filter_map(|v| Some(v.as_str().unwrap_or("").to_string()))
            .collect::<Vec<String>>();
        m_obj.has_summary = has_summary.as_u64().unwrap_or(0);
        m_obj.has_summary_vector = has_summary_vector.as_u64().unwrap_or(0);
        m_obj
    }
}

pub struct CodefuseMethodFields {
    field_id: Field,
    field_file_url: Field,
    field_content: Field,
    field_start_line: Field,
    field_end_line: Field,
    field_summary: Field,
    field_annotate: Field,
    field_project_url: Field,
    field_branch: Field,
    field_hash: Field,
    field_feature: Field,
    field_summary_keyword: Field,
    field_has_summary: Field,
    field_has_content_vector: Field,
    field_has_summary_vector: Field,
}

impl CodefuseMethodFields {
    pub fn new(schema: &Schema) -> CodefuseMethodFields {
        Self {
            field_id: schema.get_field("id").unwrap(),
            field_file_url: schema.get_field("file_url").unwrap(),
            field_content: schema.get_field("content").unwrap(),
            field_start_line: schema.get_field("start_line").unwrap(),
            field_end_line: schema.get_field("end_line").unwrap(),
            field_summary: schema.get_field("summary").unwrap(),
            field_annotate: schema.get_field("annotate").unwrap(),
            field_project_url: schema.get_field("project_url").unwrap(),
            field_branch: schema.get_field("branch").unwrap(),
            field_hash: schema.get_field("hash").unwrap(),
            field_feature: schema.get_field("feature").unwrap(),
            field_summary_keyword: schema.get_field("summary_keyword").unwrap(),
            field_has_summary: schema.get_field("has_summary").unwrap(),
            field_has_content_vector: schema.get_field("has_content_vector").unwrap(),
            field_has_summary_vector: schema.get_field("has_summary_vector").unwrap(),
        }
    }

    pub fn convert_to_method(&self, tantivy_doc: TantivyDocument) -> CodefuseMethod {
        let id = tantivy_doc.get_first(self.field_id).unwrap();
        let file_url = tantivy_doc.get_first(self.field_file_url).unwrap();
        let content = tantivy_doc.get_first(self.field_content).unwrap();
        let start_line = tantivy_doc.get_first(self.field_start_line).unwrap();
        let end_line = tantivy_doc.get_first(self.field_end_line).unwrap();
        let summary = tantivy_doc.get_first(self.field_summary).unwrap();
        let annotate = tantivy_doc.get_first(self.field_annotate).unwrap();
        let project_url = tantivy_doc.get_first(self.field_project_url).unwrap();
        let branch = tantivy_doc.get_first(self.field_branch).unwrap();
        let hash = tantivy_doc.get_first(self.field_hash).unwrap();
        let feature = tantivy_doc.get_first(self.field_feature).unwrap();
        let summary_keyword = tantivy_doc.get_all(self.field_summary_keyword);
        let has_summary = tantivy_doc.get_first(self.field_has_summary).unwrap();
        let has_content_vector = tantivy_doc
            .get_first(self.field_has_content_vector)
            .unwrap();
        let has_summary_vector = tantivy_doc
            .get_first(self.field_has_summary_vector)
            .unwrap();

        let mut m_obj = CodefuseMethod::default();

        m_obj.id = id.as_str().unwrap_or("").to_string();
        m_obj.file_url = file_url.as_str().unwrap_or("").to_string();
        m_obj.content = content.as_str().unwrap_or("").to_string();
        m_obj.start_line = start_line.as_u64().unwrap_or(0);
        m_obj.end_line = end_line.as_u64().unwrap_or(0);
        m_obj.summary = summary.as_str().unwrap_or("").to_string();
        m_obj.annotate = annotate.as_str().unwrap_or("").to_string();
        m_obj.project_url = project_url.as_str().unwrap_or("").to_string();
        m_obj.branch = branch.as_str().unwrap_or("").to_string();
        m_obj.hash = hash.as_str().unwrap_or("").to_string();
        m_obj.feature = feature.as_str().unwrap_or("").to_string();
        m_obj.summary_keyword = summary_keyword
            .filter_map(|v| Some(v.as_str().unwrap_or("").to_string()))
            .collect::<Vec<String>>();
        m_obj.has_summary = has_summary.as_u64().unwrap_or(0);
        m_obj.has_content_vector = has_content_vector.as_u64().unwrap_or(0);
        m_obj.has_summary_vector = has_summary_vector.as_u64().unwrap_or(0);

        m_obj
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct DataStatus {
    pub file_total_count:usize,
    pub file_count: usize,

    pub chunk_save_count: usize,
    pub chunk_progress: f64,
    pub chunk_vector_save_count: usize,
    pub chunk_vector_progress: f64,
    //索引构建进度(用户看，如果总进度>90即返回100，防止出现一些异常情况)
    pub build_index_progress: u8,
    //索引构建进度(实际进度，不做其他处理)
    pub build_index_progress_inner: u8,
    //必要索引构建进度
    pub build_nessary_index_progress: u8,
}

impl Default for DataStatus {
    fn default() -> Self {
        DataStatus {
            file_total_count: 0,
            file_count: 0,
            chunk_save_count: 0,
            chunk_progress: 0.0,
            chunk_vector_save_count: 0,
            chunk_vector_progress: 0.0,
            build_index_progress: 0,
            build_index_progress_inner: 0,
            build_nessary_index_progress: 0,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct RepoStatusCache{
    //是否构建中
    pub building: bool,
    //索引是否删除中
    pub deleting: bool
}

impl Default for RepoStatusCache {
    fn default() -> Self {
        RepoStatusCache{
            building: false,
            deleting: false,
        }
    }
}


use crate::dialogue::data_struct::{Codefuse<PERSON><PERSON>k<PERSON>ields, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s, Codefuse<PERSON>ethod<PERSON>ields, QueryChatItemResult, QueryChatRelatedData, QueryIndexRequest, QueryResultItem, ScanFileToIndexChunkRecord};
use crate::dialogue::index_repository_model::{IndexRepositoryRequestBean, LocalIndexQueryBean};
use crate::dialogue::instance_vec::SharedVec;
use crate::dialogue::misc_util::{calculate_content_hash, create_chunk_id, create_method_id, doc_to_chunk, doc_to_file, doc_to_method, is_java_file};
use crate::service::code_scan::check_and_del_old_project;
use crate::service::code_scan::extra_and_save_similarity_code_fragment;
use crate::service::code_scan::extra_related_code_fragment;
use crate::service::code_scan::skip_dir;
use crate::service::code_scan::skip_file;
use crate::utils::path;
use agent_common_service::model::code_scan_model::ScanConfig;
use agent_common_service::service::code_scan::CodeScanStrategy;
use agent_common_service::tools::code_tokenizer::code_snippet_tokenizer;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::index_client::{IndexClient, Indexable};
use agent_db::dal::kv_client::{KvClient, KV_CLIENT};
use agent_db::dal::remote_client::IndexTypeEnum::{CHUNK_CONTENT, CHUNK_VECTOR, CLASS_ANNOTATION, FILE_CONTENT, METHOD_ANNOTATION, METHOD_CONTENT};
use agent_db::dal::remote_client::{cge_embedding, CgeEmbeddingRequestBean, IndexTypeEnum, EMBEDDING_BUILD, EMBEDDING_QUERY};
use agent_db::dal::vector_client;
use agent_db::dal::vector_client::{QueryTopNVectorRequestBean, VectorClient, VECTOR_CLIENT};
use agent_db::domain::code_chat_domain::{CodefuseChunk, CodefuseFile, CodefuseMethod};
use agent_db::domain::code_kv_index_domain::ScanFileRecord;
use agent_db::tools::common_tools::{expand_user_home, LINE_ENDING, V_C_PREFIX, V_M_PREFIX};
use ignore::WalkBuilder;
use lazy_static::lazy_static;
use log::{debug, error, info};
use once_cell::sync::{Lazy, OnceCell};
use std::ascii::AsciiExt;
use std::collections::{HashMap, HashSet, VecDeque};
use std::hash::Hash;
use std::path::{Path, PathBuf};
use std::string::ToString;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use std::vec::Vec;
use std::{fs, future, thread};
use agent_db::remote::rpc_model::build_success_response;
use tantivy::schema::{OwnedValue, Value};
use tantivy::{Document, Score};
use tokio::sync::mpsc::{Receiver, Sender};
use tokio::sync::{mpsc, Mutex, RwLock};
use tokio::task;
use tokio::task::futures;
use tree_sitter::{Language, Node, Parser, Tree};

pub static CHUNK_CLIENT: Lazy<IndexClient<CodefuseChunk>> = Lazy::new(|| IndexClient::<CodefuseChunk>::create(CodefuseChunk::to_schema().clone(), Path::new("chunk_index"), 30).unwrap());
pub static METHOD_CLIENT: Lazy<IndexClient<CodefuseMethod>> = Lazy::new(|| IndexClient::<CodefuseMethod>::create(CodefuseMethod::to_schema().clone(), Path::new("method_index"), 30).unwrap());
pub static FILE_CLIET: Lazy<IndexClient<CodefuseFile>> = Lazy::new(|| IndexClient::<CodefuseFile>::create(CodefuseFile::to_schema().clone(), Path::new("file_index"), 30).unwrap());

///缓存Field, 提高速度
pub static CHUNK_FIELDS: Lazy<CodefuseChunkFields> = Lazy::new(|| CodefuseChunkFields::new(&CodefuseChunk::to_schema()));
pub static FILE_FIELDS: Lazy<CodefuseFileFields> = Lazy::new(|| CodefuseFileFields::new(&CodefuseFile::to_schema()));
pub static METHOD_FIELDS: Lazy<CodefuseMethodFields> = Lazy::new(|| CodefuseMethodFields::new(&CodefuseMethod::to_schema()));



lazy_static::lazy_static! {
    static ref VALID_KIND_NAMES: Vec<String> = vec![
        String::from("class_declaration"),
        String::from("method_declaration"),
        String::from("class_body"),
        String::from("interface_declaration"),
        String::from("interface_body"),
    ];
}



//todo 要加上分支
pub async fn query_index_entry(
    keys: HashSet<String>,
    questions: Vec<String>,
    types: HashSet<IndexTypeEnum>,
    user_token: &String,
    project_url: &String,
    project_type: &String,
    top: usize,
) -> Option<QueryChatRelatedData> {
    let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    info!("query_index_entry");
    info!("keys: {:?}", keys);
    info!("questions: {:?}", questions);
    info!("types: {:?}", types);
    info!("user_token: {}", user_token);
    info!("project_url: {}", project_url);
    info!("project_type: {}", project_type);
    info!("top: {}", top);
    let mut search_in_file_text = false;
    let mut search_in_method_text = false;
    let mut search_in_chunk_text = false;
    let mut search_in_chunk_vector = false;
    ///把file,  method，chunk，要搜索的字段分开存储
    let mut file_field_to_search = HashSet::new();
    let mut method_field_to_search = HashSet::new();
    let mut chunk_field_to_search = HashSet::new();
    for item in &types {
        match item {
            FILE_CONTENT => { file_field_to_search.insert("content".to_string()); }
            METHOD_CONTENT => { method_field_to_search.insert("content".to_string()); }
            CHUNK_CONTENT => { chunk_field_to_search.insert("content".to_string()); }
            CLASS_ANNOTATION => {
                file_field_to_search.insert("annotate".to_string());
            }
            METHOD_ANNOTATION => {
                method_field_to_search.insert("annotate".to_string());
            }
            _ => {}
        }
    }
    if types.contains(&CHUNK_VECTOR) {
        search_in_chunk_vector = true;
    }
    if !file_field_to_search.is_empty() {
        search_in_file_text = true;
    }
    if !method_field_to_search.is_empty() {
        search_in_method_text = true;
    }
    if !chunk_field_to_search.is_empty() {
        search_in_chunk_text = true;
    }
    let mut container_chunk: Vec<QueryChatItemResult<CodefuseChunk>> = Vec::new();
    let mut container_method: Vec<QueryChatItemResult<CodefuseMethod>> = Vec::new();
    let mut container_file: Vec<QueryChatItemResult<CodefuseFile>> = Vec::new();
    let mut container_vector_chunk: Vec<QueryChatItemResult<CodefuseChunk>> = Vec::new();

    if  search_in_chunk_vector{
        let code_embedding_req = CgeEmbeddingRequestBean {
            codeList: questions.clone(),
            r#type: EMBEDDING_QUERY.to_string(),
        };
        info!("request question vector");

        let response = cge_embedding(&code_embedding_req,AGENT_CONFIG.embedding_query_timeout).await;


        if response.data.is_some() {
            let result_vector = response.data.unwrap().result;
            let mut query_req = QueryTopNVectorRequestBean {
                query: vec![],
                top_n: top,
                project_url: project_url.clone(),
            };
            for vector_data in result_vector {
                query_req.query = vector_data;
                if search_in_chunk_vector {
                    let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                    let vector_result = VECTOR_CLIENT.query_top_n(&query_req).await;
                    let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                    info!("search chunk vector time:{}", end_time - start_time);
                    match vector_result {
                        Ok(data_vec) => {
                            if data_vec.len() > 0 {
                                let key_score_map: HashMap<String, f64> = data_vec.into_iter()
                                    .map(|(vector_item, score)| (vector_item.id, score))
                                    .collect();
                                // 提取所有的键，并收集到一个 HashSet 中
                                let id_value_set: HashSet<String> = key_score_map.keys().cloned().collect();
                                let chunk_vector_result = CHUNK_CLIENT.query_by_id(&id_value_set, project_url, top).await;
                                match chunk_vector_result {
                                    Ok(chunk_result) => {
                                        for item in chunk_result {
                                            let mut item = doc_to_chunk(item.0, item.1);
                                            let score_opt = key_score_map.get(&item.data.id);
                                            if let Some(score) = score_opt {
                                                item.score = score.clone() as f32;
                                                container_vector_chunk.push(QueryChatItemResult { search_result: item, extend_method_result: None, extend_file_result: None });
                                            }
                                        }
                                    }
                                    _ => {}
                                }
                            }
                        }
                        Err(e) => {
                            error!("search chunk vector error:{}", e);
                        }
                    }
                }
            }
        } else {
            error!("embedding result none")
        }
    }

    info!("search in file, method, chunk");
    let (file_search_result, method_search_result, chunk_search_result) =
        tokio::join!(
            FILE_CLIET.query_multi_key(&file_field_to_search, &keys, project_url, top, search_in_file_text),
    METHOD_CLIENT.query_multi_key(&method_field_to_search, &keys, project_url, top, search_in_method_text),
    CHUNK_CLIENT.query_multi_key(&chunk_field_to_search, &keys, project_url, top, search_in_chunk_text));


    match file_search_result {
        Ok(file_result) => {
            for item in file_result {
                let item = doc_to_file(item.0, item.1);
                container_file.push(QueryChatItemResult { search_result: item, extend_method_result: None, extend_file_result: None });
            }
        }
        Err(e) => {
            error!("search file error:{}", e);
        }
    }

    match method_search_result {
        Ok(method_result) => {
            for item in method_result {
                let item = doc_to_method(item.0, item.1);
                container_method.push(QueryChatItemResult { search_result: item, extend_method_result: None, extend_file_result: None });
            }
        }
        Err(e) => {
            error!("search method error:{}", e);
        }
    }

    match chunk_search_result {
        Ok(chunk_result) => {
            for item in chunk_result {
                let item = doc_to_chunk(item.0, item.1);
                container_chunk.push(QueryChatItemResult { search_result: item, extend_method_result: None, extend_file_result: None });
            }
        }

        Err(e) => {
            error!("search chunk error:{}", e);
        }
    }

    let consume_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() - start_time;
    Some(QueryChatRelatedData {
        file_content_data: Some(container_file),
        method_content_data: Some(container_method),
        chunk_content_data: Some(container_chunk),
        chunk_vector_data: Some(container_vector_chunk),
        search_time: Some(consume_time),
    },)
}

pub struct JavaParser {
    pub parser: Parser,
}

///不支持共享
impl JavaParser {
    pub fn new() -> JavaParser {
        let mut parser = Parser::new();
        parser.set_language(&tree_sitter_java::language()).unwrap();

        Self {
            parser
        }
    }
}

#[derive(Debug)]
pub struct FileBean {
    dir: String,
    file_name_suffix: String,
    lines: Vec<String>,
    project_url: String,
    branch: String,
}

impl FileBean {
    pub fn new(dir: String, file_name_suffix: String, lines: Vec<String>, project_url: String, branch: String) -> FileBean {
        Self {
            dir,
            file_name_suffix,
            lines,
            project_url,
            branch,
        }
    }
}


///切分chunk，建立索引
pub fn build_chunk_index(dir: &Path, lines: &Vec<String>, project_url: &str, branch: &str) -> anyhow::Result<Vec<CodefuseChunk>> {
    let mut chunk_index: usize = 1;
    let mut container: Vec<CodefuseChunk> = Vec::new();
    for window_start in (0..lines.len()).step_by(AGENT_CONFIG.index_chunk_slide_size as usize) {
        let window_end = usize::min(window_start + AGENT_CONFIG.index_chunk_window_size as usize, lines.len());
        let content_arr = &lines[window_start..window_end];
        let code_snippet = content_arr.join(LINE_ENDING);
        if code_snippet.trim().len() == 0 {
            continue;
        }

        let mut chunk = CodefuseChunk::default();
        chunk.id = create_chunk_id(dir.to_str().unwrap().to_string(), chunk_index);
        chunk.file_url = dir.to_str().unwrap().to_string();
        chunk.index = chunk_index as u64;
        chunk.content = code_snippet;
        chunk.start_line = window_start as u64;
        chunk.end_line = (window_end - 1) as u64;
        // chunk.summary                            //todo, 请求模型获取summary
        chunk.project_url = project_url.to_string();
        chunk.branch = branch.to_string();
        chunk.hash = calculate_content_hash(&chunk.content);      //计算chunk content的hash值
        // chunk.feature                            //todo

        container.push(chunk);
        chunk_index = chunk_index + 1;
    }
    Ok(container)
}




///建立文件索引
pub fn build_file_index(dir: &Path, source_content: &String, project_url: &str, branch: &str) -> anyhow::Result<CodefuseFile> {
    let mut index_file = CodefuseFile::default();
    index_file.id = dir.to_str().unwrap().to_string();              //承担id的作用
    index_file.content = source_content.clone();
    // index_file.summary.push_str(dir.to_str().unwrap());          //todo, 请求模型获取summary,或者注释
    index_file.project_url = project_url.to_string();
    index_file.branch = branch.to_string();
    index_file.hash = calculate_content_hash(source_content);     //content的hash值，判断内容是否发生变化
    // index_file.feature.push_str(dir.to_str().unwrap());          //todo


    if dir.extension()
        .and_then(|ext| ext.to_str())
        .map_or(false, |ext| ext.eq_ignore_ascii_case("java")) {
        let mut java_parser = JavaParser::new();

        let tree = java_parser.parser.parse(source_content, None).expect("Unable to parse source code");
        let root_node = tree.root_node();
        let class_comment = scan_first_level_class(&root_node, source_content);
        if class_comment.is_some() {
            index_file.annotate = class_comment.unwrap();
        } else {
            index_file.annotate = "".to_string();
        }
    } else {
        index_file.annotate = "".to_string();
    }

    return Ok(index_file);
}

pub fn build_method_index_exetute(node: &Node, source_code: &str, project_url: &str, branch: &str, shared_vec: &mut SharedVec, container: &mut Vec<CodefuseMethod>, dir: &Path, mut full_package_name:  Option<String>) {
    // info!("file path : {}", dir.to_str().unwrap().to_string());
    let mut cursor = node.walk();
    // 遍历子节点
    for child in node.children(&mut cursor) {
        // info!("child.kind : {}", child.kind());
        if full_package_name.is_none() {
            if child.kind() == "package_declaration" {
                let mut package_name = get_package_name(&child, source_code);
                full_package_name = package_name;
            }
        }

        match child.kind() {
            "method_declaration" | "constructor_declaration" => {
                let mut method_sigature_start = 0;
                method_sigature_start = get_method_signature_start_index(&child, source_code);

                let mut codefuse_method = CodefuseMethod::default();
                codefuse_method.project_url = project_url.to_string();
                codefuse_method.branch = branch.to_string();
                codefuse_method.file_url = dir.to_str().unwrap().to_string();
                let is_in_anonymous = is_anonymous_inner_classs_method(&child, source_code);
                if is_in_anonymous {
                    // info!("is_anonymous_inner_classs_method true, skip");
                    continue;
                }

                let method_comment = get_comment_text(&child, source_code);
                let comment_data = method_comment.unwrap();
                // info!("comment_data  : {} {} {} {}", &comment_data.0, &comment_data.1,&comment_data.2,&comment_data.3);
                if comment_data.1 == 0 {
                    codefuse_method.annotate = comment_data.0;
                } else {
                    codefuse_method.annotate = comment_data.0;
                    codefuse_method.start_line = comment_data.1 as u64;
                }

                //获取方法签名
                //获取行号
                let start_byte_contain_comment = if comment_data.1 == 0 { child.range().start_byte } else { comment_data.3 };
                let method_content_with_comment = &source_code[start_byte_contain_comment..child.range().end_byte];
                // info!("Method Content: {}", method_content_with_comment);
                codefuse_method.content = method_content_with_comment.to_string();
                codefuse_method.hash = calculate_content_hash(&codefuse_method.content);      //计算method content的hash值
                if comment_data.1 == 0 { //没有注释
                    // info!("method no comment");
                    codefuse_method.start_line = (child.start_position().row + 1) as u64;
                    codefuse_method.end_line = (child.end_position().row + 1) as u64;
                } else { //有注释
                    // info!("method with comment");
                    codefuse_method.end_line = (child.end_position().row + 1) as u64;
                }

                // let method_content_no_comment = &source_code[child.range().start_byte..child.range().end_byte];
                //过滤掉注解
                let method_content_no_comment = &source_code[method_sigature_start..child.range().end_byte];
                let method_content_str = method_content_no_comment.to_string();

                let index_opt = method_content_str.find("{");
                match index_opt {
                    Some(index) => {
                        let class_join_name = shared_vec.get_all().unwrap().join("#");
                        // info!("class_join_name : {}", class_join_name);
                        let complete_id = create_method_id(dir.to_str().unwrap().to_string(), class_join_name, method_content_str[0..index].to_string());
                        // info!("method signature : {}", &complete_id);
                        codefuse_method.id = complete_id;
                    }
                    None => {
                        // info!("not in method_content_str, maybe is interface");
                        let class_join_name = shared_vec.get_all().unwrap().join("#");
                        // info!("class_join_name : {}", class_join_name);
                        let complete_id = create_method_id(dir.to_str().unwrap().to_string(), class_join_name, method_content_str);
                        // info!("method signature : {}", &complete_id);
                        codefuse_method.id = complete_id;
                    }
                }
                // info!("build methond index {:?}", serde_json::to_string(&codefuse_method));
                if codefuse_method.content.trim().len() > 0 {
                    container.push(codefuse_method);
                }
            }
            "class_declaration" | "interface_declaration" => {
                if let Some(name_node) = child.child_by_field_name("name") {
                    //获取类名
                    let class_name = name_node.utf8_text(source_code.as_bytes()).unwrap();
                    shared_vec.add(class_name.to_string());
                    // info!("shared_vec data : {:?}", shared_vec.get_all());
                }
            }
            _ => {}
        }
        //递归处理
        if VALID_KIND_NAMES.contains(&child.kind().to_string()) {
            // info!("VALID_KIND_NAMES.contains is true  {}", &child.kind().to_string());
            build_method_index_exetute(&child, source_code, project_url, branch, shared_vec, container, dir, full_package_name.clone());
        }
        if ("class_declaration" == &child.kind().to_string() || "interface_declaration" == &child.kind().to_string()) {
            shared_vec.remove_last();
            // info!("after remove_last shared_vec.get_all().len : {}", shared_vec.get_all().unwrap().len());
        }
    }
}


///获取方法，类的注释，兼容了单行注释以及多行注释, 返回的注释是处理后的数据，去掉注释符号
fn get_comment_text(node: &tree_sitter::Node, source_code: &str) -> Option<(String, usize, usize, usize, usize)> {
    let mut pre_sibling_opt = node.prev_sibling();
    if pre_sibling_opt.is_none() {
        return Some(("".to_string(), 0, 0, 0, 0))
    }
    let pre_sibling = pre_sibling_opt.unwrap();
    let name = pre_sibling.kind().to_string();

    if "block_comment".eq_ignore_ascii_case(&name) {
        let start_byte = pre_sibling.start_byte();
        let end_byte = pre_sibling.end_byte();
        let block_comment = source_code[start_byte..end_byte].to_string();
        let start_line = pre_sibling.start_position().row + 1;
        let end_line = pre_sibling.end_position().row + 1;

        //先保留注释符号
        // let no_syntax_comment = remove_block_comment_syntax(&block_comment);
        // println!("block_comment : {:?}", &block_comment);
        return Some((block_comment, start_line, end_line, start_byte, end_byte));
    } else if "line_comment".eq(&name) {
        return get_line_comment_text(&pre_sibling, source_code);
    } else {}

    Some(("".to_string(), 0, 0, 0, 0))
}

///去掉多行注释的注释符合
fn remove_block_comment_syntax(block_comment: &String) -> Option<String> {
    if block_comment.len() > 0 {
        let lines = block_comment.split(LINE_ENDING);
        let processed_lines: Vec<&str> = lines.map(|l| l.trim().trim_start_matches("/**").trim_end_matches("**/").trim_end_matches("*/").trim_start_matches("*")).collect();
        let comment_result = processed_lines.join(LINE_ENDING).trim().to_string();
        return Some(comment_result);
    }
    Some("".to_string())
}

fn get_line_comment_text(node: &Node, source_code: &str) -> Option<(String, usize, usize, usize, usize)> {
    let mut current_node = Some(node.clone());
    let mut comment_data: Vec<&str> = Vec::new();
    let mut is_first = true;
    let mut start_line: usize = 0;
    let mut end_line: usize = 0;
    let mut start_byte: usize = 0;
    let mut end_byte: usize = 0;

    while let Some(node) = current_node {
        let kind_name = current_node.unwrap().kind().to_string();

        if "line_comment" == &kind_name {
            start_byte = current_node.unwrap().start_byte();
            end_byte = current_node.unwrap().end_byte();
            start_line = current_node.unwrap().start_position().row + 1;
            let comment_line = &source_code[start_byte..end_byte];
            // info!("get_line_comment_text single comment_line : {:?}", comment_line);
            comment_data.push(comment_line);
            if is_first {
                end_line = current_node.unwrap().start_position().row + 1;
                end_byte = current_node.unwrap().end_byte();
                is_first = false;
            }
        } else {
            // info!("no line_comment node");
            break;
        }

        if let Some(pre_sibling_node) = node.prev_sibling() {
            current_node = Some(pre_sibling_node.clone()); // 移动所有权
        } else {
            current_node = None; // 没有兄弟节点，停止循环
        }
    }
    let data = convert_line_comment_data(comment_data);
    if data.is_some() {
        let data_comment = data.unwrap();
        //保留注释符号
        // let data = remove_line_comment_syntax(&data_comment);
        let data = data_comment.join(LINE_ENDING);
        let commets = data;
        // info!("line_comment data : {} {} {} {} {}", &commets, start_line, end_line, start_byte, end_byte);
        return Some((commets, start_line, end_line, start_byte, end_byte));
    }
    Some(("".to_string(), 0, 0, 0, 0))
}

///处理但行注释的注释符合
fn remove_line_comment_syntax(line_comment: &Vec<&str>) -> Option<String> {
    if line_comment.len() > 0 {
        let data_result: Vec<&str> = line_comment.iter()
            .map(|&item| item.trim().trim_start_matches("//"))  // 这里进行修改
            .collect();
        return Some(data_result.join(LINE_ENDING).trim().to_string());
    }
    Some("".to_string())
}

/*
单行注释在转成正常顺序， 在获取的时候，是倒序获取，这里就需要转成正常的顺序
 */
fn convert_line_comment_data(mut vec: Vec<&str>) -> Option<Vec<&str>> {
    let mut comment_data: Vec<&str> = Vec::new();
    for item in vec.iter().rev() {
        comment_data.push(item)
    }
    Some(comment_data)
}

// 去掉注释符号的函数
fn strip_comment_syntax(comment: &str) -> String {
    // 如果是多行注释（如 /* comment */）
    let comment = comment.trim_start();
    if comment.starts_with("/*") {
        return comment[2..comment.len() - 2].trim().to_string();
    }
    // 如果是单行注释（如 // comment）
    if comment.starts_with("//") {
        return comment[2..].trim().to_string();
    }
    comment.to_string()
}

///是否是匿名内部类的方法
fn is_anonymous_inner_classs_method(node: &tree_sitter::Node, source_code: &str) -> bool {
    // let prarent_pre_sibling = node.parent().unwrap().prev_sibling().unwrap();
    let parent = node.parent();
    if parent.is_some() {
        let prarent_sibling = parent.unwrap().prev_sibling();
        if prarent_sibling.is_some() {
            let prarent_sibling = prarent_sibling.unwrap().kind();
            if "argument_list" == prarent_sibling {
                return true;
            }
        }
    }
    return false;
}


///扫描java文件里的一级class
fn scan_first_level_class(node: &Node, source_code: &str) -> Option<String> {
    let mut cursor = node.walk();
    let mut class_comments: Vec<String> = Vec::new();

    for child in node.children(&mut cursor) {
        let kind_name = child.kind().to_string();
        if "class_declaration" == &kind_name
            || "interface_declaration" == &kind_name {
            // println!("====== class_declaration or interface_declaration ======");
            if let Some(name_node) = child.child_by_field_name("name") {
                // 输出类名
                let class_name = name_node.utf8_text(source_code.as_bytes()).unwrap();
                // info!("Class name: {}", class_name);
            }
            let comment = get_comment_text(&child, source_code);
            class_comments.push(comment.unwrap().0);
        }
    }
    return Some(class_comments.join(LINE_ENDING));
}

///计算时间差，并打印出来
fn calculate_duration(start: &Instant, end: &Instant, content: &String) -> Option<String> {
    let duration = end.duration_since(start.clone());
    let duration_millis = duration.as_millis();
    info!("{} duration_millis: {}", content, duration_millis);
    Some("".to_string())
}

fn get_package_name(child: &Node, source_code: &str) -> Option<String> {
    if child.kind() == "package_declaration" {
        for grandchild in child.children(&mut child.walk()) {
            if grandchild.kind() == "scoped_identifier" {
                let package_name = grandchild.utf8_text(source_code.as_bytes()).unwrap();
                // info!("Package name: {}", package_name);
                return Some(package_name.to_string());
            }
        }
    }
    Some("".to_string())
}

///过滤掉注解，返回真正方法声明的开始索引
fn get_method_signature_start_index(child: &Node, source_code: &str) -> usize {
    let mut cursor = child.walk();
    for sub_child in child.children(&mut cursor){
        // info!("=== sub_child.kind : {}***{}", sub_child.kind(), &source_code[sub_child.start_byte()..sub_child.end_byte()]);
        if sub_child.kind() == "modifiers" {
            let mut cursor = sub_child.walk();
            for modefiers_sub_child in sub_child.children(&mut cursor){
                // info!("=== modefiers_child.kind : {}==={}", modefiers_sub_child.kind(), &source_code[modefiers_sub_child.start_byte()..modefiers_sub_child.end_byte()]);
                //通过“marker_annotation”过滤掉@Override
                if modefiers_sub_child.kind() != "annotation" && modefiers_sub_child.kind() != "marker_annotation"{
                    let method_signature_start = modefiers_sub_child.start_byte();
                    return method_signature_start;
                }
            }
        }
        //"type_identifier"兼容没有访问控制符的场景
        if sub_child.kind() == "type_identifier" {
            let method_signature_start = sub_child.start_byte();
            return method_signature_start;
        }
    }
    0
}

#[cfg(test)]
mod tests {
    use crate::dialogue::codefuse_index_repository::{query_index_entry, CHUNK_CLIENT, FILE_CLIET, METHOD_CLIENT, VECTOR_CLIENT};
    use crate::dialogue::misc_util::{ doc_to_file,  get_file_chunk_vector_prefix, handle_panic_info, query_method_list};
    use crate::dialogue::repo_index_operator::{monitor_git_repo_branch, update_index_by_walkthrough, RepoInfo, RepoStatusEnum, CACHE_KEY, GIT_REPO_STATUS};
    use agent_db::dal::kv_client::KV_CLIENT;
    use agent_db::dal::remote_client::IndexTypeEnum::{CHUNK_CONTENT,  METHOD_ANNOTATION, METHOD_CONTENT};
    use agent_db::dal::remote_client::{chat_query_change, deepsearch_new_query, deepsearch_query_split, query_repo_wiki_entry, IndexTypeEnum, QueryChangeRequestBean, QueryInProject};
    use serde_json::from_str;
    use std::collections::{HashMap, HashSet};
    use std::fs;
    use std::path::{Path, MAIN_SEPARATOR};
    use std::thread::sleep;
    use std::time::{Duration, SystemTime, UNIX_EPOCH};
    use agent_db::domain::ap_data::{GenerateNewQueryRequestBean, QeurySplitRequestBean, QueryWikiRequestBean, WikiTypeEnum};
    use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;

    #[test]
    fn test_query_wiki() {
        let mut repo_info = HashMap::new();
        repo_info.insert("repoURL".to_string(), "https://code.alipay.com/common_release/pairhub.git".to_string());
        repo_info.insert("branch".to_string(), "guli_code_search_eval".to_string());

        let query_wiki = QueryWikiRequestBean{
            query: "如何通过奖品代码创建一个新的团队？".to_string(),
            // r#type: WikiTypeEnum::API_CHAIN_WIKI,
            r#type: WikiTypeEnum::REPO_WIKI,
            repoInfo: repo_info,
            enableWinnow: false
        };
        let time_0 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let runtime = tokio::runtime::Runtime::new().unwrap();

        let rst = runtime.block_on(query_repo_wiki_entry(&query_wiki));
        let time_1 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        println!("query wiki cost : {}ms", time_1 - time_0);
        println!("query_repo_wiki_entry {:?}", rst);

    }


    #[test]
    fn test_deepsearch() {
        let request_bean = QeurySplitRequestBean {
            userToken: Some("dfde8495-4979-472f-9763-acf971e05b9e".to_string()),
            pluginVersion: Some("1.0.0".to_string()),
            productType: Some("IDEA".to_string()),
            ideVersion: Some("".to_string()),
            agentVersion: Some("".to_string()),
            query: "分析这个仓库的模块及其作用".to_string(),
            model:"DeepSeek-V3".to_string(),
        };
        let runtime = tokio::runtime::Runtime::new().unwrap();
        //ok
        // let rst = runtime.block_on(deepsearch_query_split(&request_bean));
        // println!("rst {:?}", rst);
        // println!("rst {:?}", rst.data.unwrap());

        //过滤 ok
        // let query = "";
        //
        // let item_0 = DeepsearchRelateCheckItem{
        //     id: "code_id_0".to_string(),
        //     code: "/**\n     * 添加集合\n     *\n     * @param modelTestCaseCollectionDO\n     */\n    BaseResponse<Void> add(ModelTestCaseCollectionDO modelTestCaseCollectionDO);".to_string(),
        //     path: "app/facade/src/main/java/com/alipay/tsingyanprod/facade/ModelTestCaseCollectionServiceFacade.java".to_string(),
        // };
        // let item_1 = DeepsearchRelateCheckItem{
        //     id: "code_id_1".to_string(),
        //     code: "/**\n     * 根据Id删除模型测试用例\n     *\n     * @param id\n     * @return\n     */\n    BaseResponse<Void> delete(long id);".to_string(),
        //     path: "app/facade/src/main/java/com/alipay/tsingyanprod/facade/ModelTestCaseServiceFacade.java".to_string(),
        // };
        //
        // let data = vec![item_0, item_1];
        //
        // let filter_request = QueryResultFilterRequestBean{
        //     userToken: Some("dfde8495-4979-472f-9763-acf971e05b9e".to_string()),
        //     pluginVersion:Some("1.0.0".to_string()),
        //     productType: Some("IDEA".to_string()),
        //     ideVersion: Some("".to_string()),
        //     agentVersion: Some("".to_string()),
        //     query: "tsingyanprod-facade模块中定义的接口及其实现类有哪些".to_string(),
        //     codeSnippetList: data,
        //     model: "DeepSeek-V3".to_string(),
        // };
        // let rst = runtime.block_on(deepsearch_filter(&filter_request));
        // println!("rst {:?}", rst);
        // println!("rst {:?}", rst.data);

        //是否新query ok

        let code_0 = ChatRelatedCodeModel {
            relativePath: "1.java".to_string(),
            snippet: "public class HelloWorld {\n    public static void main(String[] args) {\n        System.out.println(\"Hello, World!\");\n    }\n}".to_string(),
            startLine: 1,
            endLine: 10,
            source: None,
            title: None,
        };
        let code_1 = ChatRelatedCodeModel {
            relativePath: "2.java".to_string(),
            snippet: "public class Sum {\n    public static void main(String[] args) {\n        int a = 5;\n        int b = 10;\n        System.out.println(\"Sum: \" + (a + b));\n    }\n".to_string(),
            startLine: 2,
            endLine: 18,
            source: None,
            title: None,
        };
        let code_2 = ChatRelatedCodeModel{
            relativePath: "3.java".to_string(),
            snippet: "class Dog {\n    String name;\n\n    Dog(String name) {\n        this.name = name;\n    }\n\n    void bark() {\n        System.out.println(\"Woof! My name is \" + name);\n    }\n".to_string(),
            startLine: 6,
            endLine: 27,
            source: None,
            title: None,
        };

        let data = vec![code_0, code_1, code_2];

        let queries = vec![
            "这个仓库的核心模块或功能有哪些？请列出主要模块名称及其功能描述。".to_string(),
            "仓库的入口文件或主类是什么？如何初始化和启动整个应用？".to_string(),
            "仓库中是否有配置文件或关键参数（如`config.json`或`settings.py`）？它们控制哪些核心行为？".to_string()
        ];

        let new_query = GenerateNewQueryRequestBean {
            userToken: Some("dfde8495-4979-472f-9763-acf971e05b9e".to_string()),
            pluginVersion: Some("1.0.0".to_string()),
            productType: Some("IDEA".to_string()),
            ideVersion: Some("".to_string()),
            agentVersion:  Some("".to_string()),
            query: "帮忙介绍下这个仓库？".to_string(),
            subQueryList: queries,
            codeSnippetList: data,
            model: "DeepSeek-V3".to_string(),
        };

        let rst = runtime.block_on(deepsearch_new_query(&new_query));
        println!("deepsearch_new_query rst {:?}", rst);
        println!("deepsearch_new_query rst.data {:?}", rst.data);

    }
    #[test]
    fn test_query_project() {
        let query_in_project = QueryInProject {
            project_url: "/Users/<USER>/Downloads/alispace/caselike".to_string(),
            // project_url: "D:/PycharmProjects/aimsalgo".to_string(),
            field_to_search: Default::default(),
            word_to_search: Default::default(),
            top_num: 0,
            page_size: 0,
            page_number: 0,
        };


        #[test]
        fn test_query_method() {
            let file_url = "/Users/<USER>/Downloads/alispace/caselike/app/service/src/main/java/com/alipay/tsingyanprod/service/drm/CodeGPTDrmConfig.java".to_string();
            // let mut query_param = HashMap::new();
            // query_param.insert("file_url".to_string(), file_url.clone());
            // //最多取20个函数
            // let method_vec_result = METHOD_CLIENT.query(&query_param, 200);
            // let data_set = method_vec_result.unwrap();
            // println!("method_vec_result.unwrap().len() {}", data_set.len());
            //
            // let data_99 = data_set.get(99).unwrap();
            // let score = data_99.0.clone();
            // let doc = data_99.1.clone();
            // let method = doc_to_method(score, doc);
            // println!("method: {}", serde_json::to_string_pretty(&method).unwrap());
            let method_data = query_method_list(&file_url, 770, 794);

            let method_0 = method_data.0;
            let method_1 = method_data.1;

            let method_content = method_0.get(0).unwrap();

            println!("method_0.len {}, method_1.len {}", method_0.len(), method_1.len());

            // println!("method_content {}", serde_json::to_string_pretty(&method_content.content).unwrap());

        }
        #[test]
        fn test_query_change() {
            let query_change_request = QueryChangeRequestBean {
                userToken: "".to_string(),
                productType: "".to_string(),
                question: "仓库的入口文件或主类的具体路径和文件名是什么？".to_string(),
            };

            // let query_change = chat_query_change(&query_change_request).await;

            let runtime = tokio::runtime::Runtime::new().unwrap();
            // runtime.block_on(GIT_REPO_STATUS.put(repoinfo_0, status_0));

            let r = runtime.block_on(chat_query_change(&query_change_request));

            println!("{:?}", r);
        }

        #[test]
        fn test_get_has_content_vector_data_count() {
            let mut query_in_project = QueryInProject::default();
            query_in_project.project_url = "/Users/<USER>/Downloads/alispace/caselike".to_string();
            // let file_count = FILE_CLIET.query_count_in_project(query_in_project.clone()).await.unwrap();


            let runtime = tokio::runtime::Runtime::new().unwrap();
            let file_count = runtime.block_on(FILE_CLIET.query_count_in_project(query_in_project)).unwrap();
            println!("file_count {}", file_count);

            // let rst =  FILE_CLIET.get_distinct_project().unwrap();
            // println!("rst : {}", serde_json::to_string_pretty(&rst).unwrap());
            //
            //
            // let all_chunk_with_content_vector_count = CHUNK_CLIENT.get_has_content_vector_data_count().unwrap();
            // println!("111 {}", all_chunk_with_content_vector_count);
            // recover_chunk_has_content_vector_in_db();
            // let all_chunk_with_content_vector_count = CHUNK_CLIENT.get_has_content_vector_data_count().unwrap();
            // println!("222 {}", all_chunk_with_content_vector_count);

        }
        #[test]
        fn test_total_count() {
            let total_count = FILE_CLIET.total_count();
            println!("{}", total_count);
        }

        ///测试日志上传
        #[test]
        fn test_upload_panic() {
            println!("test_upload_panic");
            let runtime = tokio::runtime::Runtime::new().unwrap();
            runtime.block_on(handle_panic_info());
        }

        #[test]
        fn test_query_multi_key() {
            let chunk_field_to_search = HashSet::new();
            let mut keys = HashSet::new();
            keys.insert("更新".to_string());
            keys.insert("function".to_string());

            let project_url = "/Users/<USER>/Downloads/alispace/caselike";
            let top = 10;
            let search_in_chunk_text = true;

            let runtime = tokio::runtime::Runtime::new().unwrap();

            let query_rst = runtime.block_on(CHUNK_CLIENT.query_multi_key(&chunk_field_to_search, &keys, project_url, top, search_in_chunk_text));

            match query_rst {
                Ok(query) => {
                    println!("query.len : {}", query.len());
                    println!("{:?}", query);
                }
                Err(_) => {}
            }

            println!("====================");
        }

        ///查询总的数据
        #[test]
        fn test_query_count() {
            let project_url = "/Users/<USER>/Downloads/alispace/codefuseAgent/codefuse_local_agent".to_string();
            let mut query_in_project = QueryInProject::default();
            query_in_project.project_url = project_url;

            let runtime = tokio::runtime::Runtime::new().unwrap();
            let query_result = runtime.block_on(CHUNK_CLIENT.query_count_in_project(query_in_project));
            if let Ok(chunk_count) = query_result {
                println!("chunk count : {:?}", chunk_count);
            }
        }

        ///问题改写请求
        #[test]
        fn test_query_online() {
            let query_change_request = QueryChangeRequestBean {
                userToken: "".to_string(),
                productType: "".to_string(),
                question: "介绍这个仓库".to_string(),
            };

            println!("{}", serde_json::to_string_pretty(&query_change_request).unwrap());

            let runtime = tokio::runtime::Runtime::new().unwrap();
            let query_result = runtime.block_on(chat_query_change(&query_change_request));
            // let query_change = chat_query_change(&query_change_request).await;

            println!("query_result: {:?}", query_result);
        }

        #[test]
        fn test_delete_cache() {
            KV_CLIENT.delete(&CACHE_KEY.to_string());
            println!("{:?}", KV_CLIENT.get(&CACHE_KEY.to_string()));
            print!("===========");
        }

        #[test]
        fn test_multi_query() {
            let mut query_key_word_set = HashSet::new();
            query_key_word_set.insert("getLastLineLength".to_string());
            query_key_word_set.insert("函数".to_string());
            query_key_word_set.insert("function".to_string());

            let mut question_set = Vec::new();
            question_set.push("Find the function getLastLineLength".to_string());
            question_set.push("查找函数 getLastLineLength".to_string());


            let mut query_index_types = HashSet::new();
            query_index_types.insert(METHOD_ANNOTATION);
            query_index_types.insert(METHOD_CONTENT);
            query_index_types.insert(CHUNK_CONTENT);


            let token = &"".to_string();
            // let projectUrl = &"/Users/<USER>/Downloads/alispace/easyexcel".to_string();
            let projectUrl = &"/Users/<USER>/Downloads/alispace/easyexcel_00".to_string();
            let product_type = &"".to_string();
            let top = 10;

            let runtime = tokio::runtime::Runtime::new().unwrap();

            let query_data = runtime.block_on(query_index_entry(query_key_word_set,
                                                                question_set,
                                                                query_index_types,
                                                                token,
                                                                projectUrl,
                                                                product_type,
                                                                top));

            println!("====== {}", serde_json::to_string_pretty(&query_data.unwrap()).unwrap());
        }

        #[test]
        fn test_query() {
            let mut query_key_word_set = HashSet::new();
            query_key_word_set.insert("getLastLineLength".to_string());
            query_key_word_set.insert("函数".to_string());
            query_key_word_set.insert("function".to_string());

            let mut question_set = Vec::new();
            question_set.push("Find the function getLastLineLength".to_string());
            question_set.push("查找函数 getLastLineLength".to_string());


            let mut query_index_types = HashSet::new();
            query_index_types.insert(METHOD_ANNOTATION);
            query_index_types.insert(METHOD_CONTENT);
            query_index_types.insert(CHUNK_CONTENT);


            let token = &"".to_string();
            let projectUrl = &"".to_string();
            let product_type = &"".to_string();
            let top = 10;

            let runtime = tokio::runtime::Runtime::new().unwrap();
            let query_result = runtime.block_on(query_index_entry(query_key_word_set,
                                                                  question_set,
                                                                  query_index_types,
                                                                  token,
                                                                  projectUrl,
                                                                  product_type,
                                                                  top));
            println!("query_result : {:?}", query_result);
        }

        ///查询单个文件
        #[test]
        fn test_query_file() {
            let project_url = &"/Users/<USER>/Downloads/alispace/codeFuse".to_string();
            // let mut data_vec = get_all_file_by_url(project_url);
            // println!("data_vec: {:?}", data_vec.len());
            // /Users/<USER>/Downloads/alispace/caselike/app/service/src/main/java/com/alipay/tsingyanprod/service/antlr/gen/python
            // let file_url = "/Usrs/wuguo/Downloads/alispace/caselike/app/service/src/main/java/com/alipay/tsingyanprod/service/antlr/gen/python/Python3LexerBase.java".to_string();
            let file_url = "/Users/<USER>/Downloads/alispace/caselke/app/web/src/main/java/com/alipay/tsingyanprod/web/FunctionController.java".to_string();
            let mut query_param = HashMap::new();
            query_param.insert("id".to_string(), file_url.clone());
            let file_vec_result = FILE_CLIET.query(&query_param, 1);
            match file_vec_result {
                Ok(rst) => {
                    println!("rst.len : {}", rst.len());
                }
                Err(e) => {
                    println!("error {}", e);
                }
            }

            let runtime = tokio::runtime::Runtime::new().unwrap();

            let by_id_regex = runtime.block_on(FILE_CLIET.query_by_id_regex("", file_url.as_str()));
            if let Ok(rst) = by_id_regex {
                println!("by_id_regex: {:?}", rst.len());
            }

            let mut query_in_project = QueryInProject::default();
            query_in_project.project_url = "/Users/<USER>/Downloads/alispace/caselike".to_string();
            let file_count = runtime.block_on(FILE_CLIET.query_count_in_project(query_in_project));
            if let Ok(count) = file_count {
                println!("count: {:?}", count);
            }
        }

        #[test]
        fn test_search_file() {
            // let runtime = tokio::runtime::Runtime::new().unwrap();
            // runtime.block_on(test_git_struct());
            let project_url = "/Users/<USER>/Downloads/alispace/harmonyTestDemo";
            // let project_url = "/Users/<USER>/Downloads/alispace/totoro-java-client";
            // let project_url_reg = "/Users/<USER>/Downloads/alispace/totoro-java-client.*";
            let project_url_reg = "/Users/<USER>/Downloads/alispace/harmonyTestDemo.*";
            // let project_url = "/Users/<USER>/Downloads/alispace/rustproject";
            // let path = "/Users/<USER>/Downloads/alispace/totoro-java-client/src/main/java/com/totoro/client/deeplearning/ImageRecognitionHelper.java";
            // let path = "/Users/<USER>/Downloads/alispace/harmonyTestDemo/src/main/java/misc/TestEntry001.java";
            let path = "/Users/<USER>/Downloads/alispace/harmonyTestDemo/src/main/java/parser/JavaParserTest.java";

            let runtime = tokio::runtime::Runtime::new().unwrap();
            // let query_result_rst = runtime.block_on(FILE_CLIET.query_by_id_regex(project_url, path));
            // let query_result_rst = runtime.block_on(METHOD_CLIENT.query_by_id_regex(project_url, get_file_method_vector_prefix(path.to_string()).as_str()));
            let reg_prefix = get_file_chunk_vector_prefix(path);
            println!("reg_prefix: {}", reg_prefix);
            let chunk_reg = format!("{}{}", reg_prefix, ".*").trim().to_string();
            println!("chunk_reg: {}", chunk_reg);
            let query_result_rst = runtime.block_on(FILE_CLIET.query_by_id_regex(project_url, project_url));
            if let Ok(query_result) = query_result_rst {
                println!("query_result.len: {}", query_result.len());
                for (score, result) in query_result {
                    let file = doc_to_file(score, result);
                    // let file = doc_to_method(score, result);
                    // let file = doc_to_chunk(score, result);
                    println!("{}: {}", file.data.id, file.data.id);
                    // println!("file: {:?}", file.data.id);
                    // println!("file is same : {:?}", path == file.data.id);
                }
            }
            println!("==========================================================================");
            //数据查询
            let mut query_in_project = QueryInProject::default();
            query_in_project.project_url = project_url.to_string();

            let file_count = runtime.block_on(FILE_CLIET.query_count_in_project(query_in_project.clone())).unwrap();

            let method_count = runtime.block_on(METHOD_CLIENT.query_count_in_project(query_in_project.clone())).unwrap();

            let chunk_count = runtime.block_on(CHUNK_CLIENT.query_count_in_project(query_in_project.clone())).unwrap();

            println!("file_count : {}, method_count : {}, chunk_count : {}", file_count, method_count, chunk_count);
        }
    }
    }

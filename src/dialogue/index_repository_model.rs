use serde::{Deserialize, Serialize};

//仓库索引请求信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct IndexRepositoryRequestBean {
    // 仓库地址
    pub projectUrl: String,

    // 仓库分支
    pub branch: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LocalIndexQueryBean {
    // 查询内容
    pub content_string: String,

    pub summary_string: String,

    pub annotate_string: String,

    pub use_chunk: bool,

    pub use_method: bool,

    pub use_file: bool,

    pub use_vector: bool,

    pub top_num: usize,

}
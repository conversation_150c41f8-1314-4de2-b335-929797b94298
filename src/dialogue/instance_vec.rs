
///每个文件需要清空，重新统计,用来生成method的id
pub struct SharedVec {
    data: Vec<String>,
}

impl SharedVec {
    pub fn new() -> Self {
        SharedVec {
            data: Vec::new()
        }
    }

    pub  fn add(&mut self, value: String) {
        self.data.push(value);
        //测试释放可重入锁
        //let item = self.pop_first();
        //println!("{:?}", item);
    }

    pub  fn pop_first(&mut self) -> Option<String> {
        if self.data.len() > 0 {
            Some(self.data.remove(0))
        }else{
            None
        }
    }

    pub  fn remove(&mut self, index: usize) -> Option<String> {
        if index < self.data.len() {
            Some(self.data.remove(index))
        } else {
            None
        }
    }

    pub fn get_latest(&self) -> Option<String> {
        self.data.last().cloned()
    }

     pub  fn remove_last(&mut self) -> Option<String> {
         let mut len = self.data.len();
        if len > 0 {
            Some(self.data.remove(len - 1))
        } else {
            None
        }
    }

    // Clone the Vec to return a copy of the data
    pub  fn get_all(&self) -> Option<Vec<String>> {
        Some(self.data.clone())
    }

    //清除所有数据
    pub  fn clear_all(&mut self) {
        self.data.clear();
    }
}

#[cfg(test)]
mod tests {
    use crate::dialogue::instance_vec::SharedVec;
    use std::sync::Arc;

    #[test]
    fn test_001(){
        let shared_vec = Arc::new(SharedVec::new());
        // let mut handles = vec![];

        let rst = shared_vec.get_all();
        println!("{:?}", rst);

        // 创建线程进行操作
        // for i in 0..5 {
        //     let shared_vec_clone = Arc::clone(&shared_vec);
        //     let handle = thread::spawn(move || {
        //         shared_vec_clone.add(format!("String {}", i));
        //     });
        //     handles.push(handle);
        // }
        //
        // // 等待所有线程完成
        // for handle in handles {
        //     handle.join().unwrap();
        // }
        //
        // // 显示最新数据
        // if let Some(latest) = shared_vec.get_latest() {
        //     println!("Latest string: {}", latest);
        // }
        //
        // // 打印所有数据
        // let all_data = shared_vec.get_all();
        // println!("All data: {:?}", all_data);
        //
        // // 删除一个字符串并显示结果
        // if let Some(removed) = shared_vec.remove(2) {
        //     println!("Removed string: {}", removed);
        // } else {
        //     println!("No string removed.");
        // }
        //
        // // 再次打印所有数据
        // let all_data_after_removal = shared_vec.get_all();
        // println!("All data after removal: {:?}", all_data_after_removal);
    }
}
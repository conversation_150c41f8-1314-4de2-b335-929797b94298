use crate::ast::java::strategy::CodeFuseJavaAstStrategy;
use crate::ast::js::constant::JS_VALID_EXTENSIONS;
use crate::ast::js::strategy::CodeFuseJsAstStrategy;
use crate::dialogue::codefuse_index_repository::{build_chunk_index, build_file_index, CHUNK_CLIENT, CHUNK_FIELDS, FILE_CLIET, METHOD_CLIENT, METHOD_FIELDS};
use crate::dialogue::misc_util::{autonomous_sleep_no_bolck, get_file_chunk_vector_prefix, get_file_method_vector_prefix, get_parallel_num};
use crate::dialogue::repo_index_operator::is_need_interrupt_repo_build;
use crate::function::chat_strategy::TAB_SWITCH_TASK_TYPE;
use crate::utils::parallel_file_scan::{CHAT_INDEX_SKIP_ARR, MAX_VECTOR};
use crate::utils::file_encoding::read_file_smart_sync;
use agent_common_service::service::code_ast_analysis::AstStrategy;
use agent_db::config::runtime_config::{AgentConfig, AGENT_CONFIG};
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::dal::remote_client::IndexTypeEnum::{CHUNK_CONTENT, CHUNK_VECTOR, FILE_CONTENT, METHOD_CONTENT};
use agent_db::dal::remote_client::{vec_to_hashset, IndexTypeEnum};
use agent_db::dal::vector_client::{VectorClient, VectorItem, VECTOR_CLIENT, VECTOR_FIELD_ID};
use agent_db::domain::code_chat_domain::{CodefuseChunk, CodefuseFile, CodefuseMethod};
use agent_db::remote::http_client::post_request;
use agent_db::tools::common_tools::LINE_ENDING;
use futures::{stream, StreamExt};
use lazy_static::lazy_static;
use log::{error, info};
use std::collections::{HashMap, HashSet};
use std::fs;
use std::path::Path;
use tokio::sync::Mutex;

///从文本索引库里删除一个文件，包括相关的file，chunk, method， vector, sumary vector
pub async fn delete_file_from_index(project_url: &str, absolute_file_path: &str) {
    //删除文件
    let mut codefuse_file = CodefuseFile::default();
    codefuse_file.id = absolute_file_path.to_string();
    let _ = FILE_CLIET.delete(&codefuse_file);
    //删除文件summary, 文件summary存在索引库

    //删除chunk
    let query_chunk_result_rst = CHUNK_CLIENT.query_by_id_regex(project_url, get_file_chunk_vector_prefix(absolute_file_path).as_str()).await;
    match query_chunk_result_rst {
        Ok(query_chunk_result) => {
            let mut chunk_container = Vec::new();
            for (score, doc ) in query_chunk_result {
                chunk_container.push(CHUNK_FIELDS.convert_to_chunk(doc));
            }
            let _ = CHUNK_CLIENT.delete_batch(chunk_container.as_slice());
        }
        Err(e) => {error!("query chunk by id_prefix error, {} ", e)}
    }
    //删除chunk对应的vector
    let file_chunk_vector_prefix = get_file_chunk_vector_prefix(absolute_file_path);
    let _ = KV_CLIENT.delete_from_prefix(&file_chunk_vector_prefix);


    //删除method
    let query_method_result_rst = METHOD_CLIENT.query_by_id_regex(project_url, get_file_method_vector_prefix(absolute_file_path.to_string()).as_str()).await;
    match query_method_result_rst {
        Ok(query_method_result) => {
            let mut method_container = Vec::new();
            for (score, doc ) in query_method_result {
                method_container.push(METHOD_FIELDS.convert_to_method(doc));
            }
            let _ = METHOD_CLIENT.delete_batch(method_container.as_slice());
        }
        Err(e) => {error!("query method by id_prefix error, {} ", e)}
    }

}

pub async  fn delete_chunk_from_index( chunks: Vec<CodefuseChunk>) {
    //删除索引库数据，包括content， summary
    let _ = CHUNK_CLIENT.delete_batch(chunks.as_slice());
    //删除chunk的vector
    for chunk in &chunks {
        VECTOR_CLIENT.delete_with_key_and_value(VECTOR_FIELD_ID,chunk.id.as_str()).await;
    }

}
pub async  fn delete_method_from_index( methods: Vec<CodefuseMethod>) {
    //删除索引数据，包括content， summary
    let _ = METHOD_CLIENT.delete_batch(methods.as_slice());
    //删除method的vector
    for chunk in &methods {
        VECTOR_CLIENT.delete_with_key_and_value(VECTOR_FIELD_ID,chunk.id.as_str()).await;
    }

}
///比对两个chunk数据， 赛选出要保存， 更新，删除的数据
fn compare_chunk(chunks_index: Vec<CodefuseChunk>, chunks_in_db: Vec<CodefuseChunk>) -> (Vec<CodefuseChunk>, Vec<CodefuseChunk>, Vec<CodefuseChunk>){
    let mut chunk_to_save = Vec::new();
    let mut chunk_to_update = Vec::new();
    let mut chunk_to_delete = Vec::new();
    if chunks_index.len() > chunks_in_db.len() {
        let sub_chunk = chunks_index[chunks_in_db.len()..chunks_index.len()].to_vec();
        chunk_to_save.extend(sub_chunk);
        for (a, b) in chunks_in_db.iter().enumerate() {
            let chunk_from_db =  b;
            if let Some(chunk_from_file) =  chunks_index.get(a) {
                if chunk_from_db.hash !=  chunk_from_file.hash {
                    chunk_to_update.push(chunk_from_file.clone());
                }
            }
        }
    } else if chunks_index.len() == chunks_in_db.len() {
        for (a, b) in chunks_in_db.iter().enumerate() {
            let chunk_from_db =  b;
            // let chunk_from_file = chunks_index.get(a).unwrap();
            if let Some(chunk_from_file) =  chunks_index.get(a) {
                if chunk_from_db.hash !=  chunk_from_file.hash {
                    chunk_to_update.push(chunk_from_file.clone());
                }
            }
        }
    } else if chunks_index.len() < chunks_in_db.len() {
        let sub_chunk = chunks_in_db.get(chunks_index.len()..chunks_in_db.len()).unwrap().to_vec();
        chunk_to_delete.extend(sub_chunk);
        for (a, b) in chunks_index.iter().enumerate() {
            let chunk_from_file =  b;
            if let Some(chunk_from_db) = chunks_in_db.get(a) {
                if chunk_from_file.hash !=  chunk_from_db.hash {
                    chunk_to_update.push(chunk_from_file.clone());
                }
            }
        }
    }
    (chunk_to_save, chunk_to_update, chunk_to_delete)
}

///比对两个method数据， 赛选出要保存， 更新，删除的数据
fn compare_method(methods_index: Vec<CodefuseMethod>, methods_in_db: Vec<CodefuseMethod>) -> (Vec<CodefuseMethod>, Vec<CodefuseMethod>, Vec<CodefuseMethod>) {
    // 将 Vec 转换为 HashMap，键为 id
    let map_index: HashMap<String, &CodefuseMethod> = methods_index.iter().map(|m| (m.id.clone(), m)).collect();
    let map_db: HashMap<String, &CodefuseMethod> = methods_in_db.iter().map(|m| (m.id.clone(), m)).collect();

    let mut both_exist_vec = Vec::new();
    //要保存的数据
    let mut method_to_save = Vec::new();
    //要删除的数据
    let mut method_to_delete = Vec::new();
    //要更新的数据
    let mut method_to_update = Vec::new();

    //找出共有的id及status不等的
    for (id, method_index) in &map_index {
        if let Some(method_db) = map_db.get(id) {
            if method_index.id == method_db.id {
                both_exist_vec.push(method_index.clone());
                if method_index.hash != method_db.hash {
                    method_to_update.push(method_index.clone());
                }
            }
        } else {
            //仅在methods_index中存在
            method_to_save.push(method_index.clone());
        }
    }

    //找出仅在map_db中存在的
    for (id, method_db) in &map_db {
        if !map_index.contains_key(id) {
            method_to_delete.push(method_db.clone());
        }
    }

    (method_to_save.into_iter().cloned().collect(),
     method_to_update.into_iter().cloned().collect(),
     method_to_delete.into_iter().cloned().collect())
}


///更新文件
pub async fn update_index_by_file( absolute_file_path: &String, source_content: &String, project_url:  &String, branch: &str, token: &str, product_type: &str, mill_sleep_time: usize, index_type_set: &HashSet<IndexTypeEnum>, task_type: u8){
    let file_index_rst = build_file_index(Path::new(absolute_file_path), source_content, project_url, branch);
    match file_index_rst {
        Ok(file_index) => {
            update_index_by_codefuse_file( &file_index, project_url, branch, token, product_type, mill_sleep_time, index_type_set, task_type).await
        }
        Err(e) => { error!("build_file_index error when git diff, {}", e) }
    }
}

pub async fn update_index_by_codefusefile_vec(codefuse_files: &[CodefuseFile], project_url: &str, branch: &str, token: &str,product_type: &str, mill_sleep_time: usize, index_type_set: &HashSet<IndexTypeEnum>, task_type: u8) {
    let mut chunk_batch_save = vec![];
    let mut chunk_batch_update = vec![];
    let mut chunk_batch_delete = vec![];
    let mut method_batch_save = vec![];

    for codefuse_file in codefuse_files {
        let need_interrupt_build = is_need_interrupt_repo_build(&project_url.clone().to_string(), &branch.clone().to_string(), task_type).await;
        if need_interrupt_build {
            info!("update_index_by_codefusefile_vec, current project and url has changed, end the process");
            return;
        }
        let absolute_file_path = Path::new(codefuse_file.id.as_str());
        let relative_path_str = match absolute_file_path.strip_prefix(Path::new(project_url)) {
            Ok(rel_path) => rel_path.to_string_lossy().to_string(),
            Err(_) => absolute_file_path.to_string_lossy().to_string(),
        };
        let relative_path = Path::new(relative_path_str.as_str());

        ///更新chunk
        if index_type_set.contains(&CHUNK_CONTENT) {
            let line_vec: Vec<String> = codefuse_file.content.clone().split(LINE_ENDING).map(|s| s.to_string()).collect();
            let chunks_index = build_chunk_index(relative_path, &line_vec, project_url, branch).unwrap();
            let query_chunk_result_rst = CHUNK_CLIENT.query_by_id_regex(project_url, absolute_file_path.to_str().unwrap()).await;

            let mut chunks_in_db = Vec::new();
            match query_chunk_result_rst {
                Ok(query_chunk_result) => {
                    for (score, doc) in query_chunk_result {
                        let chunk_in_db = CHUNK_FIELDS.convert_to_chunk(doc);
                        chunks_in_db.push(chunk_in_db);
                    }
                }
                Err(e) => { error!("query_file_result_rst: {:?}", e); }
            }
            //排序
            chunks_in_db.sort_by(|a, b| a.id.cmp(&b.id));
            let (chunk_to_save, chunk_to_update, chunk_to_delete) = compare_chunk(chunks_index, chunks_in_db);
            //保存
            chunk_batch_save.extend(chunk_to_save);
            // //更新
            chunk_batch_update.extend(chunk_to_update);
            //删除
            chunk_batch_delete.extend(chunk_to_delete);
        }
    }
    info!("chunk_batch_save len: {}, chunk_batch_update len: {}, chunk_batch_delete len: {}", chunk_batch_save.len(), chunk_batch_update.len(), chunk_batch_delete.len());
    //删除chunk
    delete_chunk_from_index(chunk_batch_delete).await;
    //保存
    save_index_data_async(vec![], method_batch_save,  chunk_batch_save, project_url, token, product_type, mill_sleep_time, index_type_set).await;
    //更新
    let _ = CHUNK_CLIENT.update_batch(chunk_batch_update.as_slice());
    if index_type_set.contains(&CHUNK_VECTOR) {
        update_chunk_vector_async(&mut chunk_batch_update, token, product_type).await;
    }
    info!("force invoke manual_cleanup in update_index_by_codefusefile_vec");
    let _ = VECTOR_CLIENT.manual_cleanup().await;
}

pub async fn update_index_by_codefuse_file( codefuse_file: &CodefuseFile, project_url: &str, branch: &str, token: &str,product_type: &str, mill_sleep_time: usize, index_type_set: &HashSet<IndexTypeEnum>, task_type: u8) {
    let mut files = Vec::new();
    files.push(codefuse_file.clone());
    update_index_by_codefusefile_vec(files.as_slice(), project_url, branch, token, product_type, mill_sleep_time, index_type_set, task_type).await;
}




pub async fn update_chunk_vector_async(chunks_index: &mut Vec<CodefuseChunk>, token: &str, product_type: &str) {
    if chunks_index.len() == 0 {
        return;
    }

    // 第一步：向量化代码片段
    let vector_items_result = VECTOR_CLIENT.embedding_chunk_vec(chunks_index, &token.to_string(), &product_type.to_string()).await;
    match vector_items_result {
        Ok(vector_items) => {
            // 第二步：保存向量数据
            let save_result = VECTOR_CLIENT.batch_upsert(vector_items).await;


            if let Err(e) = save_result {
                error!("save_vector_vec error, err:{}", e);
                return;
            }

            // 更新chunk状态
            let mut chunk_container = Vec::new();
            for mut chunk in chunks_index {
                chunk.has_content_vector = 1;
                chunk_container.push(chunk.clone());
            }
            CHUNK_CLIENT.update_batch(chunk_container.as_slice());
        }
        Err(e) => {
            error!("embedding_chunk_vec error, err:{}", e);
        }
    }
}


pub async fn save_index_data_async(save_batch_file: Vec<CodefuseFile>, save_batch_method: Vec<CodefuseMethod>, save_batch_chunk: Vec<CodefuseChunk>, project_url: &str, token: &str, product_type: &str, mill_sleep_time: usize, index_type_set: &HashSet<IndexTypeEnum>) {
    for index_type in index_type_set.clone() {
        match index_type {
            CHUNK_CONTENT => {
                let save_batch_chunk_clone = save_batch_chunk.clone();
                save_batch_chunk_clone.chunks(crate::utils::parallel_file_scan::MAX_NUM).for_each(|file_arr| {
                    let _ = CHUNK_CLIENT.save_batch(file_arr);
                });
            }
            //
            CHUNK_VECTOR => {
                let max_concurrency = get_parallel_num(mill_sleep_time); // 并发的任务数，可以根据需要调节
                stream::iter(save_batch_chunk.clone())
                    .chunks(MAX_VECTOR)
                    .for_each_concurrent(max_concurrency, |chunk_arr| {
                        let token_clone = token.clone();
                        let product_type_clone = product_type.clone();
                        async move {
                            info!("invoke update_chunk_vector_async in async context");
                            let mut chunks_to_save = chunk_arr.to_vec();
                            update_chunk_vector_async(&mut chunks_to_save, &token_clone, &product_type_clone).await;
                        }
                    }).await;
            }
            _ => {}
        }
    }
}

pub async  fn incremental_update_file(fileUrl: &String, project_url :&String, source_content: &String) {
    info!("incremental_update_file, fileUrl: {}, project_url: {}", fileUrl, project_url);
    let path = Path::new(fileUrl);
    if !path.exists() || !path.is_file() {
        info!("not exist or not a file , return");
        return;
    }
    //检查后缀
    let file_name_suffix = path.extension().and_then(|ext| ext.to_str()).unwrap_or("__UNKNOWN_FUFFIX__");
    if CHAT_INDEX_SKIP_ARR.contains(file_name_suffix) {
        info!("file suffix is in skip arr, return");
        return;
    }

    // 检查文件后缀是否在指定配置范围内
    if !(AGENT_CONFIG.similarity_suffix_arr.contains(&file_name_suffix) || AGENT_CONFIG.related_suffix_arr.contains(&file_name_suffix)) {
        info!("file suffix {} not in configured suffix arrays, return", file_name_suffix);
        return;
    }

    // 检查路径是否包含测试目录
    let path_str = fileUrl.as_str();
    if path_str.contains("src/test") || path_str.contains("src\\test") {
        info!("file path contains test directory, return");
        return;
    }

    // 检查文件名是否包含Test
    if let Some(file_name) = path.file_name().and_then(|name| name.to_str()) {
        if file_name.contains("Test") {
            info!("file name contains 'Test', return");
            return;
        }
    }

    //文件路径不在工程目录下，直接返回
    if !fileUrl.starts_with(project_url) {
        info!("not under target project {} , return", project_url);
        return;
    }
    //是否为空
    if source_content.is_empty() {
        info!("source_content is empty, return");
        return;
    }
    //是否超过大小限制
    let source_content_size = source_content.len() as u64;
    if source_content_size  > AGENT_CONFIG.scan_skip_file_size {
        info!("source_content exceed max size, return");
        return;
    }
    //检查文件行数
    let line_vec: Vec<String> = source_content.split(LINE_ENDING).map(|s| s.to_string()).collect();
    if line_vec.len() > AGENT_CONFIG.scan_skip_file_max_len {
        info!("source_content line size exceed max size, return");
        return;
    }

    let nessary_index_set: HashSet<IndexTypeEnum> =
        AGENT_CONFIG.index_nessary_type.iter().cloned().collect();
    let mut extra_type_set:HashSet<IndexTypeEnum> = AGENT_CONFIG.index_extra_type.iter().cloned().collect();
    extra_type_set.extend(nessary_index_set);
    //tab页切换带来单文件更新，task_type=99 ， 避开对当前project_url, branch的检查
    update_index_by_file( &fileUrl, &source_content, &project_url, "", "", "", AGENT_CONFIG.extra_index_build_interval_time as usize, &extra_type_set, TAB_SWITCH_TASK_TYPE).await;
}



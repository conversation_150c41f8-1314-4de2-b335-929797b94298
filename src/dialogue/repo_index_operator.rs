use crate::dialogue::codefuse_index_repository::{build_file_index, CHUNK_CLIENT, FILE_CLIET, FILE_FIELDS, METHOD_CLIENT};
use crate::dialogue::data_struct::RepoStatusCache;
use crate::dialogue::file_index_operator::{ delete_file_from_index, update_index_by_codefuse_file, update_index_by_codefusefile_vec};
use crate::dialogue::index_db_utils::{delete_project, get_all_codefile_in_project, recover_chunk_has_content_vector_in_db, recover_method_has_content_vector_in_db, update_chunk_vector_by_project};
use crate::dialogue::misc_util::{accelerate_by_time, autonomous_sleep_no_bolck, autonomous_sleep_with_bolck, get_parallel_num};
use crate::dialogue::repo_index_operator::RepoStatusEnum::{CREATE, NOTHING, UPDATE};
use crate::function::chat_strategy::{query_count_in_kv, query_data_status, AUGMENT_TASK_TYPE, CHAT_TASK_MANAGER_V2, DEEPSEARCH_TASK_TYPE, TAB_SWITCH_TASK_TYPE};
use crate::utils::parallel_file_scan::{finish_sync_build_task_status, is_build_process_timeout, is_current_project_and_branch_entry};
use crate::utils::path::get_all_file_by_url;
use crate::utils::file_encoding::read_file_smart_sync;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::dal::remote_client::IndexTypeEnum::{CHUNK_CONTENT, CHUNK_VECTOR, FILE_CONTENT, METHOD_CONTENT};
use agent_db::dal::remote_client::{vec_to_hashset, IndexTypeEnum, QueryInProject};
use agent_db::domain::code_chat_domain::CodefuseFile;
use agent_db::tools::common_tools::{LINE_ENDING, V_C_PREFIX, V_M_PREFIX};
use futures::{stream, StreamExt};
use ignore::DirEntry;
use lazy_static::lazy_static;
use log::{error, info};
use once_cell::sync::Lazy;
use rayon::prelude::*;
use serde::{Deserialize, Serialize};
use serde_json::{from_str, Value};
use std::collections::{HashMap, HashSet, VecDeque};
use std::iter::Map;
use std::path::Path;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use agent_common_service::model::chat_model::IndexOperateRequestBean;
use tokio::fs;
use tokio::sync::{Mutex, RwLock};

static CACHE_INIT_SWITCHER: Lazy<Arc<AtomicBool>> = Lazy::new(|| Arc::new(AtomicBool::new(false)));

pub const CACHE_KEY: &str = "KEY_GIT_REPO_CONTAINER";
pub const PAR_PER_CHUNK_NUM: usize = 5;


lazy_static! {
   pub static ref GIT_REPO_CONTAINER: LRUCacheWrapper<String, RepoInfo> = LRUCacheWrapper::new(5);
}

///存储repo的是否在构建的状态
lazy_static! {
   pub static ref GIT_REPO_STATUS: LRUCacheWrapper<RepoInfo, RepoStatusCache> = LRUCacheWrapper::new(10);
}
///创建索引的时候需要调一次，后面有分支切换的时候再进行调用
///由于会有更新不及时的情况，通过分支对比的方式不采用
pub async fn monitor_git_repo_branch(project_url: &String, branch_name: &String) -> (u8,RepoStatusEnum) {
    if CACHE_INIT_SWITCHER.compare_exchange(false, true, Ordering::SeqCst, Ordering::SeqCst).is_ok() {
        init_git_repo_cache().await
    }
    let req = IndexOperateRequestBean {
        projectUrl: Some(project_url.clone()),
        branch: Some(branch_name.clone()),
        printDetail:Some(false),
        diffAnalysis: Some(false),
    };
    let m1 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    let build_process = query_data_status(req).await.build_index_progress_inner;
    let m2 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    info!("m1: {}",m2-m1);
    if GIT_REPO_CONTAINER.contains(project_url.to_string()).await {
        let git_repo_info_opt = GIT_REPO_CONTAINER.get(&project_url.to_string()).await;
        //更新LRU缓存数据, 取到值后再保存
        save_git_repo_cache(project_url, branch_name).await;
        match git_repo_info_opt {
            Some(git_repo_info) => {
                if branch_name.clone() != git_repo_info.current_branch {
                    (build_process,UPDATE)
                } else {
                    if build_process == 0{
                        return (build_process,CREATE);
                    }
                    if build_process >=70{
                        return (build_process,NOTHING);
                    }
                    (build_process,UPDATE)
                }
            }
            None => {
                info!("can not find git_repo_info in cache, please check");
                (build_process,UPDATE)
            }
        }
    }else {
        //更新LRU缓存数据, 取到值后再保存
        save_git_repo_cache(project_url, branch_name).await;
        (build_process,CREATE)
    }
}



#[derive(Clone, Debug)]
pub struct VecWrapper<T> {
    data: Arc<RwLock<Vec<T>>>,
}

impl<T> VecWrapper<T> {
    pub(crate) fn new() -> Self {
        VecWrapper {
            data: Arc::new(RwLock::new(Vec::new())),
        }
    }

    pub async fn push(&self, item: T) {
        let mut vec = self.data.write().await;
        vec.push(item);
    }

    pub async fn extend<I: IntoIterator<Item = T>>(&self, items: I) {
        let mut vec = self.data.write().await;
        vec.extend(items);
    }

    pub async fn remove(&self, index: usize) -> Option<T> {
        let mut vec = self.data.write().await;
        if index < vec.len() {
            Some(vec.remove(index))
        } else {
            None
        }
    }

    pub async fn get(&self, index: usize) -> Option<T>
    where
        T: Clone,
    {
        let vec = self.data.read().await;
        vec.get(index).cloned()
    }

    pub async fn len(&self) -> usize {
        let vec = self.data.read().await;
        vec.len()
    }

    pub async fn is_empty(&self) -> bool {
        let vec = self.data.read().await;
        vec.is_empty()
    }

    pub async fn clear(&self) {
        let mut vec = self.data.write().await;
        vec.clear();
    }

    pub async fn get_all(&self) -> Vec<T>
    where
        T: Clone,
    {
        let vec = self.data.read().await;
        vec.clone()
    }
}

#[derive(Serialize, Deserialize, Hash, Eq, PartialEq, Debug, Clone)]
pub enum RepoStatusEnum {
    CREATE,
    UPDATE,
    NOTHING //什么都不需要做
}

struct LRUCache<K, V> {
    capacity: usize,
    map: HashMap<K, V>,
    order: VecDeque<K>,
}

impl<K: Clone + Eq + std::hash::Hash, V: Clone> LRUCache<K, V> {
    fn new(capacity: usize) -> Self {
        Self {
            capacity,
            map: HashMap::new(),
            order: VecDeque::new(),
        }
    }

    fn get(&mut self, key: &K) -> Option<&V> {
        if let Some(pos) = self.order.iter().position(|k| k == key) {
            self.order.remove(pos);
            self.order.push_front(key.clone());
        }
        self.map.get(key)
    }

    fn put(&mut self, key: K, value: V)  -> Option<V> {
        let mut r = None;
        if self.map.contains_key(&key) {
            if let Some(pos) = self.order.iter().position(|k| k == &key) {
                self.order.remove(pos);
            }
        } else if self.map.len() >= self.capacity {
            if let Some(old_key) = self.order.pop_back() {
                r = self.map.remove(&old_key);
            }
        }
        self.order.push_front(key.clone());
        self.map.insert(key, value);
        r
    }

    fn delete(&mut self, key: &K) -> Option<V> {
        if let Some(pos) = self.order.iter().position(|k| k == key) {
            self.order.remove(pos);
        }
        self.map.remove(key)
    }

    fn contains(&self, key: &K) -> bool {
        self.map.contains_key(key)
    }

    pub fn get_all(&self) -> HashMap<K, V> {
        self.map.clone()
    }
}

/// 定义线程安全的 LRUCache
pub struct LRUCacheWrapper<K, V> {
    cache: Arc<RwLock<LRUCache<K, V>>>,
}

impl<K: Clone + Eq + std::hash::Hash + Send + Sync, V: Send + Sync + Clone> LRUCacheWrapper<K, V> {
    pub fn new(capacity: usize) -> Self {
        Self {
            cache: Arc::new(RwLock::new(LRUCache::new(capacity))),
        }
    }

    pub async fn get(&self, key: &K) -> Option<V>
    where
        V: Clone,
    {
        let mut write_guard = self.cache.write().await;
        write_guard.get(key).cloned()
    }

    pub async fn put(&self, key: K, value: V) -> Option<V> {
        let mut write_guard = self.cache.write().await;
        write_guard.put(key, value)
    }

    pub async fn delete(&self, key: &K) -> Option<V> {
        let mut write_guard = self.cache.write().await;
        write_guard.delete(key)
    }

    pub async fn contains(&self, key: K) -> bool {
        let mut write_guard = self.cache.read().await;
        write_guard.contains(&key)
    }

    pub async fn get_all(&self) -> HashMap<K, V>
    where
        V: Clone,
    {
        let mut write_guard = self.cache.read().await;
        write_guard.get_all()
    }

}

//仓库信息，如果不受git管控，branch默认是
#[derive(Debug, Serialize, Deserialize, Clone)]
#[derive(Eq, Hash, PartialEq)]
pub struct RepoInfo {
    pub project_url: String,
    pub current_branch: String,
}
impl RepoInfo {
    pub fn new(project_url: String, branch_name: String) -> RepoInfo {
        Self {
            project_url,
            current_branch: branch_name,
        }
    }
}

async fn init_git_repo_cache() {
    let git_repo_cache_rst = KV_CLIENT.get(&CACHE_KEY.to_string());
    match git_repo_cache_rst {
        Ok(v) => {
            if let Some(content) = v {
                info!("init_git_repo_cache content : {}", content);
                if content.len() > 0 {
                    let repo_info = load_repo_info(content.as_str());
                    for (k, v) in repo_info {
                        GIT_REPO_CONTAINER.put(k, v).await;
                    }
                }
            }else {
                error!("content is empty");
            }
        }
        Err(e) => {error!("git_repo_cache_rst error : {:?}", e)}
    }
}
//保存到内存，同时也更新到kv库
pub async fn save_git_repo_cache(project_url: &str, branch: &str) {
    let mut git_repo_info_new = RepoInfo::new(project_url.to_string(), branch.to_string());
    let extra_git_repo_info_opt = GIT_REPO_CONTAINER.put(project_url.to_string(), git_repo_info_new).await;
    let m = GIT_REPO_CONTAINER.get_all().await;
    let s_rst = serde_json::to_string(&m);
    info!("save_git_repo_cache : {:?}, extra_git_repo_info_opt: {:?}", s_rst, extra_git_repo_info_opt);
    if let Ok(s) = s_rst {
        let insert_result = KV_CLIENT.insert(&CACHE_KEY.to_string(), &s);
        if insert_result.is_err() {
            error!("save_git_repo_cache error : {:?}", insert_result.unwrap_err())
        }
    }
    //删除整个工程数据
    match extra_git_repo_info_opt {
        Some(extra_git_repo_info) => {
            delete_project(&extra_git_repo_info.project_url).await;
        }
        _ => {}
    }
}

///把缓存的数据转成HashMap<String, RepoInfo>结构
fn load_repo_info(content: &str) -> HashMap<String, RepoInfo> {
    let parsed_data: Value = serde_json::from_str(content).unwrap();
    let mut repo_map: HashMap<String, RepoInfo> = HashMap::new();

    let parsed_data_opt = parsed_data.as_object();
    if let Some(parsed_data) = parsed_data_opt {
        for (key, value) in parsed_data {
            let project_url = value.get("project_url").unwrap().as_str().unwrap().to_string();
            let current_branch = value.get("current_branch").unwrap().as_str().unwrap().to_string();
            repo_map.insert(
                key.clone(),
                RepoInfo {
                    project_url,
                    current_branch,
                },
            );
        }
    }
    repo_map
}

///获取对应的repo是否是在构建中
pub async fn get_repo_build_status(project_url: &String, branch: &String) -> bool{
    let repo_info = RepoInfo::new(project_url.to_string(), branch.to_string());
    let repo_status_cache_opt = GIT_REPO_STATUS.get(&repo_info).await;
    if let Some(repo_status_cache ) = repo_status_cache_opt {
        repo_status_cache.building
    }else{
        false
    }
}

///保存repo的status信息
pub async fn save_repo_build_status(project_url: &String, branch: &String, status: bool) {
    let repo_info = RepoInfo::new(project_url.to_string(), branch.to_string());
    let repo_status_cache_opt = GIT_REPO_STATUS.get(&repo_info).await;
    if let Some(mut repo_status_cache) = repo_status_cache_opt {
        repo_status_cache.building = status;
        GIT_REPO_STATUS.put(repo_info, repo_status_cache).await;
    }else{
        let mut repo_status_cache = RepoStatusCache::default();
        repo_status_cache.building = status;
        GIT_REPO_STATUS.put(repo_info, repo_status_cache).await;
    }
}
///获取repo delete的状态
pub async fn get_repo_delete_status(project_url: &String, branch: &String) -> bool{
    let repo_info = RepoInfo::new(project_url.to_string(), branch.to_string());
    let repo_status_cache_opt = GIT_REPO_STATUS.get(&repo_info).await;
    if let Some(repo_status_cache ) = repo_status_cache_opt {
        repo_status_cache.deleting
    }else{
        false
    }
}

///保存repo的delete status信息
pub async fn save_repo_delete_status(project_url: &String, branch: &String, status: bool) {
    let repo_info = RepoInfo::new(project_url.to_string(), branch.to_string());
    let repo_status_cache_opt = GIT_REPO_STATUS.get(&repo_info).await;
    if let Some(mut repo_status_cache) = repo_status_cache_opt {
        repo_status_cache.deleting = status;
        GIT_REPO_STATUS.put(repo_info, repo_status_cache).await;
    }else{
        let mut repo_status_cache = RepoStatusCache::default();
        repo_status_cache.deleting = status;
        GIT_REPO_STATUS.put(repo_info, repo_status_cache).await;
    }
}

///恢复git_repo_info信息
pub fn recover_git_repo_info() {
    tokio::spawn(async move {
        let rst =  FILE_CLIET.get_distinct_project().unwrap();
        for (k, v) in rst.iter() {
            save_git_repo_cache(k.as_str(), v.as_str()).await;
            ///恢复method在索引库里的flag
            recover_method_has_content_vector_in_db();
        }
    });
}
///恢复数据, 考虑到耗时问题， 暂时不用此方案
pub fn recover_data() {
    recover_git_repo_info();
    ///恢复chunk在索引库里的flag
    recover_chunk_has_content_vector_in_db();
}
///是否需要中断仓库的索引build的流程
/// 这里有两种情况需要中断，1. repo发生的变化 2. 用户触发了删除repo索引数据的操作，当前repo如果还在构建中，也需要中断
pub async fn is_need_interrupt_repo_build(project_url: &String, branch: &String, task_type: u8) -> bool {
    //tab页切换触发的文件更新，不进行project_url, branch的检查
    if TAB_SWITCH_TASK_TYPE == task_type {
        return false;
    }
    let is_current_project = is_current_project_and_branch_entry(&project_url.clone().to_string(), &branch.clone().to_string(), task_type).await;
    if !is_current_project {
        info!("current project and url has changed, end the process");
        return true;
    }
    if get_repo_delete_status(project_url, branch).await {
        info!("repo delete status is true, need end the build process");
        return true;
    }
    false
}

#[cfg(test)]
mod tests {
    use crate::dialogue::repo_index_operator::RepoInfo;
    use serde_json::Value;
    use std::collections::HashMap;

    ///repoInof数据增加字段的兼容性
    #[test]
    fn test_ext_import_info(){
        let info = r#"{"/Users/<USER>/Downloads/alispace/caselike":{"project_url":"/Users/<USER>/Downloads/alispace/caselike","current_branch":""}}"#;
        let parsed_data: Value = serde_json::from_str(info).unwrap();
        let mut repo_map: HashMap<String, RepoInfo> = HashMap::new();

        let parsed_data_opt = parsed_data.as_object();

        if let Some(parsed_data) = parsed_data_opt {
            for (key, value) in parsed_data {
                let project_url = value.get("project_url").unwrap().as_str().unwrap().to_string();
                let current_branch = value.get("current_branch").unwrap().as_str().unwrap().to_string();
                repo_map.insert(
                    key.clone(),
                    RepoInfo {
                        project_url,
                        current_branch,
                    },
                );
            }
        }

        println!("{}", serde_json::to_string_pretty(&repo_map).unwrap());

    }
}
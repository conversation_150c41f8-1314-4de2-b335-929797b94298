use crate::android_strategy::language_file_tyle_adapter::{fn_router_for_get_class_name, fn_router_for_prompt_related};
use agent_common_service::model::code_complete_model::{CodeCompletionRequestBean, ContextAwareInfo};
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_common_service::service::abstract_similarity::Similarity;
use agent_common_service::service::code_complete_cache::CodeCompleteCacheStrategy;
use agent_common_service::tools::code_tokenizer::code_snippet_tokenizer;
use agent_common_service::tools::common_tools::{get_file_name, get_parent_directory, CacheRelatedModuleEnum};
use agent_common_service::tools::jaccard_similarity::jaccard_similarity;
use agent_common_service::tools::related_module_score::CacheRelatedModule;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{ScanFileRecord, ScanSnippetRecord};
use std::collections::HashSet;
use agent_db::tools::common_tools::{SEPARATORSIMPLE, SNIPPET_PREFIX};

pub struct AndroidCodeCompleteCacheStrategy;

impl CodeCompleteCacheStrategy for AndroidCodeCompleteCacheStrategy {
    fn build_related_module_score(&self, code_complete_request: &CodeCompletionRequestBean, current_context: &PromptDeclaration, file_record: &ScanFileRecord) -> Option<CacheRelatedModule> {
        let file_name_suffix = &code_complete_request.fileNameSuffix.clone().unwrap();
        if let Some(current_method_info) = &current_context.method_declaration {
            //step 1 解析参数类型，默认得分6
            if let Some(param_vec) = &current_method_info.param_class_vec {
                let aware_data_opt = get_context_aware_info(false, 0.6, "methodParam".to_string(), file_record, param_vec, file_name_suffix);
                if let Some(aware_data) = aware_data_opt {
                    return CacheRelatedModule::from_tuple((Some(CacheRelatedModuleEnum::METHOD), Some(aware_data)));
                }
            }
            //step 2 解析函数体,默认得分5
            if let Some(inner_class_declaration) = &current_method_info.inner_class_declaration {
                //衰减标识为true，离光标位置越近越好
                let aware_data_opt = get_context_aware_info(true, 0.5, "methodInnerClass".to_string(), file_record, inner_class_declaration, file_name_suffix);
                if let Some(aware_data) = aware_data_opt {
                    return CacheRelatedModule::from_tuple((Some(CacheRelatedModuleEnum::METHOD), Some(aware_data)));
                }
            }
            //step 3 解析返回值类型，默认得分4
            if let Some(return_class) = &current_method_info.return_class {
                let return_vec = vec![return_class.to_string()];
                let aware_data_opt = get_context_aware_info(false, 0.4, "returnClass".to_string(), file_record, &return_vec, file_name_suffix);
                if let Some(aware_data) = aware_data_opt {
                    return CacheRelatedModule::from_tuple((Some(CacheRelatedModuleEnum::METHOD), Some(aware_data)));
                }
            }
        }
        //step 4 解析类变量，默认得分2
        if let Some(class_field_set) = &current_context.field_set {
            let class_field_vec: Vec<String> = class_field_set.iter().cloned().collect();
            let aware_data_opt = get_context_aware_info(false, 0.3, "classField".to_string(), file_record, &class_field_vec, file_name_suffix);
            if let Some(aware_data) = aware_data_opt {
                return CacheRelatedModule::from_tuple((Some(CacheRelatedModuleEnum::FIELD), Some(aware_data)));
            }
        }
        //step 5 解析类变量，默认得分2
        if let Some(extends_class) = &current_context.extends_class {
            let extends_vec = vec![extends_class.to_string()];
            let aware_data_opt = get_context_aware_info(false, 0.2, "extendClass".to_string(), file_record, &extends_vec, file_name_suffix);
            if let Some(aware_data) = aware_data_opt {
                return CacheRelatedModule::from_tuple((Some(CacheRelatedModuleEnum::EXTENDS), Some(aware_data)));
            }
        }
        //step 解析import信息 解析类变量，默认得分2
        if let Some(import_set) = &current_context.import_set {
            if file_record.code_info.is_some() {
                let code_info_value = file_record.code_info.clone().unwrap();
                let full_qulified_opt = code_info_value.full_qualified_name.clone();
                if let Some(full_qulified_value) = full_qulified_opt {
                    if import_set.contains(&full_qulified_value) {
                        let item = ContextAwareInfo {
                            filePath: file_record.file_url.clone(),
                            content: code_info_value.code_struct.clone(),
                            score: 0.1,
                            extraData: Some("importClass".to_string()),
                        };
                        return CacheRelatedModule::from_tuple((Some(CacheRelatedModuleEnum::IMPORT), Some(item)));
                    }
                }
            }
        }
        return CacheRelatedModule::from_tuple((None, None));
    }

    fn get_target_similarity_score_data(&self, scan_file_record: &ScanFileRecord, cur_score_data: &(HashSet<u32>, HashSet<u32>, HashSet<u32>)) -> (Option<Similarity<Option<String>>>, Option<Similarity<Option<String>>>, Option<Similarity<Option<String>>>) {
        let mut packge_similarity_opt: Option<Similarity<Option<String>>> = None;
        let mut import_similarity_opt: Option<Similarity<Option<String>>> = None;
        let mut name_similarity_opt: Option<Similarity<Option<String>>> = None;

        let target_url = get_parent_directory(scan_file_record.file_url.clone()).unwrap();
        let target_url_token_result = code_snippet_tokenizer(target_url, 0, 0, 0, "", &"".to_string()).unwrap();
        let target_url_token_set: HashSet<u32> = target_url_token_result.id_vec.into_iter().collect();

        let package_score = jaccard_similarity(&cur_score_data.0, &target_url_token_set);
        if package_score > AGENT_CONFIG.similarity_threshold {
            packge_similarity_opt = Some(Similarity {
                sim_score: package_score,
                value: None,
                file_url: Some(scan_file_record.file_url.clone()),
            });
        }

        if scan_file_record.total_snippet_num >= 2 {
            let first_key = format!("{}{}{}{}", SNIPPET_PREFIX, scan_file_record.file_url, SEPARATORSIMPLE, 1);
            let snippet_record_result = KV_CLIENT.get(&first_key);
            if snippet_record_result.is_ok() {
                let snippet_record_result_opt = snippet_record_result.unwrap();
                if snippet_record_result_opt.is_some() {
                    let snippet_record_result_str = snippet_record_result_opt.unwrap();
                    let scan_snippet_record: ScanSnippetRecord = serde_json::from_str(&snippet_record_result_str).unwrap();
                    let import_score = jaccard_similarity(&cur_score_data.1, &scan_snippet_record.id_vec.into_iter().collect());
                    if import_score > AGENT_CONFIG.similarity_threshold {
                        import_similarity_opt = Some(Similarity {
                            sim_score: import_score,
                            value: None,
                            file_url: Some(scan_file_record.file_url.clone()),
                        });
                    }
                }
            }
        }

        let file_name = get_file_name(&scan_file_record.file_url).unwrap();
        let target_name_token_result = code_snippet_tokenizer(file_name.to_string(), 0, 0, 0, "", &"".to_string()).unwrap();
        let target_name_token_set: HashSet<u32> = target_name_token_result.id_vec.into_iter().collect();
        let name_score = jaccard_similarity(&cur_score_data.2, &target_name_token_set);
        if name_score > AGENT_CONFIG.similarity_threshold {
            name_similarity_opt = Some(Similarity {
                sim_score: name_score,
                value: None,
                file_url: Some(scan_file_record.file_url.clone()),
            });
        }
        return (packge_similarity_opt, import_similarity_opt, name_similarity_opt);
    }

    fn is_need_build_related(&self, file_suffix: &String, current_data: &PromptDeclaration) -> bool {
        let prompt_related_fn = fn_router_for_prompt_related(&file_suffix);
        return prompt_related_fn(current_data);
    }
}

fn get_context_aware_info(decay_flag: bool, score: f64, desc_str: String, file_record: &ScanFileRecord, class_name_iter: &Vec<String>, file_name_suffix: &String) -> Option<ContextAwareInfo> {
    let find_class_fn = fn_router_for_get_class_name(file_name_suffix);
    let hit = find_class_fn(file_record, class_name_iter);
    if hit.0 {
        return if decay_flag {
            Some(ContextAwareInfo {
                filePath: file_record.file_url.clone(),
                content: file_record.code_info.clone()?.code_struct,
                score: score + 0.1 * hit.1 as f64,
                extraData: Some(desc_str),
            })
        } else {
            Some(ContextAwareInfo {
                filePath: file_record.file_url.clone(),
                content: file_record.code_info.clone()?.code_struct,
                score,
                extraData: Some(desc_str),
            })
        };
    }
    return None;
}
use crate::java_ast::java_ast_handle::{code_prompt_related_care_java, find_by_class_name_java};
use crate::java_ast::java_file_parser::{parse_java_prompt_content, scan_java_code_snippet};
use crate::kotlin_ast::kotlin_ast_handle::{code_prompt_related_care_kotlin, find_by_class_name_kotlin};
use crate::kotlin_ast::kotlin_file_parser::{parse_kotlin_prompt_content, scan_kotlin_code_snippet};
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_db::domain::code_kv_index_domain::ScanFileRecord;
use std::path::Path;

pub type ScanFileFunction = fn(&Path, &String, &Vec<String>) -> Option<ScanFileRecord>;

pub fn fn_router_for_scan_file(file_extension: &String) -> ScanFileFunction {
    return match file_extension.as_str() {
        "java" => scan_java_code_snippet,
        "kt" => scan_kotlin_code_snippet,
        _ => scan_java_code_snippet,
    };
}

pub type ParsePromptFunction = fn(&String, &String) -> Option<PromptDeclaration>;

pub fn fn_router_for_parse_prompt(file_extension: &String) -> ParsePromptFunction {
    return match file_extension.as_str() {
        "java" => parse_java_prompt_content,
        "kt" => parse_kotlin_prompt_content,
        _ => parse_java_prompt_content,
    };
}

pub type PromptRelatedFunction = fn(&PromptDeclaration) -> bool;

pub fn fn_router_for_prompt_related(file_extension: &String) -> PromptRelatedFunction {
    return match file_extension.as_str() {
        "java" => code_prompt_related_care_java,
        "kt" => code_prompt_related_care_kotlin,
        _ => code_prompt_related_care_java,
    };
}

pub type GetClassNameFunction = fn(&ScanFileRecord, &Vec<String>) -> (bool, usize);

pub fn fn_router_for_get_class_name(file_extension: &String) -> GetClassNameFunction {
    return match file_extension.as_str() {
        "java" => find_by_class_name_java,
        "kt" => find_by_class_name_kotlin,
        _ => find_by_class_name_java,
    };
}
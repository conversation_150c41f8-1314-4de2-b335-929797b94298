use crate::android_strategy::language_file_tyle_adapter::fn_router_for_parse_prompt;
use agent_common_service::model::code_complete_model::{CodeCompletionRequestBean, ContextAwareInfo};
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_common_service::service::abstract_similarity::Similarity;
use agent_common_service::service::code_complete_rag_executor::get_target_content;
use agent_common_service::service::code_complete_rag_executor::CodeCompleteRagExecutorStrategy;
use agent_common_service::tools::code_tokenizer::code_snippet_tokenizer;
use agent_common_service::tools::common_tools::{CacheRelatedModuleEnum, SimilarityCodeInfo};
use agent_common_service::tools::jaccard_similarity::jaccard_similarity;
use agent_db::config::runtime_config::AGENT_CONFIG;
use log::{debug, error};
use std::collections::{Binary<PERSON>eap, HashMap, HashSet};

pub struct AndroidCodeCompleteRagExecutorStrategy;

impl CodeCompleteRagExecutorStrategy for AndroidCodeCompleteRagExecutorStrategy {
    fn similarity_code_compare(&self, cache_data: &Vec<SimilarityCodeInfo>, code_complete_request: &CodeCompletionRequestBean) -> anyhow::Result<Vec<ContextAwareInfo>> {
        let target_content_opt = get_target_content(code_complete_request);
        if target_content_opt.is_none() {
            debug!("code size not enough. target content is none ");
            return Ok(vec![]);
        }
        let content = target_content_opt.unwrap();
        let tokenizer_result = code_snippet_tokenizer(content, 0, 0, 0, "", &"".to_string());
        let token_id_vec: Vec<HashSet<u32>> = cache_data.iter().map(|item| item.token_id_set.clone()).collect();
        return match tokenizer_result {
            Err(error) => {
                error!("tokenizer prompt 20 line failed. error={}",error);
                Ok(vec![])
            }
            Ok(tokenizer_result_value) => {
                let current_token_id_vec = tokenizer_result_value.id_vec;
                if current_token_id_vec.is_empty() {
                    return Ok(vec![]);
                }
                let current_token_id_set: HashSet<u32> = current_token_id_vec.into_iter().collect();
                //实际计算top n个相似度最高的代码块
                let top_similarities: Vec<Similarity<usize>> = token_id_vec.iter()
                    .enumerate()
                    .map(|(index, set)| {
                        let similarity = jaccard_similarity(&set, &current_token_id_set);
                        Similarity {
                            sim_score: similarity,
                            value: index,
                            file_url: None,
                        }
                    })
                    .collect();
                // 合并所有局部 BinaryHeap 到一个全局 BinaryHeap
                let mut global_heap = BinaryHeap::new();
                for similarity in top_similarities {
                    global_heap.push(similarity);
                }
                let mut num = 1;
                let mut result: Vec<ContextAwareInfo> = vec![];
                while !global_heap.is_empty() {
                    let next_value = global_heap.pop();
                    if let Some(top) = next_value {
                        let similarity_result = ContextAwareInfo {
                            filePath: cache_data.get(top.value).unwrap().file_url.clone(),
                            content: cache_data.get(top.value).unwrap().code_snippet.clone(),
                            score: top.sim_score,
                            extraData: None,
                        };
                        result.push(similarity_result);
                    }
                    if num >= AGENT_CONFIG.code_complation_similarity_top_num {
                        break;
                    }
                    num += 1;
                }

                Ok(result)
            }
        };
    }

    fn extract_prompt_declaration(&self, code_complete_request: &CodeCompletionRequestBean) -> Option<PromptDeclaration> {
        if let Some(file_suffix) = &code_complete_request.fileNameSuffix {
            let parse_fn = fn_router_for_parse_prompt(&file_suffix);
            return parse_fn(&code_complete_request.prompt, &code_complete_request.suffix);
        }
        return None;
    }
}
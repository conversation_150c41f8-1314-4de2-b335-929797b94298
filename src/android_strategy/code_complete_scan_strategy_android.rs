use crate::android_strategy::language_file_tyle_adapter::fn_router_for_scan_file;
use crate::model::lib_class_cache_model::LibClassCacheRequestBean;
use crate::scan::common_scan::skip_file;
use agent_common_service::model::code_scan_model::ScanConfig;
use agent_common_service::service::code_scan::CodeScanStrategy;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{CodeInfo, ScanFileRecord};
use log::info;
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use agent_db::tools::common_tools::FILE_PREFIX;

pub struct AndroidScanStrategy;

static SHARED_IS_RUNNING: Lazy<Arc<AtomicBool>> = Lazy::new(|| Arc::new(AtomicBool::new(false)));

impl CodeScanStrategy for AndroidScanStrategy {
    async fn scan_project_from_url(_: ScanConfig) -> anyhow::Result<()> {
        Ok(())
    }

    fn is_scan_ing() -> bool {
        let running = SHARED_IS_RUNNING.clone();
        return running.load(Ordering::SeqCst);
    }

    fn execute_scan_file(target_file: &Path, file_content_opt: Option<String>) -> anyhow::Result<Option<ScanFileRecord>> {
        let file_info = skip_file(target_file, file_content_opt);
        if file_info.0 {
            return Ok(None);
        }
        let file_name_suffix = file_info.1.unwrap();
        let lines = file_info.2.unwrap();
        let scan_fn = fn_router_for_scan_file(&file_name_suffix);
        return Ok(scan_fn(target_file, &file_name_suffix, &lines));
    }

    async fn execute_dependency_scan(_: &String) -> anyhow::Result<()> {
        Ok(())
    }
}

pub async fn do_cache_lib_class_info(cache_reqest: LibClassCacheRequestBean) {
    let project_url = cache_reqest.project_url.clone();
    for class_bean in &cache_reqest.class_list {
        let mut file_record = ScanFileRecord::default();
        let file_path = format!("{}/libclasses/{}", project_url, class_bean.full_qualified_name);
        file_record.file_url = file_path.clone();
        file_record.file_name_suffix = String::from("java");
        let mut code_info = CodeInfo::default();
        code_info.code_struct = class_bean.code_struct.clone();
        code_info.class_name = Some(class_bean.class_name.clone());
        code_info.full_qualified_name = Some(class_bean.full_qualified_name.clone());
        file_record.code_info = Some(code_info);
        let file_key = format!("{}{}", FILE_PREFIX, file_path);
        let scan_file_record_str = serde_json::to_string(&file_record).unwrap();
        let _ = KV_CLIENT.insert(&file_key, &scan_file_record_str);
        info!("Cache lib class {}", file_path);
    }
}
use crate::android_strategy::code_complete_scan_strategy_android::do_cache_lib_class_info;
use crate::model::lib_class_cache_model::LibClassCacheRequestBean;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>)]
pub struct RequestBean {
    pub op_type: String,
    pub model_str: String,
}

pub async fn handle_ideclient_request(request_str: String) -> Option<String> {
    if let Ok(request_bean) = serde_json::from_str::<RequestBean>(&request_str) {
        let op_type = request_bean.op_type;
        let model_str = request_bean.model_str;
        match op_type.as_str() {
            "cache_lib_class_info" => {
                if let Ok(request_model_bean) = serde_json::from_str::<LibClassCacheRequestBean>(&model_str) {
                    do_cache_lib_class_info(request_model_bean).await;
                }
            }
            "query_template_similarity" => {}
            _ => {}
        }
    }
    None
}
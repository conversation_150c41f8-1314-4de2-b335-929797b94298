use crate::scan::common_scan::{get_real_class_name, is_same_class_name};
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_db::domain::code_kv_index_domain::{ClassInfo, CodeInfo, MethodD<PERSON>laration, ScanFileRecord};
use once_cell::sync::Lazy;
use std::collections::{HashMap, HashSet};
use agent_db::tools::common_tools::LINE_ENDING;
use tree_sitter::{Node, TreeCursor};

pub const IMPORT_PREFIX: &str = "import ";
pub const PACKAGE_PREFIX: &str = "package ";
pub const STATIC_PREFIX: &str = "static ";

///抽取继承class
pub fn extract_extends_class(code_content: &&String, node: &Node) -> Option<String> {
    let mut cursor = node.walk();
    for extends_node in node.children(&mut cursor) {
        match extends_node.kind() {
            "type_identifier" => {
                let extends_class = &code_content[extends_node.start_byte()..extends_node.end_byte()];
                return Some(extends_class.to_string());
            }
            "generic_type" => {
                //范型
                let mut child_node_cursor = extends_node.walk();
                for child_node in extends_node.children(&mut child_node_cursor) {
                    if child_node.kind() == "type_identifier" {
                        let child_node_class = &code_content[child_node.start_byte()..child_node.end_byte()];
                        return Some(child_node_class.to_string());
                    }
                }
            }
            _ => {}
        }
    }
    return None;
}

///抽取implements实现接口set
pub fn extract_implements_class(code_content: &&String, node: &Node) -> Option<HashSet<String>> {
    let mut cursor = node.walk();
    for implements_node in node.children(&mut cursor) {
        if implements_node.kind() == "type_list" {
            let mut implements_class_set: HashSet<String> = HashSet::new();
            let mut child_cursor = implements_node.walk();
            for child_node in implements_node.children(&mut child_cursor) {
                match child_node.kind() {
                    "type_identifier" => {
                        let type_identifier_str = &code_content[child_node.start_byte()..child_node.end_byte()];
                        implements_class_set.insert(type_identifier_str.to_string());
                    }
                    "generic_type" => {
                        //范型
                        let mut child_node_cursor = child_node.walk();
                        for child_node in child_node.children(&mut child_node_cursor) {
                            if child_node.kind() == "type_identifier" {
                                let child_node_class = &code_content[child_node.start_byte()..child_node.end_byte()];
                                implements_class_set.insert(child_node_class.to_string());
                            }
                        }
                    }
                    _ => {}
                }
            }
            if implements_class_set.len() > 0 {
                return Some(implements_class_set);
            }
        }
    }
    return None;
}

/// 抽取class级别的字段类型
/// 注意： 抽取时候只会关注对象级别，类似int类型不会抽取
pub fn extract_class_field_map(class_field_map: &mut HashMap<String, String>, code_content: &&String, node: &Node) {
    let mut cursor = node.walk();
    let field_type_node_opt = node.child_by_field_name("type");
    if let Some(field_type_node) = field_type_node_opt {
        let file_type = &code_content[field_type_node.start_byte()..field_type_node.end_byte()];
        for field_node in node.children(&mut cursor) {
            match field_node.kind() {
                "variable_declarator" => {
                    let mut variable_cursor = field_node.walk();
                    for variable_node in field_node.children(&mut variable_cursor) {
                        if variable_node.kind() == "identifier" {
                            let field_name = &code_content[variable_node.start_byte()..variable_node.end_byte()];
                            class_field_map.insert(field_name.to_string(), file_type.to_string());
                        }
                    }
                }
                _ => {}
            }
        }
    }
}


///class基础信息
pub fn extract_class_base_model(code_content: &&String, code_info: &mut CodeInfo, node_cursor: &mut TreeCursor) {
    let mut class_type = "class";
    node_cursor.goto_first_child();
    let mut visibility_modifier_opt: Option<String> = Some("public".to_string()); //kotlin默认可见是public
    let mut class_name_opt: Option<String> = None;
    let mut extend_class_name_opt: Option<String> = None;
    let mut implements_class_name_set_opt: Option<HashSet<String>> = None;
    let mut class_field_map = HashMap::new();
    loop {
        let node = node_cursor.node();
        // debug!("node kind: {} {:?}", node.kind(), &code_content[node.start_byte()..node.end_byte()]);
        match node.kind() {
            "enum" => { class_type = "enum"; }
            "interface" => { class_type = "interface"; }
            "modifiers" => {
                let mut modifiers_str = &code_content[node.start_byte()..node.end_byte()];
                let modifier_value = match modifiers_str.rsplit_once(LINE_ENDING) {
                    Some((_, last)) => last,
                    None => modifiers_str
                };
                visibility_modifier_opt = Some(modifier_value.to_string());
            }
            "identifier" => {
                let identifier_str = &code_content[node.start_byte()..node.end_byte()];
                class_name_opt = Some(identifier_str.to_string());
            }
            "class_body" => {
                let mut class_body_cursor = node.walk();
                for child_node in node.children(&mut class_body_cursor) {
                    if child_node.kind() == "field_declaration" {
                        extract_class_field_map(&mut class_field_map, &&code_content, &child_node)
                    }
                }
            }
            "superclass" => {
                extend_class_name_opt = extract_extends_class(&&code_content, &node);
            }
            "super_interfaces" => {
                implements_class_name_set_opt = extract_implements_class(&&code_content, &node);
            }
            _ => {}
        }
        if !node_cursor.goto_next_sibling() {
            break;
        }
    }
    if let Some(visibility_modifier) = visibility_modifier_opt {
        if code_info.class_name.is_none() {
            code_info.class_type = Some(class_type.to_string());
            code_info.class_name = class_name_opt;
            code_info.extend_class_name = extend_class_name_opt;
            code_info.implements_class_name_set = implements_class_name_set_opt;
        } else {
            let class_info = ClassInfo {
                class_type: class_type.to_string(),
                class_name: class_name_opt.unwrap_or_default(),
                extend_class_name: extend_class_name_opt,
                field_name_class_map: Some(class_field_map),
                implements_class_name_set: implements_class_name_set_opt,
                code_struct: "".to_string(),
            };
            if let Some(class_list) = &mut code_info.class_list {
                class_list.push(class_info);
            } else {
                let mut class_list: Vec<ClassInfo> = Vec::new();
                class_list.push(class_info);
                code_info.class_list = Some(class_list);
            }
        }
    }
}


///抽取import信息
pub fn extract_import_set(code_content: &&String, import_set: &mut HashSet<String>, node: &Node) {
    let import_str = &code_content[node.start_byte()..node.end_byte()];
    //如果包含*,一般代表引入了某个package 暂不处理
    if !import_str.contains("*") {
        let last_char_opt = import_str.find(";");
        if let Some(last_char) = last_char_opt {
            // 正常情况下肯定是以import 开头, 以; 结尾, 如果不是, 那么跳过
            if import_str.starts_with(IMPORT_PREFIX) {
                let mut import_str = import_str[IMPORT_PREFIX.len()..last_char].trim();
                //如果引入的是静态x,做特殊处理
                if import_str.starts_with(STATIC_PREFIX) {
                    import_str = &import_str[STATIC_PREFIX.len()..].trim();
                } else {
                    import_set.insert(import_str.to_string());
                }
            }
        }
    }
}

///抽取package信息
pub fn extract_package_declaration(code_content: &&String, node: &Node) -> Option<String> {
    let package_str = &code_content[node.start_byte()..node.end_byte()];
    let last_char_opt = package_str.find(";");
    if let Some(last_char) = last_char_opt {
        // 正常情况下肯定是以import 开头, 以; 结尾, 如果不是, 那么跳过
        if package_str.starts_with(PACKAGE_PREFIX) {
            let package_str = package_str[IMPORT_PREFIX.len()..last_char].trim();
            return Some(package_str.to_string());
        }
    }
    return None;
}

///抽取未写完的java方法，如果写完的代码不用管
/// 主要用在补全场景，函数体内的补全时对依赖代码做排序
pub fn extract_unfinish_method(code_content: &&String, node: &Node) -> Option<MethodDeclaration> {
    if node.end_byte() < code_content.len() {
        //非当前编辑的方法
        return None;
    }
    let mut result = MethodDeclaration::default();
    let mut return_class: String = String::new();
    let mut param_class_vec: Vec<String> = Vec::new();
    let mut method_name: String = String::new();
    let mut method_inner_variable_class_vec: Vec<String> = Vec::new();
    let mut cursor = node.walk();
    for method_node in node.children(&mut cursor) {
        match method_node.kind() {
            "type_identifier" => {
                //返回值
                let node_value = &code_content[method_node.start_byte()..method_node.end_byte()];
                return_class = node_value.to_string();
            }
            "identifier" => {
                //解析方法名
                let node_value = &code_content[method_node.start_byte()..method_node.end_byte()];
                method_name = node_value.to_string();
            }
            "formal_parameters" => {
                //解析方法参数
                let mut param_cursor = method_node.walk();
                for param_node in method_node.children(&mut param_cursor) {
                    match param_node.kind() {
                        "formal_parameter" => {
                            let field_type_node_opt = param_node.child_by_field_name("type");
                            if let Some(field_type_node) = field_type_node_opt {
                                let file_type = &code_content[field_type_node.start_byte()..field_type_node.end_byte()];
                                let file_type_str = file_type.to_string();
                                if !is_sdk_class(&file_type_str) {
                                    param_class_vec.push(file_type_str);
                                }
                            }
                        }
                        _ => {}
                    }
                }
            }
            "block" => {
                let mut method_inner_cursor = method_node.walk();
                for method_item_node in method_node.children(&mut method_inner_cursor) {
                    match method_item_node.kind() {
                        "local_variable_declaration" => {
                            let mut method_variable_cursor = method_item_node.walk();
                            for method_variable_node in method_item_node.children(&mut method_variable_cursor) {
                                match method_variable_node.kind() {
                                    "type_identifier" => {
                                        let variable_name = &code_content[method_variable_node.start_byte()..method_variable_node.end_byte()];
                                        //todo 注意这里的顺序，需要和build_cache_data里面的build_related_module_score对应，内部类有个递进的得分机制
                                        method_inner_variable_class_vec.push(variable_name.to_string());
                                    }
                                    _ => {}
                                }
                            }
                        }
                        _ => {}
                    }
                    if method_inner_variable_class_vec.len() > 0 {
                        result.inner_class_declaration = Some(method_inner_variable_class_vec.clone());
                    }
                }
            }
            _ => {}
        }
    }
    if method_name.len() > 0 {
        result.method_name = method_name;
    }
    if param_class_vec.len() > 0 {
        result.param_class_vec = Some(param_class_vec);
    }
    if return_class.len() > 0 && !is_sdk_class(&return_class) {
        result.return_class = Some(return_class);
    }
    //当method_name不为空，并且有参数或者有返回值或者方法体内有类变量，返回result，否则返回None(如果仅有method_name)，没有其他信息，返回上游也没啥用
    return if result.method_name.len() > 0 && (result.param_class_vec.is_some() || result.return_class.is_some() || result.inner_class_declaration.is_some()) {
        Some(result)
    } else {
        None
    };
}

pub fn code_prompt_related_care_java(current_context: &PromptDeclaration) -> bool {
    if let Some(current_method_info) = &current_context.method_declaration {
        if let Some(param_vec) = &current_method_info.param_class_vec {
            for class_name in param_vec.iter() {
                if !is_sdk_class(class_name) {
                    return true;
                }
            }
        }
        if let Some(inner_class_declaration) = &current_method_info.inner_class_declaration {
            for class_name in inner_class_declaration.iter() {
                if !is_sdk_class(class_name) {
                    return true;
                }
            }
        }
        if let Some(class_name) = &current_method_info.return_class {
            if !is_sdk_class(class_name) {
                return true;
            }
        }
    }
    if let Some(class_field_set) = &current_context.field_set {
        for class_name in class_field_set.iter() {
            if !is_sdk_class(class_name) {
                return true;
            }
        }
    }
    if let Some(extends_class) = &current_context.extends_class {
        if !is_sdk_class(extends_class) {
            return true;
        }
    }
    if let Some(import_set) = &current_context.import_set {
        for class_name in import_set.iter() {
            if !is_sdk_import(class_name) {
                return true;
            }
        }
    }
    return false;
}

//寻找匹配的名字
pub fn find_by_class_name_java(file_record: &ScanFileRecord, class_name_iter: &Vec<String>) -> (bool, usize) {
    for (index, target_class_name) in class_name_iter.iter().enumerate() {
        if is_sdk_class(target_class_name) {
            continue;
        }
        if let Some(code_info) = &file_record.code_info {
            if let Some(record_class_name) = &code_info.class_name {
                if is_same_class_name(record_class_name, target_class_name) {
                    return (true, index);
                }
            }
            if let Some(record_class_list) = &code_info.class_list {
                for record_class_info in record_class_list.iter() {
                    if is_same_class_name(&record_class_info.class_name, target_class_name) {
                        return (true, index);
                    }
                }
            }
        }
    }
    return (false, 0);
}

static SDK_CLASS: Lazy<HashSet<String>> = Lazy::new(|| {
    let mut set: HashSet<String> = HashSet::new();
    set.insert("String".to_string());
    set.insert("Runnable".to_string());
    set.insert("int".to_string());
    set.insert("long".to_string());
    set.insert("float".to_string());
    set.insert("double".to_string());
    set.insert("boolean".to_string());
    set
});

fn is_sdk_class(class_name: &String) -> bool {
    return SDK_CLASS.contains(get_real_class_name(class_name).as_str());
}

fn is_sdk_import(import_class: &String) -> bool {
    return import_class.starts_with("java.") || import_class.starts_with("javax.") || import_class.starts_with("android.");
}
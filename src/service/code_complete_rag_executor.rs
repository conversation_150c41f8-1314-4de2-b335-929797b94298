use crate::model::code_complete_model::{CodeCompletionRequestBean, ContextAwareInfo};
use crate::model::prompt_declaration::PromptDeclaration;
use crate::service::abstract_similarity::Similarity;
use crate::tools::code_tokenizer::code_snippet_tokenizer;
use crate::tools::common_tools::{CacheRelatedModuleEnum, SimilarityCodeInfo};
use crate::tools::jaccard_similarity::jaccard_similarity;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::tools::common_tools::LINE_ENDING;
use anyhow::Result;
use log::{debug, error};
use rayon::prelude::*;
use std::collections::{BinaryHeap, HashMap, HashSet};

///定义rag检索具体执行流程
pub trait CodeCompleteRagExecutorStrategy {
    ///和相似缓存列表对比，找到相似度topN
    ///默认实现： step1 target_content_opt获取目标对比片段 ，然后将对比片段序列化成token id集合
    ///         step2 基于缓存的 tokenid集合，依次和当前对比内容的tokenid集合做相似度对比
    fn similarity_code_compare(&self,cache_data: &Vec<SimilarityCodeInfo>, code_complete_request: &CodeCompletionRequestBean) -> Result<Vec<ContextAwareInfo>> {
        let target_content_opt = get_target_content(code_complete_request);
        if target_content_opt.is_none() {
            debug!("code size not enough. target content is none ");
            return Ok(vec![]);
        }
        let content = target_content_opt.unwrap();
        // let test_data = content.clone();
        // //todo 待删除
        // debug!("************************** similarity code current: **************************");
        // debug!("{}",test_data);


        let tokenizer_result = code_snippet_tokenizer(content, 0, 0, 0, "", &"".to_string());
        let token_id_vec: Vec<HashSet<u32>> = cache_data.iter().map(|item| item.token_id_set.clone()).collect();
        match tokenizer_result {
            Err(error) => {
                error!("tokenizer prompt 20 line failed. error={}",error);
                return Ok(vec![]);
            }
            Ok(tokenizer_result_value) => {
                let current_token_id_vec = tokenizer_result_value.id_vec;
                if current_token_id_vec.is_empty() {
                    return Ok(vec![]);
                }
                let current_token_id_set: HashSet<u32> = current_token_id_vec.into_iter().collect();
                //实际计算top n个相似度最高的代码块
                let top_similarities: Vec<BinaryHeap<Similarity<usize>>> = token_id_vec.par_iter()
                    .enumerate()
                    .map(|(index, set)| {
                        let mut local_heap = BinaryHeap::new();
                        let similarity = jaccard_similarity(&set, &current_token_id_set);
                        local_heap.push(Similarity {
                            sim_score: similarity,
                            value: index,
                            file_url: None,
                        });
                        local_heap
                    })
                    .collect();
                // 合并所有局部 BinaryHeap 到一个全局 BinaryHeap
                let mut global_heap = BinaryHeap::new();
                for local_heap in top_similarities {
                    global_heap.extend(local_heap);
                }
                let mut num = 1;
                let mut result: Vec<ContextAwareInfo> = vec![];
                while !global_heap.is_empty() {
                    let next_value = global_heap.pop();
                    if let Some(top) = next_value {
                        let similarity_result = ContextAwareInfo {
                            filePath: cache_data.get(top.value).unwrap().file_url.clone(),
                            content: cache_data.get(top.value).unwrap().code_snippet.clone(),
                            score: top.sim_score,
                            extraData: None,
                        };
                        // debug!("************************** similarity code compare score:{} **************************",&similarity_result.score);
                        // debug!("文件 {}",&similarity_result.filePath.clone());
                        // debug!("内容 {}",&similarity_result.content.clone());
                        result.push(similarity_result);
                    }
                    if num >= AGENT_CONFIG.code_complation_similarity_top_num {
                        break;
                    }
                    num += 1;
                }

                return Ok(result);
            }
        }
    }

    ///根据代码元素类型，去找对应的相关片段
    /// 返回值：v.1: 当前元素和缓存对比是否有修改 v.2：如果没有修改的情况下，返回缓存集合
    /// 默认实现
    fn default_get_context_aware_from_cache(&self,related_module_enum: CacheRelatedModuleEnum,
                                    cache_module_lated_map: &HashMap<CacheRelatedModuleEnum, Vec<ContextAwareInfo>>,
                                    cache_context: &PromptDeclaration,
                                    current_context: &PromptDeclaration,
                                    language: &str) -> (bool, Option<Vec<ContextAwareInfo>>) {
        //true:没有修改 false：有修改
        let change_flag: bool = match related_module_enum {
            CacheRelatedModuleEnum::IMPORT | CacheRelatedModuleEnum::FIELD => {
                let (current_set, cache_set) = match related_module_enum {
                    CacheRelatedModuleEnum::IMPORT => (&current_context.import_set, &cache_context.import_set),
                    CacheRelatedModuleEnum::FIELD => (&current_context.field_set, &cache_context.field_set),
                    _ => unreachable!(),
                };
                //如果两者都为空，没有代码更新，但是返回空值
                //如果两者都不为空，判断差集，如果有差集，那么说明有代码更新，
                //如果一个为空，另一个不为空。。直接判断有代码更新
                match (current_set, cache_set) {
                    (Some(set_a), Some(set_b)) => (set_a.len() == set_b.len()) && set_a.is_subset(set_b),
                    (None, None) => true,
                    _ => false,
                }
            }
            CacheRelatedModuleEnum::METHOD => {
                match (&current_context.method_declaration, &cache_context.method_declaration) {
                    (Some(val_a), Some(val_b)) => val_a.method_name == val_b.method_name,
                    (None, None) => true,
                    _ => false,
                }
            }
            CacheRelatedModuleEnum::EXTENDS => {
                match (&current_context.extends_class, &cache_context.extends_class) {
                    (Some(val_a), Some(val_b)) => val_a == val_b,
                    (None, None) => true,
                    _ => false,
                }
            }
        };
        //如果更新过代码
        if !change_flag {
            return (true, None);
        }
        let related_value_result = cache_module_lated_map.get(&related_module_enum);
        match related_value_result {
            Some(related_value) => {
                if related_value.len() == 0 {
                    return (false, None);
                }
                return (false, Some(related_value.clone()));
            }
            None => {
                return (false, None);
            }
        }
    }

    fn get_context_aware_from_cache(&self,related_module_enum: CacheRelatedModuleEnum,
                                    cache_module_lated_map: &HashMap<CacheRelatedModuleEnum, Vec<ContextAwareInfo>>,
                                    cache_context: &PromptDeclaration,
                                    current_context: &PromptDeclaration,
                                    language: &str) -> (bool, Option<Vec<ContextAwareInfo>>) {
        self.default_get_context_aware_from_cache(related_module_enum, cache_module_lated_map, cache_context, current_context, language)
    }

///抽取当前补全请求中的结构
    fn extract_prompt_declaration(&self,code_complete_request: &CodeCompletionRequestBean) -> Option<PromptDeclaration>;
}


///获取目标对比的代码片段
/// 如果AGENT_CONFIG.similarity_cal_type为1：直接获取光标上文最后code_complation_window_size行
/// 如果AGENT_CONFIG.similarity_cal_type为0：先从suffix获取最多code_complation_slide_size行，比如行数是n
/// 然后用code_complation_window_size-n获取到上文目标行数m，从prompt最后获取m行，最后组装m和n
pub fn get_target_content(code_complete_request: &CodeCompletionRequestBean) -> Option<String> {
    let prompt_lines: Vec<&str> = code_complete_request.prompt.lines().collect();
    if prompt_lines.len() < AGENT_CONFIG.code_complation_window_size as usize {
        return None;
    }
    if AGENT_CONFIG.similarity_cal_type == 1 {
        let start_no = usize::max(0, prompt_lines.len().saturating_sub(AGENT_CONFIG.code_complation_window_size as usize));
        let last_code_block_arr = &prompt_lines[start_no..];
        return Some(last_code_block_arr.join(LINE_ENDING));
    }
    let suffix_lines: Vec<&str> = code_complete_request.suffix.lines().collect();
    // Step 1: Get top 5 lines of suffix (or less if not available)
    let suffix_segment: Vec<&str> = suffix_lines.iter().take(AGENT_CONFIG.code_complation_slide_size as usize).cloned().collect();

    // Step 2: Calculate how many lines we need from the bottom of the prompt
    let suffix_len = suffix_segment.len();
    let prompt_target_len = AGENT_CONFIG.code_complation_window_size as usize - suffix_len;

    // Get the last 10-n lines from the prompt
    let start_index = if prompt_lines.len() > prompt_target_len {
        prompt_lines.len() - prompt_target_len
    } else {
        0
    };
    let prompt_segment: Vec<&str> = prompt_lines[start_index..].to_vec();

    // Step 3: Combine the lines from prompt and suffix
    let mut result_lines = prompt_segment;
    result_lines.extend(suffix_segment);
    return Some(result_lines.join(LINE_ENDING));
}
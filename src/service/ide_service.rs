use crate::service::tcp::connection::ConnContext;
use crate::service::tcp::init_service::ProjectConnectionManager;
use agent_common_service::model::chat_model::{SymbolAndStreamSearchParams, SymbolSearchResponse, SymbolAndStreamSearchResult, UsagesSearchResponse};
use log::info;
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use crate::deepsearchV2::searchrouter_query_task::SEARCH_COUNT_FROM_LSP;

/// 全局项目连接管理器实例
pub static PROJECT_CONNECTION_MANAGER: Lazy<ProjectConnectionManager> =
    Lazy::new(|| ProjectConnectionManager::new());




/// 查找方法信息（内部函数，不对外暴露）
async fn find_method_internal(project_url: &String,conn_id:&String) -> Option<ConnContext> {
    // 查找项目连接映射
    let project_manager = get_project_connection_manager();
    project_manager.find_connection(project_url, conn_id).await
}





/// Symbol检索函数
///
/// # 参数
/// * `project_url` - 项目URL
/// * `conn_id` - 连接ID
/// * `symbol_search_params` - Symbol搜索参数
///
/// # 返回值
/// * `Result<Vec<SymbolSearchResult>, String>` - 成功返回转换后的Symbol搜索结果列表，失败返回错误信息
pub async fn symbol_search(
    project_url: &String,
    conn_id: &String,
    symbol_search_params: SymbolAndStreamSearchParams
) -> Result<Vec<SymbolAndStreamSearchResult>, String> {
    info!("🔍 开始执行symbol检索 - 项目: {}, 连接ID: {}, 参数: {:?}",
          project_url, conn_id, symbol_search_params);

    if conn_id.is_empty() {
        info!("⚠️ 连接ID为空，返回空结果");
        return Ok(Vec::new());
    }

    // 1. 如果className和methodName同时为空，返回空数组
    if symbol_search_params.class_name.is_none() && symbol_search_params.method_name.is_none() {
        info!("⚠️ className和methodName同时为空，返回空结果");
        return Ok(Vec::new());
    }
    let symbol_search_params_clone = symbol_search_params.clone();
    let class_name = symbol_search_params_clone.class_name.unwrap();
    let method_name = symbol_search_params_clone.method_name.unwrap();
    let topN = symbol_search_params_clone.topN.unwrap_or(SEARCH_COUNT_FROM_LSP);
    if class_name.is_empty() && method_name.is_empty() {
        info!("⚠️ className和methodName同时为空字符串，返回空结果");
        return Ok(Vec::new());
    }

    // 2. 根据conn_id和project_url从connectionManager里面获取客户端连接
    let project_manager = get_project_connection_manager();
    let conn_context_opt = project_manager.find_connection(project_url, conn_id).await;
    if conn_context_opt.is_none() {
        info!("⚠️ 找不到对应的connContext，返回空结果");
        return Ok(Vec::new());
    }

    let mut all_results = Vec::new();
    let params = SymbolAndStreamSearchParams {
        class_name: Some(class_name),
        method_name: Some(method_name),
        topN: Some(topN),
        project_url: symbol_search_params_clone.project_url,
    };
    info!("📤 准备发送symbol_search_method请求，参数: {:?}", params);
    match conn_context_opt.unwrap().request::<SymbolAndStreamSearchParams, SymbolSearchResponse>("symbol_search_method", params).await {
        Ok(response) => {
            info!("✅ Symbol检索成功，客户端返回结果:");
            info!("📋 返回结果: {}", serde_json::to_string_pretty(&response).unwrap_or_else(|_| "无法序列化结果".to_string()));

            // 5. 转换客户端返回的数据为目标格式
            let transformed_results = transform_symbol_response(&response, &symbol_search_params);
            all_results.extend(transformed_results);
        }
        Err(e) => {
            let error_msg = format!("❌ Symbol检索失败: {}", e);
            info!("{}", error_msg);
            return Err(error_msg);
        }
    }

    // if let Some(connections) = connections_opt {
    //     for conn_context in connections {
    //         // 3. 构建请求参数
    //         let params = SymbolAndStreamSearchParams {
    //             class_name: symbol_search_params.class_name.clone(),
    //             method_name: symbol_search_params.method_name.clone(),
    //             topN: symbol_search_params.topN,
    //         };
    //
    //         info!("📤 准备发送symbol_search_method请求，参数: {:?}", params);
    //
    //         // 4. 向客户端发送请求
    //         match conn_context.request::<SymbolAndStreamSearchParams, SymbolSearchResponse>("symbol_search_method", params).await {
    //             Ok(response) => {
    //                 info!("✅ Symbol检索成功，客户端返回结果:");
    //                 info!("📋 返回结果: {}", serde_json::to_string_pretty(&response).unwrap_or_else(|_| "无法序列化结果".to_string()));
    //
    //                 // 5. 转换客户端返回的数据为目标格式
    //                 let transformed_results = transform_symbol_response(&response, &symbol_search_params);
    //                 all_results.extend(transformed_results);
    //             }
    //             Err(e) => {
    //                 let error_msg = format!("❌ Symbol检索失败: {}", e);
    //                 info!("{}", error_msg);
    //                 return Err(error_msg);
    //             }
    //         }
    //     }
    // } else {
    //     info!("❌ 未找到项目连接 - 项目URL: {}", project_url);
    //     return Err(format!("未找到项目连接 - 项目URL: {}", project_url));
    // }

    info!("🎯 Symbol检索完成，共找到 {} 个结果", all_results.len());
    Ok(all_results)
}

/// 转换TCP客户端返回的Symbol响应为目标格式
///
/// # 参数
/// * `response` - TCP客户端返回的SymbolSearchResponse
/// * `search_params` - 原始搜索参数，用于判断搜索类型
///
/// # 返回值
/// * `Vec<SymbolSearchResult>` - 转换后的结果列表
fn transform_symbol_response(response: &SymbolSearchResponse, search_params: &SymbolAndStreamSearchParams) -> Vec<SymbolAndStreamSearchResult> {
    let mut results = Vec::new();

    for symbol_data in &response.data {
        // 只处理有文件路径的数据
        if let Some(file_path) = &symbol_data.file_path {
            // 判断是方法搜索还是类搜索
            match &search_params.method_name {
                Some(method_name) => {
                    // 方法搜索：从methods列表中获取匹配的方法
                    if !method_name.is_empty() {
                        for method_data in &symbol_data.methods {
                            if let Some(method_data_name) = &method_data.method_name {
                                if method_data_name == method_name {
                                    let result = SymbolAndStreamSearchResult {
                                        relative_path: file_path.clone(),
                                        class_name: symbol_data.class_name.clone(),
                                        method_name: Some(method_name.clone()),
                                        snippet: method_data.body.clone(),
                                        startLine: if method_data.startLine == -1 {0}else { method_data.startLine },
                                        endLine: if method_data.endLine == -1 {0}else { method_data.endLine },
                                    };
                                    results.push(result);
                                }
                            }
                        }
                    }else {
                        // 类搜索：使用类的body
                        if let Some(body) = &symbol_data.body {
                            let result = SymbolAndStreamSearchResult {
                                relative_path: file_path.clone(),
                                class_name: symbol_data.class_name.clone(),
                                method_name: None,
                                snippet: body.clone(),
                                startLine: 0,
                                endLine: 0,
                            };
                            results.push(result);
                        }
                    }
                }
                None => {
                    //类搜索：使用类的body
                    if let Some(body) = &symbol_data.body {
                        let result = SymbolAndStreamSearchResult {
                            relative_path: file_path.clone(),
                            class_name: symbol_data.class_name.clone(),
                            method_name: None,
                            snippet: body.clone(),
                            startLine: 0,
                            endLine: 0,
                        };
                        results.push(result);
                    }
                }
            }
        }
    }

    results
}

/// 转换TCP客户端返回的Usages响应为目标格式
///
/// # 参数
/// * `response` - TCP客户端返回的UsagesSearchResponse
///
/// # 返回值
/// * `Vec<SymbolAndStreamSearchResult>` - 转换后的结果列表
fn transform_usages_response(response: &UsagesSearchResponse) -> Vec<SymbolAndStreamSearchResult> {
    let mut results = Vec::new();

    for usage_data in &response.data {
        // 如果methodReferenceList为空，直接返回空列表
        if usage_data.method_reference_list.is_empty() {
            continue;
        }

        // 遍历methodReferenceList
        for method_reference in &usage_data.method_reference_list {
            // 遍历referencedOfMethodList
            for referenced_method in &method_reference.referenced_of_method_list {
                let result = SymbolAndStreamSearchResult {
                    relative_path: method_reference.file_path.clone(),
                    class_name: method_reference.class_name.clone().unwrap_or_else(|| "".to_string()),
                    method_name: Some(referenced_method.method_name.clone()),
                    snippet: referenced_method.text.clone(),
                    startLine: if referenced_method.startLine == -1 {0} else { referenced_method.startLine },
                    endLine: if referenced_method.endLine == -1 {0} else { referenced_method.endLine },
                };
                results.push(result);
            }
        }
    }

    results
}

/// Usages检索函数（上游链路检索）
///
/// # 参数
/// * `project_url` - 项目URL
/// * `conn_id` - 连接ID
/// * `symbol_search_params` - Symbol搜索参数
///
/// # 返回值
/// * `Result<Vec<SymbolAndStreamSearchResult>, String>` - 成功返回转换后的Usages搜索结果列表，失败返回错误信息
pub async fn usages_search(
    project_url: &String,
    conn_id: &String,
    symbol_search_params: SymbolAndStreamSearchParams
) -> Result<Vec<SymbolAndStreamSearchResult>, String> {
    info!("🔍 开始执行usages检索 - 项目: {}, 连接ID: {}, 参数: {:?}",
          project_url, conn_id, symbol_search_params);

    if conn_id.is_empty() {
        info!("⚠️ 连接ID为空，返回空结果");
        return Ok(Vec::new());
    }

    // 1. 如果className和methodName同时为空，返回空数组
    if symbol_search_params.class_name.is_none() && symbol_search_params.method_name.is_none() {
        info!("⚠️ className和methodName同时为空，返回空结果");
        return Ok(Vec::new());
    }

    let symbol_search_params_clone = symbol_search_params.clone();
    let class_name = symbol_search_params_clone.class_name.unwrap();
    let method_name = symbol_search_params_clone.method_name.unwrap();
    let topN = symbol_search_params_clone.topN.unwrap_or(SEARCH_COUNT_FROM_LSP);
    if class_name.is_empty() && method_name.is_empty() {
        info!("⚠️ className和methodName同时为空字符串，返回空结果");
        return Ok(Vec::new());
    }

    // 2. 根据conn_id和project_url从connectionManager里面获取客户端连接
    let project_manager = get_project_connection_manager();
    let conn_context_opt = project_manager.find_connection(project_url, conn_id).await;
    if conn_context_opt.is_none() {
        info!("⚠️ 找不到对应的connContext，返回空结果");
        return Ok(Vec::new());
    }
    let mut all_results = Vec::new();

    // 3. 构建请求参数
    let params = SymbolAndStreamSearchParams {
        class_name: symbol_search_params.class_name.clone(),
        method_name: symbol_search_params.method_name.clone(),
        topN: Some(topN),
        project_url: symbol_search_params_clone.project_url
    };

    match conn_context_opt.unwrap().request::<SymbolAndStreamSearchParams, UsagesSearchResponse>("symbol_search_usages", params).await {
        Ok(response) => {
            info!("✅ Usages检索成功，客户端返回结果:");
            info!("📋 返回结果: {}", serde_json::to_string_pretty(&response).unwrap_or_else(|_| "无法序列化结果".to_string()));

            // 5. 转换客户端返回的数据为目标格式
            let transformed_results = transform_usages_response(&response);
            all_results.extend(transformed_results);
        }
        Err(e) => {
            let error_msg = format!("❌ Usages检索失败: {}", e);
            info!("{}", error_msg);
            return Err(error_msg);
        }
    }

    // if let Some(connections) = connections_opt {
    //     for conn_context in connections {
    //         // 3. 构建请求参数
    //         let params = SymbolAndStreamSearchParams {
    //             class_name: symbol_search_params.class_name.clone(),
    //             method_name: symbol_search_params.method_name.clone(),
    //             topN: symbol_search_params.topN
    //         };
    //
    //         info!("📤 准备发送symbol_search_usages请求，参数: {:?}", params);
    //
    //         // 4. 向客户端发送请求
    //         match conn_context.request::<SymbolAndStreamSearchParams, UsagesSearchResponse>("symbol_search_usages", params).await {
    //             Ok(response) => {
    //                 info!("✅ Usages检索成功，客户端返回结果:");
    //                 info!("📋 返回结果: {}", serde_json::to_string_pretty(&response).unwrap_or_else(|_| "无法序列化结果".to_string()));
    //
    //                 // 5. 转换客户端返回的数据为目标格式
    //                 let transformed_results = transform_usages_response(&response);
    //                 all_results.extend(transformed_results);
    //             }
    //             Err(e) => {
    //                 let error_msg = format!("❌ Usages检索失败: {}", e);
    //                 info!("{}", error_msg);
    //                 return Err(error_msg);
    //             }
    //         }
    //     }
    // } else {
    //     info!("❌ 未找到项目连接 - 项目URL: {}", project_url);
    //     return Err(format!("未找到项目连接 - 项目URL: {}", project_url));
    // }

    info!("🎯 Usages检索完成，共找到 {} 个结果", all_results.len());
    Ok(all_results)
}

/// 获取全局项目连接管理器实例
pub fn get_project_connection_manager() -> &'static ProjectConnectionManager {
    &PROJECT_CONNECTION_MANAGER
}




#[cfg(test)]
mod tests {
    use super::*;
    use agent_common_service::model::chat_model::{MethodData, SymbolData};

    #[test]
    fn test_deserialize_client_response() {
        // 测试客户端返回的JSON能否正确反序列化
        let json_str = r#"{"data":[{"body":"/**\n * <p>\n * </p>\n *\n * <AUTHOR> * 创建时间 2024-03-01\n */\npublic class ScanProject {\n    private String url;\n\n    public String getUrl() {\n        return url;\n    }\n\n    public void setUrl(String url) {\n        this.url = url;\n    }\n}","className":"ScanProject","filePath":"src/main/java/com/alipay/agent/pb/requestbean/ScanProject.java","isInterface":false,"methods":[{"body":"public String getUrl() {\n        return url;\n    }","className":"ScanProject","id":364725685,"isConstructor":false,"isLombokBuild":false,"methodName":"getUrl"}],"useFields":[],"useVariable":[]}],"status":"SUCCESS"}"#;

        let response: SymbolSearchResponse = serde_json::from_str(json_str).expect("Failed to deserialize");

        assert_eq!(response.status, "SUCCESS");
        assert_eq!(response.data.len(), 1);

        let symbol_data = &response.data[0];
        assert_eq!(symbol_data.class_name, "ScanProject");
        assert_eq!(symbol_data.file_path, Some("src/main/java/com/alipay/agent/pb/requestbean/ScanProject.java".to_string()));
        assert!(symbol_data.body.is_some());
        assert_eq!(symbol_data.methods.len(), 1);

        println!("✅ 反序列化测试成功: {:?}", response);
    }

    #[test]
    fn test_transform_symbol_response_class_search() {
        // 测试类搜索（methodName为空）
        let response = SymbolSearchResponse {
            data: vec![
                SymbolData {
                    class_name: "ScanProject".to_string(),
                    is_interface: false,
                    file_path: Some("src/main/java/com/alipay/agent/pb/requestbean/ScanProject.java".to_string()),
                    body: Some("public class ScanProject {\n    private String url;\n    public String getUrl() {\n        return url;\n    }\n}".to_string()),
                    methods: vec![
                        MethodData {
                            id: 955812832,
                            class_name: "ScanProject".to_string(),
                            method_name: Some("getUrl".to_string()),
                            is_constructor: false,
                            body: "public String getUrl() {\n        return url;\n    }".to_string(),
                            is_lombok_build: false,
                            ref_field_data: None,
                            identification: None,
                            param_data: None,
                            startLine: 0,
                            endLine: 0,
                        }
                    ],
                    use_fields: vec![],
                    use_variable: vec![],
                },
            ],
            status: "SUCCESS".to_string(),
        };

        // 类搜索参数（methodName为空）
        let search_params = SymbolAndStreamSearchParams {
            class_name: Some("ScanProject".to_string()),
            method_name: None,
            topN: None,
            project_url: None,
        };

        // 执行转换
        let results = transform_symbol_response(&response, &search_params);

        // 验证结果
        assert_eq!(results.len(), 1);
        let result = &results[0];
        assert_eq!(result.relative_path, "src/main/java/com/alipay/agent/pb/requestbean/ScanProject.java");
        assert_eq!(result.class_name, "ScanProject");
        assert_eq!(result.method_name, None);
        assert!(result.snippet.contains("public class ScanProject"));
        assert!(result.snippet.contains("private String url"));
    }

    #[test]
    fn test_transform_symbol_response_method_search() {
        // 测试方法搜索（methodName不为空）
        let response = SymbolSearchResponse {
            data: vec![
                SymbolData {
                    class_name: "ScanProject".to_string(),
                    is_interface: false,
                    file_path: Some("src/main/java/com/alipay/agent/pb/requestbean/ScanProject.java".to_string()),
                    body: Some("public class ScanProject {\n    private String url;\n    public String getUrl() {\n        return url;\n    }\n}".to_string()),
                    methods: vec![
                        MethodData {
                            id: 955812832,
                            class_name: "ScanProject".to_string(),
                            method_name: Some("getUrl".to_string()),
                            is_constructor: false,
                            body: "public String getUrl() {\n        return url;\n    }".to_string(),
                            is_lombok_build: false,
                            ref_field_data: None,
                            identification: None,
                            param_data: None,
                            startLine: 0,
                            endLine: 0,
                        }
                    ],
                    use_fields: vec![],
                    use_variable: vec![],
                },
            ],
            status: "SUCCESS".to_string(),
        };

        // 方法搜索参数（methodName不为空）
        let search_params = SymbolAndStreamSearchParams {
            class_name: Some("ScanProject".to_string()),
            method_name: Some("getUrl".to_string()),
            topN: None,
            project_url: None
        };

        // 执行转换
        let results = transform_symbol_response(&response, &search_params);

        // 验证结果
        assert_eq!(results.len(), 1);
        let result = &results[0];
        assert_eq!(result.relative_path, "src/main/java/com/alipay/agent/pb/requestbean/ScanProject.java");
        assert_eq!(result.class_name, "ScanProject");
        assert_eq!(result.method_name, Some("getUrl".to_string()));
        assert_eq!(result.snippet, "public String getUrl() {\n        return url;\n    }");
    }

    #[test]
    fn test_transform_symbol_response_no_file_path() {
        // 测试没有文件路径的情况
        let response = SymbolSearchResponse {
            data: vec![
                SymbolData {
                    class_name: "TestClass".to_string(),
                    is_interface: false,
                    file_path: None,
                    body: Some("public class TestClass {}".to_string()),
                    methods: vec![],
                    use_fields: vec![],
                    use_variable: vec![],
                }
            ],
            status: "SUCCESS".to_string(),
        };

        let search_params = SymbolAndStreamSearchParams {
            class_name: Some("TestClass".to_string()),
            method_name: None,
            topN: None,
            project_url:None
        };

        // 执行转换
        let results = transform_symbol_response(&response, &search_params);

        // 验证结果：没有文件路径的数据应该被过滤掉
        assert_eq!(results.len(), 0);
    }

    #[test]
    fn test_transform_usages_response_with_references() {
        use agent_common_service::model::chat_model::{UsagesSearchResponse, UsageData, MethodReferenceData, ReferencedMethodData};

        // 测试有引用的情况
        let response = UsagesSearchResponse {
            data: vec![
                UsageData {
                    class_name: Some("CodeEditHandler".to_string()),
                    qualified_name_class_name: Some("com.alipay.tsingyanprod.service.codegpt.handler.completion.CodeEditHandler".to_string()),
                    file_path: "app/service/src/main/java/com/alipay/tsingyanprod/service/codegpt/handler/completion/CodeEditHandler.java".to_string(),
                    method_name: "nextTab".to_string(),
                    text: "public Object nextTab(TaskContextModel<NextTabRequestBean, NextTabResponse> taskContextModel) {\n        taskContextModel.setResult(codeEditService.nextTab(taskContextModel.getRequest(), taskContextModel.getRecord()));\n        return null;\n    }".to_string(),
                    method_reference_list: vec![
                        MethodReferenceData {
                            class_name: Some("CodeEditHandler".to_string()),
                            qualified_name_class_name: Some("com.alipay.tsingyanprod.service.codegpt.handler.completion.CodeEditHandler".to_string()),
                            file_path: "app/service/src/main/java/com/alipay/tsingyanprod/service/codegpt/handler/completion/CodeEditHandler.java".to_string(),
                            is_super_resolve: false,
                            super_class_name: None,
                            super_qualified_name_class_name: None,
                            referenced_of_method_list: vec![
                                ReferencedMethodData {
                                    method_name: "nextTab".to_string(),
                                    text: "public Object nextTab(TaskContextModel<NextTabRequestBean, NextTabResponse> taskContextModel) {\n        taskContextModel.setResult(codeEditService.nextTab(taskContextModel.getRequest(), taskContextModel.getRecord()));\n        return null;\n    }".to_string(),
                                    startLine: 0,
                                    endLine: 0
                                }
                            ],
                        }
                    ],
                }
            ],
            status: "SUCCESS".to_string(),
        };

        // 执行转换
        let results = transform_usages_response(&response);

        // 验证结果
        assert_eq!(results.len(), 1);
        let result = &results[0];
        assert_eq!(result.relative_path, "app/service/src/main/java/com/alipay/tsingyanprod/service/codegpt/handler/completion/CodeEditHandler.java");
        assert_eq!(result.class_name, "CodeEditHandler");
        assert_eq!(result.method_name, Some("nextTab".to_string()));
        assert!(result.snippet.contains("public Object nextTab"));
    }

    #[test]
    fn test_transform_usages_response_empty_references() {
        use agent_common_service::model::chat_model::{UsagesSearchResponse, UsageData};

        // 测试没有引用的情况
        let response = UsagesSearchResponse {
            data: vec![
                UsageData {
                    class_name: Some("TestClass".to_string()),
                    qualified_name_class_name: Some("com.example.TestClass".to_string()),
                    file_path: "src/main/java/com/example/TestClass.java".to_string(),
                    method_name: "testMethod".to_string(),
                    text: "public void testMethod() {}".to_string(),
                    method_reference_list: vec![], // 空的引用列表
                }
            ],
            status: "SUCCESS".to_string(),
        };

        // 执行转换
        let results = transform_usages_response(&response);

        // 验证结果：没有引用的数据应该被过滤掉
        assert_eq!(results.len(), 0);
    }
}



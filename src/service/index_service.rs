use std::collections::{HashMap, HashSet};
use std::sync::atomic::{AtomicUsize, Ordering, AtomicBool};
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH, Instant, Duration};
use tokio::sync::{Mutex, RwLock, mpsc, Semaphore};
use tokio_util::sync::CancellationToken;
use log::{info, warn, error, debug};
use serde::{Deserialize, Serialize};
use once_cell::sync::Lazy;

use crate::utils::path::{get_all_file_by_url, get_file_by_url_and_status, QueueType, FileStatusResult};
use crate::utils::file_encoding::read_file_smart;
use crate::dialogue::repo_index_operator::{monitor_git_repo_branch, RepoStatusEnum};
use crate::dialogue::codefuse_index_repository::{build_chunk_index, CHUNK_CLIENT};
use crate::ast_chunk::ast_chunk::{convert_to_codefusechunk_from_ast_chunk, convert_to_codefusechunk_and_vectoritem_from_remote_chunk_data};
use crate::ast_chunk::ast_chunk_net::{request_ast_chunk_data,  sync_batch_file_data_from_remote};
use crate::dialogue::misc_util::get_relative_path;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::remote_client::{cge_embedding, CgeEmbeddingRequestBean, CgeEmbeddingResponse, IndexTypeEnum, EMBEDDING_BUILD};
use agent_db::dal::vector_client::{VECTOR_CLIENT, VectorItem};
use agent_db::dal::index_client::Indexable;
use agent_db::domain::code_chat_domain::CodefuseChunk;
use agent_db::tools::common_tools::LINE_ENDING;
use ignore::DirEntry;
use std::fs;
use tokio::fs as async_fs;
use futures::future::join_all;
use std::pin::Pin;
use std::future::Future;
use std::path::{Path, PathBuf};
use agent_common_service::model::chat_model::IndexOperateRequestBean;
use agent_db::remote::rpc_model::BaseResponse;
use tokio::task::JoinHandle;
use crate::function::chat_strategy::query_data_status;

/// 步骤3到步骤4的数据类型
#[derive(Debug)]
enum Step3ToStep4Data {
    DirectVectorItems(Vec<VectorItem>),
    EmbeddingTask(tokio::task::JoinHandle<BaseResponse<CgeEmbeddingResponse>>, Vec<CodefuseChunk>),
}

/// 索引构建任务状态
#[derive(Debug, Clone, PartialEq)]
pub enum IndexBuildStatus {
    /// 空闲状态
    Idle,
    /// 正在构建中
    Building,
    /// 构建完成
    Completed,
    /// 构建失败
    Failed,
}

/// 索引构建任务信息
#[derive(Clone)]
pub struct IndexBuildTask {
    /// 项目URL
    pub project_url: String,
    /// 分支名称
    pub branch: String,
    /// 任务状态
    pub status: IndexBuildStatus,
    /// 开始时间
    pub start_time: u128,
    /// 取消令牌，用于停止任务
    pub cancellation_token: Option<CancellationToken>,
}

impl std::fmt::Debug for IndexBuildTask {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("IndexBuildTask")
            .field("project_url", &self.project_url)
            .field("branch", &self.branch)
            .field("status", &self.status)
            .field("start_time", &self.start_time)
            .finish()
    }
}

impl IndexBuildTask {
    pub fn new(project_url: String, branch: String) -> Self {
        Self {
            project_url,
            branch,
            status: IndexBuildStatus::Idle,
            start_time: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis(),
            cancellation_token: None,
        }
    }

    pub fn with_cancellation_token(mut self, token: CancellationToken) -> Self {
        self.cancellation_token = Some(token);
        self
    }
}



/// 全局索引构建状态管理器
pub struct IndexBuildManager {
    /// 当前正在运行的任务
    current_task: Arc<RwLock<Option<IndexBuildTask>>>,
    /// 历史任务记录，用于时间间隔检查 (project_url_branch -> last_build_time)
    build_history: Arc<RwLock<HashMap<String, u128>>>,
    /// 当前任务的异步任务句柄
    current_task_handles: Arc<RwLock<Vec<tokio::task::JoinHandle<()>>>>,
}

impl IndexBuildManager {
    pub fn new() -> Self {
        Self {
            current_task: Arc::new(RwLock::new(None)),
            build_history: Arc::new(RwLock::new(HashMap::new())),
            current_task_handles: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 获取当前任务状态
    pub async fn get_current_task(&self) -> Option<IndexBuildTask> {
        let task = self.current_task.read().await;
        task.clone()
    }

    /// 设置当前任务
    pub async fn set_current_task(&self, task: Option<IndexBuildTask>) {
        let mut current = self.current_task.write().await;
        *current = task;
    }

    /// 检查是否有相同项目和分支的任务正在运行
    /// 兜底方案：如果任务运行超过1小时，自动停止并返回false
    pub async fn is_same_project_branch_running(&self, project_url: &str, branch: &str) -> bool {
        let task = self.current_task.read().await;
        if let Some(ref current_task) = *task {
            // 检查项目URL和分支是否匹配
            if current_task.project_url == project_url
                && current_task.branch == branch
                && current_task.status == IndexBuildStatus::Building {

                // 兜底方案：检查任务是否运行超过1小时（3600000毫秒）
                let current_time = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis();

                let task_running_time = current_time - current_task.start_time;
                const ONE_HOUR_MILLIS: u128 = 60 * 60 * 1000; // 1小时 = 3600000毫秒

                if task_running_time > ONE_HOUR_MILLIS {
                    warn!("任务运行超过1小时 ({} ms)，触发兜底方案自动停止: project_url={}, branch={}",
                          task_running_time, project_url, branch);

                    // 释放读锁，避免死锁
                    drop(task);

                    // 异步停止超时任务
                    if let Err(e) = self.stop_current_task().await {
                        error!("兜底方案停止超时任务失败: {}", e);
                    } else {
                        info!("兜底方案成功停止超时任务");
                    }

                    return false;
                }

                return true;
            }
        }
        false
    }

    /// 检查是否有相同项目但不同分支的任务正在运行
    pub async fn is_same_project_different_branch_running(&self, project_url: &str, branch: &str) -> bool {
        let task = self.current_task.read().await;
        if let Some(ref current_task) = *task {
            return current_task.project_url == project_url
                && current_task.branch != branch
                && current_task.status == IndexBuildStatus::Building;
        }
        false
    }

    /// 检查是否有不同项目的任务正在运行
    pub async fn is_different_project_running(&self, project_url: &str) -> bool {
        let task = self.current_task.read().await;
        if let Some(ref current_task) = *task {
            return current_task.project_url != project_url
                && current_task.status == IndexBuildStatus::Building;
        }
        false
    }

    /// 停止当前任务
    pub async fn stop_current_task(&self) -> Result<(), String> {
        info!("开始停止当前构建任务");

        // 1. 获取当前任务并检查是否有正在运行的任务
        let current_task = {
            let task_guard = self.current_task.read().await;
            task_guard.clone()
        };

        if let Some(task) = current_task {
            if task.status != IndexBuildStatus::Building {
                info!("当前任务状态为 {:?}，无需停止", task.status);
                return Ok(());
            }

            info!("停止正在运行的任务: project_url={}, branch={}", task.project_url, task.branch);

            // 2. 发送取消信号
            if let Some(ref cancellation_token) = task.cancellation_token {
                info!("发送取消信号到任务");
                cancellation_token.cancel();
            }

            // 3. 中止所有相关的异步任务句柄
            let mut handles_guard = self.current_task_handles.write().await;
            let handles = std::mem::take(&mut *handles_guard);

            if !handles.is_empty() {
                info!("中止 {} 个异步任务句柄", handles.len());
                for handle in handles {
                    handle.abort();
                }
            }

            // 4. 清理当前任务记录，避免状态残留
            self.set_current_task(None).await;

            info!("任务停止完成，已清理任务记录");
        } else {
            info!("当前没有正在运行的任务");
        }

        Ok(())
    }

    /// 添加任务句柄到管理器
    pub async fn add_task_handle(&self, handle: tokio::task::JoinHandle<()>) {
        let mut handles_guard = self.current_task_handles.write().await;
        handles_guard.push(handle);
    }

    /// 清理已完成的任务句柄
    pub async fn cleanup_task_handles(&self) {
        let mut handles_guard = self.current_task_handles.write().await;
        handles_guard.clear();
    }

    /// 清理已完成或失败的任务记录
    /// 这个方法会检查当前任务状态，如果不是Building状态，则清理任务记录
    pub async fn cleanup_finished_tasks(&self) {
        let task = self.current_task.read().await;
        if let Some(ref current_task) = *task {
            if current_task.status != IndexBuildStatus::Building {
                info!("发现非Building状态的任务记录 (状态: {:?})，进行清理", current_task.status);
                drop(task); // 释放读锁
                self.set_current_task(None).await;
                info!("已清理非Building状态的任务记录");
            }
        }
    }

    /// 检查同一个project_url和branch在1小时内是否已经构建过
    /// 返回 (should_wait, remaining_minutes) - should_wait表示是否需要等待，remaining_minutes表示剩余等待时间（分钟）
    pub async fn check_build_time_interval(&self, project_url: &str, branch: &str) -> (bool, u64) {
        let history = self.build_history.read().await;
        let key = format!("{}#{}", project_url, branch);

        if let Some(&last_build_time) = history.get(&key) {
            let current_time = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis();

            let time_diff_ms = current_time - last_build_time;
            let time_diff_minutes = time_diff_ms / (1000 * 60); // 转换为分钟

            const ONE_HOUR_MINUTES: u128 = 60;

            if time_diff_minutes < ONE_HOUR_MINUTES {
                let remaining_minutes = ONE_HOUR_MINUTES - time_diff_minutes;
                info!("项目 {} 分支 {} 在 {} 分钟前已构建过，需要等待 {} 分钟",
                      project_url, branch, time_diff_minutes, remaining_minutes);
                return (true, remaining_minutes as u64);
            }
        }

        (false, 0)
    }

    /// 记录构建完成时间
    pub async fn record_build_completion(&self, project_url: &str, branch: &str) {
        let mut history = self.build_history.write().await;
        let key = format!("{}#{}", project_url, branch);
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();

        history.insert(key, current_time);
        info!("记录项目 {} 分支 {} 的构建完成时间", project_url, branch);
    }
}

/// 全局索引构建管理器实例
pub static INDEX_BUILD_MANAGER: Lazy<IndexBuildManager> = Lazy::new(|| IndexBuildManager::new());

/// 索引服务响应类型
#[derive(Debug, Serialize, Deserialize)]
pub struct IndexServiceResponse {
    /// 是否成功
    pub success: bool,
    /// 响应消息
    pub message: String,
    /// 任务状态
    pub status: String,
    /// 额外数据
    pub data: Option<serde_json::Value>,
}

impl IndexServiceResponse {
    pub fn success(message: &str) -> Self {
        Self {
            success: true,
            message: message.to_string(),
            status: "success".to_string(),
            data: None,
        }
    }

    pub fn error(message: &str) -> Self {
        Self {
            success: false,
            message: message.to_string(),
            status: "error".to_string(),
            data: None,
        }
    }

    pub fn skipped(message: &str) -> Self {
        Self {
            success: true,
            message: message.to_string(),
            status: "skipped".to_string(),
            data: None,
        }
    }
}

/// 索引服务主要接口
pub struct IndexService;

impl IndexService {
    /// 构建索引的主要接口
    ///
    /// # 参数
    /// * `project_url` - 项目URL
    /// * `branch` - 分支名称
    /// * `concurrency_config` - 并发控制配置
    ///
    /// # 返回值
    /// 返回索引服务响应
    pub async fn build_index(project_url: String, branch: String, concurrency_timeout_secs: u64) -> IndexServiceResponse {
        info!("收到索引构建请求: project_url={}, branch={}", project_url, branch);
        // 1. 检查并发控制和资源保护机制
        let manager = &INDEX_BUILD_MANAGER;
        if AGENT_CONFIG.build_test_flag == 0{
            // 2.1 相同项目和分支：如果当前已经在构建中，直接跳过
            if manager.is_same_project_branch_running(&project_url, &branch).await {
                let message = format!("项目 {} 分支 {} 已经在构建中，跳过此次请求", project_url, branch);
                warn!("{}", message);
                return IndexServiceResponse::skipped(&message);
            }

            // 2.1.1 检查时间间隔：同一个project_url和branch，1小时内只会构建一次
            let (should_wait, remaining_minutes) = manager.check_build_time_interval(&project_url, &branch).await;
            if should_wait {
                let message = format!("项目 {} 分支 {} 在1小时内已经构建过，请等待 {} 分钟后再试",
                                      project_url, branch, remaining_minutes);
                warn!("{}", message);
                return IndexServiceResponse::skipped(&message);
            }

            // 2.2 相同项目，不同分支：停止正在运行的任务流，启动新的构建任务
            if manager.is_same_project_different_branch_running(&project_url, &branch).await {
                info!("检测到相同项目不同分支，停止当前任务并启动新任务");
                if let Err(e) = manager.stop_current_task().await {
                    let message = format!("停止当前任务失败: {}", e);
                    error!("{}", message);
                    return IndexServiceResponse::error(&message);
                }
            }

            // 2.3 不同项目：停止正在运行的任务流，启动新的构建任务
            if manager.is_different_project_running(&project_url).await {
                info!("检测到不同项目，停止当前任务并启动新任务");
                if let Err(e) = manager.stop_current_task().await {
                    let message = format!("停止当前任务失败: {}", e);
                    error!("{}", message);
                    return IndexServiceResponse::error(&message);
                }
            }
        }
        // 2.4 在确定需要启动新任务后，清理已完成或失败的任务记录，避免状态残留
        manager.cleanup_finished_tasks().await;

        // 3. 开始新的索引构建任务
        match Self::start_index_build_task(project_url.clone(), branch.clone(),concurrency_timeout_secs).await {
            Ok(_) => {
                let message = format!("成功启动索引构建任务: {} - {}", project_url, branch);
                info!("{}", message);
                IndexServiceResponse::success(&message)
            }
            Err(e) => {
                let message = format!("启动索引构建任务失败: {}", e);
                error!("{}", message);
                IndexServiceResponse::error(&message)
            }
        }
    }

    /// 启动索引构建任务的内部实现
    async fn start_index_build_task(project_url: String, branch: String, concurrency_timeout_secs: u64) -> Result<(), String> {
        // 创建取消令牌
        let cancellation_token = CancellationToken::new();

        // 创建新的索引构建任务
        let mut task = IndexBuildTask::new(project_url.clone(), branch.clone())
            .with_cancellation_token(cancellation_token.clone());
        task.status = IndexBuildStatus::Building;

        // 设置当前任务
        INDEX_BUILD_MANAGER.set_current_task(Some(task.clone())).await;

        // 清理之前的任务句柄
        INDEX_BUILD_MANAGER.cleanup_task_handles().await;

        // 执行核心索引构建逻辑
        match Self::execute_index_build_core(&project_url, &branch, concurrency_timeout_secs, cancellation_token).await {
            Ok(_) => {
                info!("索引构建任务成功完成: project_url={}, branch={}", project_url, branch);

                // 记录构建完成时间，用于时间间隔检查
                INDEX_BUILD_MANAGER.record_build_completion(&project_url, &branch).await;

                // 清理任务句柄
                INDEX_BUILD_MANAGER.cleanup_task_handles().await;

                // 清理当前任务记录，避免状态残留
                INDEX_BUILD_MANAGER.set_current_task(None).await;
                info!("已清理任务记录，避免状态残留");

                Ok(())
            }
            Err(e) => {
                error!("索引构建任务失败: project_url={}, branch={}, error={}", project_url, branch, e);

                // 清理任务句柄
                INDEX_BUILD_MANAGER.cleanup_task_handles().await;

                // 清理当前任务记录，避免状态残留
                INDEX_BUILD_MANAGER.set_current_task(None).await;
                info!("已清理失败任务记录，避免状态残留");

                Err(e)
            }
        }
    }

    /// 核心索引构建逻辑（基于test_build_index_task的实现）
    async fn execute_index_build_core(project_url: &str, branch: &str, concurrency_timeout_secs: u64, cancellation_token: CancellationToken) -> Result<(), String> {
        info!("开始执行核心索引构建逻辑: project_url={}, branch={}", project_url, branch);

        // 这里将实现test_build_index_task的步骤1-4
        // 由于内容较多，将在下一个函数中实现
        Self::execute_build_steps(project_url, branch, concurrency_timeout_secs, cancellation_token).await
    }




    /// 执行构建步骤（重构为并发模式）
    async fn execute_build_steps(project_url: &str, branch: &str, concurrency_timeout_secs: u64, cancellation_token: CancellationToken) -> Result<(), String> {
        // 记录整个构建开始时间
        let build_start_time = Instant::now();

        // 检查是否已被取消
        if cancellation_token.is_cancelled() {
            info!("任务在步骤1开始前已被取消");
            return Err("任务已被取消".to_string());
        }

        // 1. 通过 get_file_by_url_and_status 获取文件状态信息（添加耗时监控）
        info!("步骤1：开始获取项目文件状态信息: {}", project_url);
        let step1_start_time = Instant::now();

        let index_file_status = get_file_by_url_and_status(&project_url.to_string()).await;

        let step1_duration = step1_start_time.elapsed();
        info!("步骤1完成，耗时: {:?}", step1_duration);

        // 检查是否在步骤1后被取消
        if cancellation_token.is_cancelled() {
            info!("任务在步骤1完成后被取消");
            return Err("任务已被取消".to_string());
        }

        info!("获取到文件状态信息 - CREATE_URL: {}, CREATE_MODEL: {}, UPDATE_URL: {}, UPDATE_MODEL: {}, DELETE: {}, NEED_VECTOR: {}",
              index_file_status.create_file_with_url.len(),
              index_file_status.create_file_with_model.len(),
              index_file_status.update_file_with_url.len(),
              index_file_status.update_file_with_model.len(),
              index_file_status.delete_file.len(),
              index_file_status.no_vector_file.len());

        // 记录Git仓库信息
        if let Some(ref git_repo) = index_file_status.git_repo {
            info!("Git仓库信息: {}", git_repo);
        }
        if let Some(ref remote_branch) = index_file_status.remote_branch {
            info!("远程分支信息: {}", remote_branch);
        }

        // 检查是否有文件需要处理
        let total_files = index_file_status.create_file_with_url.len() +
                         index_file_status.create_file_with_model.len() +
                         index_file_status.update_file_with_url.len() +
                         index_file_status.update_file_with_model.len()+
                         index_file_status.no_vector_file.len()+
                         index_file_status.delete_file.len();

        if total_files == 0 {
            let error_msg = format!("项目 {} 中没有找到需要处理的文件", project_url);
            error!("{}", error_msg);
            return Err(error_msg);
        }

        // 使用传入的并发控制配置
        info!("使用并发控制配置: initial_concurrency={}, reduced_concurrency={}, timeout_secs={}",
              AGENT_CONFIG.initial_concurrency,
              AGENT_CONFIG.reduced_concurrency,
              concurrency_timeout_secs);

        // 记录开始时间用于动态并发控制
        let concurrency_start_time = Instant::now();

        // 移除统计变量，简化执行流程

        // 创建数据流通道，优化缓冲区大小
        // 步骤2 -> 步骤3：异步任务句柄，步骤3通过await获取结果
        let (step2_to_step3_sender, step2_to_step3_receiver) = mpsc::channel::<tokio::task::JoinHandle<Result<(String, Vec<CodefuseChunk>, Option<Vec<VectorItem>>), String>>>(AGENT_CONFIG.step2_channel_size);
        // 步骤3 -> 步骤4：支持直接传递VectorItem或embedding任务
        let (step3_to_step4_sender, step3_to_step4_receiver) = mpsc::channel::<Step3ToStep4Data>(AGENT_CONFIG.step3_channel_size);

        // 启动步骤4：处理VectorItem和embedding任务，直接调用batch_upsert保存
        let cancellation_token_step4 = cancellation_token.clone();
        let step4_handle = tokio::spawn(async move {
            tokio::select! {
                result = Self::execute_step4_optimized(step3_to_step4_receiver, concurrency_timeout_secs, concurrency_start_time) => {
                    result
                }
                _ = cancellation_token_step4.cancelled() => {
                    info!("步骤4被取消");
                    Err("步骤4被取消".to_string())
                }
            }
        });

        // 将步骤4句柄添加到管理器
        let step4_handle_for_manager = tokio::spawn({
            let handle = step4_handle.abort_handle();
            async move {
                // 这个任务只是为了持有abort_handle
            }
        });
        INDEX_BUILD_MANAGER.add_task_handle(step4_handle_for_manager).await;

        // 启动步骤3：处理chunk数据，支持直接传递VectorItem或启动embedding任务
        let step3_to_step4_sender_clone = step3_to_step4_sender.clone();
        let project_url_clone_for_step3 = project_url.to_string();
        let index_file_status_clone_for_step3 = index_file_status.clone();
        let cancellation_token_step3 = cancellation_token.clone();
        let step3_handle = tokio::spawn(async move {
            tokio::select! {
                result = Self::execute_step3_new(
                    step2_to_step3_receiver,
                    step3_to_step4_sender_clone,
                    concurrency_timeout_secs,
                    concurrency_start_time,
                    project_url_clone_for_step3,
                    index_file_status_clone_for_step3
                ) => {
                    result
                }
                _ = cancellation_token_step3.cancelled() => {
                    info!("步骤3被取消");
                    Err("步骤3被取消".to_string())
                }
            }
        });

        // 将步骤3句柄添加到管理器
        let step3_handle_for_manager = tokio::spawn({
            let handle = step3_handle.abort_handle();
            async move {
                // 这个任务只是为了持有abort_handle
            }
        });
        INDEX_BUILD_MANAGER.add_task_handle(step3_handle_for_manager).await;

        // 检查是否在步骤2开始前被取消
        if cancellation_token.is_cancelled() {
            info!("任务在步骤2开始前被取消");
            return Err("任务已被取消".to_string());
        }

        // 2. 步骤2：并发处理文件，拆解成chunk片段并保存到tantivy
        info!("步骤2：开始并发处理文件，拆解chunk并保存到tantivy");
        let step2_start_time = Instant::now();

        // 启动步骤2：文件分块处理并发任务
        let project_url_clone = project_url.to_string();
        let branch_clone = branch.to_string();
        let step2_to_step3_sender_clone = step2_to_step3_sender.clone();
        let index_file_status_clone = index_file_status.clone();
        let cancellation_token_step2 = cancellation_token.clone();

        let step2_handle = tokio::spawn(async move {
            tokio::select! {
                result = Self::execute_step2_concurrent(
                    project_url_clone,
                    branch_clone,
                    step2_to_step3_sender_clone,
                    index_file_status_clone,
                    cancellation_token_step2.clone(),
                ) => {
                    result
                }
                _ = cancellation_token_step2.cancelled() => {
                    info!("步骤2被取消");
                    Err("步骤2被取消".to_string())
                }
            }
        });

        // 将步骤2句柄添加到管理器
        let step2_handle_for_manager = tokio::spawn({
            let handle = step2_handle.abort_handle();
            async move {
                // 这个任务只是为了持有abort_handle
            }
        });
        INDEX_BUILD_MANAGER.add_task_handle(step2_handle_for_manager).await;

        // 等待步骤2完成
        let step2_result = tokio::select! {
            result = step2_handle => {
                match result {
                    Ok(result) => result,
                    Err(e) => {
                        error!("步骤2任务执行失败: {}", e);
                        return Err("步骤2任务执行失败".to_string());
                    }
                }
            }
            _ = cancellation_token.cancelled() => {
                info!("在等待步骤2完成时任务被取消");
                return Err("任务已被取消".to_string());
            }
        };

        let step2_duration = step2_start_time.elapsed();
        info!("步骤2完成，耗时: {:?}", step2_duration);
        CHUNK_CLIENT.commit();
        // 检查步骤2结果
        if let Err(e) = step2_result {
            error!("步骤2执行失败: {}", e);
            return Err(format!("步骤2执行失败: {}", e));
        }

        // 关闭步骤2到步骤3的通道，通知步骤3没有更多数据
        drop(step2_to_step3_sender);

        // 开始计时步骤3
        let step3_start_time = Instant::now();

        // 等待步骤3完成
        let step3_result = tokio::select! {
            result = step3_handle => {
                match result {
                    Ok(result) => result,
                    Err(e) => {
                        error!("步骤3任务执行失败: {}", e);
                        return Err("步骤3任务执行失败".to_string());
                    }
                }
            }
            _ = cancellation_token.cancelled() => {
                info!("在等待步骤3完成时任务被取消");
                return Err("任务已被取消".to_string());
            }
        };

        let step3_duration = step3_start_time.elapsed();
        info!("步骤3完成，耗时: {:?}", step3_duration);

        // 检查步骤3结果
        if let Err(e) = step3_result {
            error!("步骤3执行失败: {}", e);
            return Err(format!("步骤3执行失败: {}", e));
        }

        // 关闭步骤3到步骤4的通道，通知步骤4没有更多任务数据
        drop(step3_to_step4_sender);

        // 开始计时步骤4
        let step4_start_time = Instant::now();

        // 等待步骤4完成
        let step4_result = tokio::select! {
            result = step4_handle => {
                match result {
                    Ok(result) => result,
                    Err(e) => {
                        error!("步骤4任务执行失败: {}", e);
                        return Err("步骤4任务执行失败".to_string());
                    }
                }
            }
            _ = cancellation_token.cancelled() => {
                info!("在等待步骤4完成时任务被取消");
                return Err("任务已被取消".to_string());
            }
        };

        let step4_duration = step4_start_time.elapsed();
        info!("步骤4完成，耗时: {:?}", step4_duration);

        // 步骤4已经直接完成了向量保存，无需额外等待
        let total_vectors_saved = match step4_result {
            Ok(count) => count,
            Err(_) => 0,
        };

        info!("向量保存完成，总共保存了 {} 个向量", total_vectors_saved);

        // 不再需要停止TaskExecutor

        // 5. 统计整个构建任务耗时
        let total_duration = build_start_time.elapsed();

        // 输出统计信息
        info!("=== 索引构建完成统计 ===");
        info!("项目路径: {}", project_url);
        info!("分支: {}", branch);
        info!("扫描文件总数: {}", total_files);
        info!("生成向量总数: {}", total_vectors_saved);
        info!("步骤2耗时: {:?}", step2_duration);
        info!("步骤3耗时: {:?}", step3_duration);
        info!("步骤4耗时: {:?}", step4_duration);
        info!("整个构建任务总耗时: {:?}", total_duration);
        info!("==================");
        let query_status = IndexOperateRequestBean{
            projectUrl: Some(project_url.to_string()),
            branch: Some(branch.to_string()),
            printDetail: Some(false),
            diffAnalysis: Some(false),
        };
        let result = query_data_status(query_status).await;
        info!("构建流程结束，索引状态: {:?}",result);

        // 验证结果
        if total_vectors_saved == 0 {
            warn!("没有生成任何向量");
        }

        // 检查是否需要执行删除操作（步骤5）
        if !index_file_status.delete_file.is_empty() {
            info!("步骤5：发现需要删除的文件，开始执行删除操作");

            // 检查是否在删除操作前被取消
            if cancellation_token.is_cancelled() {
                info!("任务在删除操作前被取消");
                return Err("任务已被取消".to_string());
            }

            // 执行删除操作
            match Self::execute_delete_operations(project_url, &index_file_status.delete_file).await {
                Ok(_) => {
                    info!("步骤5：删除操作完成");
                }
                Err(e) => {
                    error!("步骤5：删除操作失败: {}", e);
                    return Err(format!("删除操作失败: {}", e));
                }
            }
        }

        Ok(())
    }

    /// 步骤2：快速发起异步任务，返回任务句柄给步骤3
    async fn execute_step2_concurrent(
        project_url: String,
        branch: String,
        step2_to_step3_sender: mpsc::Sender<tokio::task::JoinHandle<Result<(String, Vec<CodefuseChunk>, Option<Vec<VectorItem>>), String>>>,
        index_file_status: FileStatusResult,
        cancellation_token: CancellationToken,
    ) -> Result<(), String> {
        info!("步骤2：开始快速发起异步文件处理任务");
        info!("文件状态数据 - Git仓库: {:?}", index_file_status.git_repo);
        info!("文件状态数据 - 远程分支: {:?}", index_file_status.remote_branch);
        info!("文件状态数据 - CREATE_URL队列大小: {}", index_file_status.create_file_with_url.len());
        info!("文件状态数据 - CREATE_MODEL队列大小: {}", index_file_status.create_file_with_model.len());

        // 检查是否需要构建向量：只有当 AGENT_CONFIG.index_extra_type 包含 CHUNK_VECTOR 时才进行向量相关操作
        let need_vector = AGENT_CONFIG.index_extra_type.contains(&IndexTypeEnum::CHUNK_VECTOR);
        if !need_vector {
            info!("步骤2：跳过向量相关操作，但保留Tantivy全文搜索索引构建，因为 AGENT_CONFIG.index_extra_type 不包含 CHUNK_VECTOR");
        }

        // 检查是否已被取消
        if cancellation_token.is_cancelled() {
            info!("步骤2在开始前被取消");
            return Err("步骤2被取消".to_string());
        }

        let mut task_count = 0;

        // 快速发起CREATE_MODEL队列异步任务（使用AST chunk）
        if !index_file_status.create_file_with_model.is_empty() {
            info!("快速发起CREATE_MODEL队列异步任务，使用AST chunk方式，文件数: {}", index_file_status.create_file_with_model.len());

            for file_path in &index_file_status.create_file_with_model {
                // 检查是否被取消
                if cancellation_token.is_cancelled() {
                    info!("步骤2在处理MODEL文件时被取消");
                    return Err("步骤2被取消".to_string());
                }

                let project_url_clone = project_url.clone();
                let branch_clone = branch.clone();
                let git_repo_clone = index_file_status.git_repo.clone().unwrap_or_default();
                let file_path_clone = file_path.clone();
                let file_path_for_error = file_path.clone(); // 用于错误日志的副本
                let cancellation_token_clone = cancellation_token.clone();

                // 创建异步任务句柄，不等待执行
                let task_handle = tokio::spawn(async move {
                    tokio::select! {
                        result = Self::process_single_model_file(
                            &file_path_clone,
                            &project_url_clone,
                            &branch_clone,
                            &git_repo_clone,
                        ) => {
                            result
                        }
                        _ = cancellation_token_clone.cancelled() => {
                            Err(format!("处理文件 {} 被取消", file_path_clone))
                        }
                    }
                });

                // 只有需要向量时才发送任务句柄给步骤3
                if need_vector {
                    if let Err(e) = step2_to_step3_sender.send(task_handle).await {
                        error!("发送MODEL文件任务句柄到步骤3失败: {} - {}", file_path_for_error, e);
                    }
                } else {
                    // 不需要向量时，等待任务完成但不发送给步骤3
                    match task_handle.await {
                        Ok(Ok(_)) => {
                            // 任务成功完成，但不发送给步骤3
                        }
                        Ok(Err(e)) => {
                            error!("处理MODEL文件失败: {} - {}", file_path_for_error, e);
                        }
                        Err(e) => {
                            error!("MODEL文件任务执行失败: {} - {}", file_path_for_error, e);
                        }
                    }
                }
                task_count += 1;
            }
        }

        // 快速发起CREATE_URL队列异步任务（使用远程同步）
        if !index_file_status.create_file_with_url.is_empty() {
            info!("快速发起CREATE_URL队列异步任务，使用远程同步方式，文件数: {}", index_file_status.create_file_with_url.len());
            info!("注意：如果远程服务没有数据，这些文件将自动降级为本地AST处理");

            // 将URL文件按批次发起异步任务
            let url_files: Vec<String> = index_file_status.create_file_with_url.iter().cloned().collect();
            for batch in url_files.chunks(AGENT_CONFIG.url_batch_size) {
                // 检查是否被取消
                if cancellation_token.is_cancelled() {
                    info!("步骤2在处理URL文件批次时被取消");
                    return Err("步骤2被取消".to_string());
                }

                let batch_files: Vec<String> = batch.iter().cloned().collect();
                let project_url_clone = project_url.clone();
                let branch_clone = branch.clone();
                let git_repo_clone = index_file_status.git_repo.clone().unwrap_or_default();
                let cancellation_token_clone = cancellation_token.clone();

                // 创建异步任务句柄，不等待执行
                let task_handle = tokio::spawn(async move {
                    tokio::select! {
                        result = Self::process_url_files_batch(
                            batch_files,
                            &project_url_clone,
                            &branch_clone,
                            &git_repo_clone,
                        ) => {
                            result
                        }
                        _ = cancellation_token_clone.cancelled() => {
                            Err("处理URL文件批次被取消".to_string())
                        }
                    }
                });

                // 立即发送任务句柄给步骤3，不等待任务完成
                if let Err(e) = step2_to_step3_sender.send(task_handle).await {
                    error!("发送URL文件批次任务句柄到步骤3失败: {}", e);
                }
                task_count += 1;
            }
        }

        // 步骤2快速完成：只发起异步任务，立即返回任务句柄
        info!("步骤2：快速发起了 {} 个异步任务，立即返回任务句柄给步骤3", task_count);
        info!("步骤2：所有耗时操作都在异步任务中执行，步骤2立即完成");
        info!("步骤2：步骤3将通过await获取异步任务的执行结果");

        info!("步骤2：异步任务已发起，步骤2执行完成");
        Ok(())
    }



    /// 处理单个MODEL文件
    async fn process_single_model_file(
        file_path: &str,
        project_url: &str,
        branch: &str,
        git_repo_url: &str,
    ) -> Result<(String, Vec<CodefuseChunk>, Option<Vec<VectorItem>>), String> {
        // 构建绝对路径：如果file_path是相对路径，则结合project_url构建绝对路径
        let absolute_file_path = if Path::new(file_path).is_absolute() {
            file_path.to_string()
        } else {
            // file_path是相对路径，结合project_url构建绝对路径
            Path::new(project_url).join(file_path).to_string_lossy().to_string()
        };

        // 智能读取文件内容，支持多种编码（UTF-8, GBK, GB2312, GB18030等）
        let content = match read_file_smart(&absolute_file_path).await {
            Ok(content) => content,
            Err(e) => {
                return Err(format!("读取文件失败: {} - {}", absolute_file_path, e));
            }
        };

        // 调用AST chunk服务
        match request_ast_chunk_data(&git_repo_url.to_string(), &file_path.to_string(), &content).await {
            Some(ast_chunk_data) => {
                let codefuse_chunks:Vec<CodefuseChunk> =   if ast_chunk_data.is_empty() {
                    info!("文件 {} 没有生成AST chunks, 降级为本地切分.", file_path);
                    let line_vec: Vec<String> = content.split(LINE_ENDING).map(|s| s.to_string()).collect();
                    let ast_result = build_chunk_index(Path::new(file_path),&line_vec,  &project_url.to_string(), &branch.to_string());
                    let chunk_result = ast_result.unwrap_or_else(|e| {
                        error!("build_chunk_index error: {:?}", e);
                        Vec::new()
                    });
                    chunk_result
                }else{
                    convert_to_codefusechunk_from_ast_chunk(
                        ast_chunk_data,
                        &project_url.to_string(),
                        &branch.to_string(),
                        &file_path.to_string(),
                    )
                };

                if !codefuse_chunks.is_empty() {
                    // 保存到tantivy
                    if let Err(e) = CHUNK_CLIENT.save_batch(&codefuse_chunks) {
                        warn!("保存chunks到tantivy失败，但继续处理: {} - {}", file_path, e);
                    }

                    // 返回结果给步骤3，不包含VectorItem（MODEL类型不需要）
                    Ok((file_path.to_string(), codefuse_chunks, None))
                } else {
                    Ok((file_path.to_string(), Vec::new(), None))
                }
            }
            None => {
                Err(format!("调用AST chunk服务失败: {}", file_path))
            }
        }
    }

    /// 处理URL文件批次（单个批次处理）
    async fn process_url_files_batch(
        batch_files: Vec<String>,
        project_url: &str,
        branch: &str,
        git_repo_url: &str,
    ) -> Result<(String, Vec<CodefuseChunk>, Option<Vec<VectorItem>>), String> {
        info!("处理URL文件批次，文件数: {}", batch_files.len());

        // 转换为相对路径
        let relative_paths: Vec<String> = batch_files.iter()
            .map(|path| get_relative_path(&project_url.to_string(), path))
            .collect();

        // 调用远程同步服务
        match sync_batch_file_data_from_remote(git_repo_url, branch, relative_paths.clone()).await {
            Some(remote_data) => {
                if remote_data.is_empty() {
                    info!("批次文件没有远程数据，降级为本地处理: {:?}", relative_paths);
                    // 降级处理：当远程没有数据时，使用本地AST chunk方式处理
                    return Self::process_batch_files_locally(batch_files, project_url, branch, git_repo_url).await;
                }

                // 转换为CodefuseChunk和VectorItem
                let (codefuse_chunks, vector_items) = convert_to_codefusechunk_and_vectoritem_from_remote_chunk_data(
                    remote_data,
                    &project_url.to_string(),
                    &branch.to_string(),
                );

                if !codefuse_chunks.is_empty() {
                    // 保存到tantivy
                    if let Err(e) = CHUNK_CLIENT.save_batch(&codefuse_chunks) {
                        warn!("保存chunks到tantivy失败，但继续处理: {:?} - {}", relative_paths, e);
                    }

                    // 检查是否需要返回向量数据
                    let vector_data = if AGENT_CONFIG.index_extra_type.contains(&IndexTypeEnum::CHUNK_VECTOR) {
                        Some(vector_items)
                    } else {
                        None // 不需要向量时返回None
                    };

                    // 返回结果给步骤3
                    let batch_name = format!("batch_{}", relative_paths.join(","));
                    Ok((batch_name, codefuse_chunks, vector_data))
                } else {
                    info!("远程数据转换后为空，降级为本地处理: {:?}", relative_paths);
                    // 降级处理：当远程数据转换后为空时，使用本地处理
                    return Self::process_batch_files_locally(batch_files, project_url, branch, git_repo_url).await;
                }
            }
            None => {
                info!("调用远程同步服务失败，降级为本地处理: {:?}", relative_paths);
                // 降级处理：当远程服务调用失败时，使用本地处理
                return Self::process_batch_files_locally(batch_files, project_url, branch, git_repo_url).await;
            }
        }
    }

    /// 本地处理文件批次（降级处理）
    async fn process_batch_files_locally(
        batch_files: Vec<String>,
        project_url: &str,
        branch: &str,
        git_repo_url: &str,
    ) -> Result<(String, Vec<CodefuseChunk>, Option<Vec<VectorItem>>), String> {
        info!("开始本地处理文件批次，文件数: {}", batch_files.len());

        let mut all_chunks = Vec::new();
        let mut processed_files = Vec::new();

        // 逐个处理文件
        for file_path in batch_files {
            // 将绝对路径转换为相对路径，因为process_single_model_file期望相对路径
            let relative_path = get_relative_path(&project_url.to_string(), &file_path);

            match Self::process_single_model_file(&relative_path, project_url, branch, git_repo_url).await {
                Ok((file_name, chunks, _)) => {
                    if !chunks.is_empty() {
                        all_chunks.extend(chunks);
                        processed_files.push(file_name);
                    }
                }
                Err(e) => {
                    warn!("本地处理文件失败: {} - {}", relative_path, e);
                    // 继续处理其他文件，不中断整个批次
                }
            }
        }

        let batch_name = format!("local_batch_{}", processed_files.len());

        if all_chunks.is_empty() {
            info!("本地处理批次完成，但没有生成chunks: {}", batch_name);
            Ok((batch_name, Vec::new(), None))
        } else {
            info!("本地处理批次完成，生成 {} 个chunks: {}", all_chunks.len(), batch_name);
            // 注意：本地处理的文件不包含VectorItem，需要后续向量化
            Ok((batch_name, all_chunks, None))
        }
    }







    /// 步骤3：处理异步任务句柄，通过await获取步骤2的结果，并处理update文件的向量化
    async fn execute_step3_new(
        mut step2_to_step3_receiver: mpsc::Receiver<tokio::task::JoinHandle<Result<(String, Vec<CodefuseChunk>, Option<Vec<VectorItem>>), String>>>,
        step3_to_step4_sender: mpsc::Sender<Step3ToStep4Data>,
        concurrency_timeout_secs:u64,
        start_time: Instant,
        project_url: String,
        index_file_status: FileStatusResult,
    ) -> Result<(), String> {
        info!("步骤3：开始处理异步任务句柄（新版本）");

        // 检查是否需要构建向量：只有当 AGENT_CONFIG.index_extra_type 包含 CHUNK_VECTOR 时才进行向量相关操作
        let need_vector = AGENT_CONFIG.index_extra_type.contains(&IndexTypeEnum::CHUNK_VECTOR);
        if !need_vector {
            info!("步骤3：跳过所有向量相关操作，因为 AGENT_CONFIG.index_extra_type 不包含 CHUNK_VECTOR");
            // 仍然需要消费所有来自步骤2的数据，但不进行向量处理
            let mut processed_count = 0;
            while let Some(task_handle) = step2_to_step3_receiver.recv().await {
                // 通过await获取步骤2异步任务的执行结果，但不处理向量
                match task_handle.await {
                    Ok(Ok((file_name, chunks, _vector_data))) => {
                        info!("步骤3：跳过向量处理，文件: {} (chunks: {})", file_name, chunks.len());
                        processed_count += 1;
                    }
                    Ok(Err(e)) => {
                        warn!("步骤3：文件处理失败: {}", e);
                        processed_count += 1;
                    }
                    Err(e) => {
                        warn!("步骤3：任务执行失败: {}", e);
                        processed_count += 1;
                    }
                }
            }
            info!("步骤3：完成，跳过了 {} 个文件的向量处理", processed_count);
            return Ok(());
        }

        let mut processed_count = 0;
        let mut pending_chunks = Vec::new();

        // 步骤3.1：处理update_file_vec_url和update_file_vec_model的数据
        Self::process_update_files(&project_url, &index_file_status, &mut pending_chunks).await?;

        info!("步骤3：处理更新文件后，pending_chunks数量: {}", pending_chunks.len());

        while let Some(task_handle) = step2_to_step3_receiver.recv().await {
            // 通过await获取步骤2异步任务的执行结果
            let task_result = match task_handle.await {
                Ok(result) => result,
                Err(e) => {
                    error!("等待步骤2异步任务失败: {}", e);
                    continue;
                }
            };

            let (file_path, chunks, vector_items_opt) = match task_result {
                Ok(data) => data,
                Err(e) => {
                    error!("步骤2异步任务执行失败: {}", e);
                    continue;
                }
            };
            processed_count += 1;

            match vector_items_opt {
                Some(vector_items) => {
                    // 有VectorItem，直接发送到步骤4
                    info!("步骤3：文件 {} 有现成的向量数据，直接发送到步骤4", file_path);
                    let data = Step3ToStep4Data::DirectVectorItems(vector_items);
                    if let Err(e) = step3_to_step4_sender.send(data).await {
                        error!("发送VectorItem到步骤4失败: {} - {}", file_path, e);
                    }
                }
                None => {
                    // 没有VectorItem，需要向量化
                    info!("步骤3：文件 {:?} 需要向量化，添加 {} 个chunks", file_path, chunks.len());
                    pending_chunks.extend(chunks);

                    info!("步骤3：当前pending_chunks数量: {}，批次大小阈值: {}",
                          pending_chunks.len(), AGENT_CONFIG.cge_batch_size);

                    // 检查是否达到批量大小
                    if pending_chunks.len() >= AGENT_CONFIG.cge_batch_size {
                        Self::process_chunk_batch(&mut pending_chunks, &step3_to_step4_sender).await?;
                    }
                }
            }

            if processed_count % 100 == 0 {
                info!("步骤3：已处理 {} 个数据项", processed_count);
            }
        }

        // 处理剩余的chunks
        if !pending_chunks.is_empty() {
            Self::process_chunk_batch(&mut pending_chunks, &step3_to_step4_sender).await?;
        }

        info!("步骤3：完成处理 {} 个数据项", processed_count);
        Ok(())
    }

    /// 处理update_file_vec_url和update_file_vec_model的数据
    /// 从tantivy查询这些文件的CodefuseChunk片段，加入到pending_chunks中等待向量化
    async fn process_update_files(
        project_url: &str,
        index_file_status: &FileStatusResult,
        pending_chunks: &mut Vec<CodefuseChunk>,
    ) -> Result<(), String> {
        // 合并update_file_vec_url和update_file_vec_model
        let mut update_file_urls = HashSet::new();
        update_file_urls.extend(index_file_status.update_file_with_url.iter().cloned());
        update_file_urls.extend(index_file_status.update_file_with_model.iter().cloned());

        if update_file_urls.is_empty() {
            info!("步骤3.1：没有需要更新向量的文件");
            return Ok(());
        }

        // 限制处理数量，避免一次性处理太多文件导致CPU过高
        const MAX_UPDATE_FILES: usize = 5000;
        if update_file_urls.len() > MAX_UPDATE_FILES {
            warn!("步骤3.1：更新文件数量({})超过限制({}), 将只处理前{}个文件，避免CPU过高",
                  update_file_urls.len(), MAX_UPDATE_FILES, MAX_UPDATE_FILES);

            let limited_urls: HashSet<String> = update_file_urls.into_iter()
                .take(MAX_UPDATE_FILES)
                .collect();
            update_file_urls = limited_urls;
        }

        info!("步骤3.1：开始处理 {} 个需要更新向量的文件", update_file_urls.len());
        info!("步骤3.1：update_file_with_url: {} 个文件", index_file_status.update_file_with_url.len());
        info!("步骤3.1：update_file_with_model: {} 个文件", index_file_status.update_file_with_model.len());

        // 使用优化后的批量查询方法
        let chunks_from_tantivy = Self::query_chunks_by_file_urls_optimized(project_url, &update_file_urls).await?;

        if !chunks_from_tantivy.is_empty() {
            info!("步骤3.1：从tantivy查询到 {} 个chunk片段，加入向量化队列", chunks_from_tantivy.len());
            pending_chunks.extend(chunks_from_tantivy);
        } else {
            info!("步骤3.1：从tantivy未查询到任何chunk片段");
        }

        Ok(())
    }

    /// 根据project_url和file_url集合从tantivy查询CodefuseChunk片段（优化版本）
    async fn query_chunks_by_file_urls_optimized(
        project_url: &str,
        file_urls: &HashSet<String>,
    ) -> Result<Vec<CodefuseChunk>, String> {
        let mut all_chunks = Vec::with_capacity(file_urls.len() * 10); // 预分配容量，减少内存重分配

        // 分批处理，避免一次性处理太多文件
        const BATCH_SIZE: usize = 50; // 每批处理50个文件
        let file_urls_vec: Vec<_> = file_urls.iter().collect();
        let total_files = file_urls_vec.len();

        info!("步骤3.1：开始批量查询 {} 个文件的chunks，每批处理 {} 个文件", total_files, BATCH_SIZE);

        for (batch_index, batch) in file_urls_vec.chunks(BATCH_SIZE).enumerate() {
            let batch_start_time = Instant::now();

            // 克隆project_url以避免生命周期问题
            let project_url_clone = project_url.to_string();
            let field_name = "file_url".to_string();

            // 并发查询当前批次的所有文件
            let futures: Vec<_> = batch.iter().map(|file_url| {
                let project_url_ref = project_url_clone.clone();
                let file_url_clone = file_url.clone();
                let field_name_clone = field_name.clone();

                async move {
                    CHUNK_CLIENT.query_term_with_field(
                        &field_name_clone,
                        &file_url_clone,
                        &project_url_ref,
                        10000, // 设置一个较大的限制以获取该文件的所有chunks
                    ).await
                }
            }).collect();

            let results = join_all(futures).await;
            let mut batch_chunks = 0;

            // 处理当前批次的查询结果
            for (i, result) in results.into_iter().enumerate() {
                match result {
                    Ok((documents, _consume_time)) => {
                        for (_score, doc) in documents {
                            match Self::document_to_codefuse_chunk(&doc) {
                                Ok(chunk) => {
                                    all_chunks.push(chunk);
                                    batch_chunks += 1;
                                }
                                Err(e) => {
                                    warn!("转换文档到CodefuseChunk失败: {} - {}", batch[i], e);
                                }
                            }
                        }
                    }
                    Err(e) => {
                        error!("查询文件chunks失败: {} - {}", batch[i], e);
                    }
                }

                // 每处理完一个文件的结果后，sleep 50ms 缓解CPU压力
                tokio::time::sleep(Duration::from_millis(10)).await;
            }

            let batch_duration = batch_start_time.elapsed();
            info!("步骤3.1：完成第 {}/{} 批查询，处理 {} 个文件，获得 {} 个chunks，耗时: {:?}",
                  batch_index + 1,
                  (total_files + BATCH_SIZE - 1) / BATCH_SIZE,
                  batch.len(),
                  batch_chunks,
                  batch_duration);

            // 添加批次间的短暂延迟，减少CPU压力
            if batch.len() == BATCH_SIZE && batch_index < file_urls_vec.chunks(BATCH_SIZE).len() - 1 {
                tokio::time::sleep(Duration::from_millis(20)).await;
            }
        }

        info!("步骤3.1：批量查询完成，从tantivy查询到 {} 个chunk片段，来自 {} 个文件", all_chunks.len(), file_urls.len());
        Ok(all_chunks)
    }

    /// 根据project_url和file_url集合从tantivy查询CodefuseChunk片段（原版本，保留作为备用）
    async fn query_chunks_by_file_urls(
        project_url: &str,
        file_urls: &HashSet<String>,
    ) -> Result<Vec<CodefuseChunk>, String> {
        let mut all_chunks = Vec::new();

        // 批量查询每个file_url对应的chunks
        for (index, file_url) in file_urls.iter().enumerate() {
            // 添加进度监控
            if index % 100 == 0 {
                info!("步骤3.1：正在查询第 {}/{} 个文件", index + 1, file_urls.len());
            }

            match CHUNK_CLIENT.query_term_with_field(
                &"file_url".to_string(),
                &file_url.to_string(),
                &project_url.to_string(),
                10000, // 设置一个较大的限制以获取该文件的所有chunks
            ).await {
                Ok((documents, _consume_time)) => {
                    for (_score, doc) in documents {
                        // 将TantivyDocument转换为CodefuseChunk
                        match Self::document_to_codefuse_chunk(&doc) {
                            Ok(chunk) => all_chunks.push(chunk),
                            Err(e) => {
                                warn!("转换文档到CodefuseChunk失败: {} - {}", file_url, e);
                            }
                        }
                    }
                }
                Err(e) => {
                    error!("查询文件chunks失败: {} - {}", file_url, e);
                }
            }
        }

        info!("从tantivy查询到 {} 个chunk片段，来自 {} 个文件", all_chunks.len(), file_urls.len());
        Ok(all_chunks)
    }

    /// 将TantivyDocument转换为CodefuseChunk
    fn document_to_codefuse_chunk(doc: &tantivy::TantivyDocument) -> Result<CodefuseChunk, String> {
        use tantivy::schema::{Schema, Value};

        // 获取schema字段
        let schema = CodefuseChunk::to_schema();
        let id_field = schema.get_field("id").map_err(|e| format!("获取id字段失败: {}", e))?;
        let file_url_field = schema.get_field("file_url").map_err(|e| format!("获取file_url字段失败: {}", e))?;
        let index_field = schema.get_field("index").map_err(|e| format!("获取index字段失败: {}", e))?;
        let content_field = schema.get_field("content").map_err(|e| format!("获取content字段失败: {}", e))?;
        let start_line_field = schema.get_field("start_line").map_err(|e| format!("获取start_line字段失败: {}", e))?;
        let end_line_field = schema.get_field("end_line").map_err(|e| format!("获取end_line字段失败: {}", e))?;
        let summary_field = schema.get_field("summary").map_err(|e| format!("获取summary字段失败: {}", e))?;
        let project_url_field = schema.get_field("project_url").map_err(|e| format!("获取project_url字段失败: {}", e))?;
        let branch_field = schema.get_field("branch").map_err(|e| format!("获取branch字段失败: {}", e))?;
        let hash_field = schema.get_field("hash").map_err(|e| format!("获取hash字段失败: {}", e))?;
        let feature_field = schema.get_field("feature").map_err(|e| format!("获取feature字段失败: {}", e))?;
        let has_summary_field = schema.get_field("has_summary").map_err(|e| format!("获取has_summary字段失败: {}", e))?;
        let has_content_vector_field = schema.get_field("has_content_vector").map_err(|e| format!("获取has_content_vector字段失败: {}", e))?;
        let has_summary_vector_field = schema.get_field("has_summary_vector").map_err(|e| format!("获取has_summary_vector字段失败: {}", e))?;

        // 提取字段值
        let id = doc.get_first(id_field).and_then(|v| v.as_str()).unwrap_or("").to_string();
        let file_url = doc.get_first(file_url_field).and_then(|v| v.as_str()).unwrap_or("").to_string();
        let index = doc.get_first(index_field).and_then(|v| v.as_u64()).unwrap_or(0);
        let content = doc.get_first(content_field).and_then(|v| v.as_str()).unwrap_or("").to_string();
        let start_line = doc.get_first(start_line_field).and_then(|v| v.as_u64()).unwrap_or(0);
        let end_line = doc.get_first(end_line_field).and_then(|v| v.as_u64()).unwrap_or(0);
        let summary = doc.get_first(summary_field).and_then(|v| v.as_str()).unwrap_or("").to_string();
        let project_url = doc.get_first(project_url_field).and_then(|v| v.as_str()).unwrap_or("").to_string();
        let branch = doc.get_first(branch_field).and_then(|v| v.as_str()).unwrap_or("").to_string();
        let hash = doc.get_first(hash_field).and_then(|v| v.as_str()).unwrap_or("").to_string();
        let feature = doc.get_first(feature_field).and_then(|v| v.as_str()).unwrap_or("").to_string();
        let has_summary = doc.get_first(has_summary_field).and_then(|v| v.as_u64()).unwrap_or(0);
        let has_content_vector = doc.get_first(has_content_vector_field).and_then(|v| v.as_u64()).unwrap_or(0);
        let has_summary_vector = doc.get_first(has_summary_vector_field).and_then(|v| v.as_u64()).unwrap_or(0);

        Ok(CodefuseChunk {
            id,
            file_url,
            index,
            content,
            start_line,
            end_line,
            summary,
            project_url,
            branch,
            hash,
            feature,
            summary_keyword: Vec::new(), // tantivy中可能没有存储这个字段，使用默认值
            has_summary,
            has_content_vector,
            has_summary_vector,
        })
    }

    /// 处理chunk批次，启动向量化任务
    async fn process_chunk_batch(
        pending_chunks: &mut Vec<CodefuseChunk>,
        step3_to_step4_sender: &mpsc::Sender<Step3ToStep4Data>,
    ) -> Result<(), String> {
        if pending_chunks.is_empty() {
            return Ok(());
        }

        let total_chunks = pending_chunks.len();
        info!("步骤3：开始处理 {} 个chunks，将分批进行向量化", total_chunks);

        let mut batch_count = 0;

        // 循环处理所有chunks，每次处理一个批次
        while !pending_chunks.is_empty() {
            // 取出一批chunks进行处理
            let batch_size = std::cmp::min(pending_chunks.len(), AGENT_CONFIG.cge_batch_size);
            let chunk_batch: Vec<CodefuseChunk> = pending_chunks.drain(0..batch_size).collect();
            batch_count += 1;

            info!("步骤3：启动第 {} 个向量化任务，批次大小: {}，剩余: {}",
                  batch_count, chunk_batch.len(), pending_chunks.len());

            // 准备向量化请求
            let chunk_content_list: Vec<String> = chunk_batch.iter()
                .map(|chunk| chunk.content.clone())
                .collect();

            // 克隆数据用于异步任务
            let chunks_for_task = chunk_batch.clone();

            // 启动向量化任务（不等待结果）
            let embedding_future:JoinHandle<BaseResponse<CgeEmbeddingResponse>> = tokio::spawn(async move {
                let req = CgeEmbeddingRequestBean{
                    codeList: chunk_content_list,
                    r#type: EMBEDDING_BUILD.to_string(),
                };
                cge_embedding(&req,AGENT_CONFIG.embedding_build_timeout).await
            });

            // 发送任务到步骤4
            let data = Step3ToStep4Data::EmbeddingTask(embedding_future, chunks_for_task);
            if let Err(e) = step3_to_step4_sender.send(data).await {
                error!("发送第 {} 个embedding任务到步骤4失败: {}", batch_count, e);
                return Err(format!("发送embedding任务失败: {}", e));
            }
        }

        info!("步骤3：完成所有chunks的批次处理，总共发送了 {} 个向量化任务，处理了 {} 个chunks",
              batch_count, total_chunks);
        Ok(())
    }

    /// 执行删除操作（步骤5）
    /// 1. 从tantivy里面查找指定project_url里面所有file_url在delete_file列表里的数据，在tantivy里面删除这些数据
    /// 2. 从1的结果里找到所有id，然后从lanceDB里面找到所有命中的id，在lanceDB里删除这些数据
    /// 3. 优先删除2的数据，再删除1的数据
    async fn execute_delete_operations(
        project_url: &str,
        delete_files: &HashSet<String>,
    ) -> Result<(), String> {
        info!("步骤5：开始执行删除操作，项目: {}, 删除文件数量: {}", project_url, delete_files.len());

        // 步骤1: 从tantivy查找指定project_url里面所有file_url在delete_file列表里的数据
        let chunks_to_delete = Self::query_chunks_by_file_urls(project_url, delete_files).await?;

        if chunks_to_delete.is_empty() {
            info!("步骤5：没有找到需要删除的chunk数据");
            return Ok(());
        }

        info!("步骤5：从tantivy查询到 {} 个需要删除的chunk", chunks_to_delete.len());

        // 步骤2: 从查询结果中提取所有id，准备从lanceDB删除
        let chunk_ids: Vec<String> = chunks_to_delete.iter().map(|chunk| chunk.id.clone()).collect();

        info!("步骤5：准备从lanceDB删除 {} 个向量", chunk_ids.len());

        // 步骤3: 优先删除lanceDB中的向量数据
        let mut vector_delete_success_count = 0;
        let mut vector_delete_error_count = 0;
        let mut loop_count = 0;

        for chunk_id in &chunk_ids {
            loop_count += 1;

            match VECTOR_CLIENT.delete_with_key_and_value("id", chunk_id).await {
                Ok(_) => {
                    vector_delete_success_count += 1;
                    if vector_delete_success_count % 100 == 0 {
                        info!("步骤5：已成功从lanceDB删除 {} 个向量", vector_delete_success_count);
                    }
                }
                Err(e) => {
                    vector_delete_error_count += 1;
                    warn!("步骤5：从lanceDB删除向量失败: {} - {}", chunk_id, e);
                }
            }

            // 每隔100次调用检查并清理无效数据
            if loop_count % 100 == 0 {
                info!("步骤5：已处理 {} 个删除操作，开始执行存储清理", loop_count);
                match VECTOR_CLIENT.manual_cleanup().await {
                    Ok(_) => {
                        info!("步骤5：存储清理完成（第 {} 次）", loop_count / 100);
                    }
                    Err(e) => {
                        warn!("步骤5：存储清理失败: {}", e);
                    }
                }
            }
        }

        info!("步骤5：遍历删除id结束，再次检查磁盘.");
        match VECTOR_CLIENT.manual_cleanup().await {
            Ok(_) => {
                info!("步骤5：存储清理完成（第 {} 次）", loop_count / 100);
            }
            Err(e) => {
                warn!("步骤5：存储清理失败: {}", e);
            }
        }

        info!("步骤5：lanceDB删除完成 - 成功: {}, 失败: {}", vector_delete_success_count, vector_delete_error_count);

        // 步骤4: 再删除tantivy中的chunk数据
        match CHUNK_CLIENT.delete_batch(&chunks_to_delete) {
            Ok(_) => {
                info!("步骤5：成功从tantivy删除 {} 个chunk", chunks_to_delete.len());

                // 提交tantivy的删除操作
                if let Err(e) = CHUNK_CLIENT.commit() {
                    warn!("步骤5：提交tantivy删除操作失败: {}", e);
                } else {
                    info!("步骤5：tantivy删除操作已提交");
                }
            }
            Err(e) => {
                error!("步骤5：从tantivy删除chunk失败: {}", e);
                return Err(format!("从tantivy删除chunk失败: {}", e));
            }
        }

        info!("步骤5：删除操作完成 - 删除了 {} 个chunk和 {} 个向量",
              chunks_to_delete.len(), vector_delete_success_count);

        Ok(())
    }

    /// 步骤4：优化版本，直接调用batch_upsert保存向量，遵循concurrency_config配置
    async fn execute_step4_optimized(
        mut step3_to_step4_receiver: mpsc::Receiver<Step3ToStep4Data>,
        concurrency_timeout_secs: u64,
        start_time: Instant,
    ) -> Result<usize, String> {
        info!("步骤4：开始处理VectorItem和embedding任务（优化版本）");

        // 检查是否需要构建向量：只有当 AGENT_CONFIG.index_extra_type 包含 CHUNK_VECTOR 时才进行向量保存操作
        let need_vector = AGENT_CONFIG.index_extra_type.contains(&IndexTypeEnum::CHUNK_VECTOR);
        if !need_vector {
            info!("步骤4：跳过所有向量保存操作，因为 AGENT_CONFIG.index_extra_type 不包含 CHUNK_VECTOR");
            // 仍然需要消费所有来自步骤3的数据，但不进行向量保存
            let mut processed_count = 0;
            while let Some(_data) = step3_to_step4_receiver.recv().await {
                processed_count += 1;
                // 只计数，不处理向量保存
            }
            info!("步骤4：完成，跳过了 {} 个向量保存任务", processed_count);
            return Ok(0); // 返回0表示没有保存任何向量
        }

        let mut processed_count = 0;
        let mut total_vectors_saved = 0;
        let mut pending_embedding_tasks = Vec::new();
        let mut last_concurrency_check = start_time;
        let mut current_concurrency = AGENT_CONFIG.initial_concurrency;
        let mut concurrency_reduced = false;

        info!("步骤4：初始并发数: {}", AGENT_CONFIG.initial_concurrency);

        while let Some(data) = step3_to_step4_receiver.recv().await {
            processed_count += 1;
            // 动态检查并发控制 - 每处理一定数量的任务后检查一次
            if processed_count % 10 == 0 || last_concurrency_check.elapsed().as_secs() >= 1 {
                let new_concurrency = Self::get_current_concurrency(concurrency_timeout_secs, start_time);
                if new_concurrency != current_concurrency {
                    let elapsed = start_time.elapsed();
                    if !concurrency_reduced && new_concurrency == AGENT_CONFIG.reduced_concurrency {
                        concurrency_reduced = true;
                        info!("步骤4：超时 {}s 后降低并发数: {} -> {} (已处理 {} 个任务)",
                              elapsed.as_secs(), AGENT_CONFIG.reduced_concurrency, new_concurrency, processed_count);
                    }
                    current_concurrency = new_concurrency;
                }
                last_concurrency_check = Instant::now();
            }

            match data {
                Step3ToStep4Data::DirectVectorItems(vector_items) => {
                    // 直接调用batch_upsert保存VectorItem
                    let count = vector_items.len();
                    info!("步骤4：接收到 {} 个直接VectorItem，立即保存", count);

                    match VECTOR_CLIENT.batch_upsert(vector_items).await {
                        Ok(_) => {
                            total_vectors_saved += count;
                            info!("步骤4：成功保存 {} 个直接向量，累计保存 {} 个", count, total_vectors_saved);

                            // 添加批量插入间隔控制，减少对其他查询的影响
                            if count > 10 {
                                let sleep_duration = Duration::from_millis(50); // 50ms延迟
                                info!("步骤4：批量插入 {} 个向量后休眠 {:?}，减少CPU占用", count, sleep_duration);
                                tokio::time::sleep(sleep_duration).await;
                            }
                        }
                        Err(e) => {
                            error!("步骤4：保存直接向量失败: {}", e);
                        }
                    }
                }
                Step3ToStep4Data::EmbeddingTask(embedding_future, chunks) => {
                    // 收集embedding任务
                    pending_embedding_tasks.push((embedding_future, chunks));

                    // 当积累了足够的任务时，批量处理（使用当前动态并发数）
                    if pending_embedding_tasks.len() >= current_concurrency {
                        let saved_count = Self::process_embedding_tasks_batch_optimized(&mut pending_embedding_tasks).await?;
                        total_vectors_saved += saved_count;
                    }
                }
            }

            if processed_count % 100 == 0 {
                let elapsed = start_time.elapsed();
                info!("步骤4：已处理 {} 个数据项，累计保存 {} 个向量，运行时间: {:?}，当前并发数: {}",
                      processed_count, total_vectors_saved, elapsed, current_concurrency);
            }
        }

        // 处理剩余的embedding任务
        if !pending_embedding_tasks.is_empty() {
            info!("步骤4：处理剩余的 {} 个embedding任务", pending_embedding_tasks.len());
            let saved_count = Self::process_embedding_tasks_batch_optimized(&mut pending_embedding_tasks).await?;
            total_vectors_saved += saved_count;
        }

        let total_elapsed = start_time.elapsed();
        info!("步骤4：完成处理 {} 个数据项，总共保存 {} 个向量，总耗时: {:?}",
              processed_count, total_vectors_saved, total_elapsed);
        Ok(total_vectors_saved)
    }

    /// 根据concurrency_config和时间动态获取当前并发数
    fn get_current_concurrency(concurrency_timeout_secs: u64, start_time: Instant) -> usize {
        let elapsed = start_time.elapsed();
        let timeout_duration = std::time::Duration::from_secs(concurrency_timeout_secs);

        if elapsed >= timeout_duration {
            AGENT_CONFIG.reduced_concurrency
        } else {
            AGENT_CONFIG.initial_concurrency
        }
    }

    /// 批量处理embedding任务（优化版本）- 直接调用batch_upsert保存
    async fn process_embedding_tasks_batch_optimized(
        pending_tasks: &mut Vec<(JoinHandle<BaseResponse<CgeEmbeddingResponse>>, Vec<CodefuseChunk>)>,
    ) -> Result<usize, String> {
        if pending_tasks.is_empty() {
            return Ok(0);
        }
        info!("步骤4：开始批量处理 {} 个embedding任务（优化版本）", pending_tasks.len());
        // 分离任务和chunks
        let tasks_to_process: Vec<_> = pending_tasks.drain(..).collect();
        let mut futures = Vec::new();
        let mut chunks_list = Vec::new();

        for (future, chunks) in tasks_to_process {
            futures.push(future);
            chunks_list.push(chunks);
        }

        // 并发等待所有任务完成
        let results = join_all(futures).await;
        let mut total_saved = 0;

        // 处理每个任务的结果
        for (i, result) in results.into_iter().enumerate() {
            match result {
                Ok(embeddings_result) => {
                    if embeddings_result.errorCode != 0 {
                        warn!("步骤4：embedding任务返回错误: {}", embeddings_result.errorMsg.unwrap_or_default());
                        continue;
                    }
                    let embeddings_response = embeddings_result.data;
                    if embeddings_response.is_none() {
                        warn!("步骤4：embedding任务返回结果为空");
                        continue;
                    }
                    let embeddings = embeddings_response.unwrap().result;
                    let chunks = &chunks_list[i];
                    if embeddings.len() != chunks.len() {
                        warn!("步骤4：embedding数量({})与chunk数量({})不匹配", embeddings.len(), chunks.len());
                        continue;
                    }

                    // 组装VectorItem列表
                    let mut vector_items = Vec::new();
                    for (j, chunk) in chunks.iter().enumerate() {
                        let vector_item = VectorItem::new(
                            chunk.id.clone(),
                            embeddings[j].clone(),
                            chunk.project_url.clone(),
                        );
                        vector_items.push(vector_item);
                    }

                    // 直接调用batch_upsert保存
                    let count = vector_items.len();
                    match VECTOR_CLIENT.batch_upsert(vector_items).await {
                        Ok(_) => {
                            total_saved += count;
                            info!("步骤4：成功保存 {} 个embedding向量", count);

                            // 添加批量插入间隔控制，减少对其他查询的影响
                            if count > 10 {
                                let sleep_duration = Duration::from_millis(50); // 50ms延迟
                                info!("步骤4：批量插入 {} 个向量后休眠 {:?}，减少CPU占用", count, sleep_duration);
                                tokio::time::sleep(sleep_duration).await;
                            }
                        }
                        Err(e) => {
                            error!("步骤4：保存embedding向量失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    error!("步骤4：等待embedding任务失败: {}", e);
                }
            }
        }

        info!("步骤4：批量处理完成，总共保存 {} 个向量", total_saved);
        Ok(total_saved)
    }

    /// 批量处理embedding任务（新版本）
    async fn process_embedding_tasks_batch_new(
        pending_tasks: &mut Vec<(tokio::task::JoinHandle<Vec<Vec<f32>>>, Vec<CodefuseChunk>)>,
        vector_sender: &mpsc::Sender<Vec<VectorItem>>,
    ) -> Result<(), String> {
        if pending_tasks.is_empty() {
            return Ok(());
        }

        info!("步骤4：开始批量处理 {} 个embedding任务", pending_tasks.len());

        // 分离任务和chunks
        let tasks_to_process: Vec<_> = pending_tasks.drain(..).collect();
        let mut futures = Vec::new();
        let mut chunks_list = Vec::new();

        for (future, chunks) in tasks_to_process {
            futures.push(future);
            chunks_list.push(chunks);
        }

        // 并发等待所有任务完成
        let results = join_all(futures).await;

        // 处理所有结果
        for (index, task_result) in results.into_iter().enumerate() {
            match task_result {
                Ok(vectors) => {
                    let chunks = &chunks_list[index];

                    // 检查向量结果长度和chunks长度是否一致
                    if vectors.len() != chunks.len() {
                        error!("向量结果长度({})与chunks长度({})不一致", vectors.len(), chunks.len());
                        continue;
                    }

                    // 构建向量项
                    let mut vector_items = Vec::new();
                    for (i, chunk) in chunks.iter().enumerate() {
                        if let Some(vector) = vectors.get(i) {
                            // 跳过空向量（表示失败的情况）
                            if !vector.is_empty() {
                                let vector_item = VectorItem {
                                    id: chunk.id.clone(),
                                    vector: vector.clone(),
                                    project_url: chunk.project_url.clone(),
                                    created_at: SystemTime::now()
                                        .duration_since(UNIX_EPOCH)
                                        .unwrap()
                                        .as_millis() as u64,
                                };
                                vector_items.push(vector_item);
                            }
                        }
                    }

                    if !vector_items.is_empty() {
                        // 发送到向量保存队列
                        if let Err(e) = vector_sender.send(vector_items).await {
                            error!("发送向量数据到保存队列失败: {}", e);
                            return Err(format!("发送向量数据失败: {}", e));
                        }
                    }
                }
                Err(e) => {
                    error!("embedding任务执行失败: {}", e);
                    // 继续处理其他任务，不中断整个流程
                }
            }
        }

        info!("步骤4：完成批量处理embedding任务");
        Ok(())
    }




}



#[cfg(test)]
mod tests {
    use super::*;
    use agent_db::config::agent_logger::init_logger;
    use agent_db::config::runtime_config::AGENT_CONFIG_ENV_KEY;
    use std::time::Instant;

    #[tokio::test(flavor = "multi_thread", worker_threads = 16)]
    async fn test_index_service() {
        // 设置环境变量以配置agent
        let scan_max_file_size = 10000000;
        let args = format!(
            "--base-data-url=~/.codefuse --scan-skip-file-size={}",
            scan_max_file_size
        );
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        init_logger().expect("初始化日志失败");

        // 测试项目路径
        let project_url = "/Users/<USER>/workspace/codegenerator/codegencore".to_string();
        let branch = "eval_4416cdf0_20250820_115548".to_string();

        // 记录测试开始时间
        let test_start_time = Instant::now();

        info!("=== 开始索引服务测试 ===");
        info!("项目URL: {}", project_url);
        info!("分支: {}", branch);

        // 查询构建前的索引进度
        let request_before = agent_common_service::model::chat_model::IndexOperateRequestBean {
            projectUrl: Some(project_url.clone()),
            branch: Some(branch.clone()),
            printDetail: Some(true),
            diffAnalysis: Some(false),
            ..Default::default()
        };
        let data_status_before = crate::function::chat_strategy::query_data_status(request_before).await;
        info!("=== 构建前索引状态 ===");
        info!("build_index_progress_inner: {}", data_status_before.build_index_progress_inner);
        info!("build_index_progress: {}", data_status_before.build_index_progress);
        info!("file_total_count: {}", data_status_before.file_total_count);
        info!("file_count: {}", data_status_before.file_count);
        info!("chunk_save_count: {}", data_status_before.chunk_save_count);
        info!("chunk_vector_save_count: {}", data_status_before.chunk_vector_save_count);
        info!("chunk_progress: {:.2}", data_status_before.chunk_progress);
        info!("chunk_vector_progress: {:.2}", data_status_before.chunk_vector_progress);
        info!("==================");

        // 调用索引服务接口
        let response = IndexService::build_index(project_url.clone(), branch.clone(), 10000).await;

        // 验证响应
        assert!(response.success, "索引构建应该成功，但收到错误: {}", response.message);
        assert_eq!(response.status, "success", "响应状态应该是success");

        info!("索引构建任务已启动，开始等待构建完成...");

        // 等待索引构建完成
        let mut wait_count = 0;
        let max_wait_iterations = 300; // 最多等待5分钟 (300 * 1秒)
        let mut last_progress = 0u8;

        loop {
            tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
            wait_count += 1;

            // 检查任务状态
            let current_task = INDEX_BUILD_MANAGER.get_current_task().await;

            // 查询当前索引进度
            let request_current = agent_common_service::model::chat_model::IndexOperateRequestBean {
                projectUrl: Some(project_url.clone()),
                branch: Some(branch.clone()),
                printDetail: Some(true),
                diffAnalysis: Some(false),
                ..Default::default()
            };
            let data_status_current = crate::function::chat_strategy::query_data_status(request_current).await;

            // 如果进度有变化，输出日志
            if data_status_current.build_index_progress_inner != last_progress {
                info!("索引构建进度更新: {}% -> {}% (等待时间: {}秒)",
                      last_progress, data_status_current.build_index_progress_inner, wait_count);
                info!("  - chunk_progress: {:.2}%", data_status_current.chunk_progress * 100.0);
                info!("  - chunk_vector_progress: {:.2}%", data_status_current.chunk_vector_progress * 100.0);
                info!("  - chunk_save_count: {}", data_status_current.chunk_save_count);
                info!("  - chunk_vector_save_count: {}", data_status_current.chunk_vector_save_count);
                last_progress = data_status_current.build_index_progress_inner;
            }

            // 检查是否构建完成的条件
            let is_completed = if let Some(task) = &current_task {
                // 任务状态已完成，或者任务已被清理（表示完成）
                task.status == IndexBuildStatus::Completed || task.status == IndexBuildStatus::Failed
            } else {
                // 任务已被清理，可能表示完成
                true
            };

            // 如果任务完成或者进度达到较高水平，退出等待
            if is_completed || data_status_current.build_index_progress_inner >= 90 {
                info!("索引构建完成检测: 任务状态完成={}, 进度={}%", is_completed, data_status_current.build_index_progress_inner);
                break;
            }

            // 超时检查
            if wait_count >= max_wait_iterations {
                warn!("等待索引构建完成超时 ({}秒)，当前进度: {}%", wait_count, data_status_current.build_index_progress_inner);
                break;
            }

            // 每30秒输出一次等待状态
            if wait_count % 30 == 0 {
                info!("继续等待索引构建完成... (已等待{}秒, 当前进度: {}%)", wait_count, data_status_current.build_index_progress_inner);
            }
        }

        // 记录测试结束时间
        let test_duration = test_start_time.elapsed();
        info!("=== 索引服务测试完成 ===");
        info!("总耗时: {:?}", test_duration);
        info!("等待时间: {}秒", wait_count);

        // 查询最终的索引进度
        let request_final = agent_common_service::model::chat_model::IndexOperateRequestBean {
            projectUrl: Some(project_url.clone()),
            branch: Some(branch.clone()),
            printDetail: Some(true),
            diffAnalysis: Some(false),
            ..Default::default()
        };
        let data_status_final = crate::function::chat_strategy::query_data_status(request_final).await;

        info!("=== 最终索引状态 ===");
        info!("build_index_progress_inner: {}", data_status_final.build_index_progress_inner);
        info!("build_index_progress: {}", data_status_final.build_index_progress);
        info!("file_total_count: {}", data_status_final.file_total_count);
        info!("file_count: {}", data_status_final.file_count);
        info!("chunk_save_count: {}", data_status_final.chunk_save_count);
        info!("chunk_vector_save_count: {}", data_status_final.chunk_vector_save_count);
        info!("chunk_progress: {:.2}%", data_status_final.chunk_progress * 100.0);
        info!("chunk_vector_progress: {:.2}%", data_status_final.chunk_vector_progress * 100.0);
        info!("==================");

        // 验证最终任务状态
        let current_task = INDEX_BUILD_MANAGER.get_current_task().await;

        // 验证索引进度有所提升
        assert!(data_status_final.build_index_progress_inner > data_status_before.build_index_progress_inner,
                "索引进度应该有所提升: 构建前={}%, 构建后={}%",
                data_status_before.build_index_progress_inner,
                data_status_final.build_index_progress_inner);

        // 验证有数据被处理
        assert!(data_status_final.chunk_save_count > data_status_before.chunk_save_count ||
                data_status_final.chunk_vector_save_count > data_status_before.chunk_vector_save_count,
                "应该有新的chunk或向量数据被保存");

        info!("所有验证通过！索引进度从 {}% 提升到 {}%",
              data_status_before.build_index_progress_inner,
              data_status_final.build_index_progress_inner);
    }

    #[tokio::test]
    async fn test_build_time_interval_check() {
        // 设置环境变量
        let args = "--base-data-url=~/.codefuse --scan-skip-file-size=10000000";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // 忽略日志初始化错误，因为可能已经初始化过了
        let _ = init_logger();

        let project_url = "/test/project".to_string();
        let branch = "test_branch".to_string();

        info!("=== 测试时间间隔检查功能 ===");

        // 1. 首次检查，应该允许构建
        let (should_wait, remaining_minutes) = INDEX_BUILD_MANAGER
            .check_build_time_interval(&project_url, &branch)
            .await;

        assert!(!should_wait, "首次检查应该允许构建");
        assert_eq!(remaining_minutes, 0, "首次检查剩余时间应该为0");
        info!("✓ 首次检查通过：允许构建");

        // 2. 手动记录一个构建完成时间
        INDEX_BUILD_MANAGER
            .record_build_completion(&project_url, &branch)
            .await;
        info!("✓ 记录构建完成时间");

        // 3. 立即再次检查，应该需要等待
        let (should_wait, remaining_minutes) = INDEX_BUILD_MANAGER
            .check_build_time_interval(&project_url, &branch)
            .await;

        assert!(should_wait, "刚构建完成后应该需要等待");
        assert!(remaining_minutes > 0, "剩余等待时间应该大于0");
        assert!(remaining_minutes <= 60, "剩余等待时间应该小于等于60分钟");
        info!("✓ 时间间隔检查通过：需要等待 {} 分钟", remaining_minutes);

        // 4. 测试不同项目和分支的独立性
        let different_project = "/test/different_project".to_string();
        let different_branch = "different_branch".to_string();

        let (should_wait, _) = INDEX_BUILD_MANAGER
            .check_build_time_interval(&different_project, &branch)
            .await;
        assert!(!should_wait, "不同项目应该允许构建");

        let (should_wait, _) = INDEX_BUILD_MANAGER
            .check_build_time_interval(&project_url, &different_branch)
            .await;
        assert!(!should_wait, "相同项目不同分支应该允许构建");

        info!("✓ 项目和分支独立性检查通过");

        info!("=== 所有时间间隔检查测试通过 ===");
    }

    #[tokio::test]
    async fn test_build_index_with_time_interval() {
        // 设置环境变量
        let args = "--base-data-url=~/.codefuse --scan-skip-file-size=10000000";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // 忽略日志初始化错误
        let _ = init_logger();

        let project_url = "/test/mock_project".to_string();
        let branch = "mock_branch".to_string();

        info!("=== 测试build_index函数的时间间隔控制 ===");

        // 创建并发配置


        // 1. 手动记录一个最近的构建时间，模拟刚刚构建完成
        INDEX_BUILD_MANAGER
            .record_build_completion(&project_url, &branch)
            .await;

        // 2. 立即尝试构建，应该被跳过
        let response = IndexService::build_index(
            project_url.clone(),
            branch.clone(),
            10
        ).await;

        assert!(response.success, "请求应该成功处理");
        assert_eq!(response.status, "skipped", "应该跳过构建");
        assert!(response.message.contains("1小时内已经构建过"), "消息应该说明时间间隔限制");
        info!("✓ 时间间隔限制测试通过：{}", response.message);

        info!("=== build_index时间间隔控制测试完成 ===");
    }

    #[tokio::test(flavor = "multi_thread", worker_threads = 1)]
    async fn test_query_data_status() {
        // 设置环境变量以配置agent
        let scan_max_file_size = 10000000;
        let args = format!(
            "--base-data-url=~/.codefuse --scan-skip-file-size={}",
            scan_max_file_size
        );
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        init_logger().expect("初始化日志失败");

        // 测试项目路径
        let project_url = "/Users/<USER>/workspace/codegenerator/backup/caselike".to_string();

        info!("=== 开始查询数据状态 ===");
        info!("项目URL: {}", project_url);

        // 创建请求
        let request = agent_common_service::model::chat_model::IndexOperateRequestBean {
            projectUrl: Some(project_url.clone()),
            branch: Some("EI64428460_20250612_qianyu".to_string()),
            ..Default::default()
        };

        // 调用query_data_status函数
        let data_status = crate::function::chat_strategy::query_data_status(request).await;

        info!("=== 数据状态查询结果 ===");
        info!("build_index_progress_inner: {}", data_status.build_index_progress_inner);
        info!("build_nessary_index_progress: {}", data_status.build_nessary_index_progress);
        info!("build_index_progress: {}", data_status.build_index_progress);
        info!("file_total_count: {}", data_status.file_total_count);
        info!("file_count: {}", data_status.file_count);
        info!("chunk_save_count: {}", data_status.chunk_save_count);
        info!("chunk_vector_save_count: {}", data_status.chunk_vector_save_count);
        info!("chunk_progress: {:.2}", data_status.chunk_progress);
        info!("chunk_vector_progress: {:.2}", data_status.chunk_vector_progress);
        info!("==================");
    }

    #[tokio::test]
    async fn test_concurrent_control_same_project_branch() {
        // 设置环境变量
        let args = "--base-data-url=~/.codefuse --scan-skip-file-size=10000000";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // 忽略日志初始化错误，因为可能已经初始化过了
        let _ = init_logger();

        let project_url = "/Users/<USER>/workspace/codegenerator/backup/caselike".to_string();
        let branch = "test_branch".to_string();

        info!("=== 测试相同项目和分支的并发控制 ===");

        // 模拟一个正在运行的任务
        let mut running_task = IndexBuildTask::new(project_url.clone(), branch.clone());
        running_task.status = IndexBuildStatus::Building;
        INDEX_BUILD_MANAGER.set_current_task(Some(running_task)).await;

        // 创建默认并发配置


        // 尝试启动相同项目和分支的任务
        let response = IndexService::build_index(project_url.clone(), branch.clone(), 10).await;

        // 验证应该被跳过
        assert!(response.success, "请求应该成功处理");
        assert_eq!(response.status, "skipped", "应该跳过重复的构建请求");
        assert!(response.message.contains("已经在构建中"), "消息应该说明已经在构建中");

        info!("并发控制测试通过！");
    }

    #[tokio::test]
    async fn test_concurrent_control_different_branch() {
        // 设置环境变量
        let args = "--base-data-url=~/.codefuse --scan-skip-file-size=10000000";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // 忽略日志初始化错误，因为可能已经初始化过了
        let _ = init_logger();

        let project_url = "/Users/<USER>/workspace/codegenerator/backup/caselike".to_string();
        let old_branch = "old_branch".to_string();
        let new_branch = "new_branch".to_string();

        info!("=== 测试相同项目不同分支的并发控制 ===");

        // 模拟一个正在运行的任务（旧分支）
        let mut running_task = IndexBuildTask::new(project_url.clone(), old_branch.clone());
        running_task.status = IndexBuildStatus::Building;
        INDEX_BUILD_MANAGER.set_current_task(Some(running_task)).await;

        // 验证当前有不同分支的任务在运行
        let is_different_branch = INDEX_BUILD_MANAGER
            .is_same_project_different_branch_running(&project_url, &new_branch).await;
        assert!(is_different_branch, "应该检测到相同项目不同分支的任务在运行");

        info!("不同分支并发控制测试通过！");
    }

    #[tokio::test]
    async fn test_stop_current_task() {
        // 设置环境变量
        let args = "--base-data-url=~/.codefuse --scan-skip-file-size=10000000";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // 忽略日志初始化错误
        let _ = init_logger();

        info!("=== 测试停止当前任务功能 ===");

        // 1. 测试没有当前任务时的停止操作
        let result = INDEX_BUILD_MANAGER.stop_current_task().await;
        assert!(result.is_ok(), "停止不存在的任务应该成功");
        info!("✓ 停止不存在的任务测试通过");

        // 2. 创建一个模拟的正在运行的任务
        let project_url = "/test/project".to_string();
        let branch = "test_branch".to_string();
        let cancellation_token = CancellationToken::new();

        let mut running_task = IndexBuildTask::new(project_url.clone(), branch.clone())
            .with_cancellation_token(cancellation_token.clone());
        running_task.status = IndexBuildStatus::Building;

        INDEX_BUILD_MANAGER.set_current_task(Some(running_task.clone())).await;

        // 验证任务状态
        let current_task = INDEX_BUILD_MANAGER.get_current_task().await;
        assert!(current_task.is_some(), "应该有当前任务");
        assert_eq!(current_task.unwrap().status, IndexBuildStatus::Building, "任务状态应该是Building");

        // 3. 测试停止正在运行的任务
        let result = INDEX_BUILD_MANAGER.stop_current_task().await;
        assert!(result.is_ok(), "停止正在运行的任务应该成功");

        // 验证取消令牌已被触发
        assert!(cancellation_token.is_cancelled(), "取消令牌应该被触发");

        // 验证任务状态已更新
        let current_task = INDEX_BUILD_MANAGER.get_current_task().await;
        assert!(current_task.is_some(), "应该有当前任务记录");
        assert_eq!(current_task.unwrap().status, IndexBuildStatus::Failed, "任务状态应该是Failed（表示被取消）");

        info!("✓ 停止正在运行的任务测试通过");

        // 4. 测试停止已完成的任务
        let mut completed_task = IndexBuildTask::new(project_url.clone(), branch.clone());
        completed_task.status = IndexBuildStatus::Completed;
        INDEX_BUILD_MANAGER.set_current_task(Some(completed_task)).await;

        let result = INDEX_BUILD_MANAGER.stop_current_task().await;
        assert!(result.is_ok(), "停止已完成的任务应该成功");
        info!("✓ 停止已完成的任务测试通过");

        info!("=== 所有停止任务测试通过 ===");
    }
    #[tokio::test]
    async fn test_task_state_cleanup_and_timeout() {
        // 设置环境变量
        let args = "--base-data-url=~/.codefuse --scan-skip-file-size=10000000";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // 忽略日志初始化错误
        let _ = init_logger();

        info!("=== 测试任务状态清理和超时机制 ===");

        // 1. 测试清理已完成任务的功能
        let project_url = "/test/project".to_string();
        let branch = "test_branch".to_string();
        let cancellation_token = CancellationToken::new();

        // 创建一个已完成的任务
        let mut completed_task = IndexBuildTask::new(project_url.clone(), branch.clone())
            .with_cancellation_token(cancellation_token.clone());
        completed_task.status = IndexBuildStatus::Completed;

        INDEX_BUILD_MANAGER.set_current_task(Some(completed_task.clone())).await;

        // 验证任务存在
        let current_task = INDEX_BUILD_MANAGER.get_current_task().await;
        assert!(current_task.is_some(), "应该有当前任务");
        assert_eq!(current_task.unwrap().status, IndexBuildStatus::Completed, "任务状态应该是Completed");

        // 调用清理方法
        INDEX_BUILD_MANAGER.cleanup_finished_tasks().await;

        // 验证任务已被清理
        let current_task = INDEX_BUILD_MANAGER.get_current_task().await;
        assert!(current_task.is_none(), "已完成的任务应该被清理");

        info!("✓ 清理已完成任务测试通过");

        // 2. 测试超时机制
        // 创建一个超时的任务（设置开始时间为2小时前）
        let two_hours_ago = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() - (2 * 60 * 60 * 1000); // 2小时前

        let mut timeout_task = IndexBuildTask::new(project_url.clone(), branch.clone())
            .with_cancellation_token(cancellation_token.clone());
        timeout_task.status = IndexBuildStatus::Building;
        timeout_task.start_time = two_hours_ago;

        INDEX_BUILD_MANAGER.set_current_task(Some(timeout_task.clone())).await;

        // 调用 is_same_project_branch_running，应该触发超时清理
        let is_running = INDEX_BUILD_MANAGER.is_same_project_branch_running(&project_url, &branch).await;
        assert!(!is_running, "超时任务应该被自动停止，返回false");

        // 验证任务已被清理
        let current_task = INDEX_BUILD_MANAGER.get_current_task().await;
        assert!(current_task.is_none(), "超时任务应该被清理");

        info!("✓ 超时机制测试通过");

        info!("=== 任务状态清理和超时机制测试完成 ===");
    }





    // 模拟CPU使用率检测的辅助函数
    fn get_current_cpu_usage_simulation() -> usize {
        // 这里模拟CPU使用率检测，实际项目中可以使用系统API
        // 返回一个模拟的CPU使用值
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        std::thread::current().id().hash(&mut hasher);
        (hasher.finish() % 100) as usize + 10 // 返回10-109之间的值
    }


}

use std::collections::HashSet;


use crate::model::code_complete_model::CodeCompletionRequestBean;
use crate::service::abstract_similarity::{AssembleComponentHandler, Similarity};
use crate::tools::code_tokenizer::code_snippet_tokenizer;
use crate::tools::common_tools::get_parent_directory;
use crate::tools::jaccard_similarity::jaccard_similarity;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::domain::code_kv_index_domain::ScanFileRecord;

pub struct CurrentPackageComponentHandler;

///相似package行为实现
impl AssembleComponentHandler for CurrentPackageComponentHandler {
    fn current_data_set(code_complete_request: &CodeCompletionRequestBean) -> anyhow::Result<HashSet<u32>> {
        let current_dir = get_parent_directory(&code_complete_request.fileUrl).unwrap();
        let current_url_token_result = code_snippet_tokenizer(current_dir.to_string(), 0, 0, 0, "", &"".to_string()).unwrap();
        let current_url_token_set: HashSet<u32> = current_url_token_result.id_vec.into_iter().collect();
        Ok(current_url_token_set)
    }

    fn get_file_level_similarity(file_recored: &ScanFileRecord, current_data_set: &HashSet<u32>) -> Option<Similarity<Option<String>>> {
        let target_url = get_parent_directory(file_recored.file_url.clone()).unwrap();
        let target_url_token_result = code_snippet_tokenizer(target_url, 0, 0, 0, "", &"".to_string()).unwrap();
        let target_url_token_set: HashSet<u32> = target_url_token_result.id_vec.into_iter().collect();

        let score = jaccard_similarity(&current_data_set, &target_url_token_set);
        //如果相似度小于阈值,跳过
        if score <= AGENT_CONFIG.similarity_threshold {
            return None;
        }

        Some(Similarity {
            sim_score: score,
            value: None,
            file_url: Some(file_recored.file_url.clone()),
        })
    }
}
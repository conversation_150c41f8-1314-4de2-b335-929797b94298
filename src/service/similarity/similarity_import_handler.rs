use std::collections::HashSet;

use anyhow::Result;
use log::{debug, error};


use crate::model::code_complete_model::CodeCompletionRequestBean;
use crate::service::abstract_similarity::{AssembleComponentHandler, Similarity};
use crate::tools::code_tokenizer::code_snippet_tokenizer;
use crate::tools::jaccard_similarity::jaccard_similarity;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{ScanFileRecord, ScanSnippetRecord};
use agent_db::tools::common_tools::{LINE_ENDING, SEPARATORSIMPLE, SNIPPET_PREFIX};

///import长度, 假设代码前50行为import语句,剪裁后用作识别similarity import
const SIMILARITY_IMPORT_SIZE: u8 = 30;


pub struct SimilarityImportHandler;

///相似import行为实现
impl AssembleComponentHandler for SimilarityImportHandler {
    ///获取当前import的tokenId 集合
    fn current_data_set(code_complete_request: &CodeCompletionRequestBean) -> Result<HashSet<u32>> {
        let prompt_line = code_complete_request.prompt.lines().collect::<Vec<_>>();

        let import_end_no = prompt_line.len().checked_sub(SIMILARITY_IMPORT_SIZE as usize).unwrap_or(prompt_line.len());
        let last_code_block_arr = &prompt_line[..import_end_no];
        let import_content = last_code_block_arr.join(LINE_ENDING);

        let import_token_result = code_snippet_tokenizer(import_content, 0, SIMILARITY_IMPORT_SIZE as usize, 0, "", &"".to_string());
        if let Err(import_token_id_set) = import_token_result {
            return Ok(HashSet::new());
        }
        //当前代码的import token id set
        let import_token_id_set: HashSet<u32> = import_token_result.unwrap().id_vec.iter().copied().collect();
        Ok(import_token_id_set)
    }

    fn get_file_level_similarity(file_record: &ScanFileRecord, current_data_set: &HashSet<u32>) -> Option<Similarity<Option<String>>> {

        //如果代码块小于2, 说明代码过少,暂时跳过
        if file_record.total_snippet_num < 2 {
            return None;
        }
        let top_0_20_token_key = format!("{}{}{}{}", SNIPPET_PREFIX, file_record.file_url, SEPARATORSIMPLE, 1);
        let top_20_40_token_key = format!("{}{}{}{}", SNIPPET_PREFIX, file_record.file_url, SEPARATORSIMPLE, 2);

        let content_1 = get_snippet_data(&top_0_20_token_key);
        let content_2 = get_snippet_data(&top_20_40_token_key);


        let target_tokenid_set_opt = match (content_1, content_2) {
            (Some(mut set_a), Some(set_b)) => {
                // 如果两个都是 Some，合并两个 HashSet
                set_a.extend(set_b);
                Some(set_a)
            }
            (Some(set_a), None) => Some(set_a),
            (None, Some(set_b)) => Some(set_b),
            (None, None) => None,
        };

        if target_tokenid_set_opt.is_none() {
            return None;
        }
        let target_tokenid_set = target_tokenid_set_opt.unwrap();

        let score = jaccard_similarity(&current_data_set, &target_tokenid_set);
        //如果相似度小于阈值,跳过
        if score <= AGENT_CONFIG.similarity_threshold {
            return None;
        }
        Some(Similarity {
            sim_score: score,
            value: None,
            file_url: Some(file_record.file_url.clone()),
        })
    }
}


fn get_snippet_data(key: &String) -> Option<HashSet<u32>> {
    let scan_snippet_record_result = KV_CLIENT.get(key);
    match scan_snippet_record_result {
        Ok(scan_snippet_record_opt) => {
            match scan_snippet_record_opt {
                Some(scan_snippet_record_value) => {
                    let scan_snippet_record: ScanSnippetRecord = serde_json::from_str(&scan_snippet_record_value).unwrap();
                    if scan_snippet_record.id_vec.is_empty() {
                        return None;
                    }
                    return Some(HashSet::from_iter(scan_snippet_record.id_vec));
                }
                None => {
                    debug!("get snippet data is empty");
                    return None;
                }
            }
        }
        Err(error) => {
            error!("get_snippet_data error: {:?}", error);
            return None;
        }
    }
}
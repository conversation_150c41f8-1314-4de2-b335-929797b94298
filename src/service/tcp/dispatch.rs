use std::future::Future;

use crate::service::tcp::connection::{ConnContext, ConnState};
use crate::service::tcp::error::LspError;
use crate::service::tcp::msg::{ErrorCode, Notification, Request, Response};
use serde::{de::DeserializeOwned, Serialize};

pub trait ThreadSafeSerde: DeserializeOwned + Serialize + Send + Sync + 'static {}
impl<T> ThreadSafeSerde for T where T: DeserializeOwned + Serialize + Send + Sync + 'static {}

pub struct RequestDispatcher<'a> {
    pub(crate) req: Option<Request>,
    pub(crate) conn_state: &'a mut ConnState,
}

impl RequestDispatcher<'_> {
    pub fn on<P, R, F>(
        &mut self,
        method: &'static str,
        f: fn(ConnContext, P) -> F,
    ) -> &mut Self
    where
        P: ThreadSafeSerde,
        R: ThreadSafeSerde,
        F: Future<Output = anyhow::Result<R>> + Send + 'static,
    {
        let (req, params) = match self.parse::<P>(method) {
            Some(it) => it,
            None => return self,
        };

        let conn_ctx = ConnContext {
            sender: self.conn_state.req_chan.sender.clone(),
            request_timeout: self.conn_state.config.request_timeout,
            conn_id: self.conn_state.conn_id.clone(),
        };

        let resp_sender = self.conn_state.resp_chan.sender.clone();
        tokio::spawn(async move {
            let result = f(conn_ctx, params).await;
            let resp = match result {
                Ok(resp) => match serde_json::to_value(&resp) {
                    Ok(result) => Response::new_ok(req.id, result),
                    Err(e) => Response::new_err(req.id, ErrorCode::InternalError as i32, e.to_string())
                },
                Err(e) => match e.downcast::<LspError>() {
                    Ok(lsp_error) => Response::new_err(req.id, lsp_error.code, lsp_error.message),
                    Err(e) => Response::new_err(req.id, ErrorCode::InternalError as i32, e.to_string()),
                },
            };
            if let Err(err) = resp_sender.send(resp).await {
                log::info!("response send error, {}", err);
            }
        });

        self
    }

    fn parse<P>(&mut self, method: &'static str) -> Option<(Request, P)>
    where
        P: DeserializeOwned,
    {
        let req = self.req.take_if(|it| it.method == method)?;
        let res = from_json(method, &req.params);
        match res {
            Ok(params) => {
                Some((req, params))
            },
            Err(err) => {
                self.respond(Response::new_err(
                    req.id, 
                    ErrorCode::InvalidParams as i32, 
                    err.to_string()
                ));

                None
            }
        }
    }

    pub(crate) fn finish(&mut self) {
        if let Some(req) = self.req.take() {
            self.respond(Response::new_err(
                req.id,
                ErrorCode::MethodNotFound as i32,
                "unknown request".to_owned(),
            ));
        }
    }

    pub(crate) fn respond(&mut self, response: Response) {
        let sender = self.conn_state.resp_chan.sender.clone();
        tokio::spawn(async move {
            if let Err(err) = sender.send(response).await {
                log::info!("response sender error, {}", err);                
            }
        });
    }
}

fn from_json<T: DeserializeOwned>(
    what: &'static str,
    json: &serde_json::Value,
) -> anyhow::Result<T> {
    serde_json::from_value(json.clone())
        .map_err(|e| anyhow::format_err!("Failed to deserialize {what}: {e}; {json}"))
}

pub struct NotificationDispatcher<'a> {
    pub(crate) not: Option<Notification>,
    pub(crate) conn_state: &'a mut ConnState,
}

impl NotificationDispatcher<'_> {
    pub fn on_sync<P>(
        &mut self,
        method: &'static str,
        f: fn(ConnContext, P) -> anyhow::Result<()>,
    ) -> &mut Self
    where
        P: ThreadSafeSerde,
    {
        let not = match self.not.take() {
            Some(it) => it,
            None => return self,
        };

        if not.method != method {
            self.not = Some(not);
            return self;
        }
        let params = match serde_json::from_value(not.params) {
            Ok(params) => params,
            Err(error) => {
                log::info!("Invalid request\nMethod: {}\n error: {}", method, error);
                return self;
            },
        };

        let conn_ctx = ConnContext {
            sender: self.conn_state.req_chan.sender.clone(),
            request_timeout: self.conn_state.config.request_timeout,
            conn_id: self.conn_state.conn_id.clone(),
        };
        if let Err(e) = f(conn_ctx, params) {
            log::error!("notification handler failed, method: {}, error: {}", method, e);
        }
        self
    }

    pub fn on<P, F>(
        &mut self,
        method: &'static str,
        f: fn(ConnContext, P) -> F,
    ) -> &mut Self
    where
        P: ThreadSafeSerde,
        F: Future<Output = anyhow::Result<()>> + Send + 'static,
    {
        let not = match self.not.take() {
            Some(it) => it,
            None => return self,
        };

        if not.method != method {
            self.not = Some(not);
            return self;
        }
        let params = match serde_json::from_value(not.params) {
            Ok(params) => params,
            Err(error) => {
                log::info!("Invalid request\nMethod: {}\n error: {}", method, error);
                return self;
            },
        };

        let conn_ctx = ConnContext {
            sender: self.conn_state.req_chan.sender.clone(),
            request_timeout: self.conn_state.config.request_timeout,
            conn_id: self.conn_state.conn_id.clone(),
        };

        tokio::spawn(async move {
            if let Err(e) = f(conn_ctx, params).await {
                log::error!("notification handler failed\nMethod: {}\n error: {}", method, e);
            }
        });
        self
    }

    pub(crate) fn finish(&mut self) {
        if let Some(not) = &self.not {
            log::error!("unhandled notification: {:?}", not);
        }
    }
}

use log::{debug, info};
use serde_json::Value as JsonValue;
use std::time::{Duration, Instant};
use tokio::{sync::{mpsc::{self, Receiver, Sender}, oneshot}, time::timeout};

use crate::service::tcp::dispatch::{NotificationDispatcher, RequestDispatcher, ThreadSafeSerde};
use crate::service::tcp::msg::{Message, Notification, Request, Response, ResponseError};
use crate::service::tcp::req_queue;

type ReqQueue = req_queue::ReqQueue<(String, Instant), oneshot::Sender<Result<Response, Error>>>;

type ReqMessage = (String, serde_json::Value, Option<oneshot::Sender<Result<Response, Error>>>);

pub struct Channel<S, C> {
    pub sender: S,
    pub receiver: C,
}

enum Event {
    Message(Message),
    MethodRequest(ReqMessage),
    MethodResponse(Response),
}

pub struct ConnConfig {
    pub response_buffer_capacity: usize,
    pub max_concurrent_requests: usize,
    pub request_timeout: Duration,
}



#[derive(Clone, Debug)]
pub struct ConnContext {
    pub(crate) sender: mpsc::Sender<ReqMessage>,
    pub(crate) request_timeout: Duration,
    pub(crate) conn_id: Option<String>,
}
impl ConnContext {
    /// 获取连接ID
    pub fn get_conn_id(&self) -> Option<String> {
        self.conn_id.clone()
    }

    /// 设置连接ID（在init调用时设置）
    pub fn set_conn_id(&mut self, conn_id: String) {
        self.conn_id = Some(conn_id);
    }

    pub async fn notification<Params>(&self, method: &str, params: Params) -> Result<(), Error>
    where
        Params: serde::Serialize,
    {
        let raw_params = serde_json::to_value(params).map_err(Error::ParseError)?;
        if self
            .sender
            .send((method.to_owned(), raw_params, None))
            .await
            .is_err()
        {
            return Err(Error::ChannelError("send error".to_owned()))
        }

        Ok(())
    }

    pub async fn request<P: ThreadSafeSerde, R: ThreadSafeSerde>(&self, method: &str, params: P) -> Result<R, Error> {
        let (send_back_tx, send_back_rx) = oneshot::channel();

        let raw_params = serde_json::to_value(params).map_err(Error::ParseError)?;

        if self
            .sender
            .send((method.to_owned(), raw_params, Some(send_back_tx)))
            .await
            .is_err()
        {
            return Err(Error::ChannelError("send error".to_owned()))
        }

        let response = match timeout(self.request_timeout, send_back_rx).await {
            Ok(data) => match data {
                Ok(Ok(resp)) => resp,
                Ok(Err(err)) => return Err(err),
                Err(err) => return Err(Error::ChannelError(err.to_string()))
            }
            Err(_) => return Err(Error::RequestTimeout),
        };

        let result_value = match response {
            Response { result: Some(result), .. } => result,
            Response { error: Some(err), .. } => return Err(Error::Call(err)),
            _ => {
                let json_str = serde_json::to_string(&response).expect("valid JSON");
                return Err(Error::Custom(format!("Unparseable message: {json_str}")))
            },
        };

        serde_json::from_value(result_value).map_err(Error::ParseError)
    }

    pub fn is_connected(&self) -> bool {
        !self.sender.is_closed()
    }

    pub async fn on_disconnect(&self) {
        self.sender.closed().await;
    }
}



pub struct ConnState {
    pub config: ConnConfig,
    pub sender: Sender<Message>,
    pub receiver: Receiver<Message>,
    pub req_queue: ReqQueue,
    pub req_chan: Channel<Sender<ReqMessage>, Receiver<ReqMessage>>,
    pub resp_chan: Channel<Sender<Response>, Receiver<Response>>,
    pub conn_id: Option<String>,
}

impl ConnState {
    pub fn new(sender: Sender<Message>, receiver: Receiver<Message>, config: ConnConfig) -> Self {
        let req_chan = {
            let (sender, receiver) = mpsc::channel(config.max_concurrent_requests);
            Channel { sender, receiver }
        };

        let resp_chan = {
            let (sender, receiver) = mpsc::channel(config.response_buffer_capacity);
            Channel { sender, receiver }
        };

        ConnState {
            config,
            sender,
            receiver,
            req_queue: Default::default(),
            req_chan,
            resp_chan,
            conn_id: None,
        }
    }

    /// 设置连接ID（在init调用时设置）
    pub fn set_conn_id(&mut self, conn_id: String) {
        self.conn_id = Some(conn_id);
    }

    pub async fn run(mut self, conn_id: Option<String>, request_dispatch: fn(&mut RequestDispatcher), notification_dispatch: fn(&mut NotificationDispatcher)) {
        self.conn_id = conn_id;
        while let Some(event) = self.next_event().await {
            let loop_start = Instant::now();
            match event {
                Event::Message(msg) => match msg {
                    Message::Request(req) => self.on_request(loop_start, req, request_dispatch),
                    Message::Notification(not) => self.on_notification(not, notification_dispatch),
                    Message::Response(resp) => self.complete_request(resp),
                },
                Event::MethodRequest((method, params, sender)) => {
                    if let Err(err) = self.request(method, params, sender).await {
                        info!("method request error {}", err);
                    }
                }
                Event::MethodResponse(resp) => {
                    if let Err(err) = self.respond(resp).await {
                        info!("method response error {}", err);
                    }
                }
            }
        }
    }

    async fn next_event(&mut self) -> Option<Event> {
        tokio::select! {
            msg = self.receiver.recv() => msg.map(Event::Message),
            req = self.req_chan.receiver.recv() => req.map(Event::MethodRequest),
            resp = self.resp_chan.receiver.recv() => resp.map(Event::MethodResponse),
        }
    }

    fn on_request(&mut self, request_received: Instant, req: Request, request_dispatch: fn(&mut RequestDispatcher)) {
        self.req_queue
            .incoming
            .register(req.id.clone(), (req.method.clone(), request_received));

        let mut dispatcher = RequestDispatcher { req: Some(req), conn_state: self };
        request_dispatch(&mut dispatcher);
        dispatcher.finish();
    }

    fn on_notification(&mut self, not: Notification, notification_dispatch: fn(&mut NotificationDispatcher)) {
        let mut dispatcher = NotificationDispatcher { not: Some(not), conn_state: self };
        notification_dispatch(&mut dispatcher);
        dispatcher.finish();
    }

    pub fn complete_request(&mut self, response: Response) {
        let sender = self
            .req_queue
            .outgoing
            .complete(response.id.clone());

        match sender {
            Some(sender) => {
                if let Err(response) = sender.send(Ok(response)) {
                    info!("request closed, {:?}", response);
                }
            }
            None => info!("received response for unknown request, {:?}", response)
        }
    }

    pub async fn respond(&mut self, response: Response) -> Result<(), mpsc::error::SendError<Message>> {
        let completed = match self.req_queue.incoming.complete(&response.id) {
            Some((method, start)) => {
                let duration = start.elapsed();
                debug!("message response, method={}, {}, duration={}", method, response.id, format_args!("{:0.2?}", duration));
                true
            },
            _ => false
        };
        if completed {
            self.sender.send(response.into()).await?
        }
        Ok(())
    }

    async fn request(&mut self, method: String, params: JsonValue, sender: Option<oneshot::Sender<Result<Response, Error>>>) -> Result<(), mpsc::error::SendError<Message>> {
        if let Some(sender) = sender {
            let request = self.req_queue.outgoing.register(method, params, sender);
            self.sender.send(request.into()).await?
        } else {
            self.sender.send(Notification::new(method, params).into()).await?
        }

        Ok(())
    }
}



#[derive(Debug, thiserror::Error)]
pub enum Error {
    /// JSON-RPC error
    #[error("{0}")]
    Call(#[from] ResponseError),
    /// Failed to parse the data.
    #[error("Parse error: {0}")]
    ParseError(#[from] serde_json::Error),
    /// The background task has been terminated.
    #[error("The background task closed {0}, restart required")]
    ChannelError(String),
    /// Request timeout
    #[error("Request timeout")]
    RequestTimeout,
    /// Custom error.
    #[error("Custom error: {0}")]
    Custom(String),
}
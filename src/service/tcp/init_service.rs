use crate::service::ide_service::get_project_connection_manager;
use log::info;
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use uuid::Uuid;
use crate::service::tcp::connection::ConnContext;

#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct InitResponse {
    pub conn_id: String,
}

#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct InitParams {
    pub project_url: Vec<String>,
    pub conn_id: Option<String>,
}


/// 项目连接映射信息
#[derive(Debug, Clone)]
pub struct ProjectConnectionMapping {
    pub project_url: String,
    pub conn_id: String,
    pub conn_context: ConnContext,
}



pub async fn handle_init(mut conn_ctx: ConnContext, params: InitParams) -> anyhow::Result<InitResponse> {
    info!("🚀 处理初始化请求，项目URL列表: {:?}, 传入conn_id: {:?}", params.project_url, params.conn_id);

    let project_manager = get_project_connection_manager();
    let final_conn_id: String;

    // 检查是否提供了conn_id且该conn_id存在
    if let Some(provided_conn_id) = &params.conn_id {
        if !provided_conn_id.is_empty() && project_manager.connection_exists(provided_conn_id).await {
            // conn_id存在，更新现有连接的项目路径列表
            info!("🔄 更新现有连接的项目路径 - 连接ID: {}", provided_conn_id);

            let update_success = project_manager.update_connection_projects(
                provided_conn_id,
                params.project_url.clone(),
                conn_ctx.clone()
            ).await;

            if update_success {
                final_conn_id = provided_conn_id.clone();
                // 更新连接上下文中的conn_id
                conn_ctx.set_conn_id(final_conn_id.clone());

                info!("✅ 成功更新连接项目映射 - 连接ID: {}, 项目数量: {}", final_conn_id, params.project_url.len());
            } else {
                return Err(anyhow::anyhow!("更新连接项目映射失败 - 连接ID: {}", provided_conn_id));
            }
        } else {
            // conn_id不存在或为空，创建新连接
            info!("🆕 conn_id不存在或为空，创建新连接记录");
            final_conn_id = generate_unique_conn_id();

            // 更新连接上下文中的conn_id
            conn_ctx.set_conn_id(final_conn_id.clone());

            // 保存项目URL和连接ID的映射关系
            project_manager.add_mapping(params.project_url.clone(), final_conn_id.clone(), conn_ctx.clone()).await;

            info!("✅ 创建新连接映射 - 连接ID: {}, 项目数量: {}", final_conn_id, params.project_url.len());
        }
    } else {
        // 没有提供conn_id，创建新连接
        info!("🆕 未提供conn_id，创建新连接记录");
        final_conn_id = generate_unique_conn_id();

        // 更新连接上下文中的conn_id
        conn_ctx.set_conn_id(final_conn_id.clone());

        // 保存项目URL和连接ID的映射关系
        project_manager.add_mapping(params.project_url.clone(), final_conn_id.clone(), conn_ctx.clone()).await;

        info!("✅ 创建新连接映射 - 连接ID: {}, 项目数量: {}", final_conn_id, params.project_url.len());
    }

    // 打印项目URL列表
    info!("📋 最终项目URL数量: {}, 返回连接ID: {}", params.project_url.len(), final_conn_id);
    for (index, url) in params.project_url.iter().enumerate() {
        info!("  {}. {}", index + 1, url);
    }

    Ok(InitResponse { conn_id: final_conn_id })
}




/// 生成唯一的连接ID（使用UUID确保全局唯一性）
fn generate_unique_conn_id() -> String {
    // 使用UUID v4确保全局唯一性
    let uuid = Uuid::new_v4();

    // 添加时间戳前缀以便于调试和排序
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();

    format!("conn_{}_{}", timestamp, uuid.simple())
}


/// 项目连接映射管理器
#[derive(Debug)]
pub struct ProjectConnectionManager {
    /// 项目URL到连接映射 project_url -> Vec<ProjectConnectionMapping>
    /// 一个项目可能有多个连接
    project_mappings: Arc<RwLock<HashMap<String, Vec<ProjectConnectionMapping>>>>,
    /// 连接ID到项目URL映射 conn_id -> Vec<String>
    /// 一个连接可能关联多个项目
    connection_mappings: Arc<RwLock<HashMap<String, Vec<String>>>>,
}

impl Default for ProjectConnectionManager {
    fn default() -> Self {
        Self::new()
    }
}

impl ProjectConnectionManager {
    pub fn new() -> Self {
        Self {
            project_mappings: Arc::new(RwLock::new(HashMap::new())),
            connection_mappings: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 添加项目连接映射
    pub async fn add_mapping(&self, project_urls: Vec<String>, conn_id: String, conn_context: ConnContext) {
        let mut project_mappings = self.project_mappings.write().await;
        let mut connection_mappings = self.connection_mappings.write().await;

        // 为每个项目URL添加映射
        for project_url in &project_urls {
            let mapping = ProjectConnectionMapping {
                project_url: project_url.clone(),
                conn_id: conn_id.clone(),
                conn_context: conn_context.clone(),
            };

            project_mappings
                .entry(project_url.clone())
                .or_insert_with(Vec::new)
                .push(mapping);
        }

        // 添加连接到项目的反向映射
        let project_count = project_urls.len();
        connection_mappings.insert(conn_id.clone(), project_urls);

        info!("✅ 已添加项目连接映射 - 连接ID: {}, 项目数量: {}", conn_id, project_count);
    }

    /// 根据项目URL和连接ID查找连接上下文
    pub async fn find_connection(&self, project_url: &str, conn_id: &str) -> Option<ConnContext> {
        let project_mappings = self.project_mappings.read().await;

        if let Some(mappings) = project_mappings.get(project_url) {
            for mapping in mappings {
                if mapping.conn_id == conn_id {
                    return Some(mapping.conn_context.clone());
                }
            }
        }
        None
    }

    /// 根据项目URL查找所有连接上下文
    ///
    /// # 参数
    /// * `project_url` - 项目URL
    ///
    /// # 返回值
    /// * `Option<Vec<ConnContext>>` - 如果找到连接则返回连接上下文列表，否则返回None
    pub async fn find_all_connection(&self, project_url: &str) -> Option<Vec<ConnContext>> {
        // let project_mappings = self.project_mappings.read().await;
        //
        // if let Some(mappings) = project_mappings.get(project_url) {
        //     if mappings.is_empty() {
        //         return None;
        //     }
        //
        //     let connections: Vec<ConnContext> = mappings
        //         .iter()
        //         .map(|mapping| mapping.conn_context.clone())
        //         .collect();
        //
        //     info!("🔍 找到项目连接 - 项目URL: {}, 连接数量: {}", project_url, connections.len());
        //     Some(connections)
        // } else {
        //     info!("❌ 未找到项目连接 - 项目URL: {}", project_url);
        //     None
        // }

        let mut result = vec![];

       let project_map =  self.project_mappings.read().await;

        for project in project_map.values() {
                let connections: Vec<ConnContext> = project
                    .iter()
                    .map(|mapping| mapping.conn_context.clone())
                    .collect();
            result.extend(connections);
        }
        Some(result)

    }

    /// 获取项目的所有连接
    pub async fn get_project_connections(&self, project_url: &str) -> Vec<ProjectConnectionMapping> {
        let project_mappings = self.project_mappings.read().await;
        project_mappings.get(project_url).cloned().unwrap_or_default()
    }

    /// 检查连接ID是否存在
    pub async fn connection_exists(&self, conn_id: &str) -> bool {
        let connection_mappings = self.connection_mappings.read().await;
        connection_mappings.contains_key(conn_id)
    }

    /// 更新现有连接的项目路径列表
    pub async fn update_connection_projects(&self, conn_id: &str, new_project_urls: Vec<String>, conn_context: ConnContext) -> bool {
        let mut project_mappings = self.project_mappings.write().await;
        let mut connection_mappings = self.connection_mappings.write().await;

        // 检查连接是否存在
        if let Some(old_project_urls) = connection_mappings.get(conn_id) {
            let old_project_urls = old_project_urls.clone();

            // 从旧项目映射中移除该连接
            for old_project_url in &old_project_urls {
                if let Some(mappings) = project_mappings.get_mut(old_project_url) {
                    mappings.retain(|mapping| mapping.conn_id != conn_id);
                    // 如果项目没有任何连接了，移除该项目
                    if mappings.is_empty() {
                        project_mappings.remove(old_project_url);
                    }
                }
            }

            // 为新项目URL添加映射
            for project_url in &new_project_urls {
                let mapping = ProjectConnectionMapping {
                    project_url: project_url.clone(),
                    conn_id: conn_id.to_string(),
                    conn_context: conn_context.clone(),
                };

                project_mappings
                    .entry(project_url.clone())
                    .or_insert_with(Vec::new)
                    .push(mapping);
            }

            // 更新连接到项目的反向映射
            connection_mappings.insert(conn_id.to_string(), new_project_urls.clone());

            info!("🔄 已更新连接项目映射 - 连接ID: {}, 旧项目数量: {}, 新项目数量: {}",
                  conn_id, old_project_urls.len(), new_project_urls.len());
            true
        } else {
            false
        }
    }

    /// 移除连接的所有映射
    pub async fn remove_connection(&self, conn_id: &str) {
        let mut project_mappings = self.project_mappings.write().await;
        let mut connection_mappings = self.connection_mappings.write().await;

        // 获取该连接关联的所有项目
        if let Some(project_urls) = connection_mappings.remove(conn_id) {
            // 从每个项目的映射中移除该连接
            for project_url in &project_urls {
                if let Some(mappings) = project_mappings.get_mut(project_url) {
                    mappings.retain(|mapping| mapping.conn_id != conn_id);
                    // 如果项目没有任何连接了，移除该项目
                    if mappings.is_empty() {
                        project_mappings.remove(project_url);
                    }
                }
            }
            info!("🗑️ 已移除连接映射 - 连接ID: {}, 影响项目数量: {}", conn_id, project_urls.len());
        }
    }
}

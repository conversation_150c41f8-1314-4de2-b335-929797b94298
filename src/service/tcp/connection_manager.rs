use crate::service::ide_service::get_project_connection_manager;
use log::{debug, info, warn};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{Mutex, RwLock};

/// 连接信息
#[derive(Debug, <PERSON>lone)]
pub struct ConnectionInfo {
    /// 连接ID
    pub conn_id: String,
    /// 远程地址
    pub remote_addr: SocketAddr,
    /// 连接建立时间
    pub connected_at: Instant,
    /// 连接状态
    pub status: ConnectionStatus,
}

/// 连接状态
#[derive(Debug, Clone, PartialEq)]
pub enum ConnectionStatus {
    /// 已连接
    Connected,
    /// 已断开
    Disconnected,
}

/// 连接统计信息
#[derive(Debug, Clone)]
pub struct ConnectionStats {
    /// 当前活跃连接数
    pub active_connections: usize,
    /// 总连接数（历史累计）
    pub total_connections: u64,
    /// 总断开连接数
    pub total_disconnections: u64,
    /// 平均连接持续时间
    pub average_connection_duration: Option<Duration>,
}

/// 连接管理器
#[derive(Debug)]
pub struct ConnectionManager {
    /// 活跃连接映射 conn_id -> ConnectionInfo
    active_connections: Arc<RwLock<HashMap<String, ConnectionInfo>>>,
    /// 连接统计信息
    stats: Arc<Mutex<ConnectionStats>>,
    /// 历史连接持续时间（用于计算平均值）
    connection_durations: Arc<Mutex<Vec<Duration>>>,
}

impl Default for ConnectionManager {
    fn default() -> Self {
        Self::new()
    }
}

impl ConnectionManager {
    /// 创建新的连接管理器
    pub fn new() -> Self {
        Self {
            active_connections: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(Mutex::new(ConnectionStats {
                active_connections: 0,
                total_connections: 0,
                total_disconnections: 0,
                average_connection_duration: None,
            })),
            connection_durations: Arc::new(Mutex::new(Vec::new())),
        }
    }

    /// 通知连接建立
    pub async fn on_connection_established(&self, conn_id: String, remote_addr: SocketAddr) {
        let connection_info = ConnectionInfo {
            conn_id: conn_id.clone(),
            remote_addr,
            connected_at: Instant::now(),
            status: ConnectionStatus::Connected,
        };

        // 添加到活跃连接
        {
            let mut connections = self.active_connections.write().await;
            connections.insert(conn_id.clone(), connection_info.clone());
        }

        // 更新统计信息
        {
            let mut stats = self.stats.lock().await;
            stats.active_connections += 1;
            stats.total_connections += 1;
        }

        info!(
            "🔗 TCP连接已建立 - ID: {}, 远程地址: {}, 当前活跃连接数: {}",
            conn_id,
            remote_addr,
            self.get_active_connection_count().await
        );

        debug!("连接详情: {:?}", connection_info);
    }

    /// 通知连接断开
    pub async fn on_connection_disconnected(&self, conn_id: &str) {
        let connection_info = {
            let mut connections = self.active_connections.write().await;
            connections.remove(conn_id)
        };

        if let Some(mut info) = connection_info {
            let connection_duration = info.connected_at.elapsed();
            info.status = ConnectionStatus::Disconnected;

            // 记录连接持续时间
            {
                let mut durations = self.connection_durations.lock().await;
                durations.push(connection_duration);
                
                // 限制历史记录数量，避免内存无限增长
                if durations.len() > 1000 {
                    durations.remove(0);
                }
            }

            // 清理项目连接映射
            let project_manager = get_project_connection_manager();
            project_manager.remove_connection(conn_id).await;

            // 更新统计信息
            {
                let mut stats = self.stats.lock().await;
                stats.active_connections = stats.active_connections.saturating_sub(1);
                stats.total_disconnections += 1;

                // 更新平均连接持续时间
                let durations = self.connection_durations.lock().await;
                if !durations.is_empty() {
                    let total_duration: Duration = durations.iter().sum();
                    stats.average_connection_duration = Some(total_duration / durations.len() as u32);
                }
            }

            info!(
                "❌ TCP连接已断开 - ID: {}, 远程地址: {}, 持续时间: {:?}, 当前活跃连接数: {}",
                conn_id,
                info.remote_addr,
                connection_duration,
                self.get_active_connection_count().await
            );

            debug!("断开连接详情: {:?}", info);
        } else {
            warn!("尝试断开未知连接: ID {}", conn_id);
        }
    }

    /// 获取当前活跃连接数
    pub async fn get_active_connection_count(&self) -> usize {
        let connections = self.active_connections.read().await;
        connections.len()
    }

    /// 获取所有活跃连接信息
    pub async fn get_active_connections(&self) -> Vec<ConnectionInfo> {
        let connections = self.active_connections.read().await;
        connections.values().cloned().collect()
    }

    /// 获取连接统计信息
    pub async fn get_stats(&self) -> ConnectionStats {
        self.stats.lock().await.clone()
    }

    /// 检查连接是否存在
    pub async fn is_connection_active(&self, conn_id: &str) -> bool {
        let connections = self.active_connections.read().await;
        connections.contains_key(conn_id)
    }

    /// 获取特定连接的信息
    pub async fn get_connection_info(&self, conn_id: &str) -> Option<ConnectionInfo> {
        let connections = self.active_connections.read().await;
        connections.get(conn_id).cloned()
    }

    /// 打印连接统计信息
    pub async fn print_stats(&self) {
        let stats = self.get_stats().await;
        let active_connections = self.get_active_connections().await;

        info!("📊 TCP连接统计信息:");
        info!("  - 当前活跃连接数: {}", stats.active_connections);
        info!("  - 历史总连接数: {}", stats.total_connections);
        info!("  - 历史总断开数: {}", stats.total_disconnections);
        
        if let Some(avg_duration) = stats.average_connection_duration {
            info!("  - 平均连接持续时间: {:?}", avg_duration);
        }

        if !active_connections.is_empty() {
            info!("  - 活跃连接详情:");
            for conn in active_connections {
                info!(
                    "    * ID: {}, 地址: {}, 持续时间: {:?}",
                    conn.conn_id,
                    conn.remote_addr,
                    conn.connected_at.elapsed()
                );
            }
        }
    }

    /// 清理所有连接（用于服务器关闭时）
    pub async fn cleanup_all_connections(&self) {
        let connection_ids: Vec<String> = {
            let connections = self.active_connections.read().await;
            connections.keys().cloned().collect()
        };

        for conn_id in connection_ids {
            self.on_connection_disconnected(&conn_id).await;
        }

        info!("🧹 所有TCP连接已清理完毕");
    }
}

/// 全局连接管理器实例
static CONNECTION_MANAGER: once_cell::sync::Lazy<ConnectionManager> = 
    once_cell::sync::Lazy::new(|| ConnectionManager::new());

/// 获取全局连接管理器实例
pub fn get_connection_manager() -> &'static ConnectionManager {
    &CONNECTION_MANAGER
}

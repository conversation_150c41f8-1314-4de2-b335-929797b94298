use crate::model::code_complete_model::{CodeCompletionRequestBean, CompletionResultModel, QueryJarInfoRequestBean};
use crate::tools::common_tools::AntJarCodeInfo;
use agent_db::remote::rpc_model::BaseResponse;

///代码扫描的抽象策略接口
pub trait CodeCompleteStrategy {
    ///代码补全策略
    async fn code_complete(code_complete_request: CodeCompletionRequestBean) -> BaseResponse<CompletionResultModel>;

    ///查询依赖jar
    async fn query_dependency_info(query_jar_info_request_bean: QueryJarInfoRequestBean) -> BaseResponse<Vec<AntJarCodeInfo>>;

}





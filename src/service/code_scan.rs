use std::fs;
use std::fs::File;
use std::io::{<PERSON><PERSON><PERSON><PERSON>, BufReader};
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};

use anyhow::Result;
use ignore::DirEntry;
use log::{debug, error};

use crate::ast::java::strategy::CodeFuseJavaAstStrategy;
use crate::ast::js::constant::JS_VALID_EXTENSIONS;
use crate::ast::js::strategy::CodeFuseJsAstStrategy;
use agent_common_service::service::code_ast_analysis::AstStrategy;
use agent_common_service::tools::code_tokenizer::code_snippet_tokenizer;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{
    CodeInfo, ScanFileRecord, ScanProjectRecord, PROJECT_DATA_KEY,
};
use agent_db::tools::common_tools::{FILE_PREFIX, LINE_ENDING, SEPARATORSIMPLE, SNIPPET_PREFIX};

///检查当前仓库是否有索引
/// 1.如果有其他仓库信息,或者当前仓库已经过期，那先清空数据(不占用用户过多磁盘空间）
/// 2.清空数据之后，PROJECT_DATA_KEY会进行保存数据
pub fn check_and_del_old_project(project_url: &String) -> Option<ScanProjectRecord> {
    let project_info_result = KV_CLIENT.get(&PROJECT_DATA_KEY.to_string());
    match project_info_result {
        Ok(project_info_opt) => {
            match project_info_opt {
                Some(project_info) => {
                    let kv_project_vec: Vec<String> = serde_json::from_str(&project_info).unwrap();
                    let mut del_info_vec: Vec<String> = vec![];
                    let mut scan_project_opt: Option<ScanProjectRecord> = None;
                    for exist_project_url in kv_project_vec {
                        // let exist_project: ScanProjectRecord = serde_json::from_str(&exist_project_url).unwrap();
                        if &exist_project_url == project_url {
                            let exist_project_result = KV_CLIENT.get(&exist_project_url);
                            match exist_project_result {
                                Ok(exist_project_opt) => {
                                    match exist_project_opt {
                                        Some(exist_project) => {
                                            let scan_project_project: ScanProjectRecord = serde_json::from_str(&exist_project).unwrap();
                                            let current_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as usize;
                                            let cache_time = current_time - scan_project_project.record_millis;
                                            //如果数据超过有效期。删除
                                            if cache_time > AGENT_CONFIG.index_cache_timeout || scan_project_project.index_file_num == 0 {
                                                del_info_vec.push(exist_project_url);
                                            } else {
                                                scan_project_opt = Some(scan_project_project);
                                            }
                                        }
                                        None => {
                                            //如果数据清空了，那么即使之前找到数据，也重新设置为none，防止意外
                                            scan_project_opt = None;
                                        }
                                    }
                                }
                                Err(e) => {
                                    error!("get project info error: {}",e);
                                }
                            }
                        } else {
                            del_info_vec.push(exist_project_url);
                        }
                    }
                    if del_info_vec.len() > 0 {
                        let _ = KV_CLIENT.clean();
                        save_new_project_base_info(project_url);
                        //如果数据清空了，那么即使之前找到数据，也重新设置为none，防止意外
                        scan_project_opt = None;
                    }
                    if scan_project_opt.is_none() {
                        return save_new_project_detail_info(project_url);
                    }
                    return scan_project_opt;
                }
                None => {
                    save_new_project_base_info(project_url);
                    return save_new_project_detail_info(project_url);
                }
            }
        }
        Err(e) => {
            error!("get project info error: {}",e);
            return None;
        }
    }
}


pub fn is_test_file(file_stem: &str, file_extension: &str) -> bool {
    // java
    if file_stem.ends_with("Test") {
        return true;
    }

    // typescript
    // a.test.js/b.spec.js/b.e2e.js 是经常使用的 unit test 的文件名
    if JS_VALID_EXTENSIONS.contains(&file_extension) && JS_VALID_EXTENSIONS.iter().any(|suffix| file_stem.ends_with(suffix)) {
        return true;
    }

    return false;
}

///判断是否跳过指定文件
/// 返回值（true:跳过，false:不跳过，file_name_suffix:文件后缀名，lines:文件内容）
pub fn skip_file(dir: &Path, file_content_opt: Option<String>) -> (bool, Option<String>, Option<Vec<String>>) {
    if !dir.exists() {
        return (true, None, None);
    }
    let file_size = fs::metadata(dir).unwrap().len();
    if file_size > AGENT_CONFIG.scan_skip_file_size {
        return (true, None, None);
    }
    let extension_opt = dir.extension();
    if let None = extension_opt {
        //没有后缀名
        return (true, None, None);
    }
    let short_file_name_opt = dir.file_stem();
    if short_file_name_opt.is_none() {
        //文件名是空
        return (true, None, None);
    }
    let short_file_name = short_file_name_opt.unwrap().to_str().unwrap();
    let file_name_suffix = extension_opt.unwrap().to_string_lossy().to_string();
    if AGENT_CONFIG.scan_skip_test_flag == 1 && is_test_file(short_file_name, &file_name_suffix) {
        //跳过测试文件
        return (true, None, None);
    }
    if !(AGENT_CONFIG.similarity_suffix_arr.contains(&file_name_suffix) || AGENT_CONFIG.related_suffix_arr.contains(&file_name_suffix)) {
        return (true, None, None);
    }
    let file_result = File::open(dir);
    if let Err(error) = file_result {
        //有可能扫描过程中用户删除文件
        error!("open file error: {:?} , url: {:?}",error,dir);
        return (true, None, None);
    }

    let mut lines: Vec<String>;
    if file_content_opt.is_none() {
        let file = file_result.unwrap();
        let reader = BufReader::new(file);
        lines = reader.lines().collect::<std::result::Result<_, _>>().unwrap_or(vec![]);
    } else {
        lines = file_content_opt.unwrap().split(LINE_ENDING).map(|s| s.to_string()).collect();
    }

    if lines.len() > AGENT_CONFIG.scan_skip_file_max_len || lines.len() < AGENT_CONFIG.scan_skip_file_min_len {
        return (true, None, None);
    }
    return (false, Some(file_name_suffix), Some(lines));
}


///判断是否跳过扫描指定dir
pub fn skip_dir(entry: &DirEntry) -> bool {
    let file_name = entry.file_name().to_string_lossy();

    let skip_file = entry.file_name()
        .to_str()
        .map(|s| s.starts_with('.') || s.contains(".log"))
        .unwrap_or(false);


    AGENT_CONFIG.ignore_file_arr.contains(&*file_name) || skip_file
}

///抽取相似代码片段
pub fn extra_and_save_similarity_code_fragment(dir: &Path, file_name_suffix: &String, lines: &Vec<String>, project_url: &String) -> Result<Option<ScanFileRecord>> {
    let mut file_record = ScanFileRecord::default();
    file_record.file_url.push_str(dir.to_str().unwrap());
    file_record.file_name_suffix = file_name_suffix.clone();
    let mut snippet_index: usize = 1;
    for window_start in (0..lines.len()).step_by(AGENT_CONFIG.code_complation_slide_size as usize) {
        let window_end = usize::min(window_start + AGENT_CONFIG.code_complation_window_size as usize, lines.len());
        let content_arr = &lines[window_start..window_end];
        let code_snippet = content_arr.join(LINE_ENDING);

        let result = code_snippet_tokenizer(code_snippet, window_start, window_end, snippet_index, dir.to_str().unwrap(), file_name_suffix);
        match result {
            Ok(mut scan_snippet_record) => {
                let file_snippet_key = format!("{}{}{}{}", SNIPPET_PREFIX, dir.to_str().unwrap(), SEPARATORSIMPLE, &snippet_index.to_string());
                if window_end < lines.len() {
                    let next_index = snippet_index + 1;
                    let next_key = format!("{}{}{}{}", SNIPPET_PREFIX, dir.to_str().unwrap(), SEPARATORSIMPLE, &next_index.to_string());
                    scan_snippet_record.next_key = Some(next_key);
                }
                let scan_snippet_record_str = serde_json::to_string(&scan_snippet_record).unwrap();
                let r = KV_CLIENT.insert(&file_snippet_key, &scan_snippet_record_str);
                if r.is_err() {
                    error!("code snippet insert error: {:?}",r.err());
                    let _ = KV_CLIENT.delete_from_prefix(&format!("{}{}", SNIPPET_PREFIX, project_url));
                    let _ = KV_CLIENT.delete_from_prefix(&format!("{}{}", FILE_PREFIX, project_url));
                    continue;
                }
            }
            Err(e) => {
                error!("code snippet tokenizer error: {}",e);
            }
        }
        snippet_index = snippet_index + 1;
    }
    file_record.total_snippet_num = snippet_index;
    Ok(Some(file_record))
}

///抽取相关代码片段
pub fn extra_related_code_fragment(file_path: &Path, file_name_suffix: &String, lines: &Vec<String>, project_path: &String) -> Option<CodeInfo> {
    let code_content = lines.join(LINE_ENDING);
    match file_name_suffix.as_str() {
        "java" => CodeFuseJavaAstStrategy::extra_code_info(&code_content),
        suffix if JS_VALID_EXTENSIONS.contains(&suffix) => CodeFuseJsAstStrategy::extract_code_info(project_path, &file_path, &code_content),
        _ => None,
    }
}

/************************ 下面是私有函数 *************************************/

///保存最上层的仓库信息，记录agent生成的仓库索引范围
fn save_new_project_base_info(project_url: &String) {
    let kv_project_vec = vec![project_url];
    let kv_project_value = serde_json::to_string(&kv_project_vec).unwrap();
    let _ = KV_CLIENT.insert(&PROJECT_DATA_KEY.to_string(), &kv_project_value);
}

///基于当前仓库地址，生成初始化仓库信息。index_file_num为0即代表在这个仓库有记录，但还没来得及索引代码信息
fn save_new_project_detail_info(project_url: &String) -> Option<ScanProjectRecord> {
    let scan_project = ScanProjectRecord {
        project_url: project_url.clone(),
        index_file_num: 0,
        record_millis: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as usize,
    };
    let scan_project_value = serde_json::to_string(&scan_project).unwrap();
    let result = KV_CLIENT.insert(project_url, &scan_project_value);
    match result {
        Ok(_) => {
            return Some(scan_project);
        }
        Err(e) => {
            error!("save project info error: {}", e);
            return None;
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    #[test]
    fn check_is_test_file() {
        let cases = [
            // java
            ("ATest", "java", true),

            //js
            ("index.test", "js", true),
            ("index.spec", "js", true),
            ("index.e2e", "js", true),
            ("index.test", "ts", true),
            ("index.spec", "ts", true),
            ("index.e2e", "ts", true),
            ("index.test", "tsx", true),
            ("index.spec", "tsx", true),
            ("index.e2e", "tsx", true)
        ];

        for (file_name, file_suffix, expected) in cases {
            let result = is_test_file(file_name, file_suffix);
            println!("is_test_file({file_name}, {file_suffix}) -> {result}");
            assert_eq!(result, expected);
        }
    }
}

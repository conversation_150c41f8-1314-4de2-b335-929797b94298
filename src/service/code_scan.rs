use agent_db::domain::code_kv_index_domain::ScanFileRecord;
use anyhow::Result;
use std::path::Path;

use crate::model::code_scan_model::ScanConfig;

///代码扫描的抽象接口
pub trait CodeScanStrategy {
    ///从一个目录下扫描文件
    async fn scan_project_from_url(scan_config: ScanConfig) -> Result<()>;
    ///扫描状态。false扫描完成，true扫描中
    fn is_scan_ing() -> bool;

    ///扫描单个文件（扫描过程中会进行相关，相似数据处理)
    fn execute_scan_file( target_file: &Path, file_content_opt: Option<String>) ->Result<Option<ScanFileRecord>>{Ok(None)}
    ///扫描仓库依赖
    async fn execute_dependency_scan(project_url:&String) -> Result<()>{Ok(())}
}
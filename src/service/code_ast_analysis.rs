use crate::model::code_complete_model::CodeCompletionRequestBean;
use crate::model::prompt_declaration::PromptDeclaration;
use agent_db::domain::code_chat_domain::CodefuseMethod;
use agent_db::domain::code_kv_index_domain::CodeInfo;
use std::path::Path;

///代码分析抽象策略接口
pub trait AstStrategy {
    ///代码补全策略
    fn extra_code_info(code_content: &String) -> Option<CodeInfo>;
    fn get_prompt_declaration(bean: &CodeCompletionRequestBean) -> Option<PromptDeclaration>;
    ///抽取对话时的函数结构
    fn extra_method_chat_index(dir: &Path, source_code: &String, project_url:&String,branch:&String) -> Option<Vec<CodefuseMethod>>;
}


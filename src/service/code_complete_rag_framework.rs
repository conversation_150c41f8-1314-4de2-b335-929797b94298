use anyhow::Result;

use crate::model::code_complete_model::{CodeCompletionRequestBean, ContextAwareInfo};

///代码补全过程中的rag检索策略
/// 注意：这个trait定义相似相关检索的执行流程，如果想改流程(检索，增量更新流程）可以重写此trait
///      如果只想重写具体检索策略和单文件增量逻辑，那么重写code_complete_rag_execute文件的trait
pub trait CodeCompleteRagFrameworkStrategy {
    ///查找相似代码
    async  fn get_similarity_snippet(code_complete_request: &CodeCompletionRequestBean) -> Result<Option<Vec<ContextAwareInfo>>>;
    ///查找相关代码
    async  fn get_related_snippet(code_complete_request: &CodeCompletionRequestBean) -> Result<Option<Vec<ContextAwareInfo>>>;
}



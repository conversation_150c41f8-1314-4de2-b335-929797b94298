use crate::model::code_complete_model::CodeCompletionRequestBean;
use crate::model::prompt_declaration::PromptDeclaration;
use crate::service::abstract_similarity::{AssembleComponentHand<PERSON>, Similarity};
use crate::service::similarity::current_package_handler::CurrentPackageComponentHandler;
use crate::service::similarity::similarity_import_handler::SimilarityImportHandler;
use crate::service::similarity::similarity_name_handler::SimilarityNameHandler;
use crate::tools::code_tokenizer::token_to_code;
use crate::tools::common_tools::SimilarityCodeInfo;
use crate::tools::related_module_score::CacheRelatedModule;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{ScanFileRecord, ScanSnippetRecord};
use agent_db::tools::common_tools::SNIPPET_PREFIX;
use log::{debug, error};
use std::collections::{BinaryHeap, HashSet};

///定义补全过程中缓存处理策略
pub trait CodeCompleteCacheStrategy {
    ///构建相关数据缓存
    fn build_related_module_score(&self, code_complete_request: &CodeCompletionRequestBean, current_context: &PromptDeclaration, file_record: &ScanFileRecord) -> Option<CacheRelatedModule>;

    ///构建相关数据缓存，支持一个文件返回多种类型
    fn build_related_module_scores(&self, code_complete_request: &CodeCompletionRequestBean, current_context: &PromptDeclaration, file_record: &ScanFileRecord) -> Option<Vec<CacheRelatedModule>> {
        self.build_related_module_score(code_complete_request, current_context, file_record).map(|item| vec![item])
    }

    ///构建相似度缓存时候，输入请求参数，给出import，文件路径，文件名的token_id集合，用于在get_target_similarity_score_data阶段计算相似度
    fn get_current_similarity_score_data(&self,code_complete_request: &CodeCompletionRequestBean) -> (HashSet<u32>, HashSet<u32>, HashSet<u32>){
        //当前package的tokenid集合
        let current_package_data_result = CurrentPackageComponentHandler::current_data_set(code_complete_request);
        let mut current_package_data: HashSet<u32> = HashSet::new();
        if current_package_data_result.is_ok() {
            current_package_data.extend(current_package_data_result.unwrap());
        }

        //当前import信息的tokenid集合
        let current_import_data_result = SimilarityImportHandler::current_data_set(code_complete_request);
        let mut current_import_data: HashSet<u32> = HashSet::new();
        if current_import_data_result.is_ok() {
            current_import_data.extend(current_import_data_result.unwrap());
        }
        //当前文件名的tokenid集合
        let current_name_data_result = SimilarityNameHandler::current_data_set(code_complete_request);
        let mut current_name_data: HashSet<u32> = HashSet::new();
        if current_name_data_result.is_ok() {
            current_name_data.extend(current_name_data_result.unwrap());
        }
        (current_package_data, current_import_data, current_name_data)
    }

    ///构建相似度缓存时候，输入目标文件信息+当前文件import，文件路径，文件名的tokenId集合，给出和目标文件(import信息，文件路径，文件名)，在多个维度的相似度数据
    fn get_target_similarity_score_data(&self,scan_file_record: &ScanFileRecord, cur_score_data: &(HashSet<u32>, HashSet<u32>, HashSet<u32>)) -> (Option<Similarity<Option<String>>>, Option<Similarity<Option<String>>>, Option<Similarity<Option<String>>>);


    ///判断是否需要构建相关索引数据
    ///补全过程中已有判断（为了耗时做了初步过滤)，在部分场景下，构建缓存过程中，需要针对性基于上下文数据，进一步分析是否有必要构建
    fn is_need_build_related(&self,file_suffix:&String,current_data: &PromptDeclaration) -> bool;
}


// ************************ 构建相似度缓存数据时候所需的工具函数 start  ************************ /

///依次从binaryHeap中取出目标文件，遍历文件的代码块，组装缓存数据
pub fn assemble_similarity_codeinfo(data_heap: &mut BinaryHeap<Similarity<Option<String>>>, limit_snippet_size: u16, result_data: &mut Vec<SimilarityCodeInfo>, exist_key: &mut HashSet<String>) {
    let mut count = 0;

    'outer: while let Some(similarity_data) = data_heap.pop() {
        let file_url_value = similarity_data.file_url.unwrap();
        let file_snippet_key = format!("{}{}", SNIPPET_PREFIX, file_url_value);
        let result = KV_CLIENT.get_from_prefix(&file_snippet_key);

        match result {
            Ok(all_project_snippet_opt) => {
                match all_project_snippet_opt {
                    Some(all_project_snippet) => {
                        //开始遍历文件列表，寻找和当前文件最相关的
                        for (key, value) in all_project_snippet {
                            if exist_key.contains(&key) {
                                continue;
                            }
                            let scan_file_data_result: serde_json::error::Result<ScanSnippetRecord> = serde_json::from_str(value.as_str());
                            match scan_file_data_result {
                                Ok(scan_snippet_record) => {
                                    let element_opt = get_similarity_codeinfo_by_config(scan_snippet_record);
                                    if let Some(element) = element_opt {
                                        exist_key.insert(key);
                                        result_data.push(element);
                                        count = count + 1;
                                        if count >= limit_snippet_size {
                                            break 'outer;
                                        }
                                    }
                                }
                                Err(e) => {
                                    error!("build_similarity_data error:{}", e)
                                }
                            }
                        }
                    }

                    None => {
                        debug!("get_similarity_codeinfo all_project_snippet is none")
                    }
                }
            }
            Err(e) => {
                error!("get_similarity_codeinfo error:{}",e)
            }
        }
    }
}

///根据配置，获取相似度计算候选集
fn get_similarity_codeinfo_by_config(scan_snippet_record: ScanSnippetRecord) -> Option<SimilarityCodeInfo> {
    let mut result = SimilarityCodeInfo::new(scan_snippet_record.file_url);
    if AGENT_CONFIG.similarity_cal_type == 1 && scan_snippet_record.next_key.is_some() {
        // 当前策略为光标后比较，取出下一个索引块
        let next_snippet_result = KV_CLIENT.get(&scan_snippet_record.next_key.unwrap());
        let code_snippet_opt: (Option<Vec<u32>>, Option<String>) = match next_snippet_result {
            Ok(next_snippet_opt) => {
                match next_snippet_opt {
                    Some(next_snippet) => {
                        let next_snippet_result: serde_json::error::Result<ScanSnippetRecord> = serde_json::from_str(next_snippet.as_str());
                        match next_snippet_result {
                            Ok(next_snippet_record) => {
                                let content_result = token_to_code(&next_snippet_record.id_vec);
                                if let Ok(content) = content_result {
                                    (Some(next_snippet_record.id_vec), Some(content))
                                } else {
                                    (None, None)
                                }
                            }
                            Err(e) => {
                                error!("get_similarity_codeinfo_by_config get next snippet error:{}", e);
                                (None, None)
                            }
                        }
                    }
                    None => {
                        debug!("get_similarity_codeinfo_by_config next snippet is none");
                        (None, None)
                    }
                }
            }
            Err(e) => {
                error!("get_similarity_codeinfo_by_config error:{}", e);
                (None, None)
            }
        };
        if code_snippet_opt.0.is_some() {
            result.token_id_set = code_snippet_opt.0.unwrap().iter().copied().collect();
            result.code_snippet = code_snippet_opt.1.unwrap();
            return Some(result);
        }
    }
    let current_content_result = token_to_code(&scan_snippet_record.id_vec);
    if let Ok(content) = current_content_result {
        result.token_id_set = scan_snippet_record.id_vec.iter().copied().collect();
        result.code_snippet = content;
        return Some(result);
    }
    return None;
}
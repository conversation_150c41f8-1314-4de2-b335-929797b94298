use crate::model::code_complete_model::{CodeCompletionRequestBean, CompletionResultModel};
use crate::model::code_edit_model::{CodeEditRequest, FileContent, NextTabRequest};
use agent_db::remote::rpc_model::BaseResponse;

///CodeEdit的抽象策略接口
pub trait CodeEditStrategy {
    ///代码补全策略
    async fn code_edit(
        code_complete_request: CodeCompletionRequestBean,
    ) -> BaseResponse<CompletionResultModel>;

    async fn next_tab(
        code_complete_request: CodeCompletionRequestBean,
    ) -> BaseResponse<CompletionResultModel>;

    ///将补全请求转换成CodeEdit请求
    async fn convert_request(input: CodeCompletionRequestBean) -> CodeEditRequest;

    ///将补全请求转换成CodeEdit请求
    async fn convert_next_tab_request(input: CodeCompletionRequestBean) -> NextTabRequest;
}

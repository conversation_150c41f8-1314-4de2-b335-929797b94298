use std::cmp::Ordering;
use std::collections::HashSet;

use anyhow::Result;
use serde::{Deserialize, Serialize};


use crate::model::code_complete_model::CodeCompletionRequestBean;
use agent_db::domain::code_kv_index_domain::ScanFileRecord;

///定义每种相似度文件类型的通用行为
pub trait AssembleComponentHandler {
    ///获取当前数据集
    fn current_data_set(code_complete_request: &CodeCompletionRequestBean) -> Result<HashSet<u32>>;
    ///找到文件级别相似度得分，后续进行第一层过滤
    fn get_file_level_similarity(file_record: &ScanFileRecord, current_data_set:&HashSet<u32>) -> Option<Similarity<Option<String>>>;
}

// 为了在二进制堆中存储相似度和索引，定义一个结构体和它的排序方法
#[derive(Debug)]
pub struct Similarity<T> {
    pub sim_score: f64,
    pub value: T,
    pub file_url: Option<String>,
}

impl<T> PartialEq for Similarity<T> {
    fn eq(&self, other: &Self) -> bool {
        self.sim_score == other.sim_score
    }
}

// Since Eq is just a marker trait that requires PartialEq, it can be empty
impl<T> Eq for Similarity<T> {}

impl<T> Ord for Similarity<T> {
    fn cmp(&self, other: &Self) -> Ordering {
        // Compare f64 directly, maintaining higher scores at the top of the heap
        self.sim_score.partial_cmp(&other.sim_score).unwrap_or(Ordering::Equal)
    }
}

impl<T> PartialOrd for Similarity<T> {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

///相似文件类型
#[derive(Debug, Serialize, Deserialize, Hash, Eq, PartialEq)]
pub enum SimilarityType {
    SIMILAR_IMPORT,
    CURRENT_DIR,
    SIMILAR_NAME,
}

use std::collections::HashMap;

use serde::{Deserialize, Serialize};
use crate::model::code_edit_model::FileDiff;

///相似/相关片段结果
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ContextAwareInfo {
    ///相似片段的文件地址
    pub filePath: String,
    ///相似片段代码块
    pub content: String,
    ///当前得分
    pub score: f64,
    ///其他信息
    pub extraData: Option<String>,
}
///代码补全请求体,由于要兼容插件已有流程,命名方式要和java对齐
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CodeCompletionRequestBean {
    //仓库路径
    pub projectUrl: Option<String>,
    //代码上文
    pub prompt: String,
    //代码下文
    pub suffix: String,
    //用户token
    pub userToken: String,
    //语言
    pub language: String,
    //路径
    pub fileUrl: String,
    //仓库名
    pub repo: Option<String>,
    //插件版本
    pub pluginVersion: String,
    //ide版本
    pub ideVersion: String,
    //特殊数据,包含agent启动时间等
    pub recordInfo: Option<HashMap<String, String>>,
    //相似文件片段
    pub similarSnippets: Option<Vec<ContextAwareInfo>>,
    //相关文件片段
    pub relatedSnippets: Option<Vec<ContextAwareInfo>>,
    //超时
    pub timeOut: u64,
    //产品类型
    pub productType: String,
    //唯一id
    pub sessionId: String,
    //是否跳过前后处理
    pub skipFilter: bool,
    //文件名后缀
    pub fileNameSuffix: Option<String>,
    //是否打开安全修复
    pub openFixSafety: Option<bool>,
    //补全类型
    pub completionType: Option<CodeCompletionType>,
    //codeEdit，nextTab专用参数：sourceCodePrefix 指定代码补全光标前部分
    pub sourceCodePrefix: Option<String>,
    //codeEdit，nextTab专用参数：sourceCodeSuffix: 指定代码补全光标后部分
    pub sourceCodeSuffix: Option<String>,
    //nextTab专用参数：originalCode: 带行号的当前代码文本，限制在200行以内，超过200行的在当前编辑区域上下截取100行
    pub originalCode: Option<String>,
    //历史diff轨迹
    pub fileDiffs: Option<Vec<FileDiff>>,
}

///对话任务状态
#[derive(Serialize, Deserialize, Hash, Eq, PartialEq, Debug, Clone)]
pub enum CodeCompletionType {
    //m默认为代码补全
    NORMAL,
    //CodeEdit
    CODEEDIT,
    //获取贯标下一次位置
    NEXT_TAB,
}
///查询jar信息请求
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryJarInfoRequestBean {
    pub groupId: String,
    pub artifactId: String,
    pub version: Option<String>,
    pub pageNo: usize,
    pub pageSize: usize,
}

///代码补全结果,由于要兼容插件已有流程,命名方式要和java对齐
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CompletionResultModel {
    pub sessionId: String,
    pub codeModelList: Option<Vec<CodeModel>>,
    pub completionType: Option<CodeCompletionType>,
    pub lineNumber: Option<i32>,
}

///具体补全结果,由于要兼容插件已有流程,命名方式要和java对齐
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CodeModel {
    pub id: Option<usize>,
    pub sessionId: String,
    pub content: String,
}

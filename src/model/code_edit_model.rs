use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::model::code_complete_model::ContextAwareInfo;

// 定义更新请求的结构，用于反序列化 JSON 请求体
#[derive(Deserialize)]
pub struct FileContent {
    pub file_name: String,
    pub new_content: String,
}

// 定义发送给 codeEdit 服务的请求结构
#[derive(Serialize, Debug)]
pub struct CodeEditRequest {
    pub userToken: String,
    pub pluginVersion: String,
    pub productType: String,
    pub sessionId: String,
    pub repo: Option<String>,
    pub fileUrl: String,
    pub language: String,
    pub prefix: String,
    pub suffix: String,
    pub sourceCodePrefix: String,
    pub sourceCodeSuffix: String,
    pub fileDiffs: Vec<FileDiff>,
    //特殊数据,包含agent启动时间等
    pub recordInfo: Option<HashMap<String, String>>,
    //相似文件片段
    pub similarSnippets: Option<Vec<ContextAwareInfo>>,
    //相关文件片段
    pub relatedSnippets: Option<Vec<ContextAwareInfo>>,
}

// 定义发送给 codeEdit 服务的请求结构
#[derive(Serialize, Debug)]
pub struct NextTabRequest {
    pub userToken: String,
    pub pluginVersion: String,
    pub productType: String,
    pub sessionId: String,
    pub repo: Option<String>,
    pub fileUrl: String,
    pub language: String,
    pub prefix: String,
    pub suffix: String,
    pub originalCode: String,
    pub sourceCodePrefix: String,
    pub sourceCodeSuffix: String,
    pub fileDiffs: Vec<FileDiff>,
    //特殊数据,包含agent启动时间等
    pub recordInfo: Option<HashMap<String, String>>,
    //相似文件片段
    pub similarSnippets: Option<Vec<ContextAwareInfo>>,
    //相关文件片段
    pub relatedSnippets: Option<Vec<ContextAwareInfo>>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct FileDiff {
    pub fileUrl: String,
    pub diffs: Vec<String>,
}

#[derive(Deserialize, Debug)]
pub struct CodeEditResponseData {
    pub generatedTextArr: Vec<String>,
    pub completionType: Option<String>,
    pub generatedTextProbArr: Vec<f64>,
    pub filterNum: i32,
    pub sessionId: Option<String>,
}

/// 初始化文本文档请求结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiffHistoricalInitModelRequest {
    pub text: String,
    pub version: usize,
    pub uri: String,
    pub eol: String,
}

/// 文本范围结构体 - 用于差异历史记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiffHistoricalRange {
    pub start_line_number: u32,
    pub start_column: u32,
    pub end_line_number: u32,
    pub end_column: u32,
}

/// 模型内容变更结构体 - 用于差异历史记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiffHistoricalContentChange {
    pub range: DiffHistoricalRange,
    pub range_offset: usize,
    pub range_length: usize,
    pub text: String,
}

/// 差异历史记录请求结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiffHistoricalRecordRequest {
    pub uri: String,
    pub content_changes: Vec<DiffHistoricalContentChange>,
    pub version: i32,
    pub eol: String,
    pub text: String,
}

/// 表示文件的差异历史
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CppFileDiffHistory {
    pub file_name: String,
    pub diff_history: Vec<String>,
    pub diff_history_timestamps: Option<Vec<u64>>,
}

/// 差异历史记录响应结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiffHistoricalRecordResponse {
    pub success: bool,
    pub diff_trajectories: Vec<CppFileDiffHistory>,
    pub error_message: Option<String>,
}

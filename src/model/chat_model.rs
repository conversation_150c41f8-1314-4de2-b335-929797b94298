use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::HashMap;
use agent_db::domain::ap_data::AtomicSearchTypeEnum;

///对话请求
#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, Default)]
pub struct ChatRelatedRequestBean {
    //项目目录
    pub projectUrl: Option<String>,
    //用户问题
    pub query: String,
    //当前打开文件路径
    pub currentFileUrl: Option<String>,
    //选中的代码
    pub referenceList: Option<Vec<ChatCodeReferenceModel>>,
    //最近代码
    pub recentFilesInfo: Option<ChatCodeRecentModel>,

    //当前分支
    pub branch: Option<String>,
    //git地址
    pub projectGitUrl: Option<String>,
    //一级指令
    pub level1Instruction: Option<i64>,
    //二级指令
    pub level2Instruction: Option<ChatPluginModel>,
    //用户token
    pub userToken: Option<String>,
    //产品类型
    pub productType: Option<String>,
    //问题唯一id
    pub questionUid: Option<String>,
    //会话id
    pub sessionId: Option<String>,
 //意图(默认走APAR)
    pub chatIntent:Option<IntentEnum>,
    //tcp连接id
    pub tcpConnID:Option<String>,
    // query
    pub changedQuery: Option<String>,
    //explanation
    pub explanation: Option<String>,
    //选择的原子搜索能力集合
    pub searchTypeList:Option<Vec<AtomicSearchTypeEnum>>
}

/// Symbol搜索请求参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SymbolAndStreamSearchParams {
    #[serde(rename = "className")]
    pub class_name: Option<String>,
    #[serde(rename = "methodName")]
    pub method_name: Option<String>,
    #[serde(rename = "topN")]
    pub topN: Option<usize>,
    #[serde(rename = "projectUrl")]
    pub project_url: Option<String>,
}

/// TCP客户端返回的Symbol搜索响应结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SymbolSearchResponse {
    pub data: Vec<SymbolData>,
    pub status: String,
}

/// Symbol数据结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SymbolData {
    #[serde(rename = "className")]
    pub class_name: String,
    #[serde(rename = "isInterface")]
    pub is_interface: bool,
    #[serde(rename = "filePath")]
    pub file_path: Option<String>,
    pub body: Option<String>,
    pub methods: Vec<MethodData>,
    #[serde(rename = "useFields")]
    pub use_fields: Vec<FieldData>,
    #[serde(rename = "useVariable")]
    pub use_variable: Vec<VariableData>,
}

/// 方法数据结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct MethodData {
    pub id: u64,
    #[serde(rename = "className")]
    pub class_name: String,
    #[serde(rename = "methodName")]
    pub method_name: Option<String>,
    #[serde(rename = "isConstructor")]
    pub is_constructor: bool,
    pub body: String,
    #[serde(rename = "isLombokBuild")]
    pub is_lombok_build: bool,
    #[serde(rename = "refFieldData")]
    pub ref_field_data: Option<RefFieldData>,
    pub identification: Option<String>,
    #[serde(rename = "paramData")]
    pub param_data: Option<String>,
    #[serde(default)]
    pub startLine: i32,
    #[serde(default)]
    pub endLine: i32,
}

/// 引用字段数据结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RefFieldData {
    pub id: u64,
    #[serde(rename = "className")]
    pub class_name: Option<String>,
    #[serde(rename = "type")]
    pub field_type: String,
    #[serde(rename = "hasInitializer")]
    pub has_initializer: bool,
    pub modifier: String,
    pub text: String,
}

/// 字段数据结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct FieldData {
    pub id: u64,
    #[serde(rename = "className")]
    pub class_name: Option<String>,
    #[serde(rename = "type")]
    pub field_type: String,
    #[serde(rename = "hasInitializer")]
    pub has_initializer: bool,
    pub modifier: String,
    pub text: String,
}

/// 变量数据结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct VariableData {
    pub id: u64,
    #[serde(rename = "className")]
    pub class_name: Option<String>,
    #[serde(rename = "type")]
    pub variable_type: String,
    #[serde(rename = "hasInitializer")]
    pub has_initializer: bool,
    pub modifier: String,
    pub text: String,
}

/// 最终返回给用户的Symbol搜索结果结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SymbolAndStreamSearchResult {
    #[serde(rename = "relativePath")]
    pub relative_path: String,
    #[serde(rename = "className")]
    pub class_name: String,
    #[serde(rename = "methodName")]
    pub method_name:Option<String>,
    pub snippet: String,
    pub startLine: i32,
    pub endLine: i32,
}

/// TCP客户端返回的Usages搜索响应结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct UsagesSearchResponse {
    pub data: Vec<UsageData>,
    pub status: String,
}

/// Usage数据结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct UsageData {
    #[serde(rename = "className")]
    pub class_name: Option<String>,
    #[serde(rename = "qualifiedNameClassName")]
    pub qualified_name_class_name: Option<String>,
    #[serde(rename = "filePath")]
    pub file_path: String,
    #[serde(rename = "methodName")]
    pub method_name: String,
    pub text: String,
    #[serde(rename = "methodReferenceList")]
    pub method_reference_list: Vec<MethodReferenceData>,
}

/// 方法引用数据结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct MethodReferenceData {
    #[serde(rename = "className")]
    pub class_name: Option<String>,
    #[serde(rename = "qualifiedNameClassName")]
    pub qualified_name_class_name: Option<String>,
    #[serde(rename = "filePath")]
    pub file_path: String,
    #[serde(rename = "isSuperResolve")]
    pub is_super_resolve: bool,
    #[serde(rename = "superClassName")]
    pub super_class_name: Option<String>,
    #[serde(rename = "superQualifiedNameClassName")]
    pub super_qualified_name_class_name: Option<String>,
    #[serde(rename = "referencedOfMethodList")]
    pub referenced_of_method_list: Vec<ReferencedMethodData>,
}

/// 被引用方法数据结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ReferencedMethodData {
    #[serde(rename = "methodName")]
    pub method_name: String,
    pub text: String,
    pub startLine: i32,
    pub endLine: i32,
}


#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct AtomicSearchRequestBean {
    //对话请求
    pub chatRequestBean:Option<ChatRelatedRequestBean>,
    //symbol搜索
    pub symbolRequestBean:Option<SymbolAndStreamSearchParams>,
    //需要用什么原子搜索能力
    pub searchTypeVec:Option<Vec<AtomicSearchTypeEnum>>,
    //项目路径
    pub projectUrl:String
}




///对话返回值
#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct ChatRelatedResponse {
    //一级指令
    pub level1Instruction: Option<i64>,
    //二级指令
    pub level2Instruction: Option<ChatPluginModel>,
    //assistant动态参数
    pub assitantParam: Option<HashMap<String, Value>>,
    //埋点信息
    pub extraData: Option<HashMap<String, Value>>,
    //选中代码
    pub localRepoSelectedList: Option<Vec<ChatRelatedCodeModel>>,
    //相关代码
    pub localRepoReferenceRagList: Option<Vec<ChatRelatedCodeModel>>,
    //对话状态
    pub chatStatus: Option<ChatFlowStatusEnum>,
    //必备索引的构建百分比(0-100),用于展示进度条
    pub necessaryIndexPercent: Option<u8>,
    //问题唯一id
    pub questionUid: Option<String>,
    //会话id
    pub sessionId: Option<String>,
}

///插件指令对象
#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct ChatPluginModel {
    //指令id
    pub pluginId: i64,
    //指令名称
    pub command: Option<String>,
    //指令提示
    pub pluginTips: Option<String>,
    //指令名称
    pub pluginName: Option<String>,
}

///对话
#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct ChatContentModel {
    //内容
    pub content: String,
    //角色
    pub role: String,
}
///对话过程中的引用代码
#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct ChatCodeReferenceModel {
    //文件名
    pub name: String,
    //路径
    pub url: String,
    //类型(FILE/CODE)
    pub r#type: String,
    //引用代码起始行号
    pub lineStart: usize,
    //引用代码结束行号
    pub lineEnd: usize,
    //内容
    pub content: Option<String>,
}
///最近打开代码
#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct ChatCodeRecentModel {
    //光标上文
    pub codeBeforeCursor: Option<String>,
    //光标下文
    pub codeAfterCursor: Option<String>,
    //选中代码内容
    pub selectContent: Option<String>,
    //当前代开文件路径
    pub currentOpenFile: Option<String>,
    //最近文件路径
    pub recentFiles: Option<Vec<String>>,
}



///对话任务状态
#[derive(Serialize, Deserialize, Hash, Eq, PartialEq, Debug, Clone)]
pub enum ChatFlowStatusEnum {
    //构建索引
    BUILD_INDEX,
    //理解问题
    UNDERSTAND_QUERY,
    //rag检索
    QUERY_INDEX,
    //回答
    ANSWER,
    //中断
    CHANGE,
    //初始化
    INIT,
    //任务状态流转失败(走到了预期外的任务状态)
    ERROR,
}



//对话意图
#[derive(Serialize, Deserialize, Hash, Eq, PartialEq, Debug, Clone)]
pub enum IntentEnum {
    //ai partner
    APAR,
    //普通对话
    CHAT,
    //测试
    TEST,
    //代码解释
    EXPLAIN_CODE
}


///插件指令对象
#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct IndexOperateRequestBean {
    //仓库地址
    pub projectUrl: Option<String>,
    //分支
    pub branch: Option<String>,
    //是否打印明细数据
    pub printDetail:Option<bool>,
    //是否进行diff分析
    pub diffAnalysis:Option<bool>
}
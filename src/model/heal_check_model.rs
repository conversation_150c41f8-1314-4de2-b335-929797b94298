use std::collections::HashMap;

use serde::{Deserialize, Serialize};

/// 当前agent的基础信息,健康检查时候会返回
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentBaseInfo {
    //agent使用端口
    pub port: u16,
    //agent进程id
    pub pid: u32,
    //建立索引的项目列表, key:项目名, value: 文件长度
    pub index_project_map: HashMap<String, usize>,
    //建立的相似缓存文件列表.key:文件名, value:代码块长度
    pub cache_similarity_size_map: HashMap<String, usize>,

}
use std::collections::HashSet;

use serde::{Deserialize, Serialize};

use agent_db::domain::code_kv_index_domain::MethodDeclaration;

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct TypescriptImportStatement {
    pub source: String,
    pub name_vec: Vec<(String, String)>,
}

#[derive(Default, Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct TypescriptFunctionDeclaration {
    // 参数类型
    pub param_type_vec: Option<Vec<String>>,
    // 返回值类型
    pub return_type: Option<String>,
    // 局部变量类型
    pub local_var_type_vec: Option<Vec<String>>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct TypescriptPromptDeclaration {
    // 导入申明
    pub import_vec: Option<Vec<TypescriptImportStatement>>,
    // 当前光标所在函数申明，如果存在函数嵌套，则为光标所在的函数
    pub function_declaration: Option<TypescriptFunctionDeclaration>,
    // 嵌套的所有函数的参数类型列表
    pub function_param_type_vec: Option<Vec<Vec<String>>>,
}

///prompt上文解析出来的相关信息，会解析import列表，类变量，正在编写的方法等
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct PromptDeclaration {
    //import列表
    pub import_set: Option<HashSet<String>>,
    //字段列表
    pub field_set: Option<HashSet<String>>,
    //继承的class
    pub extends_class: Option<String>,
    //正在编写的函数
    pub method_declaration: Option<MethodDeclaration>,
    //继承的接口列表
    pub implements_class_name_set: Option<HashSet<String>>,
    //package名字
    pub package_name:Option<String>,
    // typescript extend
    pub extend_typescript: Option<TypescriptPromptDeclaration>
}

impl PromptDeclaration {
    pub fn new() -> Self {
        PromptDeclaration {
            import_set: None,
            field_set: None,
            extends_class: None,
            method_declaration: None,
            implements_class_name_set: None,
            package_name: None,
            extend_typescript: None,
        }
    }
}
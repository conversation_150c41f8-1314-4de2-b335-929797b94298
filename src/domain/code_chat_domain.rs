use crate::dal::index_client::Indexable;
use serde::{Deserialize, Serialize};
use tantivy::schema::{<PERSON><PERSON><PERSON>, SchemaBuilder, INDEXED, STORED, STRING, TEXT};
use tantivy::{doc, TantivyDocument};



pub const ATOMIC_WEB_FETCH:&str = "WEB_FETCH";

///检索结果定义
#[derive(Debug, Serialize, Deserialize, Clone, Default, Hash, PartialEq, Eq)]
pub struct ChatRelatedCodeModel {
    //相对路径
    pub relativePath: String,
    //代码内容
    pub snippet: String,
    //开始行
    pub startLine: usize,
    //结束行
    pub endLine: usize,
    //搜索来源
    pub source:Option<String>,
    //搜索结果title（doc结果）
    pub title:Option<String>,
}


///Method
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct CodefuseMethod {
    pub id: String,

    pub file_url: String,

    pub content: String,

    //开始行号
    pub start_line: u64,

    //结束行号
    pub end_line: u64,

    pub summary: String,

    //注释
    pub annotate: String,

    pub project_url: String,

    pub branch: String,

    //branch切换时的中间态
    pub hash: String,

    //特征， *Controller/*FaceImpl/*Fade 对应的标识
    pub feature: String,

    pub summary_keyword: Vec<String>,

    pub has_summary: u64,
    pub has_content_vector: u64,
    pub has_summary_vector: u64
}

impl Default for CodefuseMethod {
    fn default() -> Self {
        CodefuseMethod {
            id: String::new(),
            file_url: String::new(),
            content: String::new(),
            start_line: 0,
            end_line: 0,
            summary: String::new(),
            annotate: String::new(),
            project_url: String::new(),
            branch: String::new(),
            hash: String::new(),
            feature: String::new(),
            summary_keyword: Vec::new(),
            has_summary: 0,
            has_content_vector: 0,
            has_summary_vector: 0,
        }
    }
}


///File
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct CodefuseFile {
    pub id: String,

    pub content: String,

    //模型的总结
    pub summary: String,

    //注释
    pub annotate: String,

    pub project_url: String,

    pub branch: String,

    //content的hash值，
    pub hash: String,

    //特征， *Controller/*FaceImpl/*Fade 对应的标识
    pub feature: String,

    pub summary_keyword: Vec<String>,

    pub has_summary: u64,
    pub has_summary_vector: u64
}

impl Default for CodefuseFile {
    fn default() -> Self {
        CodefuseFile {
            id: String::new(),
            content: String::new(),
            summary: String::new(),
            annotate: String::new(),
            project_url: String::new(),
            branch: String::new(),
            hash: String::new(),
            feature: String::new(),
            summary_keyword: Vec::new(),
            has_summary: 0,
            has_summary_vector: 0
        }
    }
}


///Chunk
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct CodefuseChunk {
    pub id: String,

    pub file_url: String,

    pub index: u64,

    pub content: String,

    //开始行号
    pub start_line: u64,

    //结束行号
    pub end_line: u64,

    pub summary: String,

    pub project_url: String,

    pub branch: String,

    //纪录content的hash值
    pub hash: String,

    //特征， *Controller/*FaceImpl/*Fade 对应的标识
    pub feature: String,

    pub summary_keyword: Vec<String>,

    pub has_summary: u64,
    //是否有content的vector
    pub has_content_vector: u64,
    //是否有summary的vector
    pub has_summary_vector: u64,
}

impl Default for CodefuseChunk {
    fn default() -> Self {
        CodefuseChunk {
            id: String::new(),
            file_url: String::new(),
            index: 0,
            content: String::new(),
            start_line: 0,
            end_line: 0,
            summary: String::new(),
            project_url: String::new(),
            branch: String::new(),
            hash: String::new(),
            feature: String::new(),
            summary_keyword: Vec::new(),
            has_summary: 0,                  // 0表示没有summary， 1表示有summary数据
            has_content_vector: 0,           // 0表示没有content_summary， 1表示有content_summary数据
            has_summary_vector: 0,           // 0表示没有summary_vector， 1表示有summary_vector数据
        }
    }
}

impl Indexable for CodefuseChunk {
    fn to_document(&self, schema: &Schema) -> TantivyDocument {
        let mut doc = doc!(
            schema.get_field("id").unwrap() => &*self.id,
            schema.get_field("file_url").unwrap() => &*self.file_url,
            schema.get_field("index").unwrap() => self.index,
            schema.get_field("content").unwrap() => &*self.content,
            schema.get_field("start_line").unwrap() => self.start_line,
            schema.get_field("end_line").unwrap() => self.end_line,
            schema.get_field("summary").unwrap() => &*self.summary,
            schema.get_field("project_url").unwrap() => &*self.project_url,
            schema.get_field("branch").unwrap() => &*self.branch,
            schema.get_field("hash").unwrap() => &*self.hash,
            schema.get_field("feature").unwrap() => &*self.feature,
            schema.get_field("has_summary").unwrap() => self.has_summary,
            schema.get_field("has_content_vector").unwrap() => self.has_content_vector,
            schema.get_field("has_summary_vector").unwrap() => self.has_summary_vector,
        );
        match schema.get_field("summary_keyword") {
            Ok(field_summary_keyword) => {
                for key_word in &self.summary_keyword {
                    doc.add_text(field_summary_keyword, key_word);
                }
            }
            Err(_) => {}
        }
        doc
    }

    fn to_schema() -> Schema {
        build_chunk_schema()
    }
}

pub fn build_chunk_schema() -> Schema {
    let mut builder = SchemaBuilder::new();
    builder.add_text_field("id", STRING | STORED);
    builder.add_text_field("file_url", STRING | STORED);
    builder.add_u64_field("index", STORED);
    builder.add_text_field("content", TEXT | STORED);
    builder.add_u64_field("start_line", STORED);
    builder.add_u64_field("end_line", STORED);
    builder.add_text_field("summary", TEXT | STORED);
    builder.add_text_field("project_url", STRING | STORED);
    builder.add_text_field("branch", STRING | STORED);
    builder.add_text_field("hash", STRING | STORED);
    builder.add_text_field("feature", STRING | STORED);
    builder.add_text_field("summary_keyword", TEXT | STORED);
    builder.add_u64_field("has_summary",   INDEXED | STORED);
    builder.add_u64_field("has_content_vector",   INDEXED | STORED);
    builder.add_u64_field("has_summary_vector",   INDEXED | STORED);
    let schema = builder.build();
    schema
}


pub fn build_file_schema() -> Schema {
    let mut builder = SchemaBuilder::new();
    builder.add_text_field("id", STRING | STORED);
    builder.add_text_field("content", TEXT | STORED);
    builder.add_text_field("summary", TEXT | STORED);
    builder.add_text_field("annotate", TEXT | STORED);
    builder.add_text_field("project_url", STRING | STORED);
    builder.add_text_field("branch", STRING | STORED);
    builder.add_text_field("hash", STRING | STORED);
    builder.add_text_field("feature", STRING | STORED);
    builder.add_text_field("summary_keyword", TEXT | STORED);
    builder.add_u64_field("has_summary",   INDEXED | STORED);
    builder.add_u64_field("has_summary_vector",   INDEXED | STORED);
    let schema = builder.build();
    schema
}

impl Indexable for CodefuseFile {
    fn to_document(&self, schema: &Schema) -> TantivyDocument {
        let mut doc = doc!(
            schema.get_field("id").unwrap() => &*self.id,
            schema.get_field("content").unwrap() => &*self.content,
            schema.get_field("summary").unwrap() => &*self.summary,
            schema.get_field("annotate").unwrap() => &*self.annotate,
            schema.get_field("project_url").unwrap() => &*self.project_url,
            schema.get_field("branch").unwrap() => &*self.branch,
            schema.get_field("hash").unwrap() => &*self.hash,
            schema.get_field("feature").unwrap() => &*self.feature,
            schema.get_field("has_summary").unwrap() => self.has_summary,
            schema.get_field("has_summary_vector").unwrap() => self.has_summary,
        );
        match schema.get_field("summary_keyword") {
            Ok(field_summary_keyword) => {
                for key_word in &self.summary_keyword {
                    doc.add_text(field_summary_keyword, key_word);
                }
            }
            Err(_) => {}
        }
        doc
    }

    fn to_schema() -> Schema {
        build_file_schema()
    }
}



pub fn build_method_schema() -> Schema {
    let mut builder = SchemaBuilder::new();
    builder.add_text_field("id", STRING | STORED);
    builder.add_text_field("file_url", STRING | STORED);
    builder.add_text_field("content", TEXT | STORED);
    builder.add_u64_field("start_line", STORED);
    builder.add_u64_field("end_line", STORED);
    builder.add_text_field("summary", TEXT | STORED);
    builder.add_text_field("annotate", TEXT | STORED);
    builder.add_text_field("project_url", STRING | STORED);
    builder.add_text_field("branch", STRING | STORED);
    builder.add_text_field("hash", STRING | STORED);
    builder.add_text_field("feature", STRING | STORED);
    builder.add_text_field("summary_keyword", TEXT | STORED);
    builder.add_u64_field("has_summary",   INDEXED | STORED);
    builder.add_u64_field("has_content_vector",   INDEXED | STORED);
    builder.add_u64_field("has_summary_vector",   INDEXED | STORED);
    let schema = builder.build();
    schema
}

impl Indexable for CodefuseMethod {
    fn to_document(&self, schema: &Schema) -> TantivyDocument {
        let mut doc = doc!(
            schema.get_field("id").unwrap() => &*self.id,
            schema.get_field("file_url").unwrap() => &*self.file_url,
            schema.get_field("content").unwrap() => &*self.content,
            schema.get_field("start_line").unwrap() => self.start_line,
            schema.get_field("end_line").unwrap() => self.end_line,
            schema.get_field("summary").unwrap() => &*self.summary,
            schema.get_field("annotate").unwrap() => &*self.annotate,
            schema.get_field("project_url").unwrap() => &*self.project_url,
            schema.get_field("branch").unwrap() => &*self.branch,
            schema.get_field("hash").unwrap() => &*self.hash,
            schema.get_field("feature").unwrap() => &*self.feature,
            schema.get_field("has_summary").unwrap() => self.has_summary,
            schema.get_field("has_content_vector").unwrap() => self.has_content_vector,
            schema.get_field("has_summary_vector").unwrap() => self.has_summary_vector,
        );
        match schema.get_field("summary_keyword") {
            Ok(field_summary_keyword) => {
                for key_word in &self.summary_keyword {
                    doc.add_text(field_summary_keyword, key_word);
                }
            }
            Err(_) => {}
        }
        doc
    }

    fn to_schema() -> Schema {
        build_method_schema()
    }
}

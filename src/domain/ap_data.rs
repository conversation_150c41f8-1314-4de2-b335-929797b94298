use crate::domain::code_chat_domain::ChatRelatedCodeModel;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fmt;

//问题拆分
#[derive(Serialize, Deserialize, Hash, Eq, PartialEq, Debug, Clone)]
pub struct QeurySplitRequestBean {
    //用户唯一标识
    pub userToken: Option<String>,
    //插件版本
    pub pluginVersion: Option<String>,
    //用户使用的产品
    pub productType: Option<String>,
    //ide版本
    pub ideVersion: Option<String>,
    //agent版本
    pub agentVersion: Option<String>,
    //原生query
    pub query: String,
    //模型
    pub model: String
}

//结果过滤
#[derive(Serialize, Deserialize, Hash,  Debug, Clone)]
pub struct QueryResultFilterRequestBean {
    //用户唯一标识
    pub userToken: Option<String>,
    //插件版本
    pub pluginVersion: Option<String>,
    //用户使用的产品
    pub productType: Option<String>,
    //ide版本
    pub ideVersion: Option<String>,
    //agent版本
    pub agentVersion: Option<String>,
    //subquery
    pub query: String,
    //检索到的结果
    pub codeSnippetList: Vec<DeepsearchRelateCheckItem>,
    //模型
    pub model: String
}

//是否产生新的query
#[derive(Serialize, Deserialize, Hash,  Debug, Clone)]
pub struct GenerateNewQueryRequestBean {
    //用户唯一标识
    pub userToken: Option<String>,
    //插件版本
    pub pluginVersion: Option<String>,
    //用户使用的产品
    pub productType: Option<String>,
    //ide版本
    pub ideVersion: Option<String>,
    //agent版本
    pub agentVersion: Option<String>,
    //原生query
    pub query: String,
    //问题列表
    pub subQueryList: Vec<String>,
    //有效的检索结果
    pub codeSnippetList: Vec<ChatRelatedCodeModel>,
    //模型
    pub model: String
}

///deepsearch相关性检查的时候， 传给大模型的数据格式
#[derive(Debug, Serialize, Deserialize, Hash, Clone, PartialEq)]
pub struct DeepsearchRelateCheckItem{
    pub id:     String,
    pub code:   String,
    pub path:   String,
}
///deepsearch相关性检查的时候， 大模型返回的数据，需要通过id再把对应的code提取出来
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct DeepsearchRelateCheckResItem{
    pub id: String,
    pub path: String,
    pub reasoning: String,
}

///wiki的几种类型, https://yuque.antfin.com/gvrke9/vx7t5z/rbw699bix9gw9fqz
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum WikiTypeEnum {
    //API链路wiki
    API_CHAIN_WIKI,
    //仓库wiki
    REPO_WIKI,
    //antcode仓库readme
    README
}
///请求wiki的bean数据
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryWikiRequestBean{
    pub query: String,
    pub r#type: WikiTypeEnum,
    pub repoInfo: HashMap<String, String>,
    //默认是false, 不做wiki的精简提取， true会用小模型对wiki信息做精简
    pub enableWinnow: bool
}

///原子搜索能力类型
#[derive(Serialize, Deserialize, Hash, Eq, PartialEq, Debug, Clone)]
pub enum AtomicSearchTypeEnum {
    //外网搜索
    Web_Search,
    //拉取网页内容
    WEB_FETCH,
    //symbol搜索
    Symbol_Search,
    //codebase搜索
    Codebase_Search,
    //上下游链路
    Upstream_Search
}
//增加to_string()转字符串实现
impl fmt::Display for AtomicSearchTypeEnum {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AtomicSearchTypeEnum::Web_Search => write!(f, "Web_Search"),
            AtomicSearchTypeEnum::WEB_FETCH => write!(f, "WEB_FETCH"),
            AtomicSearchTypeEnum::Symbol_Search => write!(f, "Symbol_Search"),
            AtomicSearchTypeEnum::Codebase_Search => write!(f, "Codebase_Search"),
            AtomicSearchTypeEnum::Upstream_Search => write!(f, "Upstream_Search"),
        }
    }
}
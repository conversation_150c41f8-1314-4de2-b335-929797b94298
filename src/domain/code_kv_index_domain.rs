use std::collections::{HashMap, HashSet};

use serde::{Deserialize, Serialize};

//记录所有项目的key对象
pub const PROJECT_DATA_KEY: &str = "TOTAL_PROJECT_CODE_DATA";

///记录缓存中所有第一层的key
/// 目前由于用户反馈资源问题，所以正常来讲只会保存一个项目的记录
pub struct TotalKeyRecord {
    ///所有项目project_url集合
    pub kv_project_vec: Vec<String>,
}


///仓库级索引记录。
/// key是project_url，value是ScanProjectRecord对象
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct ScanProjectRecord {
    //项目根目录
    pub project_url: String,
    //索引的文件数量
    pub index_file_num:usize,
    //记录时候的毫秒数
    pub record_millis: usize,
}

///文件级索引记录
/// key是 F_${file_url}
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct ScanFileRecord{
    //文件路径
    pub file_url:String,
    //文件后缀
    pub file_name_suffix:String,
    //文件切分数量
    pub total_snippet_num:usize,
    //代码结构
    pub code_info: Option<CodeInfo>,
}
///ScanProjectRecord对象的默认值
impl Default for ScanFileRecord {
    fn default() -> Self {
        ScanFileRecord {
            file_url: String::new(),
            file_name_suffix: String::new(),
            total_snippet_num: 0,
            code_info: None,
        }
    }
}

///方法信息，如果补全位置在函数体内，会解析函数体参数，返回值，内部class声明等
/// 目前仅在补全时解析用
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct MethodDeclaration {
    //方法名
    pub method_name: String,
    //参数类型
    pub param_class_vec: Option<Vec<String>>,
    //返回值类型
    pub return_class: Option<String>,
    //内部class声明
    pub inner_class_declaration: Option<Vec<String>>,
    pub content: Option<String>,
}


///JavaMethodDeclaration对象的默认值
impl Default for MethodDeclaration {
    fn default() -> Self {
        MethodDeclaration {
            method_name: String::new(),
            param_class_vec: None,
            return_class: None,
            inner_class_declaration: None,
            content: None,
        }
    }
}

///代码片段分词结果
/// key是 S_${file_url}
#[derive(Debug, Serialize, Deserialize)]
pub struct ScanSnippetRecord {
    //文件路径
    pub file_url:String,
    //文件后缀
    pub file_name_suffix:String,
    // token_arr: Vec<u32>,
    pub id_vec: Vec<u32>,
    //起始行号
    pub start_no: usize,
    //结束行号
    pub end_no: usize,
    //索引位置
    pub index: usize,
    //下个ScanSnippetRecord的key
    pub next_key: Option<String>,
}

#[derive(Default, Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct TypeScriptDeclaration {
    // 标识符名称
    pub name: String,
    // 代码结构
    pub code_struct: String,
}

#[derive(Default, Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct TypeScriptInfo {
    // import source file path
    pub import_source_set: Option<HashSet<String>>,
    // 类型申明
    pub type_vec: Option<Vec<TypeScriptDeclaration>>,
    // 函数申明
    pub function_vec: Option<Vec<TypeScriptDeclaration>>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct ClassInfo {
    /// 代码类型
    /// 每个语言可以自己定义，java 下可以为 class, interface, enum, annotation, unknown
    pub class_type: String,
    //类名
    pub class_name: String,
    //继承父类的全限定名
    pub extend_class_name: Option<String>,
    //实现接口的全限定名集合
    pub implements_class_name_set: Option<HashSet<String>>,
    //类变量的名字-类型映射
    pub field_name_class_map: Option<HashMap<String, String>>,
    //代码基础结构，不包含完整实现
    pub code_struct: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct CodeInfo {
    //代码基础结构
    pub code_struct: String,
    //全限定名
    pub full_qualified_name: Option<String>,
    //import信息. 注意: java代码,如果处在同一个文件夹..没有import
    pub import_set: Option<HashSet<String>>,
    //代码类型
    pub class_type: Option<String>,
    //类名
    pub class_name: Option<String>,
    //继承父类的全限定名
    pub extend_class_name: Option<String>,
    //实现接口的全限定名集合
    pub implements_class_name_set: Option<HashSet<String>>,
    //类变量的名字-类型映射
    pub fild_name_class_map: Option<HashMap<String, String>>,
    /// 如果一个代码块内有多个类，存在 class_list 中
    pub class_list: Option<Vec<ClassInfo>>,
    // typescript 扩展信息
    pub extend_typescript: Option<TypeScriptInfo>,
}


///ScanProjectRecord对象的默认值
impl Default for CodeInfo {
    fn default() -> Self {
        CodeInfo {
            code_struct: String::new(),
            full_qualified_name: None,
            import_set: None,
            class_type: None,
            class_name: None,
            extend_class_name: None,
            implements_class_name_set: None,
            fild_name_class_map: None,
            class_list: None,
            extend_typescript: None,
        }
    }
}
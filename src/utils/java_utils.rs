use std::collections::HashSet;

use once_cell::sync::Lazy;
use regex::Regex;

use crate::utils::file_utils::join_file_path;
use crate::utils::string_utils::{is_capitalized, remove_suffix, split_at, substring_after, substring_after_last, substring_before, substring_before_last};

pub static JAVA_IDENTIFIER_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"^[a-zA-Z_$][a-zA-Z\d_$]*$").unwrap()
});

pub static METHOD_NAME_LOG: Lazy<Vec<&str>> = Lazy::new(|| {
    vec!["debug", "error", "fatal", "info", "log", "severe", "warn", "warning"]
});

/// 匹配JavaClass
pub fn match_java_class(str: &str) -> bool {
    if str.is_empty() {
        return false;
    }
    JAVA_IDENTIFIER_REGEX.is_match(str) && is_capitalized(str)
}

/// 分割类名维度
pub fn split_dimension(class_name: &str) -> (&str, &str) {
    return split_at(class_name, ['[', '<'].as_ref());
}

/// 获取模板类信息, eg: HashMap<String,AppVO> -> [String,AppVO]
/// # Examples
/// ```
/// use agent_gentests_service::utils::java_utils::list_type_parameter_name;
/// assert_eq!(list_type_parameter_name("HashMap<String,AppVO>"), ["String","AppVO"]);
/// assert_eq!(list_type_parameter_name("HashMap<String,AppVO<Long>>"), ["String","AppVO<Long>"]);
/// assert_eq!(list_type_parameter_name("HashMap<String>"), ["String"]);
/// assert_eq!(list_type_parameter_name("HashMap<AppVO<Long>>"), ["AppVO<Long>"]);
/// assert_eq!(list_type_parameter_name("HashMap"), Vec::<&str>::new());
/// ```
pub fn list_type_parameter_name(class_name: &str) -> Vec<&str> {
    let mut ret = Vec::new();
    if let Some(left) = class_name.find('<') {
        if let Some(right) = class_name.rfind('>') {
            let type_params = &class_name[left + 1..right];
            let children = list_type_parameter_name(type_params);
            if children.is_empty() {
                ret = type_params.split(',').map(|x| x.trim()).collect();
            } else {
                return vec![type_params];
            }
        }
    }
    return ret;
}

/// 获取类的简名
/// # Examples
/// ```
/// use agent_gentests_service::utils::java_utils::get_simple_class_name;
/// assert_eq!(get_simple_class_name("ClassName"), "ClassName");
/// assert_eq!(get_simple_class_name("com.alipay.ClassName"), "ClassName");
/// assert_eq!(get_simple_class_name("com.alipay.*"), "");
/// ```
pub fn get_simple_class_name(qualified_class_name: &str) -> &str {
    if qualified_class_name.contains('.') {
        let class_name = substring_after_last(qualified_class_name, ".");
        if class_name == "*" {
            return "";
        }
        return class_name;
    }
    return qualified_class_name;
}

/// 获取类的包名
/// # Examples
/// ```
/// use agent_gentests_service::utils::java_utils::get_package_name;
/// assert_eq!(get_package_name("ClassName"), "");
/// assert_eq!(get_package_name("com.alipay.ClassName"), "com.alipay");
/// assert_eq!(get_package_name("com.alipay.*"), "com.alipay");
/// ```
pub fn get_package_name(qualified_class_name: &str) -> &str {
    if qualified_class_name.contains('.') {
        return substring_before_last(qualified_class_name, ".");
    }
    return "";
}

/// 获取bundle绝对路径, 需要POSIX格式
/// # Exampes
/// ```
/// use agent_gentests_service::utils::java_utils::get_bundle_path;
/// assert_eq!(get_bundle_path("/home/<USER>/src/main/java/com/alipay/ClassName", "/home"), "/home/<USER>");
/// assert_eq!(get_bundle_path("/home/<USER>/src/main/com/alipay/ClassName", "/home"), "/home/<USER>");
/// assert_eq!(get_bundle_path("/home/<USER>/src/com/alipay/ClassName", "/home"), "/home/<USER>");
/// assert_eq!(get_bundle_path("src/com/alipay/ClassName", "/home"), "/home");
/// assert_eq!(get_bundle_path("app/biz/src/com/alipay/ClassName", "/home"), "/home/<USER>/biz");
/// assert_eq!(get_bundle_path("app/biz/src/com/alipay/ClassName", "/home/"), "/home/<USER>/biz");
/// assert_eq!(get_bundle_path("/app/biz/src/com/alipay/ClassName", "/home"), "/app/biz");
/// assert_eq!(get_bundle_path("/home/<USER>/com/alipay/ClassName", "/home"), "/home");
/// ```
pub fn get_bundle_path(file_path: &str, project_url: &str) -> String {
    let mut full_file_path = file_path.to_string();
    if !full_file_path.starts_with(project_url) {
        full_file_path = join_file_path(project_url, file_path);
    }
    if full_file_path.contains("/src/main/") {
        return substring_before(&full_file_path, "/src/main/").to_string();
    } else if full_file_path.contains("/src/") {
        return substring_before(&full_file_path, "/src/").to_string();
    }
    return project_url.to_string();
}

/// 从import_set中查找全类名
/// # Examples
/// ```
/// use std::collections::HashSet;
/// use agent_gentests_service::utils::java_utils::find_qualified_class_name;
/// let mut import_set = HashSet::new();
/// // import_set.insert("com.alipay.ClassName".to_string());
/// import_set.insert("com.github.*".to_string());
/// assert_eq!(find_qualified_class_name("ClassName" ,&import_set), "com.alipay.ClassName");
/// assert_eq!(find_qualified_class_name("NoClass",&import_set), "");
/// assert_eq!(find_qualified_class_name("ClassName",&HashSet::new()), "");
/// ```
pub fn find_qualified_class_name<'a>(simple_class_name: &'a str, import_set: &'a HashSet<String>) -> &'a str {
    for import_item in import_set.iter() {
        if simple_class_name == get_simple_class_name(import_item) {
            return import_item;
        }
    }
    return "";
}

/// Java类路径转包名
/// # Examples
/// ```
/// use std::collections::HashSet;
/// use agent_gentests_service::utils::java_utils::to_package;
/// assert_eq!(to_package("/home/<USER>/app/test/src/main/java/com/alipay/ClassName.java"), "com.alipay.ClassName");
/// assert_eq!(to_package("/home/<USER>/app/test/src/test/java/com/alipay/ClassName.java"), "com.alipay.ClassName");
/// assert_eq!(to_package("/com/alipay/ClassName.java"), "com.alipay.ClassName");
/// assert_eq!(to_package("com/alipay/ClassName.java"), "com.alipay.ClassName");
/// ```
pub fn to_package(java_file: &str) -> String {
    let mut package_path = java_file;
    if package_path.contains("src/main/java/") {
        package_path = substring_after(java_file, "src/main/java/");
    }
    if package_path.contains("src/test/java/") {
        package_path = substring_after(java_file, "src/test/java/");
    }
    if package_path.starts_with("/") {
        package_path = substring_after(package_path, "/");
    }
    if package_path.ends_with(".java") {
        package_path = remove_suffix(package_path, ".java");
    }
    return package_path.replace("/", ".");
}

/// Java类名转包路径
/// # Examples
/// ```
/// use agent_gentests_service::utils::java_utils::to_posix;
/// assert_eq!(to_posix("com.alipay.ClassName"), "com/alipay/ClassName");
/// ```
pub fn to_posix(package: &str) -> String {
    return package.replace(".", "/");
}

/// 判断是否包含注解
/// # Examples
/// ```
/// use agent_gentests_service::utils::java_utils::with_annotation;
/// assert_eq!(with_annotation("@Test()\n@Before", "@Test", ""), true);
/// assert_eq!(with_annotation("@Test", "@Before", ""), false);
/// assert_eq!(with_annotation("@Test(\"data\")\n@Before", "@Test", "data"), true);
/// assert_eq!(with_annotation("@Test(\"name\")\n@Before", "@Test", "data"), false);
/// ```
pub fn with_annotation(modifier: &str, annotation: &str, with_text: &str) -> bool {
    return if with_text.is_empty() {
        modifier.contains(annotation)
    } else {
        modifier.contains(annotation) && modifier.contains(with_text)
    };
}

/// 判断是否包含HttpMethod注解 @RequestMapping/@GetMapping/@PostMapping/@PutMapping/@DeleteMapping
pub fn has_http_method_annotation(modifier: &str) -> bool {
    modifier.contains("@RequestMapping")
        || modifier.contains("@GetMapping")
        || modifier.contains("@PostMapping")
        || modifier.contains("@PutMapping")
        || modifier.contains("@DeleteMapping")
        || modifier.contains("@PatchMapping")
}

/// 判断是否包含HttpController注解 @Controller/@RestController
pub fn has_http_service_annotation(modifier: &str) -> bool {
    modifier.contains("@Controller") || modifier.contains("@RestController")
}

/// 判断是否包含RpcService注解 @RpcProvider/@SofaService
pub fn has_rpc_service_annotation(modifier: &str, rpc_binding: bool) -> bool {
    if modifier.contains("@RpcProvider") || modifier.contains("@HSFProvider") {
        return true;
    }
    if rpc_binding {
        return modifier.contains("@SofaService") && has_rpc_binding(modifier);
    } else {
        return modifier.contains("@SofaService");
    }
}

/// 判断是否包含RpcReference注解 @RpcConsumer/@SofaReference
pub fn has_rpc_reference_annotation(modifier: &str, rpc_binding: bool) -> bool {
    if modifier.contains("@RpcConsumer") || modifier.contains("@HSFConsumer") {
        return true;
    }
    if rpc_binding {
        return modifier.contains("@SofaReference") && has_rpc_binding(modifier);
    } else {
        return modifier.contains("@SofaReference");
    }
}

/// 判断是否RpcBinding
fn has_rpc_binding(modifier: &str) -> bool {
    modifier.contains("\"tr\"")
        || modifier.contains("\"bolt\"")
        || modifier.contains("\"rest\"")
        || modifier.contains("\"dubbo\"")
        || modifier.contains("\"h2c\"")
        || modifier.contains("\"http\"")
        || modifier.contains("\"tri\"")
        || modifier.contains("\"ws\"")
}

/// 是否DAO包
/// # Arguments
/// * `qualified_class_name` - 目标类
pub fn is_dao_package(qualified_class_name: &str) -> bool {
    return qualified_class_name.contains(".dal.")
        || qualified_class_name.contains(".dao.")
        || qualified_class_name.contains(".mapper.");
}

/// 是否DO包
/// # Arguments
/// * `qualified_class_name` - 目标类
pub fn is_do_package(qualified_class_name: &str) -> bool {
    if is_dao_package(qualified_class_name) {
        return true;
    }
    return qualified_class_name.contains(".object.")
        || qualified_class_name.contains(".model.")
        || qualified_class_name.contains(".entity.");
}

/// 是否DAO类
/// # Arguments
/// * `qualified_class_name` - 目标类
pub fn is_dao_class(modifiers: &str, qualified_class_name: &str) -> bool {
    if modifiers.contains("@Mapper") {
        return true;
    }
    if !is_dao_package(qualified_class_name) {
        return false;
    }
    return qualified_class_name.ends_with("DAO")
        || qualified_class_name.ends_with("Dao")
        || qualified_class_name.ends_with("Mapper");
}

/// 是否DO类
/// # Arguments
/// * `qualified_class_name` - 目标类
pub fn is_do_class(qualified_class_name: &str) -> bool {
    if !is_do_package(qualified_class_name) {
        return false;
    }
    return qualified_class_name.ends_with("DO")
        || qualified_class_name.ends_with("Do")
        || qualified_class_name.ends_with("Entity");
}


/// 是否DAO的Param类(Param类通常会比较大, 影响分析性能)
/// # Arguments
/// * `qualified_class_name` - 目标类
pub fn is_dal_param(qualified_class_name: &str) -> bool {
    if !is_dao_package(qualified_class_name) {
        return false;
    }
    return qualified_class_name.ends_with("Param");
}

#[cfg(test)]
mod test {
    use crate::utils::string_utils::remove_suffix;

    #[test]
    fn test() {
        println!("{}", remove_suffix("/home/<USER>/Abc.java", ".java"));
    }
}

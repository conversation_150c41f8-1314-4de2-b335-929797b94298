// 比较from_data/to_data在vec中的位置, 如果任何一个不在vec中则返回false
// return vec.pos(from_data) <= vec.pos(to_data)
pub fn index_less_equal(vec: &Vec<&str>, from_data: &str, to_data: &str) -> bool {
    if let Some(to_idx) = vec.iter().position(|&n| n == to_data) {
        if let Some(from_idx) = vec.iter().position(|&n| n == from_data) {
            if from_idx <= to_idx {
                return true;
            }
        }
    }
    return false;
}
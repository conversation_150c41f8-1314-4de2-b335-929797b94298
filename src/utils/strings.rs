pub fn remove_suffix<'a>(s: &'a str, p: &str) -> &'a str {
    if s.ends_with(p) {
        &s[..s.len() - p.len()]
    } else {
        s
    }
}

pub fn remove_prefix<'a>(s: &'a str, p: &str) -> &'a str {
    if s.starts_with(p) {
        &s[p.len()..]
    } else {
        s
    }
}

pub fn remove_empty_lines(s: &str) -> String {
    s.lines().filter(|l| !l.is_empty()).collect::<Vec<&str>>().join("\n")
}

#[cfg(test)]
mod test {
    use super::*;

    #[test]
    fn test_remove_suffix() {
        assert_eq!(remove_suffix("hello", "world"), "hello");
        assert_eq!(remove_suffix("hello", ""), "hello");
        assert_eq!(remove_suffix("", "world"), "");
        assert_eq!(remove_suffix("foobar", "bar"), "foo");
        assert_eq!(remove_suffix("foobar", "foo"), "foobar");
    }

    #[test]
    fn test_remove_prefix() {
        // 测试输入字符串以给定前缀开头的情况
        assert_eq!(remove_prefix("hello world", "hello"), " world");

        // 测试输入字符串不以给定前缀开头的情况
        assert_eq!(remove_prefix("hello world", "hi"), "hello world");

        // 测试输入字符串和前缀完全相同的情况
        assert_eq!(remove_prefix("hello", "hello"), "");

        // 测试输入字符串为空的情况
        assert_eq!(remove_prefix("", "hello"), "");

        // 测试前缀为空的情况
        assert_eq!(remove_prefix("hello world", ""), "hello world");
    }

    #[test]
    fn test_remove_empty_lines() {
        let input = "hello\nworld";
        let expected_output = "hello\nworld";
        assert_eq!(remove_empty_lines(input), expected_output);
        let input = "";
        let expected_output = "";
        assert_eq!(remove_empty_lines(input), expected_output);
        let input = "hello\n\nworld";
        let expected_output = "hello\nworld";
        assert_eq!(remove_empty_lines(input), expected_output);
        let input = "hello\n\n\nworld";
        let expected_output = "hello\nworld";
        assert_eq!(remove_empty_lines(input), expected_output);
    }
}
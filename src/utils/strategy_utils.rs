use agent_db::tools::common_tools::LINE_ENDING;
use crate::function::codecomplete_cache_strategy::CodeFuseCodeCompleteCacheStrategy;
use crate::function::codecomplete_rag_executor_strategy::CodeFuseCodeCompleteRagExecutorStrategy;

use crate::ast::java::strategy::CodeFuseJavaAstStrategy;
use crate::ast::js::constant::JS_VALID_EXTENSIONS;
use crate::ast::js::strategy::CodeFuseJsAstStrategy;
use crate::dialogue::repo_index_operator::RepoStatusEnum::UPDATE;
use crate::function::chat_strategy::CodeFuseChatStrategy;
use crate::utils::path::{fill_content, DEFAULT_BRANCH, DEFAULT_PROJECT_URL};
use agent_android_service::android_strategy::code_complete_cache_strategy_android::AndroidCodeCompleteCacheStrategy;
use agent_android_service::android_strategy::code_complete_rag_strategy_android::AndroidCodeCompleteRagExecutorStrategy;
use agent_common_service::model::chat_model::ChatFlowStatusEnum::UNDERSTAND_QUERY;
use agent_common_service::model::chat_model::{ChatFlowStatusEnum, ChatRelatedRequestBean, ChatRelatedResponse};
use agent_common_service::model::code_complete_model::CodeCompletionRequestBean;
use agent_common_service::model::prompt_declaration::PromptDeclaration;
use agent_common_service::service::abstract_similarity::Similarity;
use agent_common_service::service::code_ast_analysis::AstStrategy;
use agent_common_service::service::code_complete_cache::CodeCompleteCacheStrategy;
use agent_common_service::service::code_complete_rag_executor::CodeCompleteRagExecutorStrategy;
use agent_common_service::service::project_chat::ProjectChatStrategy;
use agent_common_service::tools::related_module_score::CacheRelatedModule;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::domain::code_kv_index_domain::ScanFileRecord;
use agent_db::remote::rpc_model::{build_success_response, BaseResponse};
use log::{debug, info};
use std::collections::HashSet;
use std::path::Path;
use uuid::Uuid;
use crate::deepsearchV2::searchrouter_data::{DeepsearchChatResponse, StepResponseResult};
use crate::deepsearchV2::searchrouter_prompt::CODE_EXPLAIN_QUERY_PROMPT;
use crate::deepsearchV2::searchrouter_util::deepsearch_chat_related_info;

pub fn is_android(code_complete_request: &CodeCompletionRequestBean) -> bool {
    let android_rule_hit = code_complete_request
        .fileNameSuffix
        .as_ref()
        .map(|suffix| suffix == "java" || suffix == "kt")
        .unwrap_or(false)
        && code_complete_request.productType == "ANDROID_STUDIO";
    android_rule_hit
}

///通过请求参数，判断使用哪种rag执行服务
/// 如果是安卓平台，并且是java/kotlin，那么走终端rag逻辑
pub fn get_rag_strategy(
    code_complete_request: &CodeCompletionRequestBean,
) -> Box<dyn CodeCompleteRagExecutorStrategy> {
    let android_rule_hit = is_android(code_complete_request);
    if android_rule_hit {
        Box::new(AndroidCodeCompleteRagExecutorStrategy)
    } else {
        Box::new(CodeFuseCodeCompleteRagExecutorStrategy)
    }
}

///通过请求参数，判断使用哪种缓存执行服务
/// 如果是安卓平台，并且是java/kotlin，那么走终端rag逻辑
pub fn get_cache_strategy(
    code_complete_request: &CodeCompletionRequestBean,
) -> Box<dyn CodeCompleteCacheStrategy> {
    let android_rule_hit = is_android(code_complete_request);
    if android_rule_hit {
        Box::new(AndroidCodeCompleteCacheStrategy)
    } else {
        Box::new(CodeFuseCodeCompleteCacheStrategy)
    }
}


/************************ 以下函数用于async环境，如果在async方法中直接调用get_rag_strategy，会报错，故封装了一层 **************************/
///在execute_build_related_cache中无法直接使用get_rag_strategy(async导致)，故独立一个函数用于异步场景
pub fn get_current_prompt_declaration(
    code_complete_request: &CodeCompletionRequestBean,
) -> Option<PromptDeclaration> {
    let rag_strategy: Box<dyn CodeCompleteRagExecutorStrategy> =
        get_rag_strategy(code_complete_request);
    return rag_strategy.extract_prompt_declaration(&code_complete_request);
}

pub async fn get_chat_strategy_result(
    mut chat_related_request: ChatRelatedRequestBean,
) -> anyhow::Result<BaseResponse<ChatRelatedResponse>> {

    CodeFuseChatStrategy::query_chat_related_info(&chat_related_request).await
}

pub async fn get_deepsearch_chat_strategy_result(
    mut chat_related_request: ChatRelatedRequestBean,
) -> anyhow::Result<BaseResponse<DeepsearchChatResponse<serde_json::Value>>> {
    //step 1 填充必备参数
    let start_time = std::time::SystemTime::now();
    let reference_vec_opt = &chat_related_request.referenceList;
    if reference_vec_opt.is_some() && chat_related_request.projectUrl.is_some() {
        fill_content(&mut chat_related_request)
    }
    //step 2 如果sessionId是空，或者questionId是空，说明是新对话，生成uuid后直接返回，不要进入对话流程。否则前端页面会一直等着
    let session_id_opt = chat_related_request.sessionId.clone();
    let question_id_opt = chat_related_request.questionUid.clone();
    if session_id_opt.as_ref().map_or(true,|v|v.is_empty()){
        let  deepsearch_related_response = DeepsearchChatResponse {
            r#type: None,
            content: None,
            subquestions: None,
            nlcontent: None,
            chatStatus: Some(UNDERSTAND_QUERY),
            necessary_index_percent: None,
            questionUid: None,
            sessionId: Some(Uuid::new_v4().simple().to_string()),
            step: Some(StepResponseResult::default())
        };
        return Ok(build_success_response(deepsearch_related_response))
    }
    if question_id_opt.as_ref().map_or(true,|v|v.is_empty()){
        let  deepsearch_related_response = DeepsearchChatResponse {
            r#type: None,
            content: None,
            subquestions: None,
            nlcontent: None,
            chatStatus: Some(UNDERSTAND_QUERY),
            necessary_index_percent: None,
            questionUid: Some(Uuid::new_v4().simple().to_string()),
            sessionId: None,
            step: Some(StepResponseResult::default())
        };
        return Ok(build_success_response(deepsearch_related_response))
    }


    if chat_related_request.branch.is_none() {
        chat_related_request.branch = Some(DEFAULT_BRANCH.to_string());
    }
    if chat_related_request.projectUrl.is_none() {
        chat_related_request.projectUrl = Some(DEFAULT_PROJECT_URL.to_string());
    }
    //如果是代码解释，增加一段prompt
    if let Some(agent_common_service::model::chat_model::IntentEnum::EXPLAIN_CODE) = chat_related_request.chatIntent.as_ref() {
        chat_related_request.query = format!("{CODE_EXPLAIN_QUERY_PROMPT}{LINE_ENDING}{}", chat_related_request.query);
    }
    deepsearch_chat_related_info(&chat_related_request).await
}

///在execute_build_related_cache中无法直接使用build_related_module_score(async导致)，故独立一个函数用于异步场景
pub fn get_related_module(
    code_complete_request: &CodeCompletionRequestBean,
    current_context: &PromptDeclaration,
    scan_file_record: &ScanFileRecord,
) -> Option<Vec<CacheRelatedModule>> {
    let cache_strategy: Box<dyn CodeCompleteCacheStrategy> =
        get_cache_strategy(code_complete_request);
    let file_suffix = &code_complete_request.fileNameSuffix;
    if !cache_strategy.is_need_build_related(file_suffix.as_ref().unwrap(), current_context) {
        info!("don't need build related module");
        return None;
    };
    return cache_strategy.build_related_module_scores(
        code_complete_request,
        current_context,
        scan_file_record,
    );
}

pub fn get_current_similarity_score_data(
    code_complete_request: &CodeCompletionRequestBean,
) -> (HashSet<u32>, HashSet<u32>, HashSet<u32>) {
    let cache_strategy: Box<dyn CodeCompleteCacheStrategy> =
        get_cache_strategy(code_complete_request);
    cache_strategy.get_current_similarity_score_data(code_complete_request)
}

pub fn get_target_similarity_score_data(
    code_complete_request: &CodeCompletionRequestBean,
    scan_file_record: &ScanFileRecord,
    cur_score_data: &(HashSet<u32>, HashSet<u32>, HashSet<u32>),
) -> (
    Option<Similarity<Option<String>>>,
    Option<Similarity<Option<String>>>,
    Option<Similarity<Option<String>>>,
) {
    let cache_strategy: Box<dyn CodeCompleteCacheStrategy> =
        get_cache_strategy(code_complete_request);
    cache_strategy.get_target_similarity_score_data(scan_file_record, cur_score_data)
}

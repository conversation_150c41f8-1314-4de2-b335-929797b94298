use anyhow::{Result, Context};
use log::info;
use reqwest::Client;
use once_cell::sync::Lazy;
use serde::de::DeserializeOwned;
use std::sync::Arc;
use serde_json::Value;

static HTTP_CLIENT: Lazy<Arc<Client>> = Lazy::new(|| Arc::new(reqwest::Client::new()));


pub async  fn fetch_from_api<T>(endpoint: &str, payload: Option<&Value>) -> Result<T> where T: DeserializeOwned, {

  let mut request = HTTP_CLIENT.get(endpoint);

    if let Some(p) = payload {
        request = request.query(p);
    }

  // let response = HTTP_CLIENT.post(endpoint)
  //     .json(&payload)
  //     .send()
  //     .await
  //     .context("Failed to send request")?;


  let response = request
      .send()
      .await
      .context("Failed to send request")?;


  if !response.status().is_success() {
      return Err(anyhow::anyhow!("API request failed with status: {}", response.status()));
  }


  info!("fetch_from_api {:?}，payload {:?} result {:?}", endpoint, payload, response);

  let json: T = response.json()
      .await
      .context("Failed to parse JSON response")?;

  // let json = json!({
  //     "trace": "1aa",
  // });

  Ok(json)
}
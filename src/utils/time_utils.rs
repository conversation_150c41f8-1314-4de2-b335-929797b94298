use std::time::{Duration, SystemTime};

/// 计算间隔时间
pub fn duration(from: SystemTime) -> u128 {
    SystemTime::now().duration_since(from).unwrap().as_millis()
}

/// 计算超时时间
pub fn timeout(from: SystemTime, secs: u64) -> bool {
    if let Ok(duration) = SystemTime::now().duration_since(from) {
        if duration > Duration::from_secs(secs) {
            return true;
        }
    }
    return false;
}
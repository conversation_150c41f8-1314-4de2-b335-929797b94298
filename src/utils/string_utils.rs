/// 查找[首个]字符串后面的数据
/// # Examples
/// ```
/// use agent_gentests_service::utils::string_utils::substring_after;
/// assert_eq!(substring_after("abc", "a"), "bc");
/// assert_eq!(substring_after("abcba", "b"), "cba");
/// assert_eq!(substring_after("abc", "c"), "");
/// assert_eq!(substring_after("abc", "d"), "");
/// assert_eq!(substring_after("abc", ""), "abc");
/// assert_eq!(substring_after("", "a"), "");
/// ```
pub fn substring_after<'a>(str: &'a str, separator: &str) -> &'a str {
    if str.is_empty() {
        return "";
    }
    if separator.is_empty() {
        return str;
    }
    match str.find(separator) {
        Some(pos) => &str[pos + separator.len()..],
        None => "",
    }
}

/// 查找[最后]字符串后面的数据
/// # Examples
/// ```
/// use agent_gentests_service::utils::string_utils::substring_after_last;
/// assert_eq!(substring_after_last("abc", "a"), "bc");
/// assert_eq!(substring_after_last("abcba", "b"), "a");
/// assert_eq!(substring_after_last("abc", "c"), "");
/// assert_eq!(substring_after_last("a", "a"), "");
/// assert_eq!(substring_after_last("abc", "z"), "");
/// assert_eq!(substring_after_last("a", ""), "");
/// assert_eq!(substring_after_last("", "a"), "");
/// ```
pub fn substring_after_last<'a>(str: &'a str, separator: &str) -> &'a str {
    if str.is_empty() {
        return "";
    }
    if separator.is_empty() {
        return "";
    }
    match str.rfind(separator) {
        Some(pos) => &str[pos + separator.len()..],
        None => "",
    }
}

/// 查找[首个]字符串前面的数据
/// # Examples
/// ```
/// use agent_gentests_service::utils::string_utils::substring_before;
/// assert_eq!(substring_before("abc", "a"), "");
/// assert_eq!(substring_before("abcba", "b"), "a");
/// assert_eq!(substring_before("abc", "c"), "ab");
/// assert_eq!(substring_before("abc", "d"), "abc");
/// assert_eq!(substring_before("abc", ""), "");
/// ```
pub fn substring_before<'a>(str: &'a str, separator: &str) -> &'a str {
    if str.is_empty() {
        return "";
    }
    if separator.is_empty() {
        return "";
    }
    match str.find(separator) {
        Some(pos) => &str[..pos],
        None => str,
    }
}

/// 查找[最后]字符串前面的数据
/// # Examples
/// ```
/// use agent_gentests_service::utils::string_utils::substring_before_last;
/// assert_eq!(substring_before_last("", "abc"), "");
/// assert_eq!(substring_before_last("abcba", "b"), "abc");
/// assert_eq!(substring_before_last("abc", "c"), "ab");
/// assert_eq!(substring_before_last("a", "a"), "");
/// assert_eq!(substring_before_last("a", "z"), "a");
/// assert_eq!(substring_before_last("a", ""), "a");
/// ```
pub fn substring_before_last<'a>(str: &'a str, separator: &str) -> &'a str {
    if str.is_empty() {
        return "";
    }
    if separator.is_empty() {
        return str;
    }
    match str.rfind(separator) {
        Some(pos) => &str[..pos],
        None => str,
    }
}


/// 删除字符串后缀
/// # Examples
/// ```
/// use agent_gentests_service::utils::string_utils::remove_suffix;
/// assert_eq!(remove_suffix("abc", "a"), "abc");
/// assert_eq!(remove_suffix("abcba", "b"), "abcba");
/// assert_eq!(remove_suffix("abc", "c"), "ab");
/// assert_eq!(remove_suffix("abc", "d"), "abc");
/// assert_eq!(remove_suffix("abc", ""), "abc");
/// ```
pub fn remove_suffix<'a>(str: &'a str, suffix: &str) -> &'a str {
    if str.is_empty() || suffix.is_empty() {
        return str;
    }
    if str.ends_with(suffix) {
        let len = str.len() - suffix.len();
        return &str[..len];
    } else {
        return str;
    }
}

// truncate_string
pub fn truncate_string(input: &str, max_length: usize) -> String {
    if input.len() <= max_length {
        input.to_string()
    } else {
        let truncated: String = input.chars().take(max_length - 3).collect();
        truncated + "..."
    }
}

// 过滤所有空字符
pub fn trim_whitespaces(str: &str) -> String {
    let ret: String = str.split_whitespace().collect();
    return ret;
}

// 分割成元组
pub fn split_at<'a>(str: &'a str, splitter: &[char]) -> (&'a str, &'a str) {
    if let Some(idx) = str.find(splitter) {
        return str.split_at(idx);
    }
    return (str, "");
}

// 分割成元组
pub fn rsplit_at<'a>(str: &'a str, splitter: &[char]) -> (&'a str, &'a str) {
    if let Some(idx) = str.rfind(splitter) {
        return str.split_at(idx);
    }
    return (str, "");
}

/// 分割成Vec
/// # Examples
/// ```
/// use agent_gentests_service::utils::string_utils::multi_split;
/// assert_eq!(multi_split("abc", [' '].as_ref()), vec!["abc"]);
/// assert_eq!(multi_split("ab c",[' '].as_ref()), vec!["ab", "c"]);
/// assert_eq!(multi_split("a,b   c",[',', ' '].as_ref()), vec!["a", "b", "c"]);
/// assert_eq!(multi_split("a,b   c;d",[',', ';', ' '].as_ref()), vec!["a", "b", "c", "d"]);
/// assert_eq!(multi_split("a,b   c;d",['x'].as_ref()), vec!["a,b   c;d"]);
/// ```
pub fn multi_split<'a>(input: &'a str, delimiters: &[char]) -> Vec<&'a str> {
    input.split(|c| delimiters.contains(&c)) // 根据 delimiters 分割
        .filter(|s| !s.is_empty()) // 移除任何空字符串
        .collect() // 收集到 Vec<&str> 中
}

// 首字母大写
pub fn is_capitalized(s: &str) -> bool {
    // 获取字符串的第一个字符
    match s.chars().next() {
        Some(first_char) => first_char.is_uppercase(),
        None => false, // 空字符串返回 false
    }
}

// 数据大写单词数量
pub fn count_camel_case_words(s: &str) -> isize {
    // 计算字符串中大写字母的数量
    let mut count = 0;
    for c in s.chars() {
        if c.is_uppercase() {
            count += 1;
        }
    }
    return count;
}

/// 删除字符串前缀
/// # Example
/// ```
/// use agent_gentests_service::utils::string_utils::remove_prefix;
/// assert_eq!(remove_prefix("/home/<USER>/Abc.java", "/home/<USER>"), "/Abc.java");
/// assert_eq!(remove_prefix("/home/<USER>/Abc.java", "/home/<USER>/"), "Abc.java");
/// assert_eq!(remove_prefix("/home/<USER>/Abc.java", "/home/<USER>/Abc.java"), "");
/// assert_eq!(remove_prefix("/home/<USER>/Abc.java", "/"), "home/good/Abc.java");
/// assert_eq!(remove_prefix("home/good/Abc.java", "/"), "home/good/Abc.java");
/// ```
pub fn remove_prefix<'a>(path_str: &'a str, prefix: &str) -> &'a str {
    if path_str.starts_with(prefix) {
        return &path_str[prefix.len()..];
    }
    return path_str;
}

#[cfg(test)]
mod test {
    use crate::utils::string_utils::remove_suffix;

    #[test]
    fn test() {
        println!("{}", remove_suffix("/home/<USER>/Abc.java", ".java"));
    }
}

use indexmap::{IndexMap, IndexSet};
use serde::{Deserialize, Deserializer, Serialize, Serializer};
use serde::de::{MapAccess, Visitor};
use serde::ser::SerializeMap;

pub fn serialize_indexmap<S, K, V>(
    map: &IndexMap<K, V>,
    serializer: S,
) -> Result<S::Ok, S::Error>
where
    S: Serializer,
    K: Serialize,
    V: Serialize,
{
    let mut map_serializer = serializer.serialize_map(Some(map.len()))?;
    for (key, value) in map.iter() {
        map_serializer.serialize_entry(key, value)?;
    }
    map_serializer.end()
}

pub fn deserialize_indexmap<'de, D, K, V>(
    deserializer: D,
) -> Result<IndexMap<K, V>, D::Error>
where
    D: Deserializer<'de>,
    K: Deserialize<'de> + std::hash::Hash + Eq,
    V: Deserialize<'de>,
{
    struct IndexMapVisitor<K, V>(std::marker::PhantomData<(K, V)>);

    impl<'de, K, V> Visitor<'de> for IndexMapVisitor<K, V>
    where
        K: Deserialize<'de> + std::hash::Hash + Eq,
        V: Deserialize<'de>,
    {
        type Value = IndexMap<K, V>;

        fn expecting(&self, formatter: &mut std::fmt::Formatter) -> std::fmt::Result {
            formatter.write_str("an object representing a map")
        }

        fn visit_map<M>(self, mut map: M) -> Result<Self::Value, M::Error>
        where
            M: MapAccess<'de>,
        {
            let mut index_map = IndexMap::new();
            while let Some((key, value)) = map.next_entry::<K, V>()? {
                index_map.insert(key, value);
            }
            Ok(index_map)
        }
    }

    deserializer.deserialize_map(IndexMapVisitor::<K, V>(std::marker::PhantomData))
}

pub fn serialize_indexset<S, T>(
    set: &IndexSet<T>,
    serializer: S,
) -> Result<S::Ok, S::Error>
where
    S: Serializer,
    T: Serialize,
{
    let v: Vec<&T> = set.iter().collect();
    v.serialize(serializer)
}

pub fn deserialize_indexset<'de, D, T>(
    deserializer: D
) -> Result<IndexSet<T>, D::Error>
where
    D: Deserializer<'de>,
    T: Deserialize<'de> + Eq + std::hash::Hash, // T需要实现Eq和Hash特性
{
    let v: Vec<T> = Vec::deserialize(deserializer)?;
    Ok(v.into_iter().collect())
}

#[cfg(test)]
mod tests {
    use serde_json;

    use crate::it::java_model::class_info::ClassInfo;

    use super::*;

    #[derive(Serialize, Deserialize, Debug)]
    struct MyStruct {
        #[serde(serialize_with = "serialize_indexmap", deserialize_with = "deserialize_indexmap")]
        data: IndexMap<String, ClassInfo>,
    }


    #[test]
    fn test_serialize_indexmap() {
        let class_info = ClassInfo::new();
        let mut map = IndexMap::new();
        map.insert("key1".to_string(), class_info);

        let my_struct = MyStruct { data: map };
        let serialized = serde_json::to_string(&my_struct).unwrap();
        println!("serialized={}", serialized);
    }

    #[test]
    fn test_deserialize_indexmap() {
        let data = r#"
{
    "data": {
        "key1": {
            "search_qualified_class_name": [],
            "qualified_class_name": "",
            "super_class": null,
            "class_type": "Class",
            "class_name": "",
            "package_name": "",
            "import_set": [],
            "import_star": [],
            "import_static": [],
            "super_interface_list": [],
            "field_map": {},
            "enum_constant_list": {},
            "method_list": [],
            "modifiers": "",
            "loaded": false,
            "sibling_class_list": [],
            "inner_class_list": []
        }
    }
}
        "#;

        let my_struct: MyStruct = serde_json::from_str(data).unwrap();
        println!("{:?}", my_struct);
    }
}
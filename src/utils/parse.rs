use anyhow::Result;
use log::info;
use serde::de::DeserializeOwned;
use std::fs;

use agent_common_service::model::chat_model::ChatCodeReferenceModel;

pub const TINA_CARD: &str = "tina-card";
pub const TINA_NORMAL: &str = "tina-normal-comp";
pub const RUNTIME_JSON: &str = "node_modules/.tina-copilot/run.json";
pub const TINA_NOT_LAUNCHED: &str = "环境检查失败，请确认当前是否是 Tina 项目或尝试重启 Tina GUI，如仍有问题请联系@南筠";

pub fn read_and_parse_json<T>(filename: &str) -> Result<T> where T: DeserializeOwned, {
  let content = fs::read_to_string(filename)?;
  // 解析 JSON
  let json: T = serde_json::from_str(&content)?;
  Ok(json)
}


pub fn parse_reference_target(scene_type: &String, reference_code_vec: &Option<Vec<ChatCodeReferenceModel>>) -> (Option<String>, Option<String>) {
  let mut logic_file_name = None;
  let mut target_context_code = None;

  if let Some(vec) = reference_code_vec {
      for reference in vec {
          if scene_type == TINA_NORMAL && reference.url.contains("src/logic") {
              logic_file_name = reference.url
                  .split('/')
                  .last()
                  .and_then(|filename| filename.rsplit_once('.'))
                  .map(|(name, _)| name.to_string());
              target_context_code = reference.content.clone();
              break;
          }
          if scene_type == TINA_CARD && reference.url.contains("src/biz-models") {
              target_context_code = reference.content.clone();
          }
      }
  }

  (logic_file_name, target_context_code)
}

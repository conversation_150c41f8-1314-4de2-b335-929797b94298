use crate::ast::java::strategy::CodeFuseJavaAstStrategy;
use crate::ast::js::constant::JS_VALID_EXTENSIONS;
use crate::ast::js::strategy::CodeFuseJsAstStrategy;
use crate::dialogue::codefuse_index_repository::{build_chunk_index, build_file_index, CHUNK_CLIENT, FILE_CLIET, METHOD_CLIENT};
use crate::dialogue::file_index_operator::update_chunk_vector_async;
use crate::dialogue::misc_util::{accelerate_by_time, autonomous_sleep_with_bolck, get_parallel_num};
use crate::dialogue::repo_index_operator::{get_repo_build_status, is_need_interrupt_repo_build, save_git_repo_cache, save_repo_build_status, save_repo_delete_status};
use crate::function::chat_strategy::{AUGMENT_TASK_TYPE,  CHAT_TASK_MANAGER_V2, DEEPSEARCH_TASK_TYPE};
use crate::service::code_scan::{is_test_file, skip_dir};
use crate::utils::file_encoding::read_file_smart_sync;
use agent_common_service::service::code_ast_analysis::AstStrategy;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::remote_client::IndexTypeEnum;
use agent_db::dal::remote_client::IndexTypeEnum::METHOD_CONTENT;
use agent_db::tools::common_tools::LINE_ENDING;
use anyhow::Result;
use futures::{stream, StreamExt};
use ignore::{DirEntry, WalkBuilder};
use log::{error, info};
use rayon::prelude::*;
use regex::Regex;
use std::collections::HashSet;
use std::fs::File;
use std::io::BufReader;
use std::ops::Deref;
use std::path::Path;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use std::{fs, thread};
use tokio::runtime::Runtime;
use crate::deepsearchV2::searchrouter_flow_task::DEEPSEARCH_TASK_MANAGER;

//每个线程处理的文件个数
const SCAN_PER_FILE_NUM: usize = 50;
//构建对话索引时跳过的文件后缀（测试验证中)
pub const CHAT_INDEX_SKIP_ARR:&str = "json,MF,md,ico";


///执行仓库级别的并行扫描
pub async fn execute_scan_for_index_repository(data_vec:&Vec<DirEntry>,project_url: &String, branch: &String, token: &String, product_type: &String, mill_sleep_time: usize, index_type_set: &HashSet<IndexTypeEnum>, task_type: u8) -> Result<()> {
    if get_repo_build_status(project_url, branch).await {
        info!("execute_scan_for_index_repository index_build is running , return");
        return Ok(());
    }else{
        info!("execute_scan_for_index_repository index_build is false , continue");
        save_repo_build_status(project_url, branch, true).await;
    }
    let start_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis();
    let mut group_id = &AtomicUsize::new(0);
    let max_concurrency = get_parallel_num(mill_sleep_time);
    stream::iter(data_vec)
        .chunks(MAX_NUM)
        .for_each_concurrent(max_concurrency, |chunk_arr| {
            async move {
                let current_group = group_id.fetch_add(1, Ordering::SeqCst);
                let data_to_handle: Vec<DirEntry> = chunk_arr.iter().map(|&entry| entry.clone()).collect();
                let batch_result = execute_scan_file_vec_async(current_group, data_to_handle.as_slice(), project_url, branch, token, product_type, mill_sleep_time, index_type_set, task_type).await;
                if batch_result.is_err() {
                    error!("error: {:?}",batch_result);
                }
            }
        }).await;
    CHUNK_CLIENT.commit();
    METHOD_CLIENT.commit();
    FILE_CLIET.commit();
    let end_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis();
    info!("parallel file end, total file size: {},consume time: {}",data_vec.len(),end_time - start_time);
    save_repo_build_status(project_url, branch, false).await;
    if mill_sleep_time ==0 {
        finish_sync_build_task_status(project_url, branch, task_type).await;
    }
    Ok(())
}




pub async fn finish_sync_build_task_status(project_url: &String, branch: &String, task_type: u8){
    match task_type {
        AUGMENT_TASK_TYPE => {
            CHAT_TASK_MANAGER_V2.finish_sync_build_task(project_url,branch).await;
        }
        DEEPSEARCH_TASK_TYPE => {
            DEEPSEARCH_TASK_MANAGER.finish_sync_build_task(project_url,branch).await;
        }
        _ => {}
    }
}

pub async fn is_current_project_and_branch_entry(project_url: &String, branch: &String, task_type: u8) -> bool {
    match task_type {
        AUGMENT_TASK_TYPE => {
            CHAT_TASK_MANAGER_V2.is_current_project_and_branch(project_url, branch).await
        }
        DEEPSEARCH_TASK_TYPE => {
            DEEPSEARCH_TASK_MANAGER.is_current_project_and_branch(project_url, branch).await
        }
        _ => { true }
    }
}

pub async fn is_build_process_timeout(task_type: u8)->bool{
    let single_file_scan_time_start = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    return match task_type {
        AUGMENT_TASK_TYPE => {
            CHAT_TASK_MANAGER_V2.is_build_process_timeout(single_file_scan_time_start).await
        }
        DEEPSEARCH_TASK_TYPE => {
            DEEPSEARCH_TASK_MANAGER.is_build_process_timeout(single_file_scan_time_start).await
        }
        _ => {true}
    }
}


    pub const MAX_NUM: usize = 1000;

    pub const MAX_VECTOR: usize = 5;

    ///批量执行扫描
    async fn execute_scan_file_vec_async(current_group: usize, file_vec: &[DirEntry], project_url: &String, branch: &String, token: &String, product_type: &String, mill_sleep_time: usize, index_type_set: &HashSet<IndexTypeEnum>, task_type: u8) -> Result<()> {
        let start_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis();
        let mut save_batch_chunk = vec![];
        let mut save_batch_method = vec![];
        let mut save_batch_file = vec![];
        for (index, file_dir) in file_vec.iter().enumerate() {
            info!("analysis file: {:?}", file_dir.path());
            let single_file_scan_time_start = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
            //如果mill_sleep_time=0，说明是同步任务，同时任务有时间限制，这里判断下,如果超时直接停止循环
            if mill_sleep_time == 0 && is_build_process_timeout(task_type).await {
                error!("file scan timeout, skip file step，current index: {},total file size: {}",index, file_vec.len());
                finish_sync_build_task_status(project_url,branch,task_type).await;
                break;
            }
            let need_interrupt_build = is_need_interrupt_repo_build(&project_url.clone().to_string(), &branch.clone().to_string(), task_type).await;
            if need_interrupt_build {
                info!("execute_scan_file_vec_async, need interrupt, end the process");
                return Ok(())
            }
            // 智能读取文件内容，支持多种编码（UTF-8, GBK, GB2312, GB18030等）
            let content = match read_file_smart_sync(&file_dir.path().to_string_lossy()) {
                Ok(content) => content,
                Err(e) => {
                    error!("读取文件失败: {} - {}", file_dir.path().display(), e);
                    continue;
                }
            };
            let line_vec: Vec<String> = content.split(LINE_ENDING).map(|s| s.to_string()).collect();

            if line_vec.len() > AGENT_CONFIG.scan_skip_file_max_len {
                error!("file line length exceeds allowed count");
                continue;
            }
            let file_data = build_file_index(file_dir.path(), &content, project_url, branch)?;
            let chunk_result = build_chunk_index(file_dir.path(), &line_vec, project_url, branch)?;
            //获取文件后缀，只有java或者前端语言才参与抽取函数逻辑
            let file_suffix_opt = file_dir.path().extension().and_then(|ext| ext.to_str());
            let method_vec = match file_suffix_opt {
                Some("java") => CodeFuseJavaAstStrategy::extra_method_chat_index(file_dir.path(), &content, project_url, branch).unwrap_or(vec![]),
                Some(suffix) if JS_VALID_EXTENSIONS.contains(&suffix) => {
                    CodeFuseJsAstStrategy::extra_method_chat_index(file_dir.path(), &content, project_url, branch).unwrap_or(vec![])
                }
                _ => vec![],
            };
            if file_data.content.trim().len() > 0 {
                save_batch_file.push(file_data);
            }
            if chunk_result.len() > 0 {
                save_batch_chunk.extend(chunk_result);
            }
            if method_vec.len() > 0 {
                save_batch_method.extend(method_vec);
            }

            let single_file_scan_time_end = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis();
            info!("single file scan finish.group:{}, index: {}, consume {} ms", current_group,index, single_file_scan_time_end - single_file_scan_time_start);
        }
        let file_scan_time_end = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis();
        info!("all file scan finish. group:{}, need file size:{},file size:{},method size:{},chunk size:{} ,consume {} ms", current_group,file_vec.len(),save_batch_file.len(),save_batch_method.len(),save_batch_chunk.len(),file_scan_time_end - start_time);

    let start_save_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    for index_type in index_type_set {
        let need_interrupt_build = is_need_interrupt_repo_build(&project_url.clone().to_string(), &branch.clone().to_string(),task_type).await;
        if need_interrupt_build {
            info!("execute_scan_file_vec_async, need interrupt, end the process");
            return Ok(())
        }
        match index_type {
            IndexTypeEnum::FILE_CONTENT => {
                let start_file_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis();
                save_batch_file.chunks(MAX_NUM).for_each(|file_arr| {
                    let _ = FILE_CLIET.save_batch(file_arr);
                    // let _ = FILE_CLIET.update_batch(&file_arr);
                    // FILE_CLIET.commit();
                });
                let end_file_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                info!("save file doc finish. file size: {} ,consume {} ms", save_batch_file.len(), end_file_time - start_file_time);
            }
            IndexTypeEnum::CHUNK_CONTENT => {
                let start_chunk_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                save_batch_chunk.chunks(MAX_NUM).for_each(|chunk_arr| {
                    let _ = CHUNK_CLIENT.save_batch(&chunk_arr);
                    // let _ = CHUNK_CLIENT.update_batch(&chunk_arr);
                    // CHUNK_CLIENT.commit();
                });
                let end_chunk_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                info!("save chunk doc finish. chunk size: {} ,consume {} ms", save_batch_chunk.len(), end_chunk_time - start_chunk_time);
            }
            IndexTypeEnum::METHOD_CONTENT => {
                let start_method_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                save_batch_method.chunks(MAX_NUM).for_each(|method_arr| {
                    let _ = METHOD_CLIENT.save_batch(&method_arr);
                    // let _ = METHOD_CLIENT.update_batch(&method_arr);
                    // METHOD_CLIENT.commit();
                });
                let end_method_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                info!("save method doc finish. method size: {} ,consume {} ms", save_batch_method.len(), end_method_time - start_method_time);
            }
            //
            IndexTypeEnum::CHUNK_VECTOR => {
                let start_method_vector_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                let max_concurrency = get_parallel_num(mill_sleep_time); // 并发的任务数，可以根据需要调节
                stream::iter(save_batch_chunk.clone())
                    .chunks(MAX_VECTOR)
                    .for_each_concurrent(max_concurrency, |chunk_arr| {
                        async move {
                            let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                            if mill_sleep_time == 0 && is_build_process_timeout(task_type).await{
                                info!("request CHUNK_VECTOR timeout in execute_scan_file_vec_async");
                                finish_sync_build_task_status(project_url,branch,task_type).await;
                                return;
                            }
                            let mut chunk_mut = chunk_arr.clone();
                            update_chunk_vector_async(&mut chunk_mut, &token.to_string(), &product_type.to_string()).await;
                            let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                            info!("single vecotr chunk {} ms", end_time - start_time);
                        }
                    }).await;
                let end_method_vector_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                info!("save method vector finish. method size: {} ,consume {} ms", save_batch_method.len(), end_method_vector_time - start_method_vector_time);

            }
            _ => {}
        }
    }
    let end_save_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();

        info!("file scan finish. ,group:{},  chunk_size: {} , method_size: {} ,save data consume: {} ms , total consume : {} ms", current_group,save_batch_chunk.len(),save_batch_method.len(),end_save_time - start_save_time,end_save_time - start_time);
        autonomous_sleep_with_bolck(mill_sleep_time);
        Ok(())
    }



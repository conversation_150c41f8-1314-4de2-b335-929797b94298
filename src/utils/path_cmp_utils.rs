use std::collections::HashMap;
use std::path::{Path, PathBuf, MAIN_SEPARATOR};
use std::sync::Arc;
use log::{debug, error, info};
use anyhow::{anyhow, Result};
// use agent_codefuse_service::service::code_scan::FILE_PREFIX;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::ScanFileRecord;
use crate::it::java_model::class_info::ClassInfo;
use crate::it::java_model::func_call::ArgumentType;
use crate::it::java_model::method_info::MethodInfo;
use crate::it::java_parser::parser::parse_class_text;
use crate::it::loader::class_loader::{load_repo_all_java_file};

// 正常应该这样引入：use agent_common_service::tools::common_tools::FILE_PREFIX，下个版本修改
pub const FILE_PREFIX: &str = "F_";

///
/// 根据测试类查找被测类，Java
///
/// # 字段
/// - `test_file_url`:  测试类文件url
/// - `project_url`: 工程目录
///
/// # 返回
/// - `Result<String>`: 匹配结果，正确：被测类路径，错误：错误信息
///
pub fn find_cut_by_test_file(test_file_url: &String, project_url: &String) -> Result<String> {
    // 通过kv cache还是class_loader处理
    let search_by_kv_cache = false;
    info!("find cut by test file, search_by_kv_cache value is : {}", search_by_kv_cache);

    // 根据文件路径找到java类文件名，注意windows和linux两种路径
    if let Some(test_file_name) = get_java_class_name_from_path(test_file_url) {
        let test_class_norm_name = &test_file_name.to_lowercase()
            .replace("_test", "")
            .replace("test_", "")
            .replace("_sstest", "")
            .replace("test", "");
        // 查找文件
        let sep = MAIN_SEPARATOR.to_string();
        let mut test_path_norm = test_file_url.to_lowercase()
            .replace(&format!("src{}test{}", sep, sep), &format!("src{}main{}", sep, sep))
            .replace(&format!("test{}", sep), "")
            .replace(&format!("{}.java", test_file_name.to_lowercase()), "");

        if let Some(pos) = test_path_norm.find("src") {
            test_path_norm = test_path_norm[pos..].to_string();
        }
        // 去掉path中文件名中的test，利用test_path_norm + test_class_norm_name
        test_path_norm = test_path_norm + &test_class_norm_name + ".java";
        info!("test_path_norm: {}", test_path_norm);

        let file_key = format!("{}{}", FILE_PREFIX, project_url);
        // 模糊匹配
        let mut file_map_result_fuzzy_first: HashMap<String, String> = HashMap::new(); // norm -> orig
        let mut file_map_result_fuzzy_second: HashMap<String, String> = HashMap::new();
        let mut name_find_first = String::new();
        let mut name_find_second = String::new();

        if search_by_kv_cache {
            if let Ok(Some(file_map)) = KV_CLIENT.get_from_prefix(&file_key) {
                for (key, value) in file_map {
                    let scan_file_data_result: serde_json::error::Result<ScanFileRecord> = serde_json::from_str(value.as_str());
                    match scan_file_data_result {
                        Ok(scan_file_data) => {
                            if &scan_file_data.file_name_suffix == "java" && !scan_file_data.file_url.contains(&format!("test{}", sep)) { // 只看java类并且不在test目录下
                                let scan_file_url = &scan_file_data.file_url;
                                let scan_file_norm_url = &scan_file_url.to_lowercase();

                                if scan_file_norm_url.ends_with(&test_path_norm) { // 判断测试类改名称后是否可以精确匹配到
                                    return Ok(scan_file_url.clone());
                                } else { // 根据测试类名模糊匹配
                                    // 先查找impl类
                                    name_find_first = if test_class_norm_name.ends_with("impl") {
                                        format!("{}.java", &test_class_norm_name).to_string()
                                    } else {
                                        format!("{}impl.java", &test_class_norm_name).to_string()
                                    };

                                    if scan_file_norm_url.ends_with(&format!("{}{}", sep, name_find_first)) {
                                        file_map_result_fuzzy_first.insert(scan_file_norm_url.clone(), scan_file_url.clone());
                                    } else if !&test_class_norm_name.ends_with("impl") {
                                        name_find_second = test_class_norm_name.clone() + ".java";
                                        if scan_file_norm_url.ends_with(&format!("{}{}", sep, &name_find_second)) {
                                            file_map_result_fuzzy_second.insert(scan_file_norm_url.clone(), scan_file_url.clone());
                                        }
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            error!("serde_json::from_str failed in cut find,{:?}",e);
                        }
                    }
                }
            } else {
                error!("get file list failed in cut find");
                anyhow!("get_file_list result is none");
            }
        } else {
            let all_java_file_path_vec: Vec<Arc<PathBuf>> = load_repo_all_java_file(project_url);
            for file_path_arc in all_java_file_path_vec {
                let file_path_str = file_path_arc.as_path().to_string_lossy().into_owned();
                if !file_path_str.contains(&format!("test{}", sep)) {
                    let scan_file_norm_url = &file_path_str.to_lowercase();
                    if scan_file_norm_url.ends_with(&test_path_norm) { // 判断测试类改名称后是否可以精确匹配到
                        return Ok(file_path_str.clone());
                    } else { // 根据测试类名模糊匹配
                        // 先查找impl类
                        name_find_first = if test_class_norm_name.ends_with("impl") {
                            format!("{}.java", &test_class_norm_name).to_string()
                        } else {
                            format!("{}impl.java", &test_class_norm_name).to_string()
                        };

                        if scan_file_norm_url.ends_with(&format!("{}{}", sep, name_find_first)) {
                            file_map_result_fuzzy_first.insert(scan_file_norm_url.clone(), file_path_str.clone());
                        } else if !&test_class_norm_name.ends_with("impl") {
                            name_find_second = test_class_norm_name.clone() + ".java";
                            if scan_file_norm_url.ends_with(&format!("{}{}", sep, &name_find_second)) {
                                file_map_result_fuzzy_second.insert(scan_file_norm_url.clone(), file_path_str.clone());
                            }
                        }
                    }
                }
            }
        }

        // 精确匹配失败，模糊匹配检查
        if !file_map_result_fuzzy_first.is_empty() {
            let keys_vec: Vec<String> = file_map_result_fuzzy_first.keys().cloned().collect();
            let test_norm_to_check = &format!("{}{}{}", &test_path_norm[..test_path_norm.rfind(&sep).unwrap() + &sep.len()], &sep, name_find_first);
            if let Some(likely_path_matched) = path_likely(&keys_vec, test_norm_to_check) {
                Ok(file_map_result_fuzzy_first.get(&likely_path_matched).unwrap().to_string())
            } else {
                // 返回错误结果
                error!("handle path likely with first fuzzing failed, test_file_url: {}, project url is {}", test_file_url, project_url);
                Err(anyhow!("handle path likely with first fuzzing failed, test_file_url{}, project url is {}", test_file_url, project_url))
            }
        } else if !file_map_result_fuzzy_second.is_empty() {
            let keys_vec: Vec<String> = file_map_result_fuzzy_second.keys().cloned().collect();
            let test_norm_to_check = &format!("{}{}{}", &test_path_norm[..test_path_norm.rfind(&sep).unwrap() + &sep.len()], &sep, name_find_second);
            if let Some(likely_path_matched) = path_likely(&keys_vec, test_norm_to_check) {
                Ok(file_map_result_fuzzy_second.get(&likely_path_matched).unwrap().to_string())
            } else {
                // 返回错误结果
                error!("handle path likely with second fuzzing failed, test_file_url: {}, project url is {}", test_file_url, project_url);
                Err(anyhow!("handle path likely with second fuzzing failed, test_file_url{}, project url is {}", test_file_url, project_url))
            }
        } else {
            // 返回错误结果
            error!("can not find cut class name from test_file_url: {}, project url is {}", test_file_url, project_url);
            Err(anyhow!("can not find cut name from test_file_url: {}, project url is {}", test_file_url, project_url))
        }
    } else {
        // 返回错误
        error!("can not find java test class name from path: {}", test_file_url);
        Err(anyhow!("can not find java test class name from path: {}", test_file_url))
    }
}

// 根据路径匹配
pub fn path_likely(fit_classes: &Vec<String>, name_to_find: &str) -> Option<String> {
    let mut max_cnt = 0;
    let mut matched = String::new();

    for item in fit_classes.iter() {
        let fit_cnt = compare_paths_backward(item, name_to_find);
        if fit_cnt > max_cnt {
            matched = item.clone();
            max_cnt = fit_cnt;
        }
    }

    if !matched.is_empty() {
        Some(matched)
    } else {
        None
    }
}

///
/// 根据测试类圈选case和被测类来判断被测方法，Java
///
/// # 字段
/// - `cut_class_info`:  被测类的ClassInfo
/// - `case_selected_content`: 测试类中圈选case内容
///
/// # 返回
/// - `Option<String>`: 匹配结果，被测方法内容
///
pub fn get_mut_from_cut_test_class_info(cut_class_info: &ClassInfo, case_selected_content: String) -> Option<String> {
    let case_info = parse_class_text(&case_selected_content);
    let case_method_list = case_info.method_list;
    // 目前只找第一个
    if case_method_list.is_empty() {
        return None;
    }

    let case_method_info = case_method_list.get(0);
    let case_name = &case_method_info.unwrap().method_name;
    let case_invoke_method_list = &case_method_info.unwrap().function_call_list;
    let cut_method_list: &Vec<MethodInfo> = &cut_class_info.method_list;

    // 获取被测类方法名到内容映射，注意因为重载问题，value是list
    let cut_method_map= cut_method_list.iter().fold(HashMap::new(), |mut map, method_info| {
        map.entry(method_info.method_name.clone())
            .or_insert_with(Vec::new)
            .push(method_info);
        map
    });

    // 1. 首先根据用例名匹配
    // 用例名可能情况：test_method_xxx, testMethodWith...；第一种分块匹配；第二种去掉test匹配，如果有多余内容则匹配失败
    let mut find_mut = false;
    if case_name.contains("_") {
        let parts: Vec<&str> = case_name.split('_').collect();
        for mut part in parts {
            let part_without_test = part.replace("test", "");
            let mut_code_body_opt = check_method_name_with_case_name(&cut_method_map, &part_without_test);
            if let Some(mut_code_body) = mut_code_body_opt {
                find_mut = true;
                return Some(mut_code_body);
            }
        }
    } else {
        let name_to_find = case_name.replace("test", "").replace("Test","");
        let mut_code_body_opt = check_method_name_with_case_name(&cut_method_map, &name_to_find);
        if let Some(mut_code_body) = mut_code_body_opt {
            find_mut = true;
            return Some(mut_code_body);
        }
    }

    // 2. 根据调用方法倒序匹配
    if find_mut || case_invoke_method_list.len() == 0 {
        return None;
    }

    for case_invoke_method in case_invoke_method_list.iter().rev() { // case_invoke: 用例中方法调用
        let invoke_method_name = case_invoke_method.method_name.clone();
        let candidate_method_list_opt = cut_method_map.get(&invoke_method_name);
        if let Some(candidate_method_list) = candidate_method_list_opt {
            let mut mut_code_body = String::new();
            if candidate_method_list.len() == 1 { // 如果只有1个，就没有重载问题，不检查参数类型
                if let Some(mut_info) = candidate_method_list.get(0) {
                    mut_code_body = mut_info.code_body.to_string();
                    return Some(mut_code_body);
                }
            } else { // 有函数重载
                let case_invoke_method_args_map = &case_invoke_method.args_map;
                debug!("case_invoke_method_args_map: {:?}", case_invoke_method_args_map);
                let case_invoke_method_param_size = case_invoke_method_args_map.len();
                for candidate_method in candidate_method_list.iter() {
                    // 目前函数重载根据参数数量和参数类型来判断，用例中invoke param type不一定有，近似
                    let candidate_param_map = &candidate_method.param_map;
                    let candidate_method_param_size = candidate_param_map.len();
                    debug!("candidate_method_param_map is {:?}", candidate_param_map);
                    if case_invoke_method_param_size != candidate_method_param_size {
                        // 如果参数数量不等，直接跳过
                        continue;
                    }

                    // 再判断参数类型
                    let mut all_param_match = true;
                    mut_code_body = candidate_method.code_body.to_string();
                    for((_, candidate_value), (_, invoke_value)) in candidate_param_map.iter().zip(case_invoke_method_args_map.iter()) {
                        let candidate_param_qualified_class_name = &candidate_value.qualified_class_name; // 例如：com.alipay.sut.service.gpt.impl.ObjectA
                        let candidate_param_type_name = candidate_param_qualified_class_name.rsplit('.').next().unwrap_or(candidate_param_qualified_class_name); // 变成 ObjectA
                        match invoke_value {
                            ArgumentType::Any | ArgumentType::Later(_, _) => {
                                // 没有类型，则走近似匹配，直接返回
                                return Some(mut_code_body);
                            }
                            ArgumentType::Type(param_class_info) => {
                                let invoke_param_qualified_class_name = &param_class_info.qualified_class_name.to_string();
                                // 事实上用例中方法调用参数qualified_name仅只有类名，例如 ObjectA，不是全称，但是为了防止意外情况，仍然处理
                                let invoke_param_type_name = invoke_param_qualified_class_name.rsplit('.').next().unwrap_or(invoke_param_qualified_class_name);
                                if candidate_param_type_name != invoke_param_type_name {
                                    all_param_match = false;
                                    break;
                                }
                            }
                        }
                    }
                    if all_param_match {
                        return Some(mut_code_body);
                    }
                }
            }
        }
    }

    // 3. 如果没找到，有可能是反射调用，处理办法：遍历被测类中method name，加引号从case中匹配
    // 反射API不确定，这里先不考虑重载问题
    for cut_method_info in cut_method_list {
        let method_name = cut_method_info.method_name.clone();
        let reflect_name = format!("\"{}\"", method_name);
        if case_selected_content.contains(&reflect_name) {
            return Some(cut_method_info.code_body.to_string());
        }
    }

    None
}

///
/// 兜底
///
pub fn find_cut_by_test_file_bak(test_file_url: &String) -> Result<String> {
    let sep = MAIN_SEPARATOR.to_string();
    let mut cut_path_str = test_file_url.replace(&format!("test{}", sep), &format!("main{}", sep));
    cut_path_str = cut_path_str.replace("Test.java", ".java");
    let cut_path = Path::new(&cut_path_str);
    if cut_path.exists() {
        info!("查找cut兜底逻辑，文件存在，路径为{}", &cut_path_str);
        Ok(cut_path_str.clone())
    } else {
        if !cut_path_str.contains("Impl") {
            cut_path_str = cut_path_str.replace(".java", "Impl.java");
            let cut_path_impl = Path::new(&cut_path_str);
            if cut_path_impl.exists() {
                info!("查找cut兜底逻辑，文件存在，路径为{}", &cut_path_str);
                return Ok(cut_path_str.clone());
            }
        }
        info!("查找cut兜底逻辑，文件不存在，路径为{}", &cut_path_str);
        Err(anyhow!("cut not found"))
    }
}

// 根据用例名匹配被测方法名
fn check_method_name_with_case_name(cut_method_map: &HashMap<String, Vec<&MethodInfo>>, name_to_find: &String) -> Option<String> {
    if let Some(first_char) = name_to_find.chars().next() {
        let mut modified = first_char.to_lowercase().to_string();
        modified.push_str(&name_to_find[1..]);
        if cut_method_map.contains_key(&modified) {
            let mut_info_opt = cut_method_map.get(&modified);
            if let Some(mut_info_list) = mut_info_opt {
                // 用例名默认取第一个
                if let Some(mut_code_info) = mut_info_list.get(0) {
                    let mut_code_body_str = mut_code_info.code_body.to_string();
                    return Some(mut_code_body_str);
                }
            }
        }
    }
    None
}

// 从路径中获取java类名
fn get_java_class_name_from_path(java_file_path: &String) -> Option<String> {
    let path = Path::new(java_file_path);
    if let Some(filename) = path.file_stem() {
        if let Some(filename_str) = filename.to_str() {
            return Some(filename_str.to_string());
        }
    }
    None
}

// 倒序比较路径，返回相同路径的深度
fn compare_paths_backward(path1: &str, path2: &str) -> usize {
    let sep = MAIN_SEPARATOR.to_string();
    let path1_dirs: Vec<&str> = path1.split(&sep).collect();
    let path2_dirs: Vec<&str> = path2.split(&sep).collect();

    let mut count = 0;
    let mut i = path1_dirs.len();
    let mut j = path2_dirs.len();

    while i > 0 && j > 0 {
        if path1_dirs[i - 1] == path2_dirs[j - 1] {
            count += 1;
            i -= 1;
            j -= 1;
        } else {
            break;
        }
    }

    count
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    //#[ignore]
    fn test_get_cut_normal() {
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        let test_file_url = "/Users/<USER>/workspace/smartunitmng/app/service/src/test/java/com/alipay/sut/service/gpt/impl/llmtestdata/ImportFilterTest.java";
        let repo_url = "/Users/<USER>/workspace/smartunitmng/";

        let result:Result<String> = find_cut_by_test_file(&test_file_url.to_string(), &repo_url.to_string());
        let cut_path = "/Users/<USER>/workspace/smartunitmng/app/service/src/main/java/com/alipay/sut/service/gpt/impl/llmtestdata/ImportFilter.java";
        match result {
            Ok(result_str) => {
                println!("result_str:{}", result_str);
                assert_eq!(result_str,cut_path );
            }
            Err(e) => {
                println!("error:{}", e);
                assert!(false);
            }
        }
    }

    #[test]
    //#[ignore]
    fn test_get_cut_diff_package_and_no_impl_name() {
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        let test_file_url = "/Users/<USER>/workspace/smartunitmng/app/service/src/test/java/smartunit/com/alipay/sut/service/gpt/impl/CalculateTokenServiceTest.java";
        let repo_url = "/Users/<USER>/workspace/smartunitmng/";

        let result:Result<String> = find_cut_by_test_file(&test_file_url.to_string(), &repo_url.to_string());
        let cut_path = "/Users/<USER>/workspace/smartunitmng/app/service/src/main/java/com/alipay/sut/service/gpt/impl/CalculateTokenServiceImpl.java";
        match result {
            Ok(result_str) => {
                println!("result_str:{}", result_str);
                assert_eq!(result_str,cut_path );
            }
            Err(e) => {
                println!("error:{}", e);
                assert!(false);
            }
        }
    }

    #[test]
    //#[ignore]
    fn test_get_cut_from_test_bundle_name() {
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        let test_file_url = "/Users/<USER>/workspace/smartunitmng/app/test/src/main/test/CalculateTokenServiceImplTest.java";
        let repo_url = "/Users/<USER>/workspace/smartunitmng/";

        let result:Result<String> = find_cut_by_test_file(&test_file_url.to_string(), &repo_url.to_string());
        let cut_path = "/Users/<USER>/workspace/smartunitmng/app/service/src/main/java/com/alipay/sut/service/gpt/impl/CalculateTokenServiceImpl.java";
        match result {
            Ok(result_str) => {
                println!("result_str:{}", result_str);
                assert_eq!(result_str,cut_path );
            }
            Err(e) => {
                println!("error:{}", e);
                assert!(false);
            }
        }
    }

    // 测试mut匹配，正常情况
    #[test]
    fn test_mut_match_by_case_name_normal() {
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);

        let cut_str = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.model.enums.AntCodeModifyEnum;
        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import com.alipay.sut.service.gpt.impl.ObjectC;

        /**
         * <AUTHOR>
         * @date 2024/09/03
         */
        public class ImportFilter {
            public String filter(ObjectA oA) {
                ObjectB objectB = new ObjectB();
                String bField = objectB.getbField();
                if(bField.equals("123")) {
                    return "haha";
                }
                return oA.getaField();
            }

            public String cFiled(ObjectC oC) {
                return oC.cMethod();
            }

            public boolean antCodeEnumGet(String code) {
                if(AntCodeModifyEnum.MODIFY.getCode().equals(code)) {
                    return true;
                }
                return false;
            }
        }
        "#;
        let test_class_str = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import org.junit.Before;
        import org.junit.Test;
        import org.mockito.InjectMocks;
        import org.mockito.MockitoAnnotations;
        import org.junit.runner.RunWith;
        import org.mockito.Mock;
        import org.mockito.junit.MockitoJUnitRunner;
        import static org.junit.Assert.assertEquals;
        import static org.mockito.Mockito.when;

        /**
         * <AUTHOR>
         * @date 2024/09/11
         */
        @RunWith(MockitoJUnitRunner.class)
        public class ImportFilterTest {
            @InjectMocks
            private ImportFilter importFilter;

            @Mock
            private ObjectB objectB;

            @Before
            public void setUp() {
                ImportFilter importFilter = new ImportFilter();
            }

            @Test
            public void handleWrite() {
                ImportFilter importFilter = new ImportFilter();
                importFilter.antCodeEnumGet("123");
            }

            /**
             * [单测用例]测试场景：当ObjectB的bField为"123"时，返回"haha"
             */
            @Test
            public void testFilter_bFieldIs123() {
                ObjectA oA = new ObjectA();
                oA.setaField("test");
                when(objectB.getbField()).thenReturn("123");

                String result = importFilter.filter(oA);

                assertEquals("haha", result);
            }

            /**
             * [单测用例]测试场景：当ObjectB的bField不为"123"时，返回ObjectA的aField
             */
            @Test
            public void testFilter_bFieldIsNot123() {
                ObjectA oA = new ObjectA();
                oA.setaField("test");
                when(objectB.getbField()).thenReturn("456");

                String result = importFilter.filter(oA);

                assertEquals("test", result);
            }
        }

        "#;

        let case_selected_str = r#"
        @Test
        public void testFilter_bFieldIsNot123() {
            ObjectA oA = new ObjectA();
            oA.setaField("test");
            when(objectB.getbField()).thenReturn("456");

            String result = importFilter.filter(oA);

            assertEquals("test", result);
        }
        "#;


        let cut_info = parse_class_text(cut_str);
        let test_info = parse_class_text(test_class_str);

        let mut_body_ret = get_mut_from_cut_test_class_info(&cut_info, case_selected_str.to_string());
        match mut_body_ret {
            Some(result_str) => {
                println!("result_str:{}", result_str);
                assert!(result_str.contains("public String filter(ObjectA oA) {"));
            }
            None => {
                assert!(false);
            }
        }
    }

    // 测试mut匹配，用例名称不规范
    #[test]
    fn test_mut_match_by_case_invoke_normal() {
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);

        let cut_str = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.model.enums.AntCodeModifyEnum;
        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import com.alipay.sut.service.gpt.impl.ObjectC;

        /**
         * <AUTHOR>
         * @date 2024/09/03
         */
        public class ImportFilter {
            public String filter(ObjectA oA) {
                ObjectB objectB = new ObjectB();
                String bField = objectB.getbField();
                if(bField.equals("123")) {
                    return "haha";
                }
                return oA.getaField();
            }

            public String cFiled(ObjectC oC) {
                return oC.cMethod();
            }

            public boolean antCodeEnumGet(String code) {
                if(AntCodeModifyEnum.MODIFY.getCode().equals(code)) {
                    return true;
                }
                return false;
            }
        }
        "#;
        let test_class_str = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import org.junit.Before;
        import org.junit.Test;
        import org.mockito.InjectMocks;
        import org.mockito.MockitoAnnotations;
        import org.junit.runner.RunWith;
        import org.mockito.Mock;
        import org.mockito.junit.MockitoJUnitRunner;
        import static org.junit.Assert.assertEquals;
        import static org.mockito.Mockito.when;

        /**
         * <AUTHOR>
         * @date 2024/09/11
         */
        @RunWith(MockitoJUnitRunner.class)
        public class ImportFilterTest {
            @InjectMocks
            private ImportFilter importFilter;

            @Mock
            private ObjectB objectB;

            @Before
            public void setUp() {
                ImportFilter importFilter = new ImportFilter();
            }

            @Test
            public void handleWrite() {
                ImportFilter importFilter = new ImportFilter();
                importFilter.antCodeEnumGet("123");
            }

            /**
             * [单测用例]测试场景：当ObjectB的bField为"123"时，返回"haha"
             */
            @Test
            public void testFilter_bFieldIs123() {
                ObjectA oA = new ObjectA();
                oA.setaField("test");
                when(objectB.getbField()).thenReturn("123");

                String result = importFilter.filter(oA);

                assertEquals("haha", result);
            }

            /**
             * [单测用例]测试场景：当ObjectB的bField不为"123"时，返回ObjectA的aField
             */
            @Test
            public void testany() {
                ObjectA oA = new ObjectA();
                oA.setaField("test");
                when(objectB.getbField()).thenReturn("456");

                String result = importFilter.filter(oA);

                assertEquals("test", result);
            }
        }

        "#;

        let case_selected_str = r#"
        @Test
        public void testany() {
            ObjectA oA = new ObjectA();
            oA.setaField("test");
            when(objectB.getbField()).thenReturn("456");

            String result = importFilter.filter(oA);

            assertEquals("test", result);
        }
        "#;

        let cut_info = parse_class_text(cut_str);
        let test_info = parse_class_text(test_class_str);

        let mut_body_ret = get_mut_from_cut_test_class_info(&cut_info, case_selected_str.to_string());
        match mut_body_ret {
            Some(result_str) => {
                println!("result_str:{}", result_str);
                assert!(result_str.contains("public String filter(ObjectA oA) {"));
            }
            None => {
                assert!(false);
            }
        }
    }

    // 测试mut匹配，有重载情况
    #[test]
    fn test_mut_match_by_case_invoke_method_overload() {
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);

        let cut_str = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.model.enums.AntCodeModifyEnum;
        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import com.alipay.sut.service.gpt.impl.ObjectC;

        /**
         * <AUTHOR>
         * @date 2024/09/03
         */
        public class ImportFilter {
            public String filter(ObjectA oA, String s) {
                ObjectB objectB = new ObjectB();
                String bField = objectB.getbField();
                if(bField.equals("123")) {
                    return "haha";
                }
                return oA.getaField();
            }
             public String filter(String s) {
                ObjectB objectB = new ObjectB();
                String bField = objectB.getbField();
                if(bField.equals("123")) {
                    return "haha";
                }
                return oA.getaField();
            }
            public String filter(ObjectA oA) {
                ObjectB objectB = new ObjectB();
                String bField = objectB.getbField();
                if(bField.equals("123")) {
                    return "haha";
                }
                return oA.getaField();
            }

            public String cFiled(ObjectC oC) {
                return oC.cMethod();
            }

            public boolean antCodeEnumGet(String code) {
                if(AntCodeModifyEnum.MODIFY.getCode().equals(code)) {
                    return true;
                }
                return false;
            }
        }
        "#;
        let test_class_str = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import org.junit.Before;
        import org.junit.Test;
        import org.mockito.InjectMocks;
        import org.mockito.MockitoAnnotations;
        import org.junit.runner.RunWith;
        import org.mockito.Mock;
        import org.mockito.junit.MockitoJUnitRunner;
        import static org.junit.Assert.assertEquals;
        import static org.mockito.Mockito.when;

        /**
         * <AUTHOR>
         * @date 2024/09/11
         */
        @RunWith(MockitoJUnitRunner.class)
        public class ImportFilterTest {
            @InjectMocks
            private ImportFilter importFilter;

            @Mock
            private ObjectB objectB;

            @Before
            public void setUp() {
                ImportFilter importFilter = new ImportFilter();
            }

            @Test
            public void handleWrite() {
                ImportFilter importFilter = new ImportFilter();
                importFilter.antCodeEnumGet("123");
            }

            /**
             * [单测用例]测试场景：当ObjectB的bField为"123"时，返回"haha"
             */
            @Test
            public void testFilter_bFieldIs123() {
                ObjectA oA = new ObjectA();
                oA.setaField("test");
                when(objectB.getbField()).thenReturn("123");

                String result = importFilter.filter(oA);

                assertEquals("haha", result);
            }

            /**
             * [单测用例]测试场景：当ObjectB的bField不为"123"时，返回ObjectA的aField
             */
            @Test
            public void testany() {
                ObjectA oA = new ObjectA();
                oA.setaField("test");
                when(objectB.getbField()).thenReturn("456");

                String result = importFilter.filter(oA);

                assertEquals("test", result);
            }
        }

        "#;

        let case_selected_str = r#"
        @Test
        public void testany() {
            ObjectA oA = new ObjectA();
            oA.setaField("test");
            when(objectB.getbField()).thenReturn("456");

            String result = importFilter.filter(oA);

            assertEquals("test", result);
        }
        "#;

        let cut_info = parse_class_text(cut_str);
        let test_info = parse_class_text(test_class_str);

        let mut_body_ret = get_mut_from_cut_test_class_info(&cut_info, case_selected_str.to_string());
        match mut_body_ret {
            Some(result_str) => {
                println!("result_str:{}", result_str);
                assert!(result_str.contains("public String filter(ObjectA oA) {"));
            }
            None => {
                assert!(false);
            }
        }
    }

    // 测试mut匹配，反射调用
    #[test]
    fn test_mut_match_by_case_invoke_reflect() {
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);

        let cut_str = r#"
        package com.alipay.sut.service.gpt.impl;

        import java.io.IOException;
        import java.util.ArrayList;
        import java.util.HashMap;
        import java.util.List;
        import java.util.Map;
        import java.util.Optional;
        import java.util.concurrent.CountDownLatch;
        import java.util.concurrent.TimeUnit;

        import javax.servlet.http.HttpServletResponse;

        import com.alibaba.fastjson.JSON;
        import com.alibaba.fastjson.JSONArray;
        import com.alibaba.fastjson.JSONObject;

        import com.alipay.arks.client.Item;
        import com.alipay.arks.client.enums.ModelServerType;
        import com.alipay.arks.client.enums.RpcMode;
        import com.alipay.maya.MayaClient;
        import com.alipay.maya.config.MayaClientConfig;
        import com.alipay.maya.model.MayaRequest;
        import com.alipay.maya.model.MayaResponse;
        import com.alipay.maya.model.MayaStreamObserver;
        import com.alipay.sut.model.gpt.MayaClientReqParam;
        import com.alipay.sut.model.gpt.ModelStreamBuffer;
        import com.alipay.sut.service.gpt.MayaService;
        import com.alipay.sut.utils.exception.SutException;
        import org.slf4j.Logger;
        import org.slf4j.LoggerFactory;
        import org.springframework.beans.factory.annotation.Value;
        import org.springframework.stereotype.Service;

        /**
         * <AUTHOR>
         * @date 2023/06/05
         */
        @Service
        public class MayaServiceImpl implements MayaService {

            private static final Logger log = LoggerFactory.getLogger("GPTACCESS");

            /**
             * 用于环境检测，dev环境mock调用
             */
            @Value("${dbmode}")
            private String dbmode;

            /** stream模型总等待时间，单位：ms **/
            private static final long DEFAULT_STREAM_ALL_TIME = 100 * 1000L;

            @Override
            public String getInferenceResult(String data) {
                MayaClientConfig config = new MayaClientConfig();
                // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
                config.setAppName("smartunitmng");
                MayaClient mayaClient = MayaClient.getInstance(config);
                // 构造请求
                MayaRequest request = new MayaRequest();
                // 业务场景名,必填,需要根据服务名寻址
                request.setSceneName("codegpt_single_finetune_v7");
                // 必填,服务版本会作为url路径http
                request.setChainName("v7");
                // 服务端类型,默认ModelServerType.MAYA
                request.setServerType(ModelServerType.MAYA);
                // 整体超时时间,不设置默认从MayaClientConfig里获取是600ms
                request.setRequestTimeOut(10000);
                // 可选,建立连接超时时间,不设置默认从MayaClientConfig里获取是100ms
                request.setConnectTimeOut(1000);
                // 可选,等待服务端返回超时时间,不设置默认从MayaClientConfig里获取是500ms
                request.setReadTimeOut(9000);
                // item,一个Item代表一次推理，n个Item就是一个长度为n的batch,返回结果也在response.items中
                List<Item> items = new ArrayList<>();
                request.setItems(items);
                Item item = new Item();
                items.add(item);
                item.setItemId("itemId1");
                Map<String, String> item1Features = new HashMap<>();
                item.setFeatures(item1Features);
                item1Features.put("data", data);
                log.info("request : {}", JSON.toJSONString(request));

                MayaResponse response;
                try {
                    response = mayaClient.call(request);
                } catch (Exception e) {
                    throw new SutException(String.format("请求maya模型%s-%s异常, %s", "codegpt_single_finetune_v7", "v7", e.getMessage()));
                }
                String responseJson = JSON.toJSONString(response);
                log.info("response : {}", responseJson);
                if (response.getErrorCode() == 0) {
                    log.info("process success : {}", JSON.toJSONString(response.getResults()));
                    return response.getItems().get(0).getAttributes().get("res");
                } else {
                    log.error("process failed: error name {}, error message {}", response.getErrorName(),
                        response.getErrorMsg());
                    throw new SutException(String.format("请求maya模型%s-%s异常, error name: %s, error message: %s",
                        "codegpt_single_finetune_v7", "v7",
                        response.getErrorName(), response.getErrorMsg()));
                }
            }

            @Override
            public String getInferenceResult(Map<String, String> itemMap, MayaClientReqParam mayaClientReqParam, boolean isCodefuseModel) {
                MayaClientConfig config = new MayaClientConfig();
                // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
                config.setAppName("smartunitmng");
                MayaClient mayaClient = MayaClient.getInstance(config);
                // 构造请求
                MayaRequest request = getMayaRequest(itemMap, mayaClientReqParam);
                log.info("maya client request : {}", request);

                MayaResponse response;
                // 线下环境无法调用maya client，这里进行mock方便功能调试
                if("dev".equals(dbmode) || "test".equals(dbmode)) {
                    response = getFakeMayaResponse(isCodefuseModel, 0);
                } else {
                    try {
                        response = mayaClient.call(request);
                    } catch (Exception e) {
                        throw (SutException)new SutException(String.format("请求maya模型%s-%s异常, %s", mayaClientReqParam.getSceneName(),
                            mayaClientReqParam.getChainName(), e.getMessage())).initCause(e);
                    }
                }
                String responseJson = JSON.toJSONString(response);
                log.info("请求模型: {}, response : {}", mayaClientReqParam.getSceneName(), responseJson);


                if (response.getErrorCode() != 0) {
                    log.error("process failed: error name {}, error message {}", response.getErrorName(),
                        response.getErrorMsg());
                    throw new SutException(String.format("请求maya模型%s-%s异常, error name: %s, error message: %s",
                        mayaClientReqParam.getSceneName(), mayaClientReqParam.getChainName(),
                        response.getErrorName(), response.getErrorMsg()));
                } else {
                    // 不同模型结果不一样，codefuse模型返回值为res
                    if(isCodefuseModel) {
                        return response.getItems().get(0).getAttributes().get("res");
                    } else {
                        // latency	TYPE_FP32	-
                        // errorMessage	TYPE_STRING	-
                        // answer	TYPE_STRING	-
                        // answers	TYPE_STRING	-
                        // resultCode	TYPE_INT32
                        // TODO: 考虑用模板怎么搞
                        return getOpensourceModelOutput(mayaClientReqParam, response);
                    }
                }
            }

            @Override
            public String getStreamResult(Map<String, String> itemMap,
                                          MayaClientReqParam mayaClientReqParam,
                                          boolean isCodefuseModel,
                                          HttpServletResponse httpServletResponse) {
                return getStreamResult(itemMap, mayaClientReqParam, isCodefuseModel, httpServletResponse, true);
            }

            @Override
            public String getStreamResult(Map<String, String> itemMap,
                                          MayaClientReqParam mayaClientReqParam,
                                          boolean isCodefuseModel,
                                          HttpServletResponse httpServletResponse,
                                          boolean frontStreamMode) {

                MayaClientConfig config = new MayaClientConfig();
                // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
                config.setAppName("smartunitmng");
                MayaClient mayaClient = MayaClient.getInstance(config);
                // 构造请求
                MayaRequest request = getMayaRequest(itemMap, mayaClientReqParam);
                // 流式调用必须使用grpc协议
                request.setRpcMode(RpcMode.GRPC);
                // 流式响应缓存
                ModelStreamBuffer modelStreamBuffer = new ModelStreamBuffer();
                log.info("maya client stream request : {}", request);
                // 超时控制
                final CountDownLatch finishLatch = new CountDownLatch(1);
                if(frontStreamMode) {
                    httpServletResponse.setContentType("text/event-stream");
                    httpServletResponse.setHeader("Cache-Control", "no-cache");
                    httpServletResponse.setCharacterEncoding("UTF-8");
                }

                // 总超时时间
                long outputAllTime = DEFAULT_STREAM_ALL_TIME;
                if(itemMap.containsKey("stream_all_timeout")) {
                    outputAllTime = Long.parseLong(itemMap.get("stream_all_timeout"));
                }

                // 本地测试，fake结果
                if("dev".equals(dbmode) || "test".equals(dbmode)) {
                    for(int index = 0; index < 10; ++index) {
                        MayaResponse response = getFakeMayaResponse(isCodefuseModel, index);
                        parseStreamMayaResponseObj(response, mayaClientReqParam, isCodefuseModel, httpServletResponse, modelStreamBuffer, frontStreamMode);
                        try {
                            Thread.sleep(1000);
                        } catch (Exception e) {
                            log.error("fake result sleep error: ", e);
                        }
                    }

                    // fake finish
                    try {
                        String contentData = "[DONE]";
                        JSONObject contentJson = new JSONObject();
                        contentJson.put("content", contentData);
                        if(frontStreamMode) {
                            flushSseResponse(httpServletResponse, contentJson.toJSONString());
                        }
                    } catch (IOException ioe) {
                        log.warn("GPT: fake stream result close exception: ", ioe);
                    }
                    finishLatch.countDown();

                } else {
                    mayaClient.modelStreamInfer(request, new MayaStreamObserver<MayaResponse>() {
                        @Override
                        public void onNext(MayaResponse response) {
                            parseStreamMayaResponseObj(response, mayaClientReqParam, isCodefuseModel, httpServletResponse, modelStreamBuffer, frontStreamMode);
                        }

                        @Override
                        public void onError(Throwable t) {
                            log.error("request maya stream onError failed", t);
                            try {
                                String errMsg = "[llm output error]";
                                JSONObject contentJson = new JSONObject();
                                contentJson.put("content", errMsg);
                                modelStreamBuffer.buffer(errMsg);
                                if(frontStreamMode) {
                                    flushSseResponse(httpServletResponse, contentJson.toJSONString());
                                }
                            } catch (IOException e) {
                                log.error("request maya stream err close httpServletResponse failed", e);
                            }
                            finishLatch.countDown();
                        }

                        @Override
                        public void onCompleted() {
                            try {
                                String contentData = "[DONE]";
                                JSONObject contentJson = new JSONObject();
                                contentJson.put("content", contentData);
                                if(frontStreamMode) {
                                    flushSseResponse(httpServletResponse, contentJson.toJSONString());
                                }
                            } catch (IOException e) {
                                log.error("request maya stream complete close httpServletResponse failed", e);
                            }
                            finishLatch.countDown();
                        }
                    });
                }

                boolean result = false;
                try {
                    result = finishLatch.await(outputAllTime, TimeUnit.MILLISECONDS);
                } catch (InterruptedException e) {
                    log.error("request maya await failed",e);
                }
                if (!result) {
                    try {
                        String timeOutMsg = "[llm output timeout]";
                        JSONObject contentJson = new JSONObject();
                        contentJson.put("content", timeOutMsg);
                        modelStreamBuffer.buffer(timeOutMsg);
                        if(frontStreamMode) {
                            flushSseResponse(httpServletResponse, contentJson.toJSONString());
                        }
                    } catch (IOException e) {
                        log.error("request maya close httpServletResponse failed 2: ", e);
                    }
                    log.error("request maya timeout");
                }

                // stream输出结束后，需要把所有输出结果收集保存起来
                return modelStreamBuffer.getBufferContent();
            }

            /**
             * 构造maya client request
             * @param itemMap                 请求内容
             * @param mayaClientReqParam      client相关参数
             * @return                        maya请求对象
             */
            private static MayaRequest getMayaRequest(Map<String, String> itemMap, MayaClientReqParam mayaClientReqParam) {
                MayaRequest request = new MayaRequest();
                // 业务场景名,必填,需要根据服务名寻址
                request.setSceneName(mayaClientReqParam.getSceneName());
                // 必填,服务版本会作为url路径http
                request.setChainName(mayaClientReqParam.getChainName());
                // 服务端类型,默认ModelServerType.MAYA
                request.setServerType(ModelServerType.MAYA);
                // 整体超时时间,不设置默认从MayaClientConfig里获取是600ms
                request.setRequestTimeOut(mayaClientReqParam.getRequestTimeOut());
                // 可选,建立连接超时时间,不设置默认从MayaClientConfig里获取是100ms
                request.setConnectTimeOut(mayaClientReqParam.getConnectionTimeOut());
                // 可选,等待服务端返回超时时间,不设置默认从MayaClientConfig里获取是500ms
                request.setReadTimeOut(mayaClientReqParam.getReadTimeOut());

                List<Item> items = new ArrayList<>();
                request.setItems(items);
                Item item = new Item();
                items.add(item);
                item.setItemId("itemId1");
                item.setFeatures(itemMap);
                return request;
            }

            private String getOpensourceModelOutput(MayaClientReqParam mayaClientReqParam, MayaResponse response) {
                Map<String, String> result = response.getItems().get(0).getAttributes();
                String answers = result.get("answers");
                if(null == answers) {
                    log.error("maya client: parse answers error, is null.");
                    throw new SutException(String.format("处理maya模型 %s-%s 响应异常,  parse answers error, is null.",
                        mayaClientReqParam.getSceneName(), mayaClientReqParam.getChainName()));
                }
                JSONObject ansObj = JSONObject.parseObject(answers);
                JSONObject firstChoice = ansObj.getJSONArray("choices").getJSONObject(0);
                // 流式返回值为delta，同步的为message
                if(firstChoice.containsKey("delta")) {
                    return firstChoice.getJSONObject("delta").getString("content");
                } else if(firstChoice.containsKey("message")) {
                    return firstChoice.getJSONObject("message").getString("content");
                } else {
                    log.error("maya client: parse content error.");
                    throw new SutException(String.format("处理maya模型 %s-%s 响应异常, parse content error.",
                        mayaClientReqParam.getSceneName(), mayaClientReqParam.getChainName()));
                }
            }

            private static MayaResponse getFakeMayaResponse(boolean isCodefuseModel, int index) {
                MayaResponse response;
                response = new MayaResponse();
                response.setErrorCode(0);
                List<Item> itemListMock = new ArrayList<>();
                response.setItems(itemListMock);
                Item itemMock = new Item();
                itemListMock.add(itemMock);
                Map<String, String> attrMock = new HashMap<>();
                itemMock.setAttributes(attrMock);
                if(isCodefuseModel) {
                    JSONObject resJson = new JSONObject();
                    JSONArray generateCode = new JSONArray();
                    JSONArray innerArray = new JSONArray();
                    innerArray.add("codefuse mode fake result " + index);
                    generateCode.add(innerArray);
                    resJson.put("generated_code", generateCode);
                    attrMock.put("res", resJson.toJSONString());
                } else {
                    String latencyMock = "5";
                    String errMsgMock = "ok";
                    JSONObject answersJsonMock = new JSONObject();
                    JSONArray choicesMock = new JSONArray();
                    answersJsonMock.put("choices", choicesMock);
                    JSONObject oneChoiceMock = new JSONObject();
                    choicesMock.add(oneChoiceMock);
                    JSONObject msgJsonMock = new JSONObject();
                    oneChoiceMock.put("message", msgJsonMock);
                    msgJsonMock.put("content", "opensource model fake result " + index + "\n");
                    attrMock.put("latency", latencyMock);
                    attrMock.put("errorMessage", errMsgMock);
                    attrMock.put("answer", answersJsonMock.toJSONString());
                    attrMock.put("answers", answersJsonMock.toJSONString());
                    attrMock.put("resultCode", "0");
                }
                return response;
            }

            private void parseStreamMayaResponseObj(MayaResponse response,
                                                    MayaClientReqParam mayaClientReqParam,
                                                    boolean isCodefuseModel,
                                                    HttpServletResponse httpServletResponse,
                                                    ModelStreamBuffer modelStreamBuffer,
                                                    boolean frontStreamMode) {
                if (response.getErrorCode() == 0) {
                    log.info("request maya process success:{}", JSON.toJSONString(response));
                    Map<String, String> responseMap = response.getItems().get(0).getAttributes();
                    String content = "";
                    if (isCodefuseModel) {
                        String res = responseMap.get("res");
                        Optional<JSONObject> responseJson = Optional.of(JSON.parseObject(res));
                        String generatedCode = responseJson.map(retJson ->
                            retJson.getJSONArray("generated_code").getJSONArray(0).getString(0)).orElse(null);
                        if (generatedCode == null) {
                            content = "model output is null";
                        } else {
                            //临时处理，后续模型会修复
                            if (generatedCode.contains("<human>: ")) {
                                content = generatedCode.split("<human>: ")[0];
                            }
                            if (generatedCode.contains("<bot>: ")) {
                                content = generatedCode.split("<bot>: ")[0];
                            }
                        }
                    } else {
                        // latency	TYPE_FP32	-
                        // errorMessage	TYPE_STRING	-
                        // answer	TYPE_STRING	-
                        // answers	TYPE_STRING	-
                        // resultCode	TYPE_INT32
                        // TODO: 考虑用模板怎么搞
                        content = getOpensourceModelOutput(mayaClientReqParam, response);
                    }

                    try {
                        modelStreamBuffer.buffer(content);
                        JSONObject jsonData = new JSONObject();
                        jsonData.put("content", content);
                        if(frontStreamMode) {
                            flushSseResponse(httpServletResponse, jsonData.toJSONString());
                        }
                        //httpServletResponse.getWriter().write(content);
                        //httpServletResponse.getWriter().flush();
                        // 本地测试，fake结果
                        if("dev".equals(dbmode) || "test".equals(dbmode)) {
                            log.info("GPT: stream delta content is: {}", content);
                        }
                    } catch (Exception e) {
                        log.error("request maya stream flush failed: ", e);
                    }
                } else {
                    // 服务框架返回错误码
                    response.getErrorCode();
                    // 服务端返回错误名
                    response.getErrorName();
                    // 服务端返回异常信息
                    response.getErrorMsg();
                    // 服务端返回debug信息
                    response.getDebugMsg();
                    // python 节点自定义错误码
                    response.getAlgoRet();
                    // python 节点的自定义异常日志输出
                    response.getAlgoMsg();
                    log.error("process failed: error name {}, error message {}", response.getErrorName(),
                        response.getErrorMsg());
                }
            }

            /**
             * 测试使用，设置dbMode
             * @param value   dbMode修改值
             */
            public void setDbModeForTest(String value) {
                dbmode = value;
            }

            /**
             * 测试使用，设置dbMode
             * @return     dbMode实际值
             */

            public String getDbModeForTest() {
                return dbmode;
            }

            private void flushSseResponse(HttpServletResponse httpServletResponse, String data) throws IOException {
                httpServletResponse.getWriter().write("data: ");
                httpServletResponse.getWriter().write(data);
                httpServletResponse.getWriter().write("\n\n");
                httpServletResponse.getWriter().flush();
            }

        }
        "#;
        let test_class_str = r#"
        package smartunit.com.alipay.sut.service.gpt.impl;

        import org.junit.Test;
        import static org.junit.Assert.*;
        import java.lang.reflect.Method;
        import static org.smartunit.shaded.org.mockito.Mockito.*;
        import org.smartunit.shaded.org.mockito.ArgumentMatchers;
        import static org.smartunit.runtime.SmartAssertions.*;
        import com.alipay.arks.client.Item;
        import com.alipay.maya.model.MayaResponse;
        import com.alipay.sut.model.gpt.MayaClientReqParam;
        import com.alipay.sut.model.gpt.ModelStreamBuffer;
        import com.alipay.sut.service.gpt.impl.MayaServiceImpl;
        import java.util.Map;
        import java.util.Stack;
        import java.util.Vector;
        import javax.servlet.http.HttpServletResponse;
        import org.junit.runner.RunWith;
        import org.smartunit.runtime.ExecutorServiceAnswer;
        import org.smartunit.runtime.PrivateAccess;
        import org.smartunit.runtime.SmartRunner;
        import org.smartunit.runtime.SmartRunnerParameters;
        import org.smartunit.runtime.TransactionTemplateAnswer;
        import org.smartunit.runtime.ViolatedAssumptionAnswer;

        @RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true)
        public class MayaServiceImpl_SSTest extends MayaServiceImpl_SSTest_scaffolding {
        // allCoveredLines:[41, 56, 58, 59, 107, 109, 110, 159, 161, 162, 261, 263, 265, 267, 269, 271, 273, 275, 276, 277, 278, 279, 280, 281, 285, 286, 287, 288, 289, 290, 292, 293, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 341, 349, 350, 351, 352, 353, 354, 355, 376, 392, 394, 396, 398, 400, 402, 403, 404, 406]

          @Test(timeout = 4000)
          public void test_getFakeMayaResponse_0()  throws Throwable  {
              //caseID:6229364b2b584e2a06516cffec26a520
              //CoveredLines: [41, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 341]
              //Input_0_boolean: true
              //Input_1_int: 0
              //Assert: assertEquals(0, method_result.getErrorCode());

              MayaServiceImpl mayaServiceImpl0 = new MayaServiceImpl();

              //Call method: getFakeMayaResponse
              MayaResponse mayaResponse0 = (MayaResponse)PrivateAccess.callMethod((Class<MayaServiceImpl>) MayaServiceImpl.class, mayaServiceImpl0, "getFakeMayaResponse", (Object) true, (Class<?>) boolean.class, (Object) 0, (Class<?>) int.class);

              //Test Result Assert
              assertEquals(0, mayaResponse0.getErrorCode());
          }

          @Test(timeout = 4000)
          public void test_getFakeMayaResponse_1()  throws Throwable  {
              //caseID:68f444426705ee1737dca2b18ff3a60f
              //CoveredLines: [41, 308, 309, 310, 311, 312, 313, 314, 315, 316, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 341]
              //Input_0_boolean: false
              //Input_1_int: 0
              //Assert: assertTrue(method_result.isSuccess());

              MayaServiceImpl mayaServiceImpl0 = new MayaServiceImpl();

              //Call method: getFakeMayaResponse
              MayaResponse mayaResponse0 = (MayaResponse)PrivateAccess.callMethod((Class<MayaServiceImpl>) MayaServiceImpl.class, mayaServiceImpl0, "getFakeMayaResponse", (Object) false, (Class<?>) boolean.class, (Object) 0, (Class<?>) int.class);

              //Test Result Assert
              assertTrue(mayaResponse0.isSuccess());
          }

          @Test(timeout = 4000)
          public void test_getInferenceResult_2()  throws Throwable  {
              //caseID:8b380d888abbbbde6ae00ceb6d2c73dc
              //CoveredLines: [41, 107, 109, 110]
              //Input_0_Map<String, String>: {}
              //Input_1_MayaClientReqParam: {}
              //Input_2_boolean: false

              MayaServiceImpl mayaServiceImpl0 = new MayaServiceImpl();
              //mock map0
              Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              //mock mayaClientReqParam0
              MayaClientReqParam mayaClientReqParam0 = mock(MayaClientReqParam.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());

              //Call method: getInferenceResult
              // Undeclared exception!
              try {
                mayaServiceImpl0.getInferenceResult(map0, mayaClientReqParam0, false);
              } catch(Throwable e) {
                 verifyException("java.util.concurrent.ThreadPoolExecutor", e);
                 assertEquals("java.lang.IllegalArgumentException", e.getClass().getName());
              }
          }

          @Test(timeout = 4000)
          public void test_getInferenceResult_3()  throws Throwable  {
              //caseID:bf05e74478d99c0f0730880cee15ef9b
              //CoveredLines: [41, 56, 58, 59]
              //Input_0_String: 1

              MayaServiceImpl mayaServiceImpl0 = new MayaServiceImpl();

              //Call method: getInferenceResult
              // Undeclared exception!
              try {
                mayaServiceImpl0.getInferenceResult("1");
              } catch(Throwable e) {
                 verifyException("java.util.concurrent.ThreadPoolExecutor", e);
                 assertEquals("java.lang.IllegalArgumentException", e.getClass().getName());
              }
          }

          @Test(timeout = 4000)
          public void testgetMaya1212Request4()  throws Throwable  {
              //caseID:e63589a169a75ce18721cd987fc90e16
              //CoveredLines: [41, 261, 263, 265, 267, 269, 271, 273, 275, 276, 277, 278, 279, 280, 281]
              //Input_0_Map<String, String>: {}
              //Input_1_com.alipay.sut.model.gpt.MayaClientReqParam: {}

              MayaServiceImpl mayaServiceImpl0 = new MayaServiceImpl();
              //mock map0
              Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              //mock mayaClientReqParam0
              MayaClientReqParam mayaClientReqParam0 = mock(MayaClientReqParam.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());

              //Call method: getMayaRequest
              PrivateAccess.callMethod((Class<MayaServiceImpl>) MayaServiceImpl.class, mayaServiceImpl0, "getMayaRequest", (Object) map0, (Class<?>) Map.class, (Object) mayaClientReqParam0, (Class<?>) MayaClientReqParam.class);
          }

          @Test(timeout = 4000)
          public void test_getOpensourceModelOutput_5()  throws Throwable  {
              //caseID:c3bc0a72237538e41a28c2ecc52d192c
              //CoveredLines: [41, 285, 286, 287, 292, 293]
              //Input_0_com.alipay.sut.model.gpt.MayaClientReqParam: {}
              //Input_1_com.alipay.maya.model.MayaResponse: {getItems=vector0}

              MayaServiceImpl mayaServiceImpl0 = new MayaServiceImpl();
              //mock mayaClientReqParam0
              MayaClientReqParam mayaClientReqParam0 = mock(MayaClientReqParam.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              Vector<Item> vector0 = new Vector<Item>();
              //mock map0
              Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              doReturn("{}").when(map0).get(any());
              //mock item0
              Item item0 = mock(Item.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              doReturn(map0).when(item0).getAttributes();

              vector0.add(item0);
              //mock mayaResponse0
              MayaResponse mayaResponse0 = mock(MayaResponse.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              doReturn(vector0).when(mayaResponse0).getItems();

              //Call method: getOpensourceModelOutput
              try {
                PrivateAccess.callMethod((Class<MayaServiceImpl>) MayaServiceImpl.class, mayaServiceImpl0, "getOpensourceModelOutput", (Object) mayaClientReqParam0, (Class<?>) MayaClientReqParam.class, (Object) mayaResponse0, (Class<?>) MayaResponse.class);
              } catch(Throwable e) {
                 verifyException("com.alipay.sut.service.gpt.impl.MayaServiceImpl", e);
                 assertEquals("java.lang.NullPointerException", e.getClass().getName());
              }
          }

          @Test(timeout = 4000)
          public void test_parseStreamMayaResponseObj_7()  throws Throwable  {
              //caseID:8b9e35e8274726c5c2a6f4b5dde21803
              //CoveredLines: [41, 349, 350, 351, 352, 353, 354, 355]
              //Input_0_com.alipay.maya.model.MayaResponse: {getItems=stack0, getErrorCode=0}
              //Input_1_com.alipay.sut.model.gpt.MayaClientReqParam: {}
              //Input_2_boolean: true
              //Input_3_HttpServletResponse: {}
              //Input_4_com.alipay.sut.model.gpt.ModelStreamBuffer: {}

              MayaServiceImpl mayaServiceImpl0 = new MayaServiceImpl();
              Stack<Item> stack0 = new Stack<Item>();
              //mock map0
              Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              doReturn((String) null).when(map0).get(any());
              //mock item0
              Item item0 = mock(Item.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              doReturn(map0).when(item0).getAttributes();

              stack0.add(item0);
              //mock mayaResponse0
              MayaResponse mayaResponse0 = mock(MayaResponse.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              doReturn(0).when(mayaResponse0).getErrorCode();
              doReturn(stack0).when(mayaResponse0).getItems();
              //mock mayaClientReqParam0
              MayaClientReqParam mayaClientReqParam0 = mock(MayaClientReqParam.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              //mock httpServletResponse0
              HttpServletResponse httpServletResponse0 = mock(HttpServletResponse.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              //mock modelStreamBuffer0
              ModelStreamBuffer modelStreamBuffer0 = mock(ModelStreamBuffer.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());

              //Call method: parseStreamMayaResponseObj
              try {
                PrivateAccess.callMethod((Class<MayaServiceImpl>) MayaServiceImpl.class, mayaServiceImpl0, "parseStreamMayaResponseObj", (Object) mayaResponse0, (Class<?>) MayaResponse.class, (Object) mayaClientReqParam0, (Class<?>) MayaClientReqParam.class, (Object) true, (Class<?>) boolean.class, (Object) httpServletResponse0, (Class<?>) HttpServletResponse.class, (Object) modelStreamBuffer0, (Class<?>) ModelStreamBuffer.class, false, boolean.class);
              } catch(Throwable e) {
                 verifyException("java.util.Objects", e);
                 assertEquals("java.lang.NullPointerException", e.getClass().getName());
              }
          }

          @Test(timeout = 4000)
          public void test_parseStreamMayaResponseObj_8()  throws Throwable  {
              //caseID:9ed8bc560c6318d6cf108c56b4c0085b
              //CoveredLines: [41, 349, 392, 394, 396, 398, 400, 402, 403, 404, 406]
              //Input_0_com.alipay.maya.model.MayaResponse: {getItems=stack0, getErrorCode=12000}
              //Input_1_com.alipay.sut.model.gpt.MayaClientReqParam: {}
              //Input_2_boolean: true
              //Input_3_HttpServletResponse: {}
              //Input_4_com.alipay.sut.model.gpt.ModelStreamBuffer: {}

              MayaServiceImpl mayaServiceImpl0 = new MayaServiceImpl();
              Stack<Item> stack0 = new Stack<Item>();
              //mock mayaResponse0
              MayaResponse mayaResponse0 = mock(MayaResponse.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              doReturn(12000).when(mayaResponse0).getErrorCode();
              doReturn(stack0).when(mayaResponse0).getItems();
              //mock mayaClientReqParam0
              MayaClientReqParam mayaClientReqParam0 = mock(MayaClientReqParam.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              //mock httpServletResponse0
              HttpServletResponse httpServletResponse0 = mock(HttpServletResponse.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              //mock modelStreamBuffer0
              ModelStreamBuffer modelStreamBuffer0 = mock(ModelStreamBuffer.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());

              //Call method: parseStreamMayaResponseObj
              PrivateAccess.callMethod((Class<MayaServiceImpl>) MayaServiceImpl.class, mayaServiceImpl0, "parseStreamMayaResponseObj", (Object) mayaResponse0, (Class<?>) MayaResponse.class, (Object) mayaClientReqParam0, (Class<?>) MayaClientReqParam.class, (Object) true, (Class<?>) boolean.class, (Object) httpServletResponse0, (Class<?>) HttpServletResponse.class, (Object) modelStreamBuffer0, (Class<?>) ModelStreamBuffer.class, false, boolean.class);
          }

          @Test(timeout = 4000)
          public void test_parseStreamMayaResponseObj_9()  throws Throwable  {
              //caseID:85440d0045b02a03d53e9271b3913f52
              //CoveredLines: [41, 285, 286, 287, 288, 289, 290, 349, 350, 351, 352, 353, 376]
              //Input_0_com.alipay.maya.model.MayaResponse: {getItems=stack0 stack0, getErrorCode=0}
              //Input_1_com.alipay.sut.model.gpt.MayaClientReqParam: {getChainName=(String) null, getSceneName=\"org.springframework.util.ConcurrentReferenceHashMap$4\"}
              //Input_2_boolean: false
              //Input_3_HttpServletResponse: {}
              //Input_4_com.alipay.sut.model.gpt.ModelStreamBuffer: {}

              MayaServiceImpl mayaServiceImpl0 = new MayaServiceImpl();
              Stack<Item> stack0 = new Stack<Item>();
              //mock map0
              Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              doReturn((String) null).when(map0).get(any());
              //mock map1
              Map<String, String> map1 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              //mock item0
              Item item0 = mock(Item.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              doReturn(map1, map0).when(item0).getAttributes();

              stack0.add(item0);
              //mock mayaResponse0
              MayaResponse mayaResponse0 = mock(MayaResponse.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              doReturn(0).when(mayaResponse0).getErrorCode();
              doReturn(stack0, stack0).when(mayaResponse0).getItems();
              //mock mayaClientReqParam0
              MayaClientReqParam mayaClientReqParam0 = mock(MayaClientReqParam.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              doReturn((String) null).when(mayaClientReqParam0).getChainName();
              doReturn("org.springframework.util.ConcurrentReferenceHashMap$4").when(mayaClientReqParam0).getSceneName();
              //mock httpServletResponse0
              HttpServletResponse httpServletResponse0 = mock(HttpServletResponse.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              //mock modelStreamBuffer0
              ModelStreamBuffer modelStreamBuffer0 = mock(ModelStreamBuffer.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());

              //Call method: parseStreamMayaResponseObj
              try {
                PrivateAccess.callMethod((Class<MayaServiceImpl>) MayaServiceImpl.class, mayaServiceImpl0, "parseStreamMayaResponseObj", (Object) mayaResponse0, (Class<?>) MayaResponse.class, (Object) mayaClientReqParam0, (Class<?>) MayaClientReqParam.class, (Object) false, (Class<?>) boolean.class, (Object) httpServletResponse0, (Class<?>) HttpServletResponse.class, (Object) modelStreamBuffer0, (Class<?>) ModelStreamBuffer.class, true, boolean.class);
              } catch(Throwable e) {
                 verifyException("com.alipay.sut.service.gpt.impl.MayaServiceImpl", e);
                 assertEquals("com.alipay.sut.utils.exception.SutException", e.getClass().getName());
                 assertEquals("\u5904\u7406maya\u6A21\u578B org.springframework.util.ConcurrentReferenceHashMap$4-null \u54CD\u5E94\u5F02\u5E38,  parse answers error, is null.", e.getMessage());
              }
          }
        }
        "#;

        let case_selected_str = r#"
          @Test(timeout = 4000)
          public void testgetMaya1212Request4()  throws Throwable  {
              //caseID:e63589a169a75ce18721cd987fc90e16
              //CoveredLines: [41, 261, 263, 265, 267, 269, 271, 273, 275, 276, 277, 278, 279, 280, 281]
              //Input_0_Map<String, String>: {}
              //Input_1_com.alipay.sut.model.gpt.MayaClientReqParam: {}

              MayaServiceImpl mayaServiceImpl0 = new MayaServiceImpl();
              //mock map0
              Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
              //mock mayaClientReqParam0
              MayaClientReqParam mayaClientReqParam0 = mock(MayaClientReqParam.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());

              //Call method: getMayaRequest
              PrivateAccess.callMethod((Class<MayaServiceImpl>) MayaServiceImpl.class, mayaServiceImpl0, "getMayaRequest", (Object) map0, (Class<?>) Map.class, (Object) mayaClientReqParam0, (Class<?>) MayaClientReqParam.class);
          }
        "#;

        let cut_info = parse_class_text(cut_str);

        let mut_body_ret = get_mut_from_cut_test_class_info(&cut_info, case_selected_str.to_string());
        match mut_body_ret {
            Some(result_str) => {
                println!("result_str:{}", result_str);
                assert!(result_str.contains("private static MayaRequest getMayaRequest(Map<String, String> itemMap"));
            }
            None => {
                assert!(false);
            }
        }
    }
}
use std::path::Path;
use tokio::fs as async_fs;
use encoding_rs::{Encoding, UTF_8, GBK, GB18030};
use chardet::detect;
use log::{debug, warn, info};

/// 支持的编码类型
#[derive(Debug, <PERSON>lone)]
pub enum SupportedEncoding {
    Utf8,
    Gbk,
    Gb18030,
    Gb2312,
    Unknown(String),
}

impl SupportedEncoding {
    /// 从编码名称字符串创建编码类型
    pub fn from_name(name: &str) -> Self {
        let name_lower = name.to_lowercase();
        match name_lower.as_str() {
            "utf-8" | "utf8" => Self::Utf8,
            "gbk" => Self::Gbk,
            "gb18030" => Self::Gb18030,
            "gb2312" => Self::Gb2312,
            _ => Self::Unknown(name.to_string()),
        }
    }

    /// 获取对应的 encoding_rs 编码
    pub fn to_encoding_rs(&self) -> &'static Encoding {
        match self {
            Self::Utf8 => UTF_8,
            Self::Gbk | Self::Gb2312 => GBK, // GB2312 是 GBK 的子集
            Self::Gb18030 => GB18030,
            Self::Unknown(_) => UTF_8, // 默认使用 UTF-8
        }
    }
}

/// 检测文件编码（异步版本）
pub async fn detect_file_encoding(file_path: &str) -> Result<SupportedEncoding, String> {
    // 读取文件的前几KB来检测编码
    let sample_size = 8192; // 8KB 样本

    let bytes = match async_fs::read(file_path).await {
        Ok(content) => {
            if content.len() > sample_size {
                content[..sample_size].to_vec()
            } else {
                content
            }
        }
        Err(e) => {
            return Err(format!("读取文件失败: {} - {}", file_path, e));
        }
    };

    detect_encoding_from_bytes(file_path, &bytes)
}

/// 检测文件编码（同步版本）
pub fn detect_file_encoding_sync(file_path: &str) -> Result<SupportedEncoding, String> {
    // 读取文件的前几KB来检测编码
    let sample_size = 8192; // 8KB 样本

    let bytes = match std::fs::read(file_path) {
        Ok(content) => {
            if content.len() > sample_size {
                content[..sample_size].to_vec()
            } else {
                content
            }
        }
        Err(e) => {
            return Err(format!("读取文件失败: {} - {}", file_path, e));
        }
    };

    detect_encoding_from_bytes(file_path, &bytes)
}

/// 从字节数组检测编码
fn detect_encoding_from_bytes(file_path: &str, bytes: &[u8]) -> Result<SupportedEncoding, String> {
    // 如果文件为空，默认为UTF-8
    if bytes.is_empty() {
        return Ok(SupportedEncoding::Utf8);
    }

    // 首先检查是否为有效的UTF-8
    if std::str::from_utf8(bytes).is_ok() {
        debug!("文件 {} 检测为 UTF-8 编码", file_path);
        return Ok(SupportedEncoding::Utf8);
    }

    // 使用 chardet 检测编码
    let (charset, confidence, _language) = detect(bytes);
    info!("文件 {} 检测编码: {} (置信度: {:.2})", file_path, charset, confidence);

    // 只有在置信度足够高时才使用检测结果
    if confidence > 0.7 {
        Ok(SupportedEncoding::from_name(&charset))
    } else {
        warn!("文件 {} 编码检测置信度较低 ({:.2})，使用 GBK 作为默认编码", file_path, confidence);
        Ok(SupportedEncoding::Gbk)
    }
}

/// 使用指定编码读取文件内容为字符串（异步版本）
pub async fn read_file_with_encoding(file_path: &str, encoding: &SupportedEncoding) -> Result<String, String> {
    let bytes = match async_fs::read(file_path).await {
        Ok(content) => content,
        Err(e) => {
            return Err(format!("读取文件失败: {} - {}", file_path, e));
        }
    };

    decode_bytes_with_encoding(file_path, &bytes, encoding)
}

/// 使用指定编码读取文件内容为字符串（同步版本）
pub fn read_file_with_encoding_sync(file_path: &str, encoding: &SupportedEncoding) -> Result<String, String> {
    let bytes = match std::fs::read(file_path) {
        Ok(content) => content,
        Err(e) => {
            return Err(format!("读取文件失败: {} - {}", file_path, e));
        }
    };

    decode_bytes_with_encoding(file_path, &bytes, encoding)
}

/// 使用指定编码解码字节数组
fn decode_bytes_with_encoding(file_path: &str, bytes: &[u8], encoding: &SupportedEncoding) -> Result<String, String> {
    if bytes.is_empty() {
        return Ok(String::new());
    }

    let encoding_rs = encoding.to_encoding_rs();
    let (decoded, _, had_errors) = encoding_rs.decode(bytes);

    if had_errors {
        warn!("文件 {} 使用 {:?} 编码解码时出现错误，但继续处理", file_path, encoding);
    }

    debug!("成功使用 {:?} 编码读取文件: {}", encoding, file_path);
    Ok(decoded.into_owned())
}

/// 智能读取文件内容，自动检测编码（异步版本）
pub async fn read_file_smart(file_path: &str) -> Result<String, String> {
    debug!("开始智能读取文件: {}", file_path);

    // 首先尝试直接用UTF-8读取
    match async_fs::read_to_string(file_path).await {
        Ok(content) => {
            debug!("文件 {} 成功使用 UTF-8 读取", file_path);
            return Ok(content);
        }
        Err(_) => {
            debug!("文件 {} UTF-8 读取失败，开始编码检测", file_path);
        }
    }

    // UTF-8读取失败，进行编码检测
    let detected_encoding = detect_file_encoding(file_path).await?;

    // 使用检测到的编码读取文件
    read_file_with_encoding(file_path, &detected_encoding).await
}

/// 智能读取文件内容，自动检测编码（同步版本）
pub fn read_file_smart_sync(file_path: &str) -> Result<String, String> {
    debug!("开始智能读取文件（同步）: {}", file_path);

    // 首先尝试直接用UTF-8读取
    match std::fs::read_to_string(file_path) {
        Ok(content) => {
            debug!("文件 {} 成功使用 UTF-8 读取", file_path);
            return Ok(content);
        }
        Err(_) => {
            debug!("文件 {} UTF-8 读取失败，开始编码检测", file_path);
        }
    }

    // UTF-8读取失败，进行编码检测
    let detected_encoding = detect_file_encoding_sync(file_path)?;

    // 使用检测到的编码读取文件
    read_file_with_encoding_sync(file_path, &detected_encoding)
}

/// 批量智能读取多个文件
pub async fn read_files_smart_batch(file_paths: &[String]) -> Vec<(String, Result<String, String>)> {
    let mut results = Vec::new();
    
    for file_path in file_paths {
        let content_result = read_file_smart(file_path).await;
        results.push((file_path.clone(), content_result));
    }
    
    results
}

/// 检查文件是否可能是文本文件（基于扩展名）
pub fn is_likely_text_file(file_path: &str) -> bool {
    let path = Path::new(file_path);
    
    if let Some(extension) = path.extension() {
        let ext = extension.to_string_lossy().to_lowercase();
        
        // 常见的文本文件扩展名
        matches!(ext.as_str(),
            "txt" | "md" | "rst" | "log" | "cfg" | "conf" | "ini" | "properties" |
            "java" | "kt" | "scala" | "groovy" |
            "js" | "ts" | "jsx" | "tsx" | "vue" | "html" | "htm" | "css" | "scss" | "sass" | "less" |
            "py" | "pyx" | "pyi" | "pyw" |
            "rs" | "toml" |
            "c" | "cpp" | "cc" | "cxx" | "h" | "hpp" | "hxx" |
            "cs" | "vb" | "fs" |
            "go" | "mod" | "sum" |
            "php" | "rb" | "pl" | "sh" | "bash" | "zsh" | "fish" |
            "sql" | "xml" | "json" | "yaml" | "yml" |
            "dockerfile" | "makefile" | "cmake" |
            "r" | "rmd" | "m" | "swift" | "dart" | "lua" |
            "gradle" | "maven" | "pom" | "build" |
            "proto" | "thrift" | "avro" |
            "tex" | "bib" | "cls" | "sty"
        )
    } else {
        // 没有扩展名的文件，检查文件名
        let filename = path.file_name()
            .map(|n| n.to_string_lossy().to_lowercase())
            .unwrap_or_default();
            
        matches!(filename.as_str(),
            "readme" | "license" | "changelog" | "authors" | "contributors" |
            "makefile" | "dockerfile" | "jenkinsfile" | "vagrantfile" |
            "gemfile" | "rakefile" | "podfile" | "cartfile" |
            ".gitignore" | ".gitattributes" | ".editorconfig" | ".eslintrc" | ".prettierrc"
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::fs;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_detect_utf8_encoding() {
        // 创建临时UTF-8文件
        let temp_dir = std::env::temp_dir();
        let test_file = temp_dir.join("test_utf8.txt");
        
        fs::write(&test_file, "Hello, 世界! UTF-8 测试").await.unwrap();
        
        let encoding = detect_file_encoding(test_file.to_str().unwrap()).await.unwrap();
        assert!(matches!(encoding, SupportedEncoding::Utf8));
        
        // 清理
        let _ = fs::remove_file(&test_file).await;
    }

    #[tokio::test]
    async fn test_read_file_smart() {
        // 创建临时UTF-8文件
        let temp_dir = std::env::temp_dir();
        let test_file = temp_dir.join("test_smart.txt");
        let test_content = "Hello, 世界! 智能读取测试";
        
        fs::write(&test_file, test_content).await.unwrap();
        
        let content = read_file_smart(test_file.to_str().unwrap()).await.unwrap();
        assert_eq!(content, test_content);
        
        // 清理
        let _ = fs::remove_file(&test_file).await;
    }

    #[test]
    fn test_is_likely_text_file() {
        assert!(is_likely_text_file("test.java"));
        assert!(is_likely_text_file("test.js"));
        assert!(is_likely_text_file("test.py"));
        assert!(is_likely_text_file("README"));
        assert!(is_likely_text_file("Makefile"));
        
        assert!(!is_likely_text_file("test.jpg"));
        assert!(!is_likely_text_file("test.bin"));
        assert!(!is_likely_text_file("test.exe"));
    }
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct MagicString {

    original_str: String,

    removed_ranges: Vec<(usize, usize)>,
}

impl MagicString {
    pub fn new(content: &str) -> Self {
        MagicString {
            original_str: String::from(content),
            removed_ranges: vec![],
        }
    }

    pub fn remove(&mut self, start: usize, end: usize) {
        self.removed_ranges.push((start, end));
    }

    pub fn keep(&mut self, start: usize, end: usize) {
        let full_len = self.original_str.len();
        self.remove(0, start);
        self.remove(end, full_len);
    }

    pub fn to_string(&self) -> String {
        let mut result = String::new();
        let mut last_end = 0;

        // 将 removed_ranges 中的按第一位进行排序
        let mut removed_ranges = self.removed_ranges.clone();
        removed_ranges.sort_by(|a, b| a.0.cmp(&b.0));

        // 将重叠的区间合并
        let mut i = 0;
        while i < removed_ranges.len() - 1 {
            // 如果当前区间的结束位置大于等于下一个区间的开始位置，则合并
            if removed_ranges[i].1 >= removed_ranges[i + 1].0 {
                removed_ranges[i].1 = removed_ranges[i + 1].1;
                removed_ranges.remove(i + 1);
            } else {
                i += 1;
            }
        }

        for (start, end) in removed_ranges.iter() {
            result.push_str(&self.original_str[last_end..*start]);
            last_end = *end;
        }
        result.push_str(&self.original_str[last_end..]);
        result
    }
}


#[cfg(test)]
mod test {
    #[test]
    fn magic_string_should_work() {
        let mut ms = super::MagicString::new("hello world");
        ms.remove(0, 5);
        ms.remove(6, 11);

        assert_eq!(ms.to_string(), " ");

        let mut ms = super::MagicString::new("hello world 123456789");
        ms.keep(0, 5);
        assert_eq!(ms.to_string(), "hello");
    }

    #[test]
    fn test_overlap() {
        let mut ms = super::MagicString::new("hello world");
        ms.remove(0, 5);
        ms.remove(4, 11);

        assert_eq!(ms.to_string(), "");

        // more complex example
        let mut ms = super::MagicString::new("123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        // remove 12345
        ms.remove(0, 5);
        // remove 56789
        ms.remove(4, 9);

        // remove EFGH
        ms.remove(13, 17);
        // remove HIJKL
        ms.remove(16, 21);

        assert_eq!(ms.to_string(), "ABCDMNOPQRSTUVWXYZ");

    }
}
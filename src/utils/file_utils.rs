use std::fs;
use std::ops::Deref;
use std::path::{MAIN_SEPARATOR_STR, Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, SystemTime};

use serde::{Deserialize, Serialize};

use crate::utils::string_utils::substring_before;

use pathdiff::diff_paths;

/// 文件Meta信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FileMetaInfo {
    pub file_path: Arc<PathBuf>,
    pub file_size: u64,
    pub file_modified: SystemTime,
}

impl FileMetaInfo {
    pub fn new() -> FileMetaInfo {
        FileMetaInfo {
            file_path: Arc::new(PathBuf::new()),
            file_size: 0,
            file_modified: SystemTime::UNIX_EPOCH,
        }
    }

    // 构建文件Meta信息
    pub fn build(file: Arc<PathBuf>) -> FileMetaInfo {
        let mut ret = FileMetaInfo {
            file_path: Arc::clone(&file),
            file_size: 0,
            file_modified: SystemTime::UNIX_EPOCH,
        };

        if let Ok(meta) = fs::metadata(file.deref()) {
            ret.file_size = meta.len();
            if let Ok(modified) = meta.modified() {
                ret.file_modified = modified;
            }
        }
        return ret;
    }

    // 判断文件是否被修改
    pub fn is_modified(&self) -> bool {
        let current_meta = FileMetaInfo::build(Arc::clone(&self.file_path));
        if let Ok(duration) = current_meta.file_modified.duration_since(self.file_modified) {
            if duration == Duration::from_millis(0) {
                return false;
            }
        }
        return true;
    }

    // 更新文件Meta信息
    pub fn update(&mut self) {
        let current_meta = FileMetaInfo::build(Arc::clone(&self.file_path));
        self.clone_from(&current_meta)
    }
}

// 统一转为POSIX, 兼容MacOS/Linux/Windows
pub fn path_to_posix(path: &Path) -> Option<String> {
    path.to_str().map(|s| s.replace("\\", "/"))
}

// 统一转为POSIX, 兼容MacOS/Linux/Windows
pub fn path_buf_to_posix(path_buf: &PathBuf) -> Option<String> {
    path_buf.to_str().map(|s| s.replace("\\", "/"))
}

// 统一转为POSIX, 兼容MacOS/Linux/Windows
pub fn path_str_to_posix(path_str: &str) -> String {
    path_str.replace("\\", "/")
}

// 统一转为OS, 兼容MacOS/Linux/Windows
pub fn path_to_os(path: &Path) -> Option<String> {
    let from_sep = if MAIN_SEPARATOR_STR == "/" { "\\" } else { "/" };
    path.to_str().map(|s| s.replace(from_sep, MAIN_SEPARATOR_STR))
}

// 统一转为OS, 兼容MacOS/Linux/Windows
pub fn path_buf_to_os(path_buf: &PathBuf) -> Option<String> {
    let from_sep = if MAIN_SEPARATOR_STR == "/" { "\\" } else { "/" };
    path_buf.to_str().map(|s| s.replace(from_sep, MAIN_SEPARATOR_STR))
}

// 统一转为OS, 兼容MacOS/Linux/Windows
pub fn path_str_to_os(path_str: &str) -> String {
    let from_sep = if MAIN_SEPARATOR_STR == "/" { "\\" } else { "/" };
    path_str.replace(from_sep, MAIN_SEPARATOR_STR)
}

// 获取文件相对目录(不带后缀'/'), eg: META-INF/spring
pub fn file_path_to_dir(file_path: &str) -> &str {
    if file_path.contains("/") {
        substring_before(file_path, "/")
    } else {
        ""
    }
}

// 添加末尾斜杠
pub fn add_slash(path_str: &str) -> String {
    if path_str.is_empty() || path_str.ends_with("/") {
        return path_str.to_string();
    }
    return format!("{}/", path_str);
}

// 转为绝对路径, 需要POSIX格式
pub fn join_file_path(base_dir: &str, relative_path: &str) -> String {
    if relative_path.starts_with("/") {
        // POSIX绝对路径
        return relative_path.to_string();
    }
    if relative_path.chars().nth(1).map(|c| c == ':').unwrap_or(false) {
        // Windows绝对路径
        return relative_path.to_string();
    }
    return add_slash(base_dir) + relative_path;
}

/// 简易实现：返回 `from` 相对于 `base` 的相对路径
/// 如果base路径不是from前缀，则直接返回from
pub fn diff_paths_local(from: impl AsRef<Path>, base: impl AsRef<Path>) -> Option<PathBuf> {
    let from = from.as_ref();
    let base = base.as_ref();

    // 获取路径字符串用于跨平台处理
    let from_str = from.to_string_lossy();
    let base_str = base.to_string_lossy();
    
    // 检查是否都是Windows风格路径（包含 : 且在第二个字符位置）
    let is_windows_style = |path_str: &str| -> bool {
        path_str.len() >= 2 && path_str.chars().nth(1) == Some(':')
    };
    
    if is_windows_style(&from_str) && is_windows_style(&base_str) {
        // 都是Windows风格路径，手动解析
        let from_parts: Vec<&str> = from_str.split('\\').collect();
        let base_parts: Vec<&str> = base_str.split('\\').collect();
        
        // 检查base是否是from的前缀
        if base_parts.len() > from_parts.len() {
            return Some(from.to_path_buf());
        }
        
        // 检查base是否是from的前缀
        for (i, base_part) in base_parts.iter().enumerate() {
            if i >= from_parts.len() || *base_part != from_parts[i] {
                return Some(from.to_path_buf());
            }
        }
        
        // base是from的前缀，构造相对路径
        if from_parts.len() == base_parts.len() {
            // 完全相同
            return Some(PathBuf::from("."));
        }
        
        // 提取相对部分
        let relative_parts = &from_parts[base_parts.len()..];
        let relative_path = relative_parts.join("\\");
        return Some(PathBuf::from(relative_path));
    }

    // 特殊处理根目录不同的情况（如不同的Windows盘符）
    if let (Some(from_root), Some(base_root)) = (from.components().next(), base.components().next()) {
        // 在Unix系统上，Windows路径可能被当作单个组件，需要特殊处理
        if from_root != base_root {
            // 检查是否是Windows风格路径在Unix系统上被错误解析的情况
            if is_windows_style(&from_str) && is_windows_style(&base_str) {
                let from_drive = from_str.chars().nth(0);
                let base_drive = base_str.chars().nth(0);
                if from_drive == base_drive {
                    // 相同盘符，继续处理
                } else {
                    // 不同盘符，返回from路径
                    return Some(from.to_path_buf());
                }
            } else {
                // 根目录不同，直接返回from路径
                return Some(from.to_path_buf());
            }
        }
    }

    // 检查base是否是from的前缀
    let from_components: Vec<_> = from.components().collect();
    let base_components: Vec<_> = base.components().collect();
    
    // 如果base的组件数量大于from，base不可能是from的前缀
    if base_components.len() > from_components.len() {
        return Some(from.to_path_buf());
    }
    
    // 检查base是否是from的前缀
    if !from_components.starts_with(&base_components) {
        return Some(from.to_path_buf());
    }

    let mut from_iter = from.components().peekable();
    let mut base_iter = base.components().peekable();

    // 跳过公共前缀
    while from_iter.peek() == base_iter.peek() {
        if from_iter.next().is_none() {
            break;
        }
        base_iter.next();
    }

    let mut rel = PathBuf::new();
    // 每多一个 base 的目录，就向上走一层
    for _ in base_iter {
        rel.push("..");
    }
    // 再把剩余 from 的路径拼进来
    for comp in from_iter {
        rel.push(comp);
    }

    // 如果两者完全一致，返回 "."
    if rel.as_os_str().is_empty() {
        rel.push(".");
    }

    Some(rel)
}

/// 使用开源库
pub fn diff_paths_with_open_sdk(from: &Path, base: &Path) -> Option<PathBuf> {
    let relative_path = diff_paths(from, base);
    relative_path
}

#[cfg(test)]
mod tests {
    use std::path::PathBuf;
    use std::sync::Arc;
    use std::thread::sleep;
    use std::time::Duration;

    use log::info;

    use crate::it::logger_init::init_logger;
    use crate::utils::file_utils::{FileMetaInfo, join_file_path, diff_paths_local, diff_paths_with_open_sdk};

    #[test]
    #[ignore]
    fn test1() {
        let _ = init_logger();
        let file = PathBuf::from("/Users/<USER>/tinghe-source/rcqualitydataprod/app/core/service/src/main/java/com/alipay/rcqualitydataprod/core/service/itac/impl/AliDingTalkClientImpl.java");

        let file_meta = FileMetaInfo::build(Arc::new(file));
        loop {
            let x = file_meta.is_modified();
            info!("file_meta: {:?}, is_modified: {}", &file_meta, x);
            sleep(Duration::from_secs(1));
        }
    }

    #[test]
    fn test2() {
        println!("{:?}", join_file_path("/usr/local", "/bin"));  // Unix-style paths
        println!("{:?}", join_file_path("C:\\Program Files", "c:"));  // Windows-style paths
    }

    #[test]
    // [单测用例]测试场景：两个相同路径应该返回"."
    fn test_diff_paths_same_paths() {
        let path = PathBuf::from("/usr/local/bin");
        let result = diff_paths_local(&path, &path);
        assert_eq!(result.unwrap(), PathBuf::from("."));
    }

    #[test]
    // [单测用例]测试场景：from路径是base路径的子目录
    fn test_diff_paths_subfolder() {
        let from = PathBuf::from("/usr/local/bin/app/hi.java");
        let base = PathBuf::from("/usr/local/bin");
        let result = diff_paths_local(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("app/hi.java"));
    }

    #[test]
    // [单测用例]测试场景：from路径是base路径的父目录
    fn test_diff_paths_parent_folder() {
        let from = PathBuf::from("/usr/local");
        let base = PathBuf::from("/usr/local/bin");
        let result = diff_paths_local(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("/usr/local"));
    }

    #[test]
    // [单测用例]测试场景：from路径与base路径有部分重叠
    fn test_diff_paths_partial_overlap() {
        let from = PathBuf::from("/usr/local/app");
        let base = PathBuf::from("/usr/local/bin");
        let result = diff_paths_local(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("/usr/local/app"));
    }

    #[test]
    // [单测用例]测试场景：from路径是None
    fn test_diff_paths_from_none() {
        let from = PathBuf::from("/usr/local/bin");
        let base = PathBuf::from("");
        let result = diff_paths_local(from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("/usr/local/bin"));
    }

    #[test]
    // [单测用例]测试场景：base不是from的前缀时应该直接返回from
    fn test_diff_paths_base_not_prefix() {
        let from = PathBuf::from("/home/<USER>/documents");
        let base = PathBuf::from("/opt/software");
        let result = diff_paths_local(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("/home/<USER>/documents"));
    }

    #[test]
    // [单测用例]测试场景：base路径比from路径更长时应该直接返回from
    fn test_diff_paths_base_longer_than_from() {
        let from = PathBuf::from("/usr/local");
        let base = PathBuf::from("/usr/local/bin/app");
        let result = diff_paths_local(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("/usr/local"));
    }

    #[test]
    // [单测用例]测试场景：base与from有公共前缀但base不是from前缀时应该直接返回from
    fn test_diff_paths_common_prefix_but_base_not_prefix() {
        let from = PathBuf::from("/usr/local/app/src");
        let base = PathBuf::from("/usr/local/bin");
        let result = diff_paths_local(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("/usr/local/app/src"));
    }

    #[test]
    // [单测用例]测试场景：完全不同的根路径应该直接返回from
    fn test_diff_paths_different_roots() {
        let from = PathBuf::from("/home/<USER>");
        let base = PathBuf::from("/var/log");
        let result = diff_paths_local(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("/home/<USER>"));
    }

    #[test]
    // [单测用例]测试场景：完全不同的路径
    fn test_diff_paths_no_overlap() {
        let from = PathBuf::from("/home/<USER>/documents");
        let base = PathBuf::from("/opt/software");
        let result = diff_paths_local(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("/home/<USER>/documents"));
    }

    #[test]
    // [单测用例]测试场景：diff_paths_with_open_sdk - Windows风格路径
    fn test_diff_paths_with_open_sdk_windows_style_local() {
        let from = PathBuf::from("C:\\Users\\<USER>\\file.txt");
        let base = PathBuf::from("C:\\Users");
        let result = diff_paths_local(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("Documents\\file.txt"));
    }

    #[test]
    // [单测用例]测试场景：diff_paths_with_open_sdk - 两个相同路径应该返回"."
    fn test_diff_paths_with_open_sdk_same_paths() {
        let path = PathBuf::from("/usr/local/bin");
        let result = diff_paths_with_open_sdk(&path, &path);
        assert_eq!(result.unwrap(), PathBuf::from(""));
    }

    #[test]
    // [单测用例]测试场景：diff_paths_with_open_sdk - from路径是base路径的子目录
    fn test_diff_paths_with_open_sdk_subfolder() {
        let from = PathBuf::from("/usr/local/bin/app/hi.java");
        let base = PathBuf::from("/usr/local/bin");
        let result = diff_paths_with_open_sdk(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("app/hi.java"));
    }

    #[test]
    // [单测用例]测试场景：diff_paths_with_open_sdk - from路径是base路径的父目录
    fn test_diff_paths_with_open_sdk_parent_folder() {
        let from = PathBuf::from("/usr/local");
        let base = PathBuf::from("/usr/local/bin");
        let result = diff_paths_with_open_sdk(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from(".."));
    }

    #[test]
    // [单测用例]测试场景：diff_paths_with_open_sdk - 多层嵌套的相对路径
    fn test_diff_paths_with_open_sdk_deep_nested() {
        let from = PathBuf::from("/a/b/c/d/e/f");
        let base = PathBuf::from("/a/b");
        let result = diff_paths_with_open_sdk(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("c/d/e/f"));
    }

    #[test]
    // [单测用例]测试场景：diff_paths_with_open_sdk - Windows风格路径
    fn test_diff_paths_with_open_sdk_windows_style() {
        let from = PathBuf::from("C:\\Users\\<USER>\\file.txt");
        let base = PathBuf::from("C:\\Users");
        let result = diff_paths_with_open_sdk(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("../C:\\Users\\<USER>\\file.txt"));
    }

    #[test]
    // [单测用例]测试场景：diff_paths_with_open_sdk - 相对路径作为输入
    fn test_diff_paths_with_open_sdk_relative_paths() {
        let from = PathBuf::from("/src/utils/mod.rs");
        let base = PathBuf::from("/src");
        let result = diff_paths_with_open_sdk(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("utils/mod.rs"));
    }

    #[test]
    // [单测用例]测试场景：diff_paths_with_open_sdk - 完全不同的路径应该返回from
    fn test_diff_paths_with_open_sdk_different_roots() {
        let from = PathBuf::from("/home/<USER>");
        let base = PathBuf::from("/var/log");
        let result = diff_paths_with_open_sdk(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("../../home/<USER>"));
    }

    #[test]
    // [单测用例]测试场景：diff_paths_with_open_sdk - base路径比from路径更长时应该返回from
    fn test_diff_paths_with_open_sdk_base_longer_than_from() {
        let base = PathBuf::from("/usr/local");
        let from = PathBuf::from("/usr/local/bin/app");
        let result = diff_paths_with_open_sdk(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("bin/app"));
    }

    #[test]
    // [单测用例]测试场景：diff_paths_with_open_sdk - 空路径处理
    fn test_diff_paths_with_open_sdk_empty_paths() {
        let from = PathBuf::from("");
        let base = PathBuf::from("src");
        let result = diff_paths_with_open_sdk(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from(".."));
    }

    #[test]
    // [单测用例]测试场景：diff_paths_with_open_sdk - 当前目录作为base
    fn test_diff_paths_with_open_sdk_current_dir_base() {
        let from = PathBuf::from("./docs/readme.md");
        let base = PathBuf::from(".");
        let result = diff_paths_with_open_sdk(&from, &base);
        assert_eq!(result.unwrap(), PathBuf::from("docs/readme.md"));
    }
}
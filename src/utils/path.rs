use std::{env, path::PathBuf};

pub fn get_current_dir() -> std::io::Result<PathBuf> {
  env::current_dir()
}

pub fn combine_host(path: &str, host: &str) -> String {
  format!("{}{}", host, path)
}

pub fn combine_project_path(project_path: &str, file_path: &str) -> String {
  let project_path_buf = PathBuf::from(project_path);
  let file_path_buf = PathBuf::from(file_path);

  let mut combined = PathBuf::from(project_path_buf);
  combined.push(file_path);

  combined.to_string_lossy().into_owned()
}
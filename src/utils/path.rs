use crate::service::code_scan::{is_test_file, skip_dir};
use crate::utils::parallel_file_scan::CHAT_INDEX_SKIP_ARR;
use crate::utils::file_encoding::read_file_smart_sync;
use crate::dialogue::codefuse_index_repository::CHUNK_CLIENT;
use crate::dialogue::repo_index_operator::RepoStatusEnum;
use agent_common_service::model::chat_model::{ChatCodeReferenceModel, ChatRelatedRequestBean};
use agent_common_service::tools::git_utils::{get_repository_url, get_diff_files_from_path};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::remote_client::query_latest_commit;
use agent_db::dal::vector_client::VECTOR_CLIENT;
use agent_db::tools::common_tools::{LINE_ENDING, V_C_PREFIX};
use ignore::{DirEntry, Walk<PERSON><PERSON>er};
use log::{error, info, warn};
use std::collections::HashSet;
use std::fmt;
use std::fs;
use std::fs::File;
use std::io::{BufRead, BufReader};
use std::path::Path;
use serde::{Deserialize, Serialize};

///默认分支名
pub const DEFAULT_BRANCH:&str = "UNKNOWN_BRANCH";
pub const DEFAULT_PROJECT_URL:&str = "UNKNOWN_PROJECT";



pub fn remove_prefix<'a>(s: &'a Path, p: &'a Path) -> &'a str {
    let s = s.to_str().unwrap();
    let p = p.to_str().unwrap();
    if s.starts_with(p) {
        &s[p.len()..]
    } else {
        s
    }
}


///填充文件内容
pub fn fill_content(chat_related_request: &mut ChatRelatedRequestBean) {
    let binding = chat_related_request.projectUrl.clone().unwrap_or("".to_string());
    let project_path = Path::new(&binding);
    if let Some(referenceList) = &mut chat_related_request.referenceList {
        for reference in referenceList.iter_mut() {
            let file_url = project_path.clone().join(reference.url.clone());
            match reference.r#type.as_str() {
                "CODE" => {
                    // 检查行号有效性
                    if reference.lineStart == 0 || reference.lineEnd < reference.lineStart {
                        error!("Invalid line numbers: start_line={}, end_line={}", reference.lineStart, reference.lineEnd)
                    } else {
                        let file = File::open(&file_url).unwrap();
                        let reader = BufReader::new(file);

                        let mut selected_lines = Vec::new();
                        for (index, line) in reader.lines().enumerate() {
                            let current_line = index + 1; // 行号从1开始
                            if current_line >= reference.lineStart && current_line <= reference.lineEnd {
                                selected_lines.push(line.unwrap());
                            }
                            if current_line > reference.lineEnd {
                                break; // 提前结束遍历
                            }
                        }
                        if selected_lines.len() > 0 {
                            let selected_lines_str = selected_lines.join(LINE_ENDING);
                            reference.content = Some(selected_lines_str);
                        }
                    }
                }
                "FILE" => {
                    // 智能读取文件内容，支持多种编码（UTF-8, GBK, GB2312, GB18030等）
                    match read_file_smart_sync(&file_url.to_string_lossy()) {
                        Ok(file_content) => {
                            reference.content = Some(file_content);
                        }
                        Err(e) => {
                            error!("读取文件失败: {} - {}", file_url.display(), e);
                            // 继续处理其他文件，不中断整个流程
                            continue;
                        }
                    }
                }
                _ => {
                    continue;
                }
            }
        }
    }
}

///获取指定目录下的所有文件，返回(全部文件，普通文件列表, diff文件列表)
pub async fn get_all_file_by_url(url: &String, need_diff: bool) -> (Vec<DirEntry>, Vec<DirEntry>, Vec<DirEntry>, String, String) {
    let base = Path::new(url);
    let mut base_walk_build = WalkBuilder::new(base);
    let mut all_vec: Vec<DirEntry> = Vec::new();
    let mut data_vec: Vec<DirEntry> = Vec::new();
    let mut diff_vec: Vec<DirEntry> = Vec::new();
    let mut diff_files_set: HashSet<String> = HashSet::new();
    let mut branch_name = "".to_string();
    let mut repo_url = "".to_string();

    let ignore_file_names = [".codefuseignore", "codefuseignore"];
    for &name in &ignore_file_names {
        let ignore_file_path = base.join(".codefuse").join(name);
        if ignore_file_path.exists() {
            base_walk_build.add_custom_ignore_filename(name);
            break;  // 找到一个即可
        }
    }

    for result in base_walk_build.filter_entry(|e| !skip_dir(e)).build() {
        match result {
            Ok(dir) => {
                let file_type = dir.file_type();
                if file_type.is_none() {
                    continue;
                }
                if !file_type.unwrap().is_file() {
                    continue;
                }
                let file_size = fs::metadata(dir.path()).unwrap().len();
                //过滤掉空文件
                if file_size == 0 {
                    continue;
                }
                if file_size > AGENT_CONFIG.scan_skip_file_size {
                    info!("chat index skip file: {}",dir.path().display().to_string());
                    continue;
                }
                //超过指定大小的文件（40kb）, 要计算行数， 超过行数上限的文件要过滤掉
                if file_size > AGENT_CONFIG.file_size_to_calculate_line {
                    let lines_count_rst = count_content_line(dir.path().to_str().unwrap());
                    if let Ok(lines_count) = lines_count_rst {
                        if lines_count > AGENT_CONFIG.scan_skip_file_max_len {
                            info!("chat index skip file: {} since exceed max_line_count",dir.path().display().to_string());
                            continue;
                        }
                    }
                }
                let extension_opt = dir.path().extension();
                if let None = extension_opt {
                    continue;
                }
                let path_str = dir.path().to_string_lossy();
                if path_str.contains("src/test") || path_str.contains("src\\test") {
                    continue;
                }

                let file_name_suffix = extension_opt.unwrap().to_string_lossy().to_string();

                if !(AGENT_CONFIG.similarity_suffix_arr.contains(&file_name_suffix) || AGENT_CONFIG.related_suffix_arr.contains(&file_name_suffix)) {
                    continue;
                }

                if CHAT_INDEX_SKIP_ARR.contains(&file_name_suffix) {
                    continue;
                }
                all_vec.push(dir);
            }
            Err(e) => {
                error!("Error while scanning directory: {}", e);
            }
        }
    }

    if need_diff {
        // 步骤1: 获取git地址
        let git_url_opt = match get_repository_url(base) {
            Ok(git_url) => {
                info!("Found git repository URL: {}", git_url);
                Some(git_url)
            }
            Err(e) => {
                warn!("Failed to get git repository URL: {}, treating all files as diff files", e);
                None
            }
        };

        // 步骤2: 获取远程最新commit ID
        diff_files_set = if let Some(git_url) = git_url_opt {
            match query_latest_commit(git_url).await {
                Some((branch, commit_id, repoUrl)) => {
                    info!("Found remote latest commit ID: {}", commit_id);
                    branch_name = branch.clone();
                    repo_url = repoUrl.clone();
                    // 步骤3: 获取diff文件列表
                    match get_diff_files_from_path(base, &commit_id) {
                        Ok(diff_files) => {
                            info!("Found {} diff files", diff_files.len());
                            diff_files.into_iter().collect()
                        }
                        Err(e) => {
                            warn!("Failed to get diff files: {}, treating all files as diff files", e);
                            // 当git操作失败时，将所有文件都当作diff文件处理
                            convert_to_relative_path_set(&all_vec, base)
                        }
                    }
                }
                None => {
                    warn!("Failed to get remote latest commit ID, treating all files as diff files");
                    // 当无法获取远程commit ID时，将所有文件都当作diff文件处理
                    convert_to_relative_path_set(&all_vec, base)
                }
            }
        } else {
            // 当无法获取git URL时，将所有文件都当作diff文件处理
            convert_to_relative_path_set(&all_vec, base)
        };

        //步骤4: 检查文件是否在diff列表中
        for dir in all_vec.clone() {
            let relative_path = match dir.path().strip_prefix(base) {
                Ok(rel_path) => rel_path.to_string_lossy().to_string(),
                Err(_) => dir.path().to_string_lossy().to_string(),
            };

            if diff_files_set.contains(&relative_path) {
                // 文件在diff列表中，添加到diff_vec
                diff_vec.push(dir);
            } else {
                // 文件不在diff列表中，添加到data_vec
                data_vec.push(dir);
            }
        }
    }

    (all_vec, data_vec, diff_vec, branch_name, repo_url)
}

/// 获取行数, 当大于40k的时候，再计算行数
pub fn count_content_line(filename: &str) -> Result<usize, std::io::Error>{
    let file = File::open(filename)?;
    let reader = BufReader::new(file);
    Ok(reader.lines().count())
}

fn convert_to_relative_path_set(data: &Vec<DirEntry>, base: &Path) -> HashSet<String> {
    let mut relative_path_set = HashSet::new();
    for dir in data {
        let relative_path = match dir.path().strip_prefix(base) {
            Ok(rel_path) => rel_path.to_string_lossy().to_string(),
            Err(_) => dir.path().to_string_lossy().to_string(),
        };
        relative_path_set.insert(relative_path);
    }
    relative_path_set
}

/// 队列类型枚举，对应FileStatusResult中的不同队列
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum QueueType {
    /// 新增数据，向量数据要调用远程服务端直接拉数据
    CREATE_URL,
    /// 新增数据，但是向量数据要请求模型服务
    CREATE_MODEL,
    /// 更新数据，向量数据要调用远程服务直接拉数据
    UPDATE_URL,
    /// 更新数据，向量数据要请求模型服务
    UPDATE_MODEL,
    /// 删除本地索引数据
    DELETE,
}

impl fmt::Display for QueueType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            QueueType::CREATE_URL => write!(f, "CREATE_URL"),
            QueueType::CREATE_MODEL => write!(f, "CREATE_MODEL"),
            QueueType::UPDATE_URL => write!(f, "UPDATE_URL"),
            QueueType::UPDATE_MODEL => write!(f, "UPDATE_MODEL"),
            QueueType::DELETE => write!(f, "DELETE"),
        }
    }
}

/// 文件状态分类结果
#[derive(Debug, Clone)]
pub struct FileStatusResult {
    /// 要新增数据，向量数据要调用远程服务端直接拉数据
    pub create_file_with_url: HashSet<String>,
    /// 要新增数据，但是向量数据要请求模型服务
    pub create_file_with_model: HashSet<String>,
    /// 要更新数据，向量数据要调用远程服务直接拉数据
    pub update_file_with_url: HashSet<String>,
    /// 要更新数据，向量数据要请求模型服务
    pub update_file_with_model: HashSet<String>,
    /// 要删除本地索引数据
    pub delete_file: HashSet<String>,
    /// tantivy中有数据但lanceDB中没有数据的file_url列表
    pub no_vector_file: HashSet<String>,
    /// 当前仓库下所有文件列表
    pub all_files: HashSet<String>,
    ///仓库地址
    pub git_repo: Option<String>,
    ///远程最近分支
    pub remote_branch:Option<String>
}

impl FileStatusResult {
    pub fn new() -> Self {
        FileStatusResult {
            create_file_with_url: HashSet::new(),
            create_file_with_model: HashSet::new(),
            update_file_with_url: HashSet::new(),
            update_file_with_model: HashSet::new(),
            delete_file: HashSet::new(),
            no_vector_file: HashSet::new(),
            all_files: HashSet::new(),
            git_repo: None,
            remote_branch: None,
        }
    }

    /// 获取队列类型映射，返回每个队列对应的类型
    pub fn get_queue_types(&self) -> Vec<(QueueType, usize)> {
        vec![
            (QueueType::CREATE_URL, self.create_file_with_url.len()),
            (QueueType::CREATE_MODEL, self.create_file_with_model.len()),
            (QueueType::UPDATE_URL, self.update_file_with_url.len()),
            (QueueType::UPDATE_MODEL, self.update_file_with_model.len()),
            (QueueType::DELETE, self.delete_file.len()),
        ]
    }

    /// 获取非空队列的类型和数据
    pub fn get_non_empty_queues(&self) -> Vec<QueueType> {
        let mut non_empty_queues = Vec::new();

        if !self.create_file_with_url.is_empty() {
            non_empty_queues.push(QueueType::CREATE_URL);
        }
        if !self.create_file_with_model.is_empty() {
            non_empty_queues.push(QueueType::CREATE_MODEL);
        }
        if !self.update_file_with_url.is_empty() {
            non_empty_queues.push(QueueType::UPDATE_URL);
        }
        if !self.update_file_with_model.is_empty() {
            non_empty_queues.push(QueueType::UPDATE_MODEL);
        }
        if !self.delete_file.is_empty() {
            non_empty_queues.push(QueueType::DELETE);
        }

        non_empty_queues
    }
}

/// 基于请求参数，和当前本地tantivy里面的数据做对比，找到那些文件是要新增，哪些文件是要更新，
/// 哪些文件可以直接从服务端拉到向量数据，哪些文件需要请求模型临时构建向量，哪些文件列表要删除
pub async fn get_file_by_url_and_status(project_url: &String) -> FileStatusResult {
    let mut result = FileStatusResult::new();

    // 步骤1: 调用get_all_file_by_url和query_file_vec
    let (all_vec, data_vec, diff_vec, branch, repo_url) = get_all_file_by_url(project_url, true).await;
    let index_file_vec_result = CHUNK_CLIENT.query_file_vec(project_url).await.unwrap_or(vec![]);

    // 将各种数据转换为HashSet以便快速查找
    let index_file_set: HashSet<String> = index_file_vec_result.into_iter().collect();

    // 将all_vec转换为相对路径的HashSet
    let base = Path::new(project_url);
    let all_vec_set: HashSet<String> = all_vec.iter()
        .map(|entry| {
            match entry.path().strip_prefix(base) {
                Ok(rel_path) => rel_path.to_string_lossy().to_string(),
                Err(_) => entry.path().to_string_lossy().to_string(),
            }
        })
        .collect();

    let data_vec_set: HashSet<String> = data_vec.iter()
        .map(|entry| {
            match entry.path().strip_prefix(base) {
                Ok(rel_path) => rel_path.to_string_lossy().to_string(),
                Err(_) => entry.path().to_string_lossy().to_string(),
            }
        })
        .collect();

    let diff_vec_set: HashSet<String> = diff_vec.iter()
        .map(|entry| {
            match entry.path().strip_prefix(base) {
                Ok(rel_path) => rel_path.to_string_lossy().to_string(),
                Err(_) => entry.path().to_string_lossy().to_string(),
            }
        })
        .collect();

    // 获取no_vector_files
    let vector_ids_result = VECTOR_CLIENT.query_all_ids_by_project(project_url).await.unwrap_or(vec![]);
    let vector_ids_set: HashSet<String> = vector_ids_result.into_iter().collect();
    let tantivy_chunk_ids_result = CHUNK_CLIENT.query_all_ids_by_project(project_url).await.unwrap_or(vec![]);
    let mut no_vector_files: HashSet<String> = HashSet::new();

    for chunk_id in tantivy_chunk_ids_result {
        if !vector_ids_set.contains(&chunk_id) {
            if let Some(file_url) = chunk_id.split('#').next() {
                // 删除开头的"VC_"字符.V_C_PREFIX
                let cleaned_file_url = if file_url.starts_with(V_C_PREFIX) {
                    &file_url[3..]
                } else {
                    file_url
                };
                no_vector_files.insert(cleaned_file_url.to_string());
            }
        }
    }

    // 设置all_files和no_vector_file
    result.all_files = all_vec_set.clone();
    result.no_vector_file = no_vector_files.clone();

    // 1: 找到index_file_set里面存在，但是all_vec不存在的数据列表: delete_file_vec
    let delete_file_vec: HashSet<String> = index_file_set.difference(&all_vec_set).cloned().collect();
    result.delete_file = delete_file_vec;

    // 2: 找到data_vec里面存在，但是index_file_set不存在的数据： new_file_vec_url
    let new_file_vec_url: HashSet<String> = data_vec_set.difference(&index_file_set).cloned().collect();
    result.create_file_with_url = new_file_vec_url;

    // 3: 找到diff_vec里面存在，但是index_file_set不存在的数据： new_file_vec_model
    let new_file_vec_model: HashSet<String> = diff_vec_set.difference(&index_file_set).cloned().collect();
    result.create_file_with_model = new_file_vec_model;

    // 4: 找到data_vec里面存在，同时no_vector_files也存在的数据：update_file_vec_url
    let update_file_vec_url: HashSet<String> = data_vec_set.intersection(&no_vector_files).cloned().collect();
    result.update_file_with_url = update_file_vec_url;

    // 5: 找到diff_vec里面存在，同时no_vector_files也存在的数据：update_file_vec_model
    let update_file_vec_model: HashSet<String> = diff_vec_set.intersection(&no_vector_files).cloned().collect();
    result.update_file_with_model = update_file_vec_model;

    // 设置仓库信息
    result.git_repo = if repo_url.is_empty() { None } else { Some(repo_url) };
    result.remote_branch = if branch.is_empty() { None } else { Some(branch) };

    result
}
#[cfg(test)]
mod test {
    use std::time::Instant;
    use agent_db::config::agent_logger::init_logger;
    use agent_db::config::runtime_config::AGENT_CONFIG_ENV_KEY;
    use log::info;
    use crate::dialogue::repo_index_operator::RepoStatusEnum;
    use crate::service::index_service::{IndexBuildStatus, IndexService, INDEX_BUILD_MANAGER};
    use crate::utils::path::get_file_by_url_and_status;


}
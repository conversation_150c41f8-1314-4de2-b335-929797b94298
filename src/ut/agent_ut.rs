use std::collections::{HashMap, HashSet};
use std::fmt::format;
use anyhow::Result;
use log::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::remote::rpc_model::{build_success_response, build_error_response, BaseResponse};
use crate::api::req_model::{QueryRelatedRequestBean};
use crate::api::ut_agent_prompt_req_model::{TestCasePromptRequestBean};
use crate::api::res_model::{RelatedData};
use crate::ut::project_info::{parser_unit_test};
use reqwest::{Client,Url};
use crate::utils::string_utils::truncate_string;
use serde_json::{Value, Error};

/// 在ai par agent模式下解析ut依赖，直接返回模型输入prompt
pub async fn parser_unit_test_for_ai_agent(query_related_request: &QueryRelatedRequestBean) -> Result<BaseResponse<String>> {
    info!("[单元测试][Agent] 获取上下文");
    debug!("[单元测试][Agent] 完整上下文为: {:?}", query_related_request);
    let search_ret_vec:Vec<RelatedData> = parser_unit_test(query_related_request);
    let json_obj = serde_json::json!({
        "related_data_vec": search_ret_vec
    });
    let related_json_string = serde_json::to_string(&json_obj)?;
    let test_framework = if query_related_request.testFramework.is_empty() {
        // 如果 testFramework 为空，检查 language
        if query_related_request.language.to_lowercase() == "java" {
            String::from("junit4")
        } else {
            String::new()
        }
    } else {
        query_related_request.testFramework.clone()
    };

    let select_content = query_related_request.selectedContent.clone();
    let prompt_req = if test_framework.is_empty() {
        format!("为以下代码写单测:\n```\n{}\n```", select_content)
    } else {
        format!("使用{}为以下代码写单测:\n```\n{}\n```", test_framework, select_content)
    };

    let request = TestCasePromptRequestBean {
        session_id: query_related_request.sessionId.clone(),
        related_data: related_json_string,
        language: query_related_request.language.clone(),
        model_name: query_related_request.modelName.clone(),
        file_content: query_related_request.fileContent.clone(),
        prompt_req: prompt_req.clone(),
        test_framework: query_related_request.testFramework.clone(),
        user_query: query_related_request.userOrigQuery.clone(),
    };

    debug!("[单元测试][Agent] 请求api request为: {:?}", &request);

    // 请求失败走兜底逻辑，采用默认的prompt_req
    match send_request(request).await {
        Ok(response) => {
            info!("[单元测试][Agent] 获取上下文成功，结果为： {:?}", truncate_string(&response, 32));
            debug!("[单元测试][Agent] 获取上下文成功，完整结果为: {:?}", &response);
            // 解析 JSON 响应
            match serde_json::from_str::<Value>(&response) {
                Ok(json) => {
                    // 检查 errorCode
                    if let Some(error_code) = json.get("errorCode") {
                        if error_code.as_i64() == Some(0) {
                            // 提取 data 字段
                            if let Some(data) = json.get("data") {
                                if let Some(data_str) = data.as_str() {
                                    let mut real_response = data_str.to_string();
                                    if !real_response.contains("[单测用例]测试场景") {
                                        real_response = real_response + "\n生成用例额外要求：在每个测试用例方法前使用注释标注测试场景，示例：[单测用例]测试场景：xxx";
                                    }
                                    let file_url = query_related_request.fileUrl.clone();
                                    info!("[单元测试][Agent] 被测文件url：{:?}", file_url);
                                    // 增加被测文件相对路径
                                    let real_response_with_file_path = format!("<file_path>{}</file_path>\n{}", file_url, real_response);
                                    Ok(build_success_response(real_response_with_file_path))
                                } else {
                                    warn!("[单元测试][Agent] 上下文响应处理失败，原因：Data field is not a string！采用兜底逻辑");
                                    let mut real_reps = prompt_req.clone();
                                    if !real_reps.contains("[单测用例]测试场景") {
                                        real_reps = real_reps + "\n生成用例额外要求：在每个测试用例方法前使用注释标注测试场景，示例：[单测用例]测试场景：xxx";
                                    }
                                    Ok(build_success_response(real_reps))
                                    // Ok(build_error_response(1, "Data field is not a string".to_string()))
                                }
                            } else {
                                warn!("[单元测试][Agent] 上下文响应处理失败，原因：Data field not found！采用兜底逻辑");
                                let mut real_reps = prompt_req.clone();
                                if !real_reps.contains("[单测用例]测试场景") {
                                    real_reps = real_reps + "\n生成用例额外要求：在每个测试用例方法前使用注释标注测试场景，示例：[单测用例]测试场景：xxx";
                                }
                                Ok(build_success_response(real_reps))
                                // Ok(build_error_response(2, "Data field not found".to_string()))
                            }
                        } else {
                            // 如果 errorCode 不为 0，获取 errorMsg
                            let error_msg = json.get("errorMsg")
                                .and_then(|msg| msg.as_str())
                                .unwrap_or("Unknown error")
                                .to_string();
                            warn!("[单元测试][Agent] 上下文响应处理失败，原因：{:?}！采用兜底逻辑", error_msg);
                            let mut real_reps = prompt_req.clone();
                            if !real_reps.contains("[单测用例]测试场景") {
                                real_reps = real_reps + "\n生成用例额外要求：在每个测试用例方法前使用注释标注测试场景，示例：[单测用例]测试场景：xxx";
                            }
                            Ok(build_success_response(real_reps))
                            // Ok(build_error_response(3, error_msg))
                        }
                    } else {
                        warn!("[单元测试][Agent] 上下文响应处理失败，原因：ErrorCode field not found！采用兜底逻辑");
                        let mut real_reps = prompt_req.clone();
                        if !real_reps.contains("[单测用例]测试场景") {
                            real_reps = real_reps + "\n生成用例额外要求：在每个测试用例方法前使用注释标注测试场景，示例：[单测用例]测试场景：xxx";
                        }
                        Ok(build_success_response(real_reps))
                        // Ok(build_error_response(4, "ErrorCode field not found".to_string()))
                    }
                },
                Err(e) => {
                    // error!("[单元测试][Agent] JSON 解析失败： {}", e);
                    warn!("[单元测试][Agent] 上下文响应处理失败，原因：Invalid JSON response: {}！采用兜底逻辑", e);
                    let mut real_reps = prompt_req.clone();
                    if !real_reps.contains("[单测用例]测试场景") {
                        real_reps = real_reps + "\n生成用例额外要求：在每个测试用例方法前使用注释标注测试场景，示例：[单测用例]测试场景：xxx";
                    }
                    Ok(build_success_response(real_reps))
                    // Ok(build_error_response(5, format!("Invalid JSON response: {}", e)))
                }
            }
        }
        Err(e) => {
            error!("[单元测试][Agent] 获取上下文出现异常，Error： {}", e);
            eprintln!("Error: {}", e);
            Err(e)
        }
    }
}

async fn send_request(request: TestCasePromptRequestBean) -> Result<String> {
    let client = Client::new();

    let url = "https://caselike.alipay.com";
    // let url = "https://caselike-pre.alipay.com";
    // let url = "http://127.0.0.1:8888";
    let uri = "/v1/api/ut/prompt";
    let base_url = Url::parse(url)?;
    let full_url = base_url.join(uri)?;
    let response = client
        .post(full_url)
        .json(&request)
        .send()
        .await?;

    let response_text = response.text().await?;
    Ok(response_text)
}

#[cfg(test)]
mod tests {
    use std::fs;
    use crate::api::req_model::{IntentionType, QueryRelatedRequestBean};
    use super::*;

    #[tokio::test]
    // #[ignore]
    async fn test_method_prompt_normal() {
        // 方法级别正常链路
        fs::remove_file("/Users/<USER>/.codefuse/temp/LOCK");
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        let select_content = r#"
        public String filter(ObjectA oA) {
            ObjectB objectB = new ObjectB();
            String bField = objectB.getbField();
            if(bField.equals("123")) {
                return "haha";
            }
            return oA.getaField();
        }
        "#;
        let file_content = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.model.enums.AntCodeModifyEnum;
        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import com.alipay.sut.service.gpt.impl.ObjectC;

        /**
         * <AUTHOR>
         * @date 2024/09/03
         */
        public class ImportFilter {
            public String filter(ObjectA oA) {
                ObjectB objectB = new ObjectB();
                String bField = objectB.getbField();
                if(bField.equals("123")) {
                    return "haha";
                }
                return oA.getaField();
            }

            public String cFiled(ObjectC oC) {
                return oC.cMethod();
            }

            public boolean antCodeEnumGet(String code) {
                if(AntCodeModifyEnum.MODIFY.getCode().equals(code)) {
                    return true;
                }
                return false;
            }
        }
        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "123".to_string(),
            projectUrl: "".to_string(),
            intentionType: IntentionType::UnitTest,
            language: "java".to_string(),
            modelName: "DeepSeek-V3".to_string(),
            fileContent: file_content.to_string(),
            selectedContent: select_content.to_string(),
            fileUrl: "".to_string(),
            intention: "".to_string(),
            testFile: false,
            testFramework: "junit5".to_string(),
            ..default_req_bean
        };

        let response = match parser_unit_test_for_ai_agent(&query_related_request).await {
            Ok(response) => {
                println!("[单元测试][Agent] 获取上下文成功，上下文为： {:?}", &response);
                assert!(response.data.unwrap().contains("被测方法为ImportFilter类下的filter方法"))
            }
            Err(e) => {
                eprintln!("Error: {}", e);
                "".to_string();
                assert!(false);
            }
        };
    }

    #[tokio::test]
    // #[ignore]
    async fn test_class_prompt_normal() {
        // 类级别正常链路
        fs::remove_file("/Users/<USER>/.codefuse/temp/LOCK");
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        let select_content = r#"
        ImportFilter
        "#;
        let file_content = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.model.enums.AntCodeModifyEnum;
        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import com.alipay.sut.service.gpt.impl.ObjectC;

        /**
         * <AUTHOR>
         * @date 2024/09/03
         */
        public class ImportFilter {
            public String filter(ObjectA oA) {
                ObjectB objectB = new ObjectB();
                String bField = objectB.getbField();
                if(bField.equals("123")) {
                    return "haha";
                }
                return oA.getaField();
            }

            public String cFiled(ObjectC oC) {
                return oC.cMethod();
            }

            public boolean antCodeEnumGet(String code) {
                if(AntCodeModifyEnum.MODIFY.getCode().equals(code)) {
                    return true;
                }
                return false;
            }
        }
        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "123".to_string(),
            projectUrl: "".to_string(),
            intentionType: IntentionType::UnitTest,
            language: "java".to_string(),
            modelName: "DeepSeek-V3".to_string(),
            fileContent: file_content.to_string(),
            selectedContent: select_content.to_string(),
            fileUrl: "".to_string(),
            intention: "".to_string(),
            testFile: false,
            testFramework: "junit5".to_string(),
            ..default_req_bean
        };

        let response = match parser_unit_test_for_ai_agent(&query_related_request).await {
            Ok(response) => {
                println!("[单元测试][Agent] 获取上下文成功，上下文为： {:?}", &response);
                assert!(response.data.unwrap().contains("为以下整个类生成单测"))
            }
            Err(e) => {
                eprintln!("Error: {}", e);
                "".to_string();
                assert!(false);
            }
        };
    }

    #[tokio::test]
    // #[ignore]
    async fn test_not_java() {
        // 非java方法
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        let select_content = r#"
        const getData = async () => {
            const { data } = await queryTaskByTemplate({
              templateCode: templateCode,
            });

            realData.splice(0, realData.length);
            for (let i = 0; i < data?.cells?.length; i++) {
              if (data?.cells[i].shape === 'edge') {
                realData.push({ ...data?.cells[i] });
              } else {
                if (data?.cells[i].data?.nodeType === 'Begin' || data?.cells[i].data?.nodeType === 'End') {
                  realData.push({
                    width: 60,
                    height: 60,
                    id: data?.cells[i].id,
                    shape: 'rect',
                    x: data?.cells[i].position.x,
                    y: data?.cells[i].position.y,
                    attrs: {
                      body: {
                        rx: 30,
                        ry: 30,
                        fill: '#fff',
                        stroke: '#C2C8D5',
                        strokeWidth: 1,
                      },
                      label: {
                        text:
                          data?.cells[i].data?.nodeType === 'Begin'
                            ? formatMessage({ id: 'SmartUnitPage.taskDetail.start' })
                            : formatMessage({ id: 'SmartUnitPage.taskDetail.end' }),
                      },
                    },
                    data: {},
                    ports: {
                      groups: {
                        flag: {
                          position: data?.cells[i].data?.nodeType === 'Begin' ? 'right' : 'left',
                          attrs: {
                            circle: {
                              r: 4,
                              magnet: true,
                              stroke: '#31d0c6',
                              strokeWidth: 1,
                              fill: '#fff',
                            },
                          },
                        },
                      },
                      items: [
                        {
                          id: data?.cells[i].data?.nodeType === 'Begin' ? 'right' : 'left',
                          group: 'flag',
                        },
                      ],
                    },
                  });
                } else {
                  realData.push({
                    id: data?.cells[i].id,
                    shape: 'dagNode',
                    x: data?.cells[i].position?.x,
                    y: data?.cells[i].position?.y,
                    data: data?.cells[i].data,
                  });
                }
              }
            }
            setgraphData({
              nodes: realData?.filter((item: any) => item.shape === 'dagNode'),
              edges: realData?.filter((item: any) => item.shape !== 'dagNode'),
            });
          };
        "#;
        let file_content = r#"
        const getData = async () => {
            const { data } = await queryTaskByTemplate({
              templateCode: templateCode,
            });

            realData.splice(0, realData.length);
            for (let i = 0; i < data?.cells?.length; i++) {
              if (data?.cells[i].shape === 'edge') {
                realData.push({ ...data?.cells[i] });
              } else {
                if (data?.cells[i].data?.nodeType === 'Begin' || data?.cells[i].data?.nodeType === 'End') {
                  realData.push({
                    width: 60,
                    height: 60,
                    id: data?.cells[i].id,
                    shape: 'rect',
                    x: data?.cells[i].position.x,
                    y: data?.cells[i].position.y,
                    attrs: {
                      body: {
                        rx: 30,
                        ry: 30,
                        fill: '#fff',
                        stroke: '#C2C8D5',
                        strokeWidth: 1,
                      },
                      label: {
                        text:
                          data?.cells[i].data?.nodeType === 'Begin'
                            ? formatMessage({ id: 'SmartUnitPage.taskDetail.start' })
                            : formatMessage({ id: 'SmartUnitPage.taskDetail.end' }),
                      },
                    },
                    data: {},
                    ports: {
                      groups: {
                        flag: {
                          position: data?.cells[i].data?.nodeType === 'Begin' ? 'right' : 'left',
                          attrs: {
                            circle: {
                              r: 4,
                              magnet: true,
                              stroke: '#31d0c6',
                              strokeWidth: 1,
                              fill: '#fff',
                            },
                          },
                        },
                      },
                      items: [
                        {
                          id: data?.cells[i].data?.nodeType === 'Begin' ? 'right' : 'left',
                          group: 'flag',
                        },
                      ],
                    },
                  });
                } else {
                  realData.push({
                    id: data?.cells[i].id,
                    shape: 'dagNode',
                    x: data?.cells[i].position?.x,
                    y: data?.cells[i].position?.y,
                    data: data?.cells[i].data,
                  });
                }
              }
            }
            setgraphData({
              nodes: realData?.filter((item: any) => item.shape === 'dagNode'),
              edges: realData?.filter((item: any) => item.shape !== 'dagNode'),
            });
          };
        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "123".to_string(),
            projectUrl: "".to_string(),
            intentionType: IntentionType::UnitTest,
            language: "js".to_string(),
            modelName: "DeepSeek-V3".to_string(),
            fileContent: file_content.to_string(),
            selectedContent: select_content.to_string(),
            fileUrl: "".to_string(),
            intention: "".to_string(),
            testFile: false,
            testFramework: "jest".to_string(),
            ..default_req_bean
        };

        let response = match parser_unit_test_for_ai_agent(&query_related_request).await {
            Ok(response) => {
                println!("[单元测试][Agent] 获取上下文成功，上下文为： {:?}", &response);
                assert!(response.data.unwrap().contains("在每个测试用例方法前使用注释标注测试场景"))
            }
            Err(e) => {
                eprintln!("Error: {}", e);
                "".to_string();
                assert!(false);
            }
        };
    }
}
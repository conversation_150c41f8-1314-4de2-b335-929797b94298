use std::fs;
use std::io::Read;
use std::collections::{HashMap, HashSet};
use std::fmt::format;
use std::path::{Path, PathBuf, MAIN_SEPARATOR};
use std::sync::Arc;
use anyhow::Result;
use log::{debug, error, info, warn};
use regex::Regex;
use serde::{Deserialize, Serialize};
use agent_codefuse_service::ast::java::strategy::{CodeFuseJavaAstStrategy, get_relate_item_from_prompt};
use agent_codefuse_service::function::build_cache_data::find_by_class_name;
// use agent_codefuse_service::service::code_scan::FILE_PREFIX;
// use agent_common_service::model::rpc_model::{BaseResponse, build_success_response};
use agent_common_service::service::code_ast_analysis::AstStrategy;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::domain::code_kv_index_domain::{CodeInfo, MethodDeclaration, ScanFileRecord, ScanProjectRecord};
use agent_db::remote::rpc_model::{build_success_response, BaseResponse};
use crate::api::req_model::{IntentionType, QueryRelatedRequestBean};
use crate::api::res_model::{QueryRelatedResult, RelatedData};
use crate::it::java_model::class_info::ClassInfo;
use crate::it::java_parser::parser::parse_class_text;
use crate::utils::path_cmp_utils::{find_cut_by_test_file, find_cut_by_test_file_bak, get_mut_from_cut_test_class_info};
use crate::it::loader::class_loader::{load_repo_class_file, load_repo_all_java_file};
use crate::utils::file_utils::diff_paths_local;

// 正常应该这样引入：use agent_common_service::tools::common_tools::FILE_PREFIX，下个版本修改
pub const FILE_PREFIX: &str = "F_";

const METHOD_KEYWORDS: &[&str] = &[
    "public",
    "private",
    "protected",
    "static",
    "final",
    "abstract",
    "synchronized",
    "native",
    "void",
];

pub fn query_related_data_ut(query_related_request: &QueryRelatedRequestBean) -> Result<BaseResponse<QueryRelatedResult>> {
    let related_data_vec = parser_unit_test(query_related_request);
    Ok(build_success_response(QueryRelatedResult {
        agent_version: AGENT_CONFIG.agent_version.clone(),
        related_data_vec,
        interface_prompt_data: None,
        interface_prompt_config: None,
        intention_type: IntentionType::UnitTest,
    }))
}

/// 根据被测类中import，获取被测类在同一个仓库中的依赖代码
pub fn query_local_repo_class_level_relate_import_code(import_list: &Vec<String>,
                                                       project_url: &str) -> Vec<Arc<PathBuf>> {
    // Set存储qualified_class_name，防止重复
    let mut qualified_class_name_set: HashSet<String> = HashSet::new();
    // 遍历import_list，找到以com.alipay开头的import字符串。拿到qualify_class_name，从load_repo_class_file获取信息
    for import_str in import_list {
        // * 暂时不处理
        if import_str.contains("*") {
            continue;
        }
        // 去掉import标识
        let mut full_qualified_name: String;
        if import_str.contains(" static ") {
            let middle_stage = import_str.replace("import static","").replace(";","").trim().to_string();
            // 找到.号之前的部分
            full_qualified_name = middle_stage.rfind('.').map(|i| &middle_stage[..i]).unwrap().to_string();
        } else {
            full_qualified_name = import_str.replace("import ", "").replace(";", "").trim().to_string();
        }

        // 只关注com.alipay开头的
        if !full_qualified_name.starts_with("com.alipay"){
            continue;
        }
        qualified_class_name_set.insert(full_qualified_name.clone());
    }

    let mut local_repo_related_class_path_vec: Vec<Arc<PathBuf>> = vec![];
    for qualified_class_name in qualified_class_name_set {
        let local_class_path_opt = load_repo_class_file(project_url, &qualified_class_name);
        if let Some(local_class_path) = local_class_path_opt {
            local_repo_related_class_path_vec.push(local_class_path);
        }
    }

    local_repo_related_class_path_vec
}

/// 单元测试RAG数据查询
pub fn parser_unit_test(query_related_request: &QueryRelatedRequestBean) -> Vec<RelatedData> {
    info!("[单元测试] 单元测试检索, request=[{:?}]", query_related_request.simplify());
    debug!("[单元测试] 完整上下文为: {:?}", query_related_request);

    let mut related_data_vec: Vec<RelatedData> = vec![];

    // 由于圈选代码可能不完整，先解析整个类
    let mut has_class_info_opt = false;
    let mut class_info_check_related = parse_class_text(&query_related_request.fileContent);
    let class_info_opt = Some(class_info_check_related.clone());
    if class_info_check_related.class_name.is_empty() {
        warn!("can not find related file,{:?}", query_related_request);
    } else {
        has_class_info_opt = true;
    }

    let mut method_content_to_check = query_related_request.selectedContent.to_string();
    // 1.1 先判断是否是完整方法
    let is_complete_method = is_complete_java_method(&method_content_to_check);
    let mut selected_content_rw_opt: Option<String> = None;
    let mut need_rewrite = !is_complete_method;
    if is_complete_method { // 判断是否是多个方法
        let method_cnt = java_method_cnt(&method_content_to_check);
        if method_cnt > 1 {
            need_rewrite = true;
            info!("[单元测试] 多个完整圈选方法，目前针对第一个处理");
        }
    }
    if has_class_info_opt && need_rewrite {
        (method_content_to_check, selected_content_rw_opt) = selected_rewrite(query_related_request, class_info_opt.clone());
    }

    // selected content rewrite related data
    if let Some(select_rewrite_content) = &selected_content_rw_opt {
        let related_data = RelatedData {
            content: select_rewrite_content.clone(),
            model: None, // 不需要？
            extract: Some("SELECTED_CONTENT_RW".to_string()),
        };
        related_data_vec.push(related_data);
    }
    let mut file_content_to_check_related = &query_related_request.fileContent;

    // 判断触发位置是否是测试类
    if query_related_request.testFile {
        info!("[单元测试] [补充用例] 测试类补充测试场景");
        let related_data_is_test_file = RelatedData {
            content: "true".to_string(),
            model: None, // 不需要？
            extract: Some("IS_TEST_FILE".to_string()),
        };
        related_data_vec.push(related_data_is_test_file);
        let cut_path_ret:Result<String> = find_cut_by_test_file(&query_related_request.fileUrl, &query_related_request.projectUrl);
        let cut_file_path;
        if let Err(cut_path) = &cut_path_ret {
            warn!("can not find cut class, only add is_test_file related data, use backup, reason is {}", cut_path);
            let cut_path_ret_bak = find_cut_by_test_file_bak(&query_related_request.fileUrl);
            if let Err(cut_path_bak) = &cut_path_ret_bak {
                warn!("can not find cut class with backup, only add is_test_file related data, use backup, reason is {}", cut_path_bak);
                return related_data_vec;
            } else {
                cut_file_path = cut_path_ret_bak.unwrap();
            }
        } else {
            // 前面判断过Err了，这里可以直接unwrap拿到被测类路径
            cut_file_path = cut_path_ret.unwrap();
        }
        // 前面判断过Err了，这里可以直接unwrap拿到被测类路径
        // let cut_file_path = cut_path_ret.unwrap();
        // 读取被测类内容，kv client索引的内容不完整，需要重新读取
        let cut_file_content_ret = fs::read_to_string(cut_file_path);
        if let Err(file_content_err) = &cut_file_content_ret {
            warn!("read cut file content failed, only add is_test_file related data, error is {}", file_content_err);
            return related_data_vec;
        }
        let cut_file_content = cut_file_content_ret.unwrap();
        file_content_to_check_related = &cut_file_content.clone();

        let cut_class_info = parse_class_text(&cut_file_content.clone());
        // 如果被测类解析失败，则直接返回
        if cut_class_info.class_name.is_empty() {
            let cut_file_data = RelatedData {
                content: cut_file_content.clone(),
                model: None, // 不需要？
                extract: Some("CLASS_UNDER_TEST".to_string()),
            };

            related_data_vec.push(cut_file_data);
            debug!("[单测用例][补充用例]被测类解析失败, data_vec is:  {:?}", related_data_vec);
            return related_data_vec;
        }

        let mut_code_body_opt = get_mut_from_cut_test_class_info(&cut_class_info, method_content_to_check.clone());
        if let Some(mut_code_body) = mut_code_body_opt {
            let cut_file_data = RelatedData {
                content: cut_file_content.clone(),
                model: None, // 不需要？
                extract: Some("CLASS_UNDER_TEST".to_string()),
            };
            related_data_vec.push(cut_file_data);

            let mut_code_data = RelatedData {
                content: mut_code_body.clone(),
                model: None, // 不需要？
                extract: Some("METHOD_UNDER_TEST".to_string()),
            };
            related_data_vec.push(mut_code_data);
            // file_content_to_check_related = cut_file_content;
            method_content_to_check = mut_code_body;
            class_info_check_related = cut_class_info.clone();
            has_class_info_opt = true;
        } else {
            let cut_file_data = RelatedData {
                content: cut_file_content.clone(),
                model: None, // 不需要？
                extract: Some("CLASS_UNDER_TEST".to_string()),
            };
            related_data_vec.push(cut_file_data);
            warn!("[单测用例][补充用例]未找到被测方法, query_related_request: {:?}", query_related_request);
            return related_data_vec;
        }
    }

    info!("[单元测试] 获取被测方法上下文");
    info!("[单元测试] 获取仓库file vector");
    let scan_file_vec = get_scan_file_vec(&query_related_request.projectUrl);
    let related_data = check_and_get_related_data(&query_related_request.language, &query_related_request.projectUrl, method_content_to_check, &scan_file_vec);
    //如果是状态值是false，则不支持获取相关数据，直接返回
    if !related_data.0 {
        info!("[单测用例]check_and_get_related_data依赖检索失败, data_vec is:  {:?}", related_data_vec);
        info!("[单测用例]检索测试用例");
        for scan_project_record in scan_file_vec {
            // 如果是测试类补充用例，不需要再次提供test类
            if query_related_request.testFile {
                continue;
            }

            // 不能通过classname来判断，只能通过file url最后文件名字来判断
            let normalized = &query_related_request.fileUrl.replace('\\', "/");
            let test_class_name = Path::new(&normalized)
                .file_stem()
                .and_then(|s| s.to_str())
                .map(|stem| format!("{}Test", stem))
                .unwrap_or_else(|| "UnknownTest".to_string());

            let test_case_name_vec = vec![test_class_name];
            let is_hit = find_by_class_name(&scan_project_record, &test_case_name_vec);
            if is_hit.0 {
                let param_data = read_file(&scan_project_record.file_url);
                if param_data.0.is_some() {
                    // 2. 计算 scan_project_url 相对于 project_url 的相对路径
                    let relative_path = diff_paths_local(&scan_project_record.file_url, &query_related_request.projectUrl)
                        .unwrap_or_else(|| PathBuf::from("."));
                    let path_display = format!("{}{}{}", "<file_path>" ,relative_path.display().to_string() ,"</file_path>\n");
                    // 3. 拼接相对路径 + 文件内容
                    let content_pass = format!(
                        "{}{}",
                        path_display,
                        &param_data.0.unwrap()
                    );
                    let related_data = RelatedData {
                        content: content_pass,
                        model: param_data.1,
                        extract: Some("TEST_CASE_SAMPLE".to_string()),
                    };
                    related_data_vec.push(related_data);
                }
                break;
            }
        }

        // 兜底：如果索引查找失败，通过import，找到整个类依赖代码
        info!("[单测用例]检索import代码");
        let cut_import_list = &class_info_opt.unwrap().import_list;
        let project_url = query_related_request.projectUrl.clone();
        let local_repo_related_class_path_vec: Vec<Arc<PathBuf>> = query_local_repo_class_level_relate_import_code(&cut_import_list, &project_url);
        let local_related_vec_size = local_repo_related_class_path_vec.len();
        info!("[单元测试] 被测方法所属类import相关本仓库代码2 list size is：{:?}", local_related_vec_size);
        for local_repo_related_class_path_arc in local_repo_related_class_path_vec {
            let local_repo_related_class_path = local_repo_related_class_path_arc.to_str().unwrap();
            let mut related_class_content = fs::read_to_string(local_repo_related_class_path).unwrap();
            let relative_path = diff_paths_local(local_repo_related_class_path, &project_url);
            related_class_content = format!("<file_path>{}</file_path>\n{}", relative_path.unwrap().display(), related_class_content);
            let related_data = RelatedData {
                content: related_class_content,
                model: None,
                extract: Some("LOCAL_REPO_CLASS_LEVEL_RELATE_IMPORT_CODE".to_string())
            };

            related_data_vec.push(related_data);
        }
        return related_data_vec;
    }

    if !has_class_info_opt {
        info!("[单测用例]has no class info opt, data_vec is:  {:?}", related_data_vec);
        return related_data_vec;
    }

    let method_info = related_data.2.unwrap();
    // let code_info = related_data.3.unwrap();
    // let scan_project_record_vec = related_data.1.unwrap();
    let class_name_opt = Some(&class_info_check_related.class_name);
    let super_class_opt = &class_info_check_related.super_class;

    let param_vec_opt: &Option<Vec<String>>= &method_info.param_class_vec;
    let inner_obj_vac_opt: &Option<Vec<String>>= &method_info.inner_class_declaration;
    let mut param_and_inner_obj_vec_opt = merge_vec_options(param_vec_opt, inner_obj_vac_opt);
    if let Some(return_class_str) = &method_info.return_class {
        if let Some(ref mut param_vec) = param_and_inner_obj_vec_opt {
            param_vec.push(return_class_str.clone());
        } else {
            param_and_inner_obj_vec_opt = Some(vec![return_class_str.clone()]);
        }
    }

    info!("[单元测试] 被测方法上下文对象list：{:?}", param_and_inner_obj_vec_opt);
    //遍历所有class，然后去找所需的数据是否存在（如果反向遍历，耗时更长，目前还没有做file级别的持久化数据)
    // 参数对象保证顺序，先把查找结果放到一个map中，然后在插入到vec中
    let mut param_data_map: HashMap<usize, RelatedData> = HashMap::new();
    let project_url = query_related_request.projectUrl.clone();
    for scan_project_record in scan_file_vec {
        //step 判断是不是参数对象
        if let Some(param_vec) = &param_and_inner_obj_vec_opt {
            let is_hit = find_by_class_name(&scan_project_record, param_vec);
            if is_hit.0 {
                let param_data = read_file(&scan_project_record.file_url);

                if param_data.0.is_some() {
                    // 2. 计算 scan_project_url 相对于 project_url 的相对路径
                    let relative_path = diff_paths_local(&scan_project_record.file_url, &project_url)
                        .unwrap_or_else(|| PathBuf::from("."));
                    let path_display = format!("{}{}{}", "<file_path>" ,relative_path.display().to_string() ,"</file_path>\n");
                    // 3. 拼接相对路径 + 文件内容
                    let content_pass = format!(
                        "{}{}",
                        path_display,
                        &param_data.0.unwrap()
                    );
                    info!("[单元测试] 被测方法上下文依赖：{:?}", &param_data.1.as_ref().unwrap().class_name);
                    let related_data = RelatedData {
                        content: content_pass,
                        model: param_data.1,
                        extract: Some("PARAM_DATA".to_string()),
                    };
                    // related_data_vec.push(related_data);
                    // 通过is_hit.1这个index保证去重
                    param_data_map.insert(is_hit.1, related_data);
                }
                continue;
            }
        }
        if let Some(class_name_value) = class_name_opt {
            // 如果是测试类补充用例，不需要再次提供test类
            if query_related_request.testFile {
                continue;
            }
            let test_case_name_vec = vec![class_name_value.clone() + "Test"];
            let is_hit = find_by_class_name(&scan_project_record, &test_case_name_vec);
            if is_hit.0 {
                let param_data = read_file(&scan_project_record.file_url);
                if param_data.0.is_some() {
                    // 2. 计算 scan_project_url 相对于 project_url 的相对路径
                    let relative_path = diff_paths_local(&scan_project_record.file_url, &project_url)
                        .unwrap_or_else(|| PathBuf::from("."));
                    let path_display = format!("{}{}{}", "<file_path>" ,relative_path.display().to_string() ,"</file_path>\n");
                    // 3. 拼接相对路径 + 文件内容
                    let content_pass = format!(
                        "{}{}",
                        path_display,
                        &param_data.0.unwrap()
                    );
                    let related_data = RelatedData {
                        content: content_pass,
                        model: param_data.1,
                        extract: Some("TEST_CASE_SAMPLE".to_string()),
                    };
                    related_data_vec.push(related_data);
                }
                continue;
            }
        }

        if let Some(super_class) = super_class_opt {
            let extend_class_name = &super_class.class_name;
            let extend_class_vec = vec![extend_class_name.clone()];
            let is_hit = find_by_class_name(&scan_project_record, &extend_class_vec);
            if is_hit.0 {
                let param_data = read_file(&scan_project_record.file_url);
                if param_data.0.is_some() {
                    // 2. 计算 scan_project_url 相对于 project_url 的相对路径
                    let relative_path = diff_paths_local(&scan_project_record.file_url, &project_url)
                        .unwrap_or_else(|| PathBuf::from("."));
                    let path_display = format!("{}{}{}", "<file_path>" ,relative_path.display().to_string() ,"</file_path>\n");
                    // 3. 拼接相对路径 + 文件内容
                    let content_pass = format!(
                        "{}{}",
                        path_display,
                        &param_data.0.unwrap()
                    );
                    let related_data = RelatedData {
                        content: content_pass,
                        model: param_data.1,
                        extract: Some("EXTEND_CLASS".to_string()),
                    };
                    related_data_vec.push(related_data);
                }
                continue;
            }
        }
    }

    // param按照出现顺序插入
    let mut keys: Vec<usize> = param_data_map.keys().cloned().collect();
    keys.sort_unstable();
    for key in keys {
        if let Some(value) = param_data_map.get(&key) {
            related_data_vec.push(value.clone());
        }
    }

    // 通过import，找到整个类依赖代码
    let cut_import_list = &class_info_opt.unwrap().import_list;
    let project_url = query_related_request.projectUrl.clone();
    let local_repo_related_class_path_vec: Vec<Arc<PathBuf>> = query_local_repo_class_level_relate_import_code(&cut_import_list, &project_url);
    // let local_related_vec_size = local_repo_related_class_path_vec.len();
    // info!("[单元测试] 被测方法所属类import相关本仓库代码list size is：{:?}", local_related_vec_size);
    info!("[单元测试] 被测方法所属类import相关本仓库代码list is：{:?}", local_repo_related_class_path_vec);
    for local_repo_related_class_path_arc in local_repo_related_class_path_vec {
        let local_repo_related_class_path = local_repo_related_class_path_arc.to_str().unwrap();
        let relative_path = diff_paths_local(local_repo_related_class_path, &project_url);
        let mut related_class_content = fs::read_to_string(local_repo_related_class_path).unwrap();
        related_class_content = format!("<file_path>{}</file_path>\n{}", relative_path.unwrap().display(), related_class_content);
        let related_data = RelatedData {
            content: related_class_content,
            model: None,
            extract: Some("LOCAL_REPO_CLASS_LEVEL_RELATE_IMPORT_CODE".to_string())
        };

        related_data_vec.push(related_data);
    }

    debug!("[单测用例]related_data_vec: {:?}", related_data_vec);
    related_data_vec
}

fn selected_rewrite(query_related_request: &QueryRelatedRequestBean, class_info_opt: Option<ClassInfo>) -> (String, Option<String>){
    info!("[单元测试] 圈选方法不完整或有多个方法");
    let mut method_content_to_check= String::new();
    let mut method_content_tmp = String::new();
    let mut selected_content_rw_opt: Option<String> = None;
    // 1.2 如果不完整，根据行号确定方法。
    // 为什么不所有选择内容都做如此处理？因为索引更新可能延迟，减少bug，尽可能用原始圈选方法内容
    let start_line = query_related_request.startLine;  // 插件前端传来数值从0开始
    let end_line = query_related_request.endLine;      // 插件前端传来数值从0开始
    if end_line != 0 { // 0是前端传来的默认值，虽然实际可能是第0行,但概率较低
        // 1.3 遍历class_info中method_list，确定方法内容
        info!("[单元测试] 圈选方法不完整，有起止行号，start_line is {}, end_line is {}", start_line, end_line);
        if let Some(class_info) = class_info_opt.clone() {
            // 注意：由于圈选代码可能涉及多个方法，应该返回vec<Option<PromptDeclaration>>，TODO：但本次只处理一个
            // 多个圈选被测方法直接调用，在本地核心是拿到最后一个方法，而case like中拿到第一个，因此需要改造。
            let mut method_info_checked = false;  // 记录是否调用get_relate_item_from_prompt，保证只有第一个方法处理
            for method_info in &class_info.method_list {
                // 因为从第一个方法开始遍历，只需要找到区间内方法
                let start_end_pos = method_info.start_end_position;
                if start_end_pos.1 == 0 {
                    // 如果方法结束行为0，大概率是旧版没有采用 start_end_position，直接退出加快处理速度
                    break;
                }

                let method_start_line = start_end_pos.0;
                let method_end_line = start_end_pos.1;

                // 不需要全部检查，退出条件，圈选代码结束行 < 方法开始行
                if end_line + 1 < method_start_line {
                    break;
                }

                // 处理圈选边界方法
                if (start_line + 1 >= method_start_line && start_line + 1 <= method_end_line) || (end_line + 1 <= method_end_line && end_line + 1 >= method_start_line) {
                    method_content_tmp += "\n";
                    method_content_tmp += &method_info.code_body.to_string();
                    if !method_info_checked {
                        method_content_to_check = method_info.code_body.to_string();
                        method_info_checked = true;
                    }
                } else if start_line + 1 < method_start_line && end_line + 1 > method_end_line {  // 处理圈选内部方法
                    method_content_tmp += "\n";
                    method_content_tmp += &method_info.code_body.to_string();
                    if !method_info_checked {
                        method_content_to_check = method_info.code_body.to_string();
                        method_info_checked = true;
                    }
                }
            }
        }
    }

    if !method_content_tmp.is_empty() {
        selected_content_rw_opt = Some(method_content_tmp.clone());
        info!("[单元测试] 圈选方法不完整，改写后selected_content_rw: {}", &method_content_tmp);
    }
    (method_content_to_check, selected_content_rw_opt)
}

fn read_file(file_path: &String) -> (Option<String>, Option<CodeInfo>) {
    let file_result = fs::File::open(file_path); // 打开文件
    if file_result.is_err() {
        error!("open file failed,{}",file_path);
        return (None, None);
    }
    let mut file = file_result.unwrap();

    let mut content = String::new();
    let read_result = file.read_to_string(&mut content); // 读取文件内容到字符串
    if read_result.is_err() {
        error!("read file failed,{}",file_path);
        return (None, None);
    }

    let java_model = CodeFuseJavaAstStrategy::extra_code_info(&content);
    return (Some(content), java_model);
}

///检查请求参数是否有效
fn check_and_get_related_data(language: &String, project_url: &String, method_content_rw: String, scan_file_vec: &Vec<ScanFileRecord>) -> (bool, Option<Vec<ScanFileRecord>>, Option<MethodDeclaration>) {
    if language != "java" {
        warn!("language not support, language is {}", language);
        return (false, None, None);
    }

    if scan_file_vec.is_empty() {
        return (false, None, None);
    }

    let method_content_to_check = method_content_rw;

    let method_info_opt = get_relate_item_from_prompt(&method_content_to_check);
    if method_info_opt.is_none() {
        warn!("can not find relate item from selected code, project url is {}, selected_code is {}", project_url, &method_content_to_check);
        return (false, None, None);
    }
    let method_declaration_opt = method_info_opt.unwrap().method_declaration;
    if method_declaration_opt.is_none() {
        warn!("can not find method_declaration item from prompt, project url is {}, selected_code is {}", project_url, &method_content_to_check);
        return (false, None, None);
    }
    // (true, Some(scan_file_vec), method_declaration_opt)
    // 不需要返回scan_vec，已经放到方法外面获取
    (true, None, method_declaration_opt)
}

fn get_scan_file_vec(project_url: &String) -> Vec<ScanFileRecord> {
    let scan_file_vec_result = get_file_list(project_url);

    let mut get_file_list_ret = true;
    let mut scan_file_vec = vec![];

    if scan_file_vec_result.is_err() {
        error!("get project record failed with project url {}", project_url);
        get_file_list_ret = false;
        // return (false, None, None);
    } else {
        let scan_file_vec_opt = scan_file_vec_result.unwrap();
        if scan_file_vec_opt.is_none() {
            warn!("no file record in this project with project url {}", project_url);
            get_file_list_ret = false;
        } else {
            scan_file_vec = scan_file_vec_opt.unwrap();
            if scan_file_vec.is_empty() {
                warn!("no file record in this project url {}", project_url);
                get_file_list_ret = false;
            }
        }
    }

    if !get_file_list_ret {
        warn!("no file record with kv cache, use backup, project_url: {}", project_url);
        let scan_file_vec_bak = get_file_list_from_class_loader(project_url);
        if scan_file_vec_bak.is_empty() {
            warn!("no file record in class loader with backup recall, project url: {}", project_url);
        }
        scan_file_vec = scan_file_vec_bak;
    }
    scan_file_vec
}

///
/// 从class loader召回，备用召回
fn get_file_list_from_class_loader(project_url: &String) -> Vec<ScanFileRecord> {
    let all_java_file_path_vec: Vec<Arc<PathBuf>> = load_repo_all_java_file(project_url);
    let mut scan_file_vec: Vec<ScanFileRecord> = Vec::new();

    for file_path_arc in all_java_file_path_vec {
        let file_path_str = file_path_arc.as_path().to_string_lossy().into_owned();
        let suffix = file_path_arc.as_path().extension().unwrap().to_string_lossy().to_string();
        let file_record = ScanFileRecord {
            file_url: file_path_str,
            file_name_suffix: suffix,
            total_snippet_num: 0,
            code_info: None,
        };
        scan_file_vec.push(file_record);
    }
    scan_file_vec
}

///
/// 从kv cache召回，主召回
///
fn get_file_list(project_url: &String) -> Result<Option<Vec<ScanFileRecord>>> {
    let file_key = format!("{}{}", FILE_PREFIX, project_url);
    let file_map_result = KV_CLIENT.get_from_prefix(&file_key);
    let mut file_scan_result: Vec<ScanFileRecord> = vec![];

    match file_map_result {
        Ok(file_map_opt) => {
            match file_map_opt {
                Some(file_map) => {
                    for (key, value) in file_map {
                        let scan_file_data_result: serde_json::error::Result<ScanFileRecord> = serde_json::from_str(value.as_str());
                        match scan_file_data_result {
                            Ok(scan_file_data) => {
                                file_scan_result.push(scan_file_data);
                            }
                            Err(e) => {
                                error!("serde_json::from_str failed,{:?}",e);
                            }
                        }
                    }
                }
                None => {
                    debug!("get_file_list result is none")
                }
            }
        }
        Err(e) => {
            error!("get file list failed,{:?}",e);
        }
    }
    if file_scan_result.is_empty() {
        return Ok(None);
    }
    return Ok(Some(file_scan_result));
}


fn merge_vec_options(vec_opt_one: &Option<Vec<String>>, vec_opt_two: &Option<Vec<String>>) -> Option<Vec<String>> {
    let mut result = Vec::new();

    // 从两个 Option 中提取 Vec，若存在则合并
    result.extend(vec_opt_one.as_ref().unwrap_or(&Vec::new()).clone());
    result.extend(vec_opt_two.as_ref().unwrap_or(&Vec::new()).clone());

    // 返回合并后的 Vec，如果是空的则返回 None
    if result.is_empty() {
        None
    } else {
        Some(result)
    }
}

// 通过大括号匹配判断是否是完整java方法，需要注意字符串和注释中的大括号需要排除
// 只能近似判断，不过识别错误影响不大，只是需要走圈选改写逻辑
fn is_complete_java_method(java_code: &str) -> bool {
    // 先检查方法上的关键字
    // let method_regex = Regex::new(r"(?xism) # ignore case, whitespace, and enable multiline
    //    ^                           # Start of the string
    //     (?:                        # Non-capturing group for comments/annotations
    //         (?:                    # Non-capturing group for optional comments/annotations
    //             (?:\s*//.*?$|/\*.*?\*/\s*)? # Single or multi-line comments
    //             | @\w+\s*          # Annotations
    //         )*                     # Multiple comments/annotations
    //     )?                         # Comments/annotations are optional
    //     \s*                        # Allow for whitespace before access modifier
    //     (public|protected|private|static|final|abstract|synchronized|native|void)+.*\s*\{").unwrap();
    // let method_keyword_contains = method_regex.is_match(java_code);

    let mut method_keyword_contains = false;
    let re = Regex::new(r"\r?\n").unwrap();
    for cur_line in re.split(java_code.trim()) {
        if (cur_line.trim().starts_with("```") || cur_line.trim().starts_with("@") || cur_line.trim().starts_with("//") || cur_line.trim()
            .starts_with("/*") || cur_line.trim().starts_with("*") || cur_line.trim().starts_with("*/")) {
            continue;
        }
        for &keyword in METHOD_KEYWORDS {
            if cur_line.trim().starts_with(keyword) {
                method_keyword_contains = true;
                break;
            }
        }
        break;
    }
    if !method_keyword_contains {
        return false;
    }
    // 然后检查大括号匹配
    let mut brace_count = 0;  // 统计大括号的数量
    let mut in_string = false; // 判断当前是否在字符串中
    let mut in_single_comment = false; // 判断是否在单行注释中
    let mut in_block_comment = false; // 判断是否在多行注释中
    let mut has_brace = false;

    let chars: Vec<char> = java_code.chars().collect();

    for i in 0..chars.len() {
        match chars[i] {
            '"' => {
                // 切换字符串状态
                if !in_single_comment && !in_block_comment {
                    in_string = !in_string;
                }
            },
            '/' => {
                // 检查可能的注释
                if let Some(&next) = chars.get(i + 1) {
                    if next == '/' && !in_string && !in_block_comment {
                        in_single_comment = true; // 进入单行注释
                    }
                    if next == '*' && !in_string && !in_single_comment {
                        in_block_comment = true; // 进入多行注释
                    }
                }
            },
            '*' => {
                // 检查退出多行注释
                if let Some(&next) = chars.get(i + 1) {
                    if next == '/' && in_block_comment {
                        in_block_comment = false; // 退出多行注释
                    }
                }
            },
            '{' => {
                if !in_string && !in_single_comment && !in_block_comment {
                    brace_count += 1; // 计数左大括号
                    has_brace = true;
                }
            },
            '}' => {
                if !in_string && !in_single_comment && !in_block_comment {
                    brace_count -= 1; // 计数右大括号
                    if brace_count < 0 {
                        // 括号不匹配，返回false
                        return false;
                    }
                }
            },
            '\n' => {
                if in_single_comment {
                    in_single_comment = false; // 到达行尾，退出单行注释
                }
            },
            _ => {}
        }
    }

    // 如果大括号数相等且为0，则是完整的方法
    brace_count == 0 && has_brace
}

// 近似判断方法个数，精确方法应该通过tree-sitter遍历判断，但是目前tree-sitter代码不在本仓库
fn java_method_cnt(java_code: &str) -> usize {
    let keywords = ["public", "private", "protected", "abstract"];
    let mut total_count = 0;

    for &keyword in &keywords {
        let re = Regex::new(&format!(r"\b{}\b", keyword)).unwrap();
        let count = re.find_iter(java_code).count();
        total_count += count;
    }

    total_count
}

#[cfg(test)]
mod tests {
    use crate::api::req_model::{IntentionType, QueryRelatedRequestBean};
    use super::*;

    #[test]
    //#[ignore]
    fn test_parser_unit_test() {
        fs::remove_file("/Users/<USER>/.codefuse/temp/LOCK");
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        let select_content = r#"
        public String filter(ObjectA oA) {
            ObjectB objectB = new ObjectB();
            String bField = objectB.getbField();
            if(bField.equals("123")) {
                return "haha";
            }
            return oA.getaField();
        }
        "#;
        let file_content = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.model.enums.AntCodeModifyEnum;
        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import com.alipay.sut.service.gpt.impl.ObjectC;

        /**
         * <AUTHOR>
         * @date 2024/09/03
         */
        public class ImportFilter {
            public String filter(ObjectA oA) {
                ObjectB objectB = new ObjectB();
                String bField = objectB.getbField();
                if(bField.equals("123")) {
                    return "haha";
                }
                return oA.getaField();
            }

            public String cFiled(ObjectC oC) {
                return oC.cMethod();
            }

            public boolean antCodeEnumGet(String code) {
                if(AntCodeModifyEnum.MODIFY.getCode().equals(code)) {
                    return true;
                }
                return false;
            }
        }
        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let req_bean = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/workspace/smartunitmng".to_string(),
            fileUrl: "/Users/<USER>/workspace/smartunitmng/app/service/src/main/java/com/alipay/sut/service/gpt/impl/llmtestdata/ImportFilter.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::UnitTest,
            fileContent: file_content.to_string(),
            selectedContent: select_content.to_string(),
            ..default_req_bean
        };

        let related_data_vec = parser_unit_test(&req_bean);
        let mut contains_object_b = false;
        let mut contains_test_class = false;
        for item in &related_data_vec {
            if item.extract.as_ref().unwrap() == "TEST_CASE_SAMPLE" {
                contains_test_class = true;
            } else if item.extract.as_ref().unwrap() == "PARAM_DATA" && item.content.contains("public class ObjectB") {
                contains_object_b = true;
            }
        }
        assert!(contains_object_b);
        assert!(contains_test_class);
    }

    #[test]
    //#[ignore]
    fn test_parse_extend_class() {
        fs::remove_file("/Users/<USER>/.codefuse/temp/LOCK");
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        let select_content = r#"
        private String cMethod() {
            String s1 = mayaService.getInferenceResult("121");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        "#;
        let file_content = r#"
        package com.alipay.sut.service.gpt.impl;

        import javax.annotation.Resource;

        import com.alipay.sut.dal.model.GptModelDetailDO;
        import com.alipay.sut.service.gpt.GptModelService;

        /**
         * <AUTHOR>
         * @date 2024/03/14
         */
        public class ObjectC extends ObjectB{
            @Resource
            private GptModelService gptModelService;

            private String cMethod() {
                String s1 = mayaService.getInferenceResult("121");
                GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
                String s2 = bMethod();
                return s1 + detailDO.getName() + s2;
            }
        }
        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let req_bean = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/workspace/smartunitmng".to_string(),
            fileUrl: "/Users/<USER>/workspace/smartunitmng/app/service/src/main/java/com/alipay/sut/service/gpt/impl/ObjectC.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::UnitTest,
            fileContent: file_content.to_string(),
            selectedContent: select_content.to_string(),
            ..default_req_bean
        };

        let related_data_vec = parser_unit_test(&req_bean);
        let mut contains_super_class = false;
        for item in &related_data_vec {
            if item.extract.as_ref().unwrap() == "EXTEND_CLASS" {
                contains_super_class = true;
                break;
            }
        }
        assert!(contains_super_class);
    }

    #[test]
    //#[ignore]
    fn test_parser_with_partial_selected() {
        // 测试圈选一个方法中部分代码结果
        fs::remove_file("/Users/<USER>/.codefuse/temp/LOCK");
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // # case1
        let method_name_selected = r#"
        filter
        "#;
        let file_content = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.model.enums.AntCodeModifyEnum;
        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import com.alipay.sut.service.gpt.impl.ObjectC;

        /**
         * <AUTHOR>
         * @date 2024/09/03
         */
        public class ImportFilter {
            public String filter(ObjectA oA) {
                ObjectB objectB = new ObjectB();
                String bField = objectB.getbField();
                if(bField.equals("123")) {
                    return "haha";
                }
                return oA.getaField();
            }

            public String cFiled(ObjectC oC) {
                return oC.cMethod();
            }

            public boolean antCodeEnumGet(String code) {
                if(AntCodeModifyEnum.MODIFY.getCode().equals(code)) {
                    return true;
                }
                return false;
            }
        }
        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let req_bean = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/workspace/smartunitmng".to_string(),
            fileUrl: "/Users/<USER>/workspace/smartunitmng/app/service/src/main/java/com/alipay/sut/service/gpt/impl/llmtestdata/ImportFilter.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::UnitTest,
            fileContent: file_content.to_string(),
            selectedContent: method_name_selected.to_string(),
            startLine: 12,
            endLine: 12,
            ..default_req_bean
        };

        let related_data_vec = parser_unit_test(&req_bean);
        let mut contains_object_b = false;
        let mut contains_test_class = false;
        let mut contains_rw = false;
        for item in &related_data_vec {
            if item.extract.as_ref().unwrap() == "TEST_CASE_SAMPLE" {
                contains_test_class = true;
            } else if item.extract.as_ref().unwrap() == "SELECTED_CONTENT_RW" {
                contains_rw = true;
            } else if item.extract.as_ref().unwrap() == "PARAM_DATA" && item.content.contains("public class ObjectB") {
                contains_object_b = true;
            }
        }
        assert!(contains_object_b);
        assert!(contains_test_class);
        assert!(contains_rw);

        // # case2
        let one_method_body_partial_selected = r#"
            String bField = objectB.getbField();
            if(bField.equals("123")) {
                return "haha";
            }
        "#;

        let req_bean_body_partial = QueryRelatedRequestBean {
            selectedContent: one_method_body_partial_selected.to_string(),
            startLine: 14,
            endLine: 17,
            ..req_bean.clone()
        };

        let related_data_vec = parser_unit_test(&req_bean_body_partial);
        let mut contains_object_b = false;
        let mut contains_test_class = false;
        let mut contains_rw = false;
        for item in &related_data_vec {
            if item.extract.as_ref().unwrap() == "TEST_CASE_SAMPLE" {
                contains_test_class = true;
            } else if item.extract.as_ref().unwrap() == "SELECTED_CONTENT_RW" {
                contains_rw = true;
            } else if item.extract.as_ref().unwrap() == "PARAM_DATA" && item.content.contains("public class ObjectB") {
                contains_object_b = true;
            }
        }
        assert!(contains_object_b);
        assert!(contains_test_class);
        assert!(contains_rw);
    }

    #[test]
    //#[ignore]
    fn test_parser_with_two_method_partial_selected() {
        // 测试圈选两个方法部分代码结果
        // 可以测试import补充逻辑
        fs::remove_file("/Users/<USER>/.codefuse/temp/LOCK");
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // # case1
        let file_content = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.model.enums.AntCodeModifyEnum;
        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import com.alipay.sut.service.gpt.impl.ObjectC;

        /**
         * <AUTHOR>
         * @date 2024/09/03
         */
        public class ImportFilter {
            public String filter(ObjectA oA) {
                ObjectB objectB = new ObjectB();
                String bField = objectB.getbField();
                if(bField.equals("123")) {
                    return "haha";
                }
                return oA.getaField();
            }

            public String cFiled(ObjectC oC) {
                return oC.cMethod();
            }

            public boolean antCodeEnumGet(String code) {
                if(AntCodeModifyEnum.MODIFY.getCode().equals(code)) {
                    return true;
                }
                return false;
            }
        }
        "#;
        let two_method_body_partial_selected = r#"
            String bField = objectB.getbField();
            if(bField.equals("123")) {
                return "haha";
            }
            return oA.getaField();
        }

        public String cFiled(ObjectC oC) {
            return oC.cMethod();
        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let req_bean_body_two_methods = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/workspace/smartunitmng".to_string(),
            fileUrl: "/Users/<USER>/workspace/smartunitmng/app/service/src/main/java/com/alipay/sut/service/gpt/impl/llmtestdata/ImportFilter.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::UnitTest,
            fileContent: file_content.to_string(),
            selectedContent: two_method_body_partial_selected.to_string(),
            startLine: 14,
            endLine: 22,
            ..default_req_bean
        };

        let related_data_vec = parser_unit_test(&req_bean_body_two_methods);
        let mut contains_object_a = false;
        let mut contains_test_class = false;
        let mut contains_rw = false;
        for item in &related_data_vec {
            if item.extract.as_ref().unwrap() == "TEST_CASE_SAMPLE" {
                contains_test_class = true;
            } else if item.extract.as_ref().unwrap() == "SELECTED_CONTENT_RW" {
                contains_rw = true;
            } else if item.extract.as_ref().unwrap() == "PARAM_DATA" && item.content.contains("public class ObjectA") {
                contains_object_a = true;
            }
        }
        assert!(contains_object_a);
        assert!(contains_test_class);
        assert!(contains_rw);
    }

    #[test]
    //#[ignore]
    fn test_parser_with_three_method_partial_selected() {
        // 测试圈选三个方法部分代码结果
        fs::remove_file("/Users/<USER>/.codefuse/temp/LOCK");
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // # case1
        let file_content = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.model.CaseRunResult;

        /**
         * <AUTHOR>
         * @date 2024/09/05
         * 测试目的有几个：1. 测试方法参数对象field是否正确精简。2. 参数对象import是否加入
         */
        public class ParamFieldFilter {

            private ImportFilter2 iFilter;
            public CaseRunResult entry(String op) {
                boolean ret = getBoolean(iFilter, op);
                CaseRunResult caseRunResult = new CaseRunResult();
                if(ret) {
                    caseRunResult.setClassName("TrueClass");
                    caseRunResult.setRunResult(iFilter.getOne());
                } else {
                    caseRunResult.setClassName("FalseClass");
                    caseRunResult.setRunResult("No");
                }
                return caseRunResult;
            }

            public boolean entry2(ObjectA oA) {
                String ret = iFilter.filter(oA);
                return ret.equals("123");
            }

            private boolean getBoolean(ImportFilter2 importFilter, String op ) {
                return importFilter.antCodeEnumGet(op);
            }

            public ImportFilter2 getiFilter() {
                return iFilter;
            }

            public void setiFilter(ImportFilter2 iFilter) {
                this.iFilter = iFilter;
            }
        }

        "#;
        let three_method_body_partial_selected = r#"
            CaseRunResult caseRunResult = new CaseRunResult();
            if(ret) {
                caseRunResult.setClassName("TrueClass");
                caseRunResult.setRunResult(iFilter.getOne());
            } else {
                caseRunResult.setClassName("FalseClass");
                caseRunResult.setRunResult("No");
            }
            return caseRunResult;
        }

        public boolean entry2(ObjectA oA) {
            String ret = iFilter.filter(oA);
            return ret.equals("123");
        }

        private boolean getBoolean(ImportFilter2 importFilter, String op ) {
            return importFilter.antCodeEnumGet(op);
        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let req_bean_body_two_methods = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/workspace/smartunitmng".to_string(),
            fileUrl: "/Users/<USER>/workspace/smartunitmng/app/service/src/main/java/com/alipay/sut/service/gpt/impl/llmtestdata/ImportFilter.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::UnitTest,
            fileContent: file_content.to_string(),
            selectedContent: three_method_body_partial_selected.to_string(),
            startLine: 15,
            endLine: 32,
            endOffSet: 1007,
            startOffSet: 421,
            ..default_req_bean
        };

        let related_data_vec = parser_unit_test(&req_bean_body_two_methods);
        let mut contains_rw = false;
        for item in &related_data_vec {
            if item.extract.as_ref().unwrap() == "SELECTED_CONTENT_RW" {
                contains_rw = true;
            }
        }
        // 目前圈选多个方法会拿到第一个方法，这里先这样判断
        assert!(related_data_vec.get(1).unwrap().content.contains("public class CaseRunResult"));
        assert!(contains_rw);
    }


    #[test]
    //#[ignore]
    fn test_parser_with_only_method_name_selected() {
        // 测试圈选三个方法部分代码结果
        fs::remove_file("/Users/<USER>/.codefuse/temp/LOCK");
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // # case1
        let file_content = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.model.enums.AntCodeModifyEnum;
        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import com.alipay.sut.service.gpt.impl.ObjectC;

        /**
         * <AUTHOR>
         * @date 2024/09/03
         */
        public class ImportFilter {
            public String filter(ObjectA oA) {
                ObjectB objectB = new ObjectB();
                String bField = objectB.getbField();
                if(bField.equals("123")) {
                    return "haha";
                }
                return oA.getaField();
            }

            private String cFiled(ObjectC oC) {
                return oC.cMethod();
            }

            public boolean antCodeEnumGet(String code) {
                if(AntCodeModifyEnum.MODIFY.getCode().equals(code)) {
                    return true;
                }
                return false;
            }
        }

        "#;
        let three_method_body_partial_selected = r#"
        cFiled
        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let req_bean_body_only_method_name = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/workspace/smartunitmng".to_string(),
            fileUrl: "/Users/<USER>/workspace/smartunitmng/app/service/src/main/java/com/alipay/sut/service/gpt/impl/llmtestdata/ImportFilter.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::UnitTest,
            fileContent: file_content.to_string(),
            selectedContent: three_method_body_partial_selected.to_string(),
            startLine: 21,
            endLine: 21,
            endOffSet: 588,
            startOffSet: 582,
            ..default_req_bean
        };

        let related_data_vec = parser_unit_test(&req_bean_body_only_method_name);
        let mut contains_test_class = false;
        let mut contains_rw = false;
        for item in &related_data_vec {
            if item.extract.as_ref().unwrap() == "TEST_CASE_SAMPLE" {
                contains_test_class = true;
            } else if item.extract.as_ref().unwrap() == "SELECTED_CONTENT_RW" {
                contains_rw = true;
            }
        }
        // 目前圈选多个方法会拿到第一个方法，这里先这样判断
        assert!(related_data_vec.get(2).unwrap().content.contains("public class ObjectC extends ObjectB"));
        assert!(contains_test_class);
        assert!(contains_rw);
    }

    // 测试返回值是对象时是否能正确拿到依赖
    #[test]
    //#[ignore]
    fn test_parser_unit_test_for_return_obj() {
        fs::remove_file("/Users/<USER>/.codefuse/temp/LOCK");
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        let select_content = r#"
        public CaseRunResult tryWith(String op) {
            boolean ret = getBoolean(iFilter, op);
            return null;
        }
        "#;
        let file_content = r#"
        public class ParamFieldFilter {

            private ImportFilter2 iFilter;
            public CaseRunResult entry(String op) {
                boolean ret = getBoolean(iFilter, op);
                CaseRunResult caseRunResult = new CaseRunResult();
                if(ret) {
                    caseRunResult.setClassName("TrueClass");
                    caseRunResult.setRunResult(iFilter.getOne());
                } else {
                    caseRunResult.setClassName("FalseClass");
                    caseRunResult.setRunResult("No");
                }
                return caseRunResult;
            }

            public CaseRunResult tryWith(String op) {
                boolean ret = getBoolean(iFilter, op);
                return null;
            }

            public boolean entry2(ObjectA oA) {
                String ret = iFilter.filter(oA);
                return ret.equals("123");
            }

            private boolean getBoolean(ImportFilter2 importFilter, String op ) {
                return importFilter.antCodeEnumGet(op);
            }

            public ImportFilter2 getiFilter() {
                return iFilter;
            }

            public void setiFilter(ImportFilter2 iFilter) {
                this.iFilter = iFilter;
            }
        }
        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let req_bean = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/workspace/smartunitmng".to_string(),
            fileUrl: "/Users/<USER>/workspace/smartunitmng/app/service/src/main/java/com/alipay/sut/service/gpt/impl/llmtestdata/ParamFieldFilter.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::UnitTest,
            fileContent: file_content.to_string(),
            selectedContent: select_content.to_string(),
            ..default_req_bean
        };

        let related_data_vec = parser_unit_test(&req_bean);
        let mut contains_return_class = false;
        for item in &related_data_vec {
            if item.extract.as_ref().unwrap() == "PARAM_DATA" {
                contains_return_class = true;
            }
        }
        //  改过以后objectB对象位置固定，param_data第二个
        assert!(related_data_vec.get(0).unwrap().content.contains("public class CaseRunResult"));
        assert!(contains_return_class);
    }


    #[test]
    fn test_complete_java_method_simple() {
        let simple_select_content = r#"
        public String cMethod(){
            String s1 = mayaService.getInferenceResult("121");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        "#;
        let is_complete = is_complete_java_method(simple_select_content);
        assert!(is_complete);

        let simple_select_content_wrong = r#"
        public String cMethod()
            String s1 = mayaService.getInferenceResult("121");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        "#;
        let is_complete = is_complete_java_method(simple_select_content_wrong);
        assert!(!is_complete);

        let simple_select_content_wrong_2 = r#"
        public String cMethod(){
            String s1 = mayaService.getInferenceResult("121");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;

        "#;
        let is_complete = is_complete_java_method(simple_select_content_wrong_2);
        assert!(!is_complete);

        let simple_select_content_wrong_only_method = r#"
        cMethod
        "#;
        let is_complete = is_complete_java_method(simple_select_content_wrong_only_method);
        assert!(!is_complete);
    }

    #[test]
    fn test_complete_java_method_simple_str() {
        let simple_select_content = r#"
        public String cMethod(){
            String s1 = mayaService.getInferenceResult("121{");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        "#;
        let is_complete = is_complete_java_method(simple_select_content);
        assert!(is_complete);

        let simple_select_content_2 = r#"
        public String cMethod(){
            String s1 = mayaService.getInferenceResult("{121");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        "#;
        let is_complete = is_complete_java_method(simple_select_content_2);

        assert!(is_complete);
        let simple_select_content_wrong = r#"
        public String cMethod()
            String s1 = mayaService.getInferenceResult("{121");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        "#;
        let is_complete = is_complete_java_method(simple_select_content_wrong);
        assert!(!is_complete);

        let simple_select_content_wrong_2 = r#"
        public String cMethod(){
            String s1 = mayaService.getInferenceResult("121}");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;

        "#;
        let is_complete = is_complete_java_method(simple_select_content_wrong_2);
        assert!(!is_complete);
    }

    #[test]
    fn test_complete_java_method_simple_comment() {
        let simple_select_content = r#"
        public String cMethod(){
            // 这是注释{
            String s1 = mayaService.getInferenceResult("121{");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        "#;
        let is_complete = is_complete_java_method(simple_select_content);
        assert!(is_complete);

        let simple_select_content_2 = r#"
        public String cMethod(){
            // 这是注释{} }
            String s1 = mayaService.getInferenceResult("{121");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        "#;
        let is_complete = is_complete_java_method(simple_select_content_2);
        assert!(is_complete);

        let simple_select_content_multi_comment_1 = r#"
        public String cMethod() {
            /*
            多行注释1 {
            */
            String s1 = mayaService.getInferenceResult("{121");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        "#;
        let is_complete = is_complete_java_method(simple_select_content_multi_comment_1);
        assert!(is_complete);

        let simple_select_content_multi_comment_2 = r#"
        public String cMethod(){
            /**
             * 多行注释2 }
             *
             */
            String s1 = mayaService.getInferenceResult("121}");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        "#;
        let is_complete = is_complete_java_method(simple_select_content_multi_comment_2);
        assert!(is_complete);

        let simple_select_content_multi_comment_3 = r#"
        /**
         * 多行注释3 }
         *
         */
        public String cMethod(){
            String s1 = mayaService.getInferenceResult("121}");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        /**
         * 多行注释3 {
         *
         */
        "#;
        let is_complete = is_complete_java_method(simple_select_content_multi_comment_3);
        assert!(is_complete);

        let simple_select_content_wrong = r#"
        if (hello){
            // 这是注释{} }
            String s1 = mayaService.getInferenceResult("{121");
            GptModelDetailDO detailDO = gptModelService.queryGptModelDetail(123L);
            String s2 = bMethod();
            return s1 + detailDO.getName() + s2;
        }
        "#;
        let is_complete = is_complete_java_method(simple_select_content_wrong);
        assert!(!is_complete);
    }

    #[test]
    fn test_complete_java_method_complex() {
        let complex_select_content = r#"
        @Override
        public String getStreamResult(Map<String, String> itemMap,
                                      MayaClientReqParam mayaClientReqParam,
                                      boolean isCodefuseModel,
                                      HttpServletResponse httpServletResponse,
                                      boolean frontStreamMode) {

            MayaClientConfig config = new MayaClientConfig();
            // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
            config.setAppName("smartunitmng");
            MayaClient mayaClient = MayaClient.getInstance(config);
            // 构造请求
            MayaRequest request = getMayaRequest(itemMap, mayaClientReqParam);
            // 流式调用必须使用grpc协议
            request.setRpcMode(RpcMode.GRPC);
            // 流式响应缓存
            ModelStreamBuffer modelStreamBuffer = new ModelStreamBuffer();
            log.info("maya client stream request : {}", request);
            // 超时控制
            final CountDownLatch finishLatch = new CountDownLatch(1);
            if(frontStreamMode) {
                httpServletResponse.setContentType("text/event-stream");
                httpServletResponse.setHeader("Cache-Control", "no-cache");
                httpServletResponse.setCharacterEncoding("UTF-8");
            }

            // 总超时时间
            long outputAllTime = DEFAULT_STREAM_ALL_TIME;
            if(itemMap.containsKey("stream_all_timeout")) {
                outputAllTime = Long.parseLong(itemMap.get("stream_all_timeout"));
            }

            // 本地测试，fake结果
            if("dev".equals(dbmode) || "test".equals(dbmode)) {
                for(int index = 0; index < 10; ++index) {
                    MayaResponse response = getFakeMayaResponse(isCodefuseModel, index);
                    parseStreamMayaResponseObj(response, mayaClientReqParam, isCodefuseModel, httpServletResponse, modelStreamBuffer, frontStreamMode);
                    try {
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error("fake result sleep error: ", e);
                    }
                }

                // fake finish }
                try {
                    String contentData = "[DONE]";
                    JSONObject contentJson = new JSONObject();
                    contentJson.put("content", contentData);
                    if(frontStreamMode) {
                        flushSseResponse(httpServletResponse, contentJson.toJSONString());
                    }
                } catch (IOException ioe) {
                    log.warn("GPT: fake stream result close exception: ", ioe);
                }
                finishLatch.countDown();

            } else {
                mayaClient.modelStreamInfer(request, new MayaStreamObserver<MayaResponse>() {
                    @Override
                    public void onNext(MayaResponse response) {
                        parseStreamMayaResponseObj(response, mayaClientReqParam, isCodefuseModel, httpServletResponse, modelStreamBuffer, frontStreamMode);
                    }

                    @Override
                    public void onError(Throwable t) {
                        log.error("request maya stream onError failed", t);
                        try {
                            String errMsg = "[llm output error]";
                            JSONObject contentJson = new JSONObject();
                            contentJson.put("content", errMsg);
                            modelStreamBuffer.buffer(errMsg);
                            if(frontStreamMode) {
                                flushSseResponse(httpServletResponse, contentJson.toJSONString());
                            }
                        } catch (IOException e) {
                            log.error("request maya stream err close httpServletResponse failed", e);
                        }
                        finishLatch.countDown();
                    }

                    @Override
                    public void onCompleted() {
                        try {
                            String contentData = "[DONE]";
                            JSONObject contentJson = new JSONObject();
                            contentJson.put("content", contentData);
                            if(frontStreamMode) {
                                flushSseResponse(httpServletResponse, contentJson.toJSONString());
                            }
                        } catch (IOException e) {
                            log.error("request maya stream complete close httpServletResponse failed", e);
                        }
                        finishLatch.countDown();
                    }
                });
            }

            boolean result = false;
            try {
                result = finishLatch.await(outputAllTime, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                log.error("request maya } await failed",e);
            }
            if (!result) {
                try {
                    String timeOutMsg = "[llm output timeout]";
                    JSONObject contentJson = new JSONObject();
                    contentJson.put("content", timeOutMsg);
                    modelStreamBuffer.buffer(timeOutMsg);
                    if(frontStreamMode) {
                        flushSseResponse(httpServletResponse, contentJson.toJSONString());
                    }
                } catch (IOException e) {
                    log.error("request maya close httpServletResponse failed 2: ", e);
                }
                log.error("request maya timeout{");
            }

            // stream输出结束后，需要把所有输出结果收集保存起来
            return modelStreamBuffer.getBufferContent();
        }
        "#;
        let is_complete = is_complete_java_method(complex_select_content);
        assert!(is_complete);

        let complex_select_content_wrong = r#"
         @Override
        public String getStreamResult(Map<String, String> itemMap,
                                      MayaClientReqParam mayaClientReqParam,
                                      boolean isCodefuseModel,
                                      HttpServletResponse httpServletResponse,
                                      boolean frontStreamMode) {

            MayaClientConfig config = new MayaClientConfig();
            // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
            config.setAppName("smartunitmng");
            MayaClient mayaClient = MayaClient.getInstance(config);
            // 构造请求
            MayaRequest request = getMayaRequest(itemMap, mayaClientReqParam);
            // 流式调用必须使用grpc协议
            request.setRpcMode(RpcMode.GRPC);
            // 流式响应缓存
            ModelStreamBuffer modelStreamBuffer = new ModelStreamBuffer();
            log.info("maya client stream request : {}", request);
            // 超时控制
            final CountDownLatch finishLatch = new CountDownLatch(1);
            if(frontStreamMode) {
                httpServletResponse.setContentType("text/event-stream");
                httpServletResponse.setHeader("Cache-Control", "no-cache");
                httpServletResponse.setCharacterEncoding("UTF-8");
            }

            // 总超时时间
            long outputAllTime = DEFAULT_STREAM_ALL_TIME;
            if(itemMap.containsKey("stream_all_timeout")) {
                outputAllTime = Long.parseLong(itemMap.get("stream_all_timeout"));
            }

            // 本地测试，fake结果
            if("dev".equals(dbmode) || "test".equals(dbmode)) {
                for(int index = 0; index < 10; ++index) {
                    MayaResponse response = getFakeMayaResponse(isCodefuseModel, index);
                    parseStreamMayaResponseObj(response, mayaClientReqParam, isCodefuseModel, httpServletResponse, modelStreamBuffer, frontStreamMode);
                    try {
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error("fake result sleep error: ", e);
                    }
                }

                // fake finish }
                try {
                    String contentData = "[DONE]";
                    JSONObject contentJson = new JSONObject();
                    contentJson.put("content", contentData);
                    if(frontStreamMode) {
                        flushSseResponse(httpServletResponse, contentJson.toJSONString());
                    }
                } catch (IOException ioe) {
                    log.warn("GPT: fake stream result close exception: ", ioe);
                }
                finishLatch.countDown();

            } else {
                mayaClient.modelStreamInfer(request, new MayaStreamObserver<MayaResponse>() {
                    @Override
                    public void onNext(MayaResponse response) {
                        parseStreamMayaResponseObj(response, mayaClientReqParam, isCodefuseModel, httpServletResponse, modelStreamBuffer, frontStreamMode);
                    }

                    @Override
                    public void onError(Throwable t) {
                        log.error("request maya stream onError failed", t);
                        try {
                            String errMsg = "[llm output error]";
                            JSONObject contentJson = new JSONObject();
                            contentJson.put("content", errMsg);
                            modelStreamBuffer.buffer(errMsg);
                            if(frontStreamMode) {
                                flushSseResponse(httpServletResponse, contentJson.toJSONString());

                        } catch (IOException e) {
                            log.error("request maya stream err close httpServletResponse failed", e);
                        }
                        finishLatch.countDown();
                    }

                    @Override
                    public void onCompleted() {
                        try {
                            String contentData = "[DONE]";
                            JSONObject contentJson = new JSONObject();
                            contentJson.put("content", contentData);
                            if(frontStreamMode) {
                                flushSseResponse(httpServletResponse, contentJson.toJSONString());
                            }
                        } catch (IOException e) {
                            log.error("request maya stream complete close httpServletResponse failed", e);
                        }
                        finishLatch.countDown();
                    }
                });
            }

            boolean result = false;
            try {
                result = finishLatch.await(outputAllTime, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                log.error("request maya } await failed",e);
            }
            if (!result) {
                try {
                    String timeOutMsg = "[llm output timeout]";
                    JSONObject contentJson = new JSONObject();
                    contentJson.put("content", timeOutMsg);
                    modelStreamBuffer.buffer(timeOutMsg);
                    if(frontStreamMode) {
                        flushSseResponse(httpServletResponse, contentJson.toJSONString());
                    }
                } catch (IOException e) {
                    log.error("request maya close httpServletResponse failed 2: ", e);
                }
                log.error("request maya timeout{");
            }

            // stream输出结束后，需要把所有输出结果收集保存起来
            return modelStreamBuffer.getBufferContent();
        }
        "#;
        let is_complete = is_complete_java_method(complex_select_content_wrong);
        assert!(!is_complete);
    }

    #[test]
    fn test_vec_insert() {
        let mut vec_insert = Vec::new();
        vec_insert.insert(0, "haha");
        println!("{}", vec_insert.get(0).unwrap());
    }

    #[test]
    //#[ignore]
    fn test_test_file_normal() {
        // 测试 测试类补充场景，正常用例
        fs::remove_file("/Users/<USER>/.codefuse/temp/LOCK");
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        // # case1
        let file_content = r#"
        package com.alipay.sut.service.gpt.impl.llmtestdata;

        import com.alipay.sut.service.gpt.impl.ObjectA;
        import com.alipay.sut.service.gpt.impl.ObjectB;
        import org.junit.Before;
        import org.junit.Test;
        import org.mockito.InjectMocks;
        import org.mockito.MockitoAnnotations;
        import org.junit.runner.RunWith;
        import org.mockito.Mock;
        import org.mockito.junit.MockitoJUnitRunner;
        import static org.junit.Assert.assertEquals;
        import static org.mockito.Mockito.when;

        /**
         * <AUTHOR>
         * @date 2024/09/11
         */
        @RunWith(MockitoJUnitRunner.class)
        public class ImportFilterTest {
            @InjectMocks
            private ImportFilter importFilter;

            @Mock
            private ObjectB objectB;

            @Before
            public void setUp() {
                ImportFilter importFilter = new ImportFilter();
            }

            @Test
            public void handleWrite() {
                ImportFilter importFilter = new ImportFilter();
                importFilter.antCodeEnumGet("123");
            }

            /**
             * [单测用例]测试场景：当ObjectB的bField为"123"时，返回"haha"
             */
            @Test
            public void testFilter_bFieldIs123() {
                ObjectA oA = new ObjectA();
                oA.setaField("test");
                when(objectB.getbField()).thenReturn("123");

                String result = importFilter.filter(oA);

                assertEquals("haha", result);
            }

            /**
             * [单测用例]测试场景：当ObjectB的bField不为"123"时，返回ObjectA的aField
             */
            @Test
            public void testFilter_bFieldIsNot123() {
                ObjectA oA = new ObjectA();
                oA.setaField("test");
                when(objectB.getbField()).thenReturn("456");

                String result = importFilter.filter(oA);

                assertEquals("test", result);
            }
        }

        "#;
        let three_method_body_partial_selected = r#"
        @Test
        public void testFilter_bFieldIsNot123() {
            ObjectA oA = new ObjectA();
            oA.setaField("test");
            when(objectB.getbField()).thenReturn("456");

            String result = importFilter.filter(oA);

            assertEquals("test", result);
        }
        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let req_bean_body_only_method_name = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/workspace/smartunitmng".to_string(),
            fileUrl: "/Users/<USER>/workspace/smartunitmng/app/service/src/test/java/com/alipay/sut/service/gpt/impl/llmtestdata/ImportFilterTest.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::UnitTest,
            fileContent: file_content.to_string(),
            selectedContent: three_method_body_partial_selected.to_string(),
            startLine: 0,
            endLine: 0,
            endOffSet: 588,
            startOffSet: 582,
            testFile: true,
            ..default_req_bean
        };

        let related_data_vec = parser_unit_test(&req_bean_body_only_method_name);
        let mut contains_test_class = false;
        let mut contains_rw = false;
        for item in &related_data_vec {
            if item.extract.as_ref().unwrap() == "TEST_CASE_SAMPLE" {
                contains_test_class = true;
            } else if item.extract.as_ref().unwrap() == "SELECTED_CONTENT_RW" {
                contains_rw = true;
            }
        }
        // 目前圈选多个方法会拿到第一个方法，这里先这样判断
        assert!(related_data_vec.get(0).unwrap().content.contains("true"));  // is_test_file
        assert!(related_data_vec.get(1).unwrap().content.contains("public class ImportFilter {")); // class_under_test
        assert!(related_data_vec.get(1).unwrap().content.contains("public String filter(ObjectA oA) {")); // method_under_test
        assert!(!contains_test_class);
        assert!(!contains_rw);
    }

    #[test]
    //#[ignore]
    fn test_parser_unit_test_with_wrong_context() {
        // TODO: BAD CASE! 圈选方法有内部方法时，method_info(get_relate_item_from_prompt)会有问题
        fs::remove_file("/Users/<USER>/.codefuse/temp/LOCK");
        let scan_max_file_size = 10000000;
        let args = format!("--base-data-url=~/.codefuse --scan-skip-file-size={}", scan_max_file_size);
        pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        let select_content = r#"
            @Override
        public String getStreamResult(Map<String, String> itemMap,
                                      MayaClientReqParam mayaClientReqParam,
                                      boolean isCodefuseModel,
                                      HttpServletResponse httpServletResponse,
                                      boolean frontStreamMode) {

            MayaClientConfig config = new MayaClientConfig();
            // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
            config.setAppName("smartunitmng");
            MayaClient mayaClient = MayaClient.getInstance(config);
            // 构造请求
            MayaRequest request = getMayaRequest(itemMap, mayaClientReqParam);
            // 流式调用必须使用grpc协议
            request.setRpcMode(RpcMode.GRPC);
            // 流式响应缓存
            ModelStreamBuffer modelStreamBuffer = new ModelStreamBuffer();
            log.info("maya client stream request : {}", request);
            // 超时控制
            final CountDownLatch finishLatch = new CountDownLatch(1);
            if(frontStreamMode) {
                httpServletResponse.setContentType("text/event-stream");
                httpServletResponse.setHeader("Cache-Control", "no-cache");
                httpServletResponse.setCharacterEncoding("UTF-8");
            }

            // 总超时时间
            long outputAllTime = DEFAULT_STREAM_ALL_TIME;
            if(itemMap.containsKey("stream_all_timeout")) {
                outputAllTime = Long.parseLong(itemMap.get("stream_all_timeout"));
            }

            // 本地测试，fake结果
            if("dev".equals(dbmode) || "test".equals(dbmode)) {
                for(int index = 0; index < 10; ++index) {
                    MayaResponse response = getFakeMayaResponse(isCodefuseModel, index);
                    parseStreamMayaResponseObj(response, mayaClientReqParam, isCodefuseModel, httpServletResponse, modelStreamBuffer, frontStreamMode);
                    try {
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error("fake result sleep error: ", e);
                    }
                }

                // fake finish
                try {
                    String contentData = "[DONE]";
                    JSONObject contentJson = new JSONObject();
                    contentJson.put("content", contentData);
                    if(frontStreamMode) {
                        flushSseResponse(httpServletResponse, contentJson.toJSONString());
                    }
                } catch (IOException ioe) {
                    log.warn("GPT: fake stream result close exception: ", ioe);
                }
                finishLatch.countDown();

            } else {
                mayaClient.modelStreamInfer(request, new MayaStreamObserver<MayaResponse>() {
                    @Override
                    public void onNext(MayaResponse response) {
                        parseStreamMayaResponseObj(response, mayaClientReqParam, isCodefuseModel, httpServletResponse, modelStreamBuffer, frontStreamMode);
                    }

                    @Override
                    public void onError(Throwable t) {
                        log.error("request maya stream onError failed", t);
                        try {
                            String errMsg = "[llm output error]";
                            JSONObject contentJson = new JSONObject();
                            contentJson.put("content", errMsg);
                            modelStreamBuffer.buffer(errMsg);
                            if(frontStreamMode) {
                                flushSseResponse(httpServletResponse, contentJson.toJSONString());
                            }
                        } catch (IOException e) {
                            log.error("request maya stream err close httpServletResponse failed", e);
                        }
                        finishLatch.countDown();
                    }

                    @Override
                    public void onCompleted() {
                        try {
                            String contentData = "[DONE]";
                            JSONObject contentJson = new JSONObject();
                            contentJson.put("content", contentData);
                            if(frontStreamMode) {
                                flushSseResponse(httpServletResponse, contentJson.toJSONString());
                            }
                        } catch (IOException e) {
                            log.error("request maya stream complete close httpServletResponse failed", e);
                        }
                        finishLatch.countDown();
                    }
                });
            }

            boolean result = false;
            try {
                result = finishLatch.await(outputAllTime, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                log.error("request maya await failed",e);
            }
            if (!result) {
                try {
                    String timeOutMsg = "[llm output timeout]";
                    JSONObject contentJson = new JSONObject();
                    contentJson.put("content", timeOutMsg);
                    modelStreamBuffer.buffer(timeOutMsg);
                    if(frontStreamMode) {
                        flushSseResponse(httpServletResponse, contentJson.toJSONString());
                    }
                } catch (IOException e) {
                    log.error("request maya close httpServletResponse failed 2: ", e);
                }
                log.error("request maya timeout");
            }

            // stream输出结束后，需要把所有输出结果收集保存起来
            return modelStreamBuffer.getBufferContent();
        }
        "#;
        let file_content = r#"
package com.alipay.sut.service.gpt.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.alipay.arks.client.Item;
import com.alipay.arks.client.enums.ModelServerType;
import com.alipay.arks.client.enums.RpcMode;
import com.alipay.maya.MayaClient;
import com.alipay.maya.config.MayaClientConfig;
import com.alipay.maya.model.MayaRequest;
import com.alipay.maya.model.MayaResponse;
import com.alipay.maya.model.MayaStreamObserver;
import com.alipay.sut.model.gpt.MayaClientReqParam;
import com.alipay.sut.model.gpt.ModelStreamBuffer;
import com.alipay.sut.service.gpt.MayaService;
import com.alipay.sut.utils.exception.SutException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/06/05
 */
@Service
public class MayaServiceImpl implements MayaService {

    private static final Logger log = LoggerFactory.getLogger("GPTACCESS");

    /**
     * 用于环境检测，dev环境mock调用
     */
    @Value("${dbmode}")
    private String dbmode;

    /** stream模型总等待时间，单位：ms **/
    private static final long DEFAULT_STREAM_ALL_TIME = 100 * 1000L;

    @Override
    public String getInferenceResult(String data) {
        MayaClientConfig config = new MayaClientConfig();
        // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
        config.setAppName("smartunitmng");
        MayaClient mayaClient = MayaClient.getInstance(config);
        // 构造请求
        MayaRequest request = new MayaRequest();
        // 业务场景名,必填,需要根据服务名寻址
        request.setSceneName("codegpt_single_finetune_v7");
        // 必填,服务版本会作为url路径http
        request.setChainName("v7");
        // 服务端类型,默认ModelServerType.MAYA
        request.setServerType(ModelServerType.MAYA);
        // 整体超时时间,不设置默认从MayaClientConfig里获取是600ms
        request.setRequestTimeOut(10000);
        // 可选,建立连接超时时间,不设置默认从MayaClientConfig里获取是100ms
        request.setConnectTimeOut(1000);
        // 可选,等待服务端返回超时时间,不设置默认从MayaClientConfig里获取是500ms
        request.setReadTimeOut(9000);
        // item,一个Item代表一次推理，n个Item就是一个长度为n的batch,返回结果也在response.items中
        List<Item> items = new ArrayList<>();
        request.setItems(items);
        Item item = new Item();
        items.add(item);
        item.setItemId("itemId1");
        Map<String, String> item1Features = new HashMap<>();
        item.setFeatures(item1Features);
        item1Features.put("data", data);
        log.info("request : {}", JSON.toJSONString(request));

        MayaResponse response;
        try {
            response = mayaClient.call(request);
        } catch (Exception e) {
            throw new SutException(String.format("请求maya模型%s-%s异常, %s", "codegpt_single_finetune_v7", "v7", e.getMessage()));
        }
        String responseJson = JSON.toJSONString(response);
        log.info("response : {}", responseJson);
        if (response.getErrorCode() == 0) {
            log.info("process success : {}", JSON.toJSONString(response.getResults()));
            return response.getItems().get(0).getAttributes().get("res");
        } else {
            log.error("process failed: error name {}, error message {}", response.getErrorName(),
                response.getErrorMsg());
            throw new SutException(String.format("请求maya模型%s-%s异常, error name: %s, error message: %s",
                "codegpt_single_finetune_v7", "v7",
                response.getErrorName(), response.getErrorMsg()));
        }
    }

    @Override
    public String getInferenceResult(Map<String, String> itemMap, MayaClientReqParam mayaClientReqParam, boolean isCodefuseModel) {
        MayaClientConfig config = new MayaClientConfig();
        // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
        config.setAppName("smartunitmng");
        MayaClient mayaClient = MayaClient.getInstance(config);
        // 构造请求
        MayaRequest request = getMayaRequest(itemMap, mayaClientReqParam);
        log.info("maya client request : {}", request);

        MayaResponse response;
        // 线下环境无法调用maya client，这里进行mock方便功能调试
        if("dev".equals(dbmode) || "test".equals(dbmode)) {
            response = getFakeMayaResponse(isCodefuseModel, 0);
        } else {
            try {
                response = mayaClient.call(request);
            } catch (Exception e) {
                throw (SutException)new SutException(String.format("请求maya模型%s-%s异常, %s", mayaClientReqParam.getSceneName(),
                    mayaClientReqParam.getChainName(), e.getMessage())).initCause(e);
            }
        }
        String responseJson = JSON.toJSONString(response);
        log.info("请求模型: {}, response : {}", mayaClientReqParam.getSceneName(), responseJson);


        if (response.getErrorCode() != 0) {
            log.error("process failed: error name {}, error message {}", response.getErrorName(),
                response.getErrorMsg());
            throw new SutException(String.format("请求maya模型%s-%s异常, error name: %s, error message: %s",
                mayaClientReqParam.getSceneName(), mayaClientReqParam.getChainName(),
                response.getErrorName(), response.getErrorMsg()));
        } else {
            // 不同模型结果不一样，codefuse模型返回值为res
            if(isCodefuseModel) {
                return response.getItems().get(0).getAttributes().get("res");
            } else {
                // latency	TYPE_FP32	-
                // errorMessage	TYPE_STRING	-
                // answer	TYPE_STRING	-
                // answers	TYPE_STRING	-
                // resultCode	TYPE_INT32
                // TODO: 考虑用模板怎么搞
                return getOpensourceModelOutput(mayaClientReqParam, response);
            }
        }
    }

    @Override
    public String getStreamResult(Map<String, String> itemMap,
                                  MayaClientReqParam mayaClientReqParam,
                                  boolean isCodefuseModel,
                                  HttpServletResponse httpServletResponse) {
        return getStreamResult(itemMap, mayaClientReqParam, isCodefuseModel, httpServletResponse, true);
    }

    @Override
    public String getStreamResult(Map<String, String> itemMap,
                                  MayaClientReqParam mayaClientReqParam,
                                  boolean isCodefuseModel,
                                  HttpServletResponse httpServletResponse,
                                  boolean frontStreamMode) {

        MayaClientConfig config = new MayaClientConfig();
        // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
        config.setAppName("smartunitmng");
        MayaClient mayaClient = MayaClient.getInstance(config);
        // 构造请求
        MayaRequest request = getMayaRequest(itemMap, mayaClientReqParam);
        // 流式调用必须使用grpc协议
        request.setRpcMode(RpcMode.GRPC);
        // 流式响应缓存
        ModelStreamBuffer modelStreamBuffer = new ModelStreamBuffer();
        log.info("maya client stream request : {}", request);
        // 超时控制
        final CountDownLatch finishLatch = new CountDownLatch(1);
        if(frontStreamMode) {
            httpServletResponse.setContentType("text/event-stream");
            httpServletResponse.setHeader("Cache-Control", "no-cache");
            httpServletResponse.setCharacterEncoding("UTF-8");
        }

        // 总超时时间
        long outputAllTime = DEFAULT_STREAM_ALL_TIME;
        if(itemMap.containsKey("stream_all_timeout")) {
            outputAllTime = Long.parseLong(itemMap.get("stream_all_timeout"));
        }

        // 本地测试，fake结果
        if("dev".equals(dbmode) || "test".equals(dbmode)) {
            for(int index = 0; index < 10; ++index) {
                MayaResponse response = getFakeMayaResponse(isCodefuseModel, index);
                parseStreamMayaResponseObj(response, mayaClientReqParam, isCodefuseModel, httpServletResponse, modelStreamBuffer, frontStreamMode);
                try {
                    Thread.sleep(1000);
                } catch (Exception e) {
                    log.error("fake result sleep error: ", e);
                }
            }

            // fake finish
            try {
                String contentData = "[DONE]";
                JSONObject contentJson = new JSONObject();
                contentJson.put("content", contentData);
                if(frontStreamMode) {
                    flushSseResponse(httpServletResponse, contentJson.toJSONString());
                }
            } catch (IOException ioe) {
                log.warn("GPT: fake stream result close exception: ", ioe);
            }
            finishLatch.countDown();

        } else {
            mayaClient.modelStreamInfer(request, new MayaStreamObserver<MayaResponse>() {
                @Override
                public void onNext(MayaResponse response) {
                    parseStreamMayaResponseObj(response, mayaClientReqParam, isCodefuseModel, httpServletResponse, modelStreamBuffer, frontStreamMode);
                }

                @Override
                public void onError(Throwable t) {
                    log.error("request maya stream onError failed", t);
                    try {
                        String errMsg = "[llm output error]";
                        JSONObject contentJson = new JSONObject();
                        contentJson.put("content", errMsg);
                        modelStreamBuffer.buffer(errMsg);
                        if(frontStreamMode) {
                            flushSseResponse(httpServletResponse, contentJson.toJSONString());
                        }
                    } catch (IOException e) {
                        log.error("request maya stream err close httpServletResponse failed", e);
                    }
                    finishLatch.countDown();
                }

                @Override
                public void onCompleted() {
                    try {
                        String contentData = "[DONE]";
                        JSONObject contentJson = new JSONObject();
                        contentJson.put("content", contentData);
                        if(frontStreamMode) {
                            flushSseResponse(httpServletResponse, contentJson.toJSONString());
                        }
                    } catch (IOException e) {
                        log.error("request maya stream complete close httpServletResponse failed", e);
                    }
                    finishLatch.countDown();
                }
            });
        }

        boolean result = false;
        try {
            result = finishLatch.await(outputAllTime, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.error("request maya await failed",e);
        }
        if (!result) {
            try {
                String timeOutMsg = "[llm output timeout]";
                JSONObject contentJson = new JSONObject();
                contentJson.put("content", timeOutMsg);
                modelStreamBuffer.buffer(timeOutMsg);
                if(frontStreamMode) {
                    flushSseResponse(httpServletResponse, contentJson.toJSONString());
                }
            } catch (IOException e) {
                log.error("request maya close httpServletResponse failed 2: ", e);
            }
            log.error("request maya timeout");
        }

        // stream输出结束后，需要把所有输出结果收集保存起来
        return modelStreamBuffer.getBufferContent();
    }

    /**
     * 构造maya client request
     * @param itemMap                 请求内容
     * @param mayaClientReqParam      client相关参数
     * @return                        maya请求对象
     */
    private static MayaRequest getMayaRequest(Map<String, String> itemMap, MayaClientReqParam mayaClientReqParam) {
        MayaRequest request = new MayaRequest();
        // 业务场景名,必填,需要根据服务名寻址
        request.setSceneName(mayaClientReqParam.getSceneName());
        // 必填,服务版本会作为url路径http
        request.setChainName(mayaClientReqParam.getChainName());
        // 服务端类型,默认ModelServerType.MAYA
        request.setServerType(ModelServerType.MAYA);
        // 整体超时时间,不设置默认从MayaClientConfig里获取是600ms
        request.setRequestTimeOut(mayaClientReqParam.getRequestTimeOut());
        // 可选,建立连接超时时间,不设置默认从MayaClientConfig里获取是100ms
        request.setConnectTimeOut(mayaClientReqParam.getConnectionTimeOut());
        // 可选,等待服务端返回超时时间,不设置默认从MayaClientConfig里获取是500ms
        request.setReadTimeOut(mayaClientReqParam.getReadTimeOut());

        List<Item> items = new ArrayList<>();
        request.setItems(items);
        Item item = new Item();
        items.add(item);
        item.setItemId("itemId1");
        item.setFeatures(itemMap);
        return request;
    }

    private String getOpensourceModelOutput(MayaClientReqParam mayaClientReqParam, MayaResponse response) {
        Map<String, String> result = response.getItems().get(0).getAttributes();
        String answers = result.get("answers");
        if(null == answers) {
            log.error("maya client: parse answers error, is null.");
            throw new SutException(String.format("处理maya模型 %s-%s 响应异常,  parse answers error, is null.",
                mayaClientReqParam.getSceneName(), mayaClientReqParam.getChainName()));
        }
        JSONObject ansObj = JSONObject.parseObject(answers);
        JSONObject firstChoice = ansObj.getJSONArray("choices").getJSONObject(0);
        // 流式返回值为delta，同步的为message
        if(firstChoice.containsKey("delta")) {
            return firstChoice.getJSONObject("delta").getString("content");
        } else if(firstChoice.containsKey("message")) {
            return firstChoice.getJSONObject("message").getString("content");
        } else {
            log.error("maya client: parse content error.");
            throw new SutException(String.format("处理maya模型 %s-%s 响应异常, parse content error.",
                mayaClientReqParam.getSceneName(), mayaClientReqParam.getChainName()));
        }
    }

    private static MayaResponse getFakeMayaResponse(boolean isCodefuseModel, int index) {
        MayaResponse response;
        response = new MayaResponse();
        response.setErrorCode(0);
        List<Item> itemListMock = new ArrayList<>();
        response.setItems(itemListMock);
        Item itemMock = new Item();
        itemListMock.add(itemMock);
        Map<String, String> attrMock = new HashMap<>();
        itemMock.setAttributes(attrMock);
        if(isCodefuseModel) {
            JSONObject resJson = new JSONObject();
            JSONArray generateCode = new JSONArray();
            JSONArray innerArray = new JSONArray();
            innerArray.add("codefuse mode fake result " + index);
            generateCode.add(innerArray);
            resJson.put("generated_code", generateCode);
            attrMock.put("res", resJson.toJSONString());
        } else {
            String latencyMock = "5";
            String errMsgMock = "ok";
            JSONObject answersJsonMock = new JSONObject();
            JSONArray choicesMock = new JSONArray();
            answersJsonMock.put("choices", choicesMock);
            JSONObject oneChoiceMock = new JSONObject();
            choicesMock.add(oneChoiceMock);
            JSONObject msgJsonMock = new JSONObject();
            oneChoiceMock.put("message", msgJsonMock);
            msgJsonMock.put("content", "opensource model fake result " + index + "\n");
            attrMock.put("latency", latencyMock);
            attrMock.put("errorMessage", errMsgMock);
            attrMock.put("answer", answersJsonMock.toJSONString());
            attrMock.put("answers", answersJsonMock.toJSONString());
            attrMock.put("resultCode", "0");
        }
        return response;
    }

    private void parseStreamMayaResponseObj(MayaResponse response,
                                            MayaClientReqParam mayaClientReqParam,
                                            boolean isCodefuseModel,
                                            HttpServletResponse httpServletResponse,
                                            ModelStreamBuffer modelStreamBuffer,
                                            boolean frontStreamMode) {
        if (response.getErrorCode() == 0) {
            log.info("request maya process success:{}", JSON.toJSONString(response));
            Map<String, String> responseMap = response.getItems().get(0).getAttributes();
            String content = "";
            if (isCodefuseModel) {
                String res = responseMap.get("res");
                Optional<JSONObject> responseJson = Optional.of(JSON.parseObject(res));
                String generatedCode = responseJson.map(retJson ->
                    retJson.getJSONArray("generated_code").getJSONArray(0).getString(0)).orElse(null);
                if (generatedCode == null) {
                    content = "model output is null";
                } else {
                    //临时处理，后续模型会修复
                    if (generatedCode.contains("<human>: ")) {
                        content = generatedCode.split("<human>: ")[0];
                    }
                    if (generatedCode.contains("<bot>: ")) {
                        content = generatedCode.split("<bot>: ")[0];
                    }
                }
            } else {
                // latency	TYPE_FP32	-
                // errorMessage	TYPE_STRING	-
                // answer	TYPE_STRING	-
                // answers	TYPE_STRING	-
                // resultCode	TYPE_INT32
                // TODO: 考虑用模板怎么搞
                content = getOpensourceModelOutput(mayaClientReqParam, response);
            }

            try {
                modelStreamBuffer.buffer(content);
                JSONObject jsonData = new JSONObject();
                jsonData.put("content", content);
                if(frontStreamMode) {
                    flushSseResponse(httpServletResponse, jsonData.toJSONString());
                }
                //httpServletResponse.getWriter().write(content);
                //httpServletResponse.getWriter().flush();
                // 本地测试，fake结果
                if("dev".equals(dbmode) || "test".equals(dbmode)) {
                    log.info("GPT: stream delta content is: {}", content);
                }
            } catch (Exception e) {
                log.error("request maya stream flush failed: ", e);
            }
        } else {
            // 服务框架返回错误码
            response.getErrorCode();
            // 服务端返回错误名
            response.getErrorName();
            // 服务端返回异常信息
            response.getErrorMsg();
            // 服务端返回debug信息
            response.getDebugMsg();
            // python 节点自定义错误码
            response.getAlgoRet();
            // python 节点的自定义异常日志输出
            response.getAlgoMsg();
            log.error("process failed: error name {}, error message {}", response.getErrorName(),
                response.getErrorMsg());
        }
    }

    /**
     * 测试使用，设置dbMode
     * @param value   dbMode修改值
     */
    public void setDbModeForTest(String value) {
        dbmode = value;
    }

    /**
     * 测试使用，设置dbMode
     * @return     dbMode实际值
     */

    public String getDbModeForTest() {
        return dbmode;
    }

    private void flushSseResponse(HttpServletResponse httpServletResponse, String data) throws IOException {
        httpServletResponse.getWriter().write("data: ");
        httpServletResponse.getWriter().write(data);
        httpServletResponse.getWriter().write("\n\n");
        httpServletResponse.getWriter().flush();
    }

}

        "#;
        let default_req_bean = QueryRelatedRequestBean::default();
        let req_bean = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/workspace/smartunitmng".to_string(),
            fileUrl: "/Users/<USER>/workspace/smartunitmng/app/service/src/main/java/com/alipay/sut/service/gpt/impl/MayaServiceImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::UnitTest,
            fileContent: file_content.to_string(),
            selectedContent: select_content.to_string(),
            startLine: 159,
            endLine: 280,
            ..default_req_bean
        };

        let related_data_vec = parser_unit_test(&req_bean);
        let mut contains_test_class = false;
        for item in &related_data_vec {
            if item.extract.as_ref().unwrap() == "TEST_CASE_SAMPLE" {
                contains_test_class = true;
            }
        }
        //  改过以后objectB对象位置固定，param_data第二个
        assert!(contains_test_class);
    }
}
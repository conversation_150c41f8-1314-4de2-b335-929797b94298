//! 插件配置
//! 收敛运行时配置信息

use crate::dal::remote_client::IndexTypeEnum;
use anyhow::Result;
use clap::Parser;
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};

pub const AGENT_CONFIG_ENV_KEY: &str = "CODE_FUSE_AGENT_CLIENT_CONFIG";

pub static AGENT_CONFIG: Lazy<AgentConfig> = Lazy::new(|| {
    if std::env::var(AGENT_CONFIG_ENV_KEY).is_ok() {
        return AgentConfig::get_config_from_env().expect("fail to parse config from env");
    }
    AgentConfig::from_cli().expect("failed to read agentconfig from cli")
});

///agent配置信息
#[derive(Serialize, Deserialize, Parser, Debug)]
#[clap(author, version, about, long_about = None)]
pub struct AgentConfig {
    //扫描相似文件后缀限制集合
    #[clap(
        long,
        default_value = "java,rs,py,ts,js,go,cpp,tsx,jsx,m,mm,swift,hpp,cc,c,h,ets,kt,swi,html"
    )]
    pub similarity_suffix_arr: String,
    //扫描相关文件后缀限制集合
    #[clap(short, long, default_value = "java,js,mjs,cjs,jsx,ts,mts,cts,tsx,kt")]
    pub related_suffix_arr: String,
    //过滤的文件夹名字
    #[clap(
        short,
        long,
        default_value = "build,target,dist,node_modules,vendor,gitsubmodules,submodule,oh_modules,Pods,SourcePackages,out"
    )]
    pub ignore_file_arr: String,
    //agent数据的根目录
    #[clap(short, long, default_value = "~/.codefuse")]
    pub base_data_url: String,
    //agent的端口号
    #[clap(short, long, default_value_t = 8899)]
    pub port: u16,
    //tcp 服务端口号
    #[clap(long, default_value_t = 0)]
    pub tcp_port: u16,
    //补全时候默认取topN相似度的数量
    #[clap(long, default_value_t = 20)]
    pub code_complation_similarity_top_num: u8,
    //相似度代码块的窗口大小
    #[clap(long, default_value_t = 10)]
    pub code_complation_window_size: u8,
    //相似度代码块的滑动大小
    #[clap(long, default_value_t = 5)]
    pub code_complation_slide_size: u8,
    //相似度判断阈值
    #[clap(long, default_value_t = 0.5)]
    pub similarity_threshold: f64,

    //agent的日志级别
    #[clap(long, default_value = "INFO")]
    pub log_level: String,

    #[clap(short, long, default_value = "0.0.1")]
    pub agent_version: String,

    //如果是1,那么检查当前数据版本,如果数据版本为空或者低于agent_version,那么先删除本地数据
    #[clap(short, long, default_value_t = 0)]
    pub del_data_flag: u8,

    //全量扫描时的每个文件间隔时间(单位:毫秒)
    #[clap(long, default_value_t = 40)]
    pub scan_interval_time: u64,
    //构建对话索引时每个文件间隔时间(单位:毫秒)
    #[clap(long, default_value_t = 60)]
    pub extra_index_build_interval_time: u64,
    //扫描时是否跳过测试文件
    #[clap(long, default_value_t = 0)]
    pub scan_skip_test_flag: u8,

    //扫描时跳过文件大小阈值（默认5M）
    #[clap(long, default_value_t = 5242880)]
    pub scan_skip_file_size: u64,

    //扫描时跳过文件行阈值
    #[clap(long, default_value_t = 10000)]
    pub scan_skip_file_max_len: usize,

    //扫描时超过指定大小时（默认40kb）,才计算文件的行数，文件行数上限参考scan_skip_file_max_len
    #[clap(long, default_value_t = 40960)]
    pub file_size_to_calculate_line: u64,

    //扫描时跳过文件行阈值
    #[clap(long, default_value_t = 10)]
    pub scan_skip_file_min_len: usize,

    //索引缓存超时时间，默认15天
    #[clap(long, default_value_t = 1296000000)]
    pub index_cache_timeout: usize,


    //相似度比较类型。0:光标前后比较。1:光标前比较，命中后向下偏移code_complation_slide_size行
    #[clap(long, default_value_t = 1)]
    pub similarity_cal_type: u8,

    // [接口测试] 类加载器缓存大小
    #[clap(long, default_value_t = 1000)]
    pub it_class_loader_cached_size: usize,

    // [接口测试] 重建仓库文件索引的超时设置（强制刷新）
    #[clap(long, default_value_t = 3600)]
    pub it_rebuild_repo_timeout: u64,

    // [接口测试] 未命中类文件时，重建仓库文件索引的超时设置（eg: 新增文件时刷新）
    #[clap(long, default_value_t = 600)]
    pub it_missing_rebuild_repo_timeout: u64,

    //索引代码chunk的窗口大小s
    #[clap(long, default_value_t = 30)]
    pub index_chunk_window_size: u8,

    //构建测试开关，默认0，如果是1的话，会关闭构建索引前的一系列校验
    #[clap(long, default_value_t = 0)]
    pub build_test_flag: u8,


    //索引代码chunk的滑动大小
    #[clap(long, default_value_t = 30)]
    pub index_chunk_slide_size: u8,

    //embedding 查询超时时间
    #[clap(long, default_value_t = 3000)]
    pub embedding_query_timeout:u64,

    //embedding 查询超时时间
    #[clap(long, default_value_t = 7000)]
    pub embedding_build_timeout:u64,


    //必要索引类型
    #[clap(
        long,
        default_value = "CHUNK-CONTENT",
        value_delimiter = ',',
        ignore_case = true
    )]
    pub index_nessary_type: Vec<IndexTypeEnum>,
    //补充的索引类型. default_value = "CHUNK-CONTENT,CHUNK-VECTOR",
    #[clap(
        long,
        default_value = "CHUNK-CONTENT",
        value_delimiter = ',',
        ignore_case = true
    )]
    pub index_extra_type: Vec<IndexTypeEnum>,

    //对话任务node超时时间(主要用于构建索引阶段），单位：毫秒
    #[clap(long, default_value_t = 10000)]
    pub chat_task_time_out: u128,
    //本地文本，向量检索时，检索的top数据
    #[clap(long, default_value_t = 15)]
    pub index_search_top_num: usize,
    //文本库和本地文件数量的差值，超过就需要跟更新
    #[clap(long, default_value_t = 5)]
    pub diff_count_between_fs_db: usize,
    //对话时rerank排序类型
    #[clap(long, default_value_t = 1)]
    pub chat_rerank_type:u8,
    //对话时rerank的top值
    #[clap(long, default_value_t = 15)]
    pub chat_rerank_top:u8,
    //构建索引时异步线程的数量
    #[clap(long, default_value_t = 1)]
    pub index_async_thread_num:u8,
    //deepsearch轮询的次数，默认3
    #[clap(long, default_value_t = 3)]
    pub deepsearch_count: u8,
    //代码解释场景deepsearch轮询的次数，默认2
    #[clap(long, default_value_t = 2)]
    pub ds_count_explain_code: u8,
    //向量数据库批量写入的大小，默认50
    #[clap(long, default_value_t = 50)]
    pub vector_batch_write_size: usize,
    //是否启用仓库wiki,0是不启用，1是启用
    #[clap(long, default_value_t = 0)]
    pub use_repo_wiki: u8,

    //向量数据库最大存储大小限制（字节），默认3GB
    #[clap(long, default_value_t = 3221225472)]
    pub vector_db_max_storage_size: u64,

    //向量数据库存储检查间隔（毫秒），默认5分钟
    #[clap(long, default_value_t = 300000)]
    pub vector_db_storage_check_interval: u64,

    //向量数据库数据保留天数，默认30天
    #[clap(long, default_value_t = 30)]
    pub vector_db_data_retention_days: u32,

    //向量超过最大磁盘后，删除的文件个数
    #[clap(long, default_value_t = 5000)]
    pub vector_del_count: usize,
    //本地保存索引的最大项目数量
    #[clap(long, default_value_t = 3)]
    pub max_project_size:u8,

    //Git fetch操作超时时间（秒），默认5秒
    #[clap(long, default_value_t = 5)]
    pub git_fetch_timeout_secs: u64,

    //构建索引期间初始化并发
    #[clap(long, default_value_t = 15)]
    pub initial_concurrency:usize,
    //构建索引期间 concurrency_timeout_secs 秒后的并发
    #[clap(long, default_value_t = 4)]
    pub reduced_concurrency:usize,
    //全力用cpu concurrency_timeout_secs秒
    #[clap(long, default_value_t = 20)]
    pub concurrency_timeout_secs:u64,
    #[clap(long, default_value_t = 12)]
    pub cge_batch_size:usize,
    #[clap(long, default_value_t = 30)]
    pub url_batch_size:usize,
    #[clap(long, default_value_t = 50)]
    pub step2_channel_size:usize,

    #[clap(long, default_value_t = 50)]
    pub step3_channel_size:usize

}

///插件配置
/// 默认从cli命令行读取参数, 读取不到的用agent自身默认值
impl AgentConfig {
    // 这个函数返回一个包含默认后缀的 Vec<String>
    pub fn from_cli() -> Result<Self> {
        Ok(Self::try_parse()?)
    }

    /// 从环境变量读取配置
    /// 在进行单元测试时非常有用
    pub fn get_config_from_env() -> Result<Self> {
        let mut default_test_args = vec![env!("CARGO_PKG_NAME").to_string()];
        if let Ok(config) = std::env::var(AGENT_CONFIG_ENV_KEY) {
            let result: Vec<String> = config.split(' ').map(str::to_string).collect();
            default_test_args.extend(result);
        }
        Ok(Self::try_parse_from(default_test_args)?)
    }
}

//单测模块
#[cfg(test)]
mod tests {
    use super::*;
    //测试读取默认配置
    #[test]
    fn read_config_from_env() {
        let scan_max_file_size = 10000000;
        let args = format!(
            "--base-data-url=~/.tmp/codefuse_dev --scan-skip-file-size={}",
            scan_max_file_size
        );
        std::env::set_var(AGENT_CONFIG_ENV_KEY, args);

        assert_eq!(
            AGENT_CONFIG.base_data_url,
            "~/.tmp/codefuse_dev".to_string()
        );
        assert_eq!(AGENT_CONFIG.scan_skip_file_size, scan_max_file_size);
    }
}


use std::fs;

use log::LevelFilter;
use log4rs::append::console::ConsoleAppender;
use log4rs::append::rolling_file::policy::compound::roll::fixed_window::FixedWindowRoller;
use log4rs::append::rolling_file::policy::compound::trigger::size::SizeTrigger;
use log4rs::append::rolling_file::policy::compound::CompoundPolicy;
use log4rs::append::rolling_file::RollingFileAppender;
use log4rs::config::{Appe<PERSON>, Lo<PERSON>, Root};
use log4rs::encode::pattern::PatternEncoder;
use log4rs::Config;
use crate::config::runtime_config::AGENT_CONFIG;
use crate::tools::common_tools::expand_user_home;

const MAX_LOG_NUM: u32 = 3;
const MAX_LOG_SIZE: u64 = 10 * 1024 * 1024;

///初始化日志组件.两个appender: 一个输出到控制台，一个输出到文件
pub fn init_logger() -> Result<(), log::SetLoggerError> {
    //如果没有日志目录则新建
    let log_url = expand_user_home(&AGENT_CONFIG.base_data_url).unwrap().join("logs");
    if !log_url.exists() {
        let _ = fs::create_dir_all(&log_url);
    }
    // 控制台Appender
    let stdout = ConsoleAppender::builder()
        .encoder(Box::new(PatternEncoder::new("{d(%Y-%m-%d %H:%M:%S)} {M} {l} {L}:{m}{n}")))
        .build();

    // 滚动文件Appender
    let size_trigger = SizeTrigger::new(MAX_LOG_SIZE);
    // let pattern = "/Users/<USER>/.codefuse/log/test.{}.log";
    let pattern = log_url.join("codefuse_agent.{}.log");
    let fixed_window_roller = FixedWindowRoller::builder()
        .base(1)
        .build(pattern.to_str().unwrap().trim(), MAX_LOG_NUM)
        .unwrap();

    let compound_policy = CompoundPolicy::new(Box::new(size_trigger), Box::new(fixed_window_roller));
    //此处由于rs机制问题,join单独声明,否则有所有权移动问题
    let current_log = log_url.join("codefuse_agent.log");
    let current_log = current_log.to_str().unwrap().trim();

    let rolling_file = RollingFileAppender::builder()
        .encoder(Box::new(PatternEncoder::new("{d(%Y-%m-%d %H:%M:%S)} {M} {l} {L}:{m}{n}")))
        .build(current_log, Box::new(compound_policy))
        .unwrap();

    // 配置

    let mut log_filter: LevelFilter;
    match AGENT_CONFIG.log_level.trim() {
        "DEBUG" => log_filter = LevelFilter::Debug,
        "WARN" => log_filter = LevelFilter::Warn,
        "ERROR" => log_filter = LevelFilter::Error,
        _ => log_filter = LevelFilter::Info,
    }

    let config = Config::builder()
        .appender(Appender::builder().build("stdout", Box::new(stdout)))
        .appender(Appender::builder().build("rolling_file", Box::new(rolling_file)))

        // 白名单方式：只允许当前项目的日志输出，屏蔽所有第三方依赖日志
        // 允许当前项目的日志
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)
                .build("agent_db", log_filter)
        )
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)
                .build("agent_common_service", log_filter)
        )
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)
                .build("agent_codefuse_service", log_filter)
        )
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)
                .build("agent_client", log_filter)
        )
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)
                .build("agent_gentests_service", log_filter)
        )
        .logger(
            Logger::builder()
                .appender("stdout")
                .appender("rolling_file")
                .additive(false)
                .build("agent_android_service", log_filter)
        )
        // 屏蔽所有 lance 相关的日志（使用通配符模式）
        .logger(
            Logger::builder()
                .build("lance", LevelFilter::Off)
        )
        .logger(
            Logger::builder()
                .build("lancedb", LevelFilter::Off)
        )

        // 屏蔽其他常见的第三方依赖日志
        .logger(
            Logger::builder()
                .build("tantivy", LevelFilter::Warn)
        )
        .logger(
            Logger::builder()
                .build("arrow", LevelFilter::Off)
        )
        .logger(
            Logger::builder()
                .build("datafusion", LevelFilter::Off)
        )
        .logger(
            Logger::builder()
                .build("object_store", LevelFilter::Off)
        )
        .logger(
            Logger::builder()
                .build("reqwest", LevelFilter::Off)
        )
        .logger(
            Logger::builder()
                .build("hyper", LevelFilter::Off)
        )
        .logger(
            Logger::builder()
                .build("tokio", LevelFilter::Off)
        )
        .logger(
            Logger::builder()
                .build("mio", LevelFilter::Off)
        )
        .logger(
            Logger::builder()
                .build("want", LevelFilter::Off)
        )
        .logger(
            Logger::builder()
                .build("h2", LevelFilter::Off)
        )
        .logger(
            Logger::builder()
                .build("tower", LevelFilter::Off)
        )
        .logger(
            Logger::builder()
                .build("tracing", LevelFilter::Off)
        )
        .build(
            Root::builder()
                .appender("stdout")
                .appender("rolling_file")
                // 将 root logger 设置为 WARN 级别，这样只有明确配置的 logger 才会输出详细日志
                .build(LevelFilter::Warn),
        )
        .unwrap();
    // 日志热重载
    log4rs::init_config(config)?;
    Ok(())
}

use serde::{Deserialize, Serialize};

use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::domain::code_kv_index_domain::CodeInfo;

use crate::api::req_model::IntentionType;
use crate::it::interface_model::InterfacePromptData;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RelatedData {
    //代码内容
    pub content: String,
    //java代码模型
    pub model: Option<CodeInfo>,
    //其他信息
    pub extract: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryRelatedResult {
    // agent版本
    pub agent_version: String,
    // [UT] 相关内容列表
    pub related_data_vec: Vec<RelatedData>,
    // [IT] 接口测试prompt数据
    pub interface_prompt_data: Option<InterfacePromptData>,
    // [IT] 接口测试prompt配置
    pub interface_prompt_config: Option<String>,
    // 意图类型
    pub intention_type: IntentionType,
}


impl QueryRelatedResult {
    pub fn build(intention_type: IntentionType) -> Self {
        QueryRelatedResult {
            agent_version: AGENT_CONFIG.agent_version.clone(),
            related_data_vec: Vec::new(),
            interface_prompt_data: None,
            interface_prompt_config: None,
            intention_type,
        }
    }
}
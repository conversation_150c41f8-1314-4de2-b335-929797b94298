use anyhow::Result;
use log::{info, warn};

// use agent_common_service::model::rpc_model::{BaseResponse, build_success_response};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::remote::rpc_model::{build_success_response, BaseResponse};
use crate::api::req_model::{IntentionType, QueryRelatedRequestBean};
use crate::api::res_model::QueryRelatedResult;
use crate::intent::test_intent::intent_recognition;
use crate::it::loader::class_loader::load_xml_files;
use crate::it::config::codefuse_it_config::parser_codefuse_config;
use crate::it::interface_parser::{parse_interface, query_related_data_it};
use crate::ut::project_info::{parser_unit_test, query_related_data_ut};

/// 测试代码生成入口API
pub fn query_related_data_api(query_related_request: QueryRelatedRequestBean) -> Result<BaseResponse<QueryRelatedResult>> {
    info!("[API] 测试生成(query_related_data_api), request=[{:?}], ", query_related_request.simplify());

    let mut intention_type = query_related_request.intentionType.clone();
    if intention_type == IntentionType::None {
        // 完全回退到旧插件, 只有单元测试
        return query_related_data_ut(&query_related_request);
    }

    if intention_type == IntentionType::Auto {
        // 判定意图, 无法判断时全部设置为UnitTest
        intention_type = match intent_recognition(&query_related_request).as_deref() {
            Some("ut") => IntentionType::UnitTest,
            Some("it") => IntentionType::InterfaceTest,
            _ => IntentionType::UnitTest
        };
        info!("[意图识别] 意图识别结果, session_id=[{}], intentionType=[{:?}]", query_related_request.sessionId, intention_type);
    }

    query_related_data_all(&query_related_request, intention_type)
}

/// [模式1] 同时查询UT+IT数据（适用场景: UT/IT的切换是在H5侧，插件侧无法获取H5的点击事件重新发起，所以提前把所有数据准备好）
pub fn query_related_data_all(query_related_request: &QueryRelatedRequestBean, intention_type: IntentionType) -> Result<BaseResponse<QueryRelatedResult>> {
    let related_data_vec = parser_unit_test(&query_related_request);
    let interface_prompt_data = parse_interface(&query_related_request);
    let interface_prompt_config = parser_codefuse_config(query_related_request);
    Ok(build_success_response(QueryRelatedResult {
        agent_version: AGENT_CONFIG.agent_version.clone(),
        related_data_vec,
        interface_prompt_data: Some(interface_prompt_data),
        interface_prompt_config,
        intention_type,
    }))
}

/// [模式2] 根据意图选择UT或IT一种数据（适用场景: 追求代码分析性能）
pub fn query_related_data_one(query_related_request: &QueryRelatedRequestBean, intention_type: IntentionType) -> Result<BaseResponse<QueryRelatedResult>> {
    if intention_type == IntentionType::InterfaceTest {
        // 明确为接口测试
        return query_related_data_it(&query_related_request);
    } else {
        // 明确为单元测试
        return query_related_data_ut(&query_related_request);
    }
}

#[cfg(test)]
mod test {
    use std::fs;
    use log::info;
    use crate::api::api::query_related_data_api;
    use crate::api::req_model::{IntentionType, QueryRelatedRequestBean};
    use crate::it::logger_init::init_logger;


    #[test]
    fn test_parse_smart_server() {
        let _ = init_logger();
        let selected_content: &str = r#"
            public CommonApiResult<Void> record(NodeKeyPointRecordRequest request) {
    "#;

        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/afworkflow/app/application/src/main/java/com/alipay/aiforce/afworkflow/application/facade/OperationMeasureFacadeImpl.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/afworkflow".to_string(),
            fileUrl: "app/application/src/main/java/com/alipay/aiforce/afworkflow/application/facade/OperationMeasureFacadeImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::InterfaceTest,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };

        let result = query_related_data_api(query_related_request);
        if let Ok(result) = result {
            info!("interface_prompt_info={}", serde_json::to_string(&result.data).unwrap());
            println!("{}", serde_json::to_string(&result.data).unwrap());
        }
    }


    #[test]
    fn test_query_related_data_api_get_app() {
        let _ = init_logger();
        let selected_content: &str = r#"
            public AppVO getApp(AppReq req) {
    "#;

        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/rcqualitydataprod/app/core/service/src/main/java/com/alipay/rcqualitydataprod/core/service/itac/impl/ItacAppServiceImpl.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/rcqualitydataprod".to_string(),
            fileUrl: "app/core/service/src/main/java/com/alipay/rcqualitydataprod/core/service/itac/impl/ItacAppServiceImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };

        let result = query_related_data_api(query_related_request);
        if let Ok(result) = result {
            println!("{}", serde_json::to_string(&result.data).unwrap());
            println!("{:?}", serde_json::to_string(&result.data).unwrap());
        }
    }

    #[test]
    fn test_query_related_data_api() {
        let _ = init_logger();
        let selected_content: &str = r#"
            public String doInference(ItacGptApiConfigDO apiConfigDO, GptInferenceReq req) {
    "#;

        let class_content = fs::read_to_string("/Users/<USER>/tinghe-source/rcqualitydataprod/app/core/service/src/main/java/com/alipay/rcqualitydataprod/core/service/itac/impl/gpt/ItacGptServiceImpl.java").unwrap();
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/tinghe-source/rcqualitydataprod".to_string(),
            fileUrl: "app/core/service/src/main/java/com/alipay/rcqualitydataprod/core/service/itac/impl/gpt/ItacGptServiceImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: class_content.to_string(),
            selectedContent: selected_content.to_string(),
            ..default_req_bean
        };

        let result = query_related_data_api(query_related_request);
        if let Ok(result) = result {
            println!("{}", serde_json::to_string(&result.data).unwrap());
            println!("{:?}", serde_json::to_string(&result.data).unwrap());
        }
    }

    #[test]
    #[ignore]
    fn test_query_related_data_api_intent() {
        let new_annotation_file_content = "package com.alipay.sofaboot.rpc.demo.server.service.impl;\\n\\nimport com.alipay.sofa.rpc.api.annotation.RpcProvider;\\nimport com.alipay.sofaboot.rpc.demo.server.facade.NewAnnotateSampleService;\\n\\n@RpcProvider\\npublic class NewAnnotateSampleServiceImpl implements NewAnnotateSampleService {\\n\\n\\n    @Override\\n    public String message() {\\n        return \"Congratulations! Here is call result from RpcProvider Pub Service of Server SOFABoot Server!\";\\n    }\\n}\\n\\n";
        let default_req_bean = QueryRelatedRequestBean::default();
        let query_related_request = QueryRelatedRequestBean {
            sessionId: "".to_string(),
            projectUrl: "/Users/<USER>/workspace/sofaboot-rpc-demo-server".to_string(),
            fileUrl: "/Users/<USER>/workspace/sofaboot-rpc-demo-server/app/biz/service/impl/src/main/java/com/alipay/sofaboot/rpc/demo/server/service/impl/NewAnnotateSampleServiceImpl.java".to_string(),
            language: "java".to_string(),
            intention: "".to_string(),
            fileContent: new_annotation_file_content.to_string(),
            selectedContent: "public void func2() {}".to_string(),
            intentionType: IntentionType::Auto,
            ..default_req_bean
        };
        let result = query_related_data_api(query_related_request.clone());
        // println!("{:?}", serde_json::to_string(&result.unwrap()).unwrap());
        assert_eq!(&IntentionType::InterfaceTest, &result.unwrap().data.unwrap().intention_type);

        // sofa service
        let sofa_service_annotation_file_content = "package com.alipay.sofaboot.rpc.demo.server.service.impl;\\n\\nimport com.alipay.sofa.runtime.api.annotation.SofaService;\\nimport com.alipay.sofa.runtime.api.annotation.SofaServiceBinding;\\nimport com.alipay.sofaboot.rpc.demo.server.facade.AnnotateSampleService;\\nimport org.springframework.stereotype.Component;\\n\\n/**\\n * 通过 {@link SofaService} 注解发布 RPC 服务\\n */\\n@SofaService(bindings = { @SofaServiceBinding(bindingType = \"tr\") })\\n@Component\\npublic class AnnotateSampleServiceImpl implements AnnotateSampleService {\\n\\n    @Override\\n    public String message() {\\n        return \"Congratulations! Here is call result from Annotate Pub Service of Server SOFABoot Server!\";\\n    }\\n}\\n\\n";
        let sofa_service_query_related_request = QueryRelatedRequestBean {
            fileContent: sofa_service_annotation_file_content.to_string(),
            ..query_related_request.clone()
        };
        let result = query_related_data_api(sofa_service_query_related_request.clone());
        // println!("{:?}", serde_json::to_string(&result.unwrap()).unwrap());
        assert_eq!(&IntentionType::InterfaceTest, &result.unwrap().data.unwrap().intention_type);

        // query_related_data_ut还有问题，注释掉
        // let private_query_related_request = QueryRelatedRequestBean {
        //     selectedContent: "private void func1() {}".to_string(),
        //     ..query_related_request.clone()
        // };
        // let result = query_related_data_api(private_query_related_request.clone());
        // println!("{:?}", serde_json::to_string(&result.unwrap()).unwrap());

        let xml_query_related_request = QueryRelatedRequestBean {
            fileUrl: "/Users/<USER>/workspace/sofaboot-rpc-demo-server/app/biz/service/impl/src/main/java/com/alipay/sofaboot/rpc/demo/server/service/impl/XmlSampleServiceImpl.java".to_string(),
            ..query_related_request.clone()
        };
        let result = query_related_data_api(xml_query_related_request.clone());
        // println!("{:?}", serde_json::to_string(&result.unwrap()).unwrap());
        assert_eq!(&IntentionType::InterfaceTest, &result.unwrap().data.unwrap().intention_type);
    }
}
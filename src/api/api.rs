use std::time::{SystemTime, UNIX_EPOCH};
use agent_db::remote::rpc_model::BaseResponse;
use anyhow::Result;
use log::info;
use agent_common_service::model::chat_model::{ChatRelatedRequestBean, ChatRelatedResponse};
use agent_common_service::service::project_chat::ProjectChatStrategy;
use crate::indicator::tina::tina_copilot;

pub struct RichChatStrategy;

impl ProjectChatStrategy for RichChatStrategy {
    async fn query_chat_related_info(chat_related_request: &ChatRelatedRequestBean ) -> Result<BaseResponse<ChatRelatedResponse>> {
        // info!("RichChatStrategy {}", SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis());
        let result = tina_copilot(&chat_related_request).await;
        info!("RichChatStrategy return {:?}", serde_json::to_string(&result).expect("Failed to serialize to JSON"));

       return Ok(result);
    }
}

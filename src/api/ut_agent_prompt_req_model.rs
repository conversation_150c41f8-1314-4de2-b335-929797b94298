use serde::{Deserialize, Serialize};

/// ut agent模式访问case_like获取prompt
#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, Default)]
pub struct TestCasePromptRequestBean {
    #[serde(rename = "sessionId")]
    pub session_id: String,
    #[serde(rename = "relatedData")]
    pub related_data: String,
    pub language: String,
    #[serde(rename = "modelName")]
    pub model_name: String,
    #[serde(rename = "fileContent")]
    pub file_content: String,
    #[serde(rename = "promptReq")]
    pub prompt_req: String,
    #[serde(rename = "testFramework")]
    pub test_framework: String,
    #[serde(rename = "userQuery")]
    pub user_query: String,
}

impl TestCasePromptRequestBean {
    pub fn new(
        session_id: String,
        related_data: String,
        language: String,
        model_name: String,
        file_content: String,
        prompt_req: String,
        test_framework: String,
        user_query: String,
    ) -> Self {
        TestCasePromptRequestBean {
            session_id,
            related_data,
            language,
            model_name,
            file_content,
            prompt_req,
            test_framework,
            user_query,
        }
    }
}
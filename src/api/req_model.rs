use std::cmp::PartialEq;

use serde::{Deserialize, Serialize};

use crate::utils::string_utils::truncate_string;

///查询仓库信息请求参数
#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct QueryRelatedRequestBean {
    // 请求唯一id
    pub sessionId: String,
    // 仓库地址
    pub projectUrl: String,
    // 文件路径
    pub fileUrl: String,
    // 语言
    pub language: String,
    // 意图
    pub intention: String,
    // 意图细分类型, 默认为: UNIT_TEST
    #[serde(default)]
    pub intentionType: IntentionType,
    // 当前文件内容
    pub fileContent: String,
    // 选中的内容
    pub selectedContent: String,
    // 圈选起止行号
    #[serde(default)]
    pub startLine: usize,
    #[serde(default)]
    pub endLine: usize,
    // 圈选起止字符偏移量
    #[serde(default)]
    pub startOffSet: usize,
    #[serde(default)]
    pub endOffSet: usize,

    // 传过来的是否是测试文件，如果是测试文件则走补全用例逻辑
    #[serde(default)]
    pub testFile: bool,

    // 访问模型
    #[serde(default)]
    pub modelName: String,
    // 测试框架
    #[serde(default)]
    pub testFramework: String,
    // 用户原始请求
    #[serde(default)] 
    pub userOrigQuery: String,
}

#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
pub enum IntentionType {
    // 旧插件没有传入intentionType字段, 默认为None, 只做UT处理
    #[serde(rename = "NONE")]
    None,
    // 自动判断意图, (数据包括: UT+IT)
    #[serde(rename = "AUTO")]
    Auto,
    // 单测, (数据包括: UT+IT)
    #[serde(rename = "UNIT_TEST")]
    UnitTest,
    // 接口, (数据包括: UT+IT)
    #[serde(rename = "INTERFACE_TEST")]
    InterfaceTest,
}

// 兼容旧的IDE插件, modelName在字段intentionType缺失情况下默认是Unknown, 回退到单元测试场景
impl Default for IntentionType {
    fn default() -> Self {
        IntentionType::None
    }
}

impl QueryRelatedRequestBean {
    pub fn is_it(&self) -> bool {
        self.intentionType == IntentionType::InterfaceTest
    }
    pub fn is_ut(&self) -> bool {
        self.intentionType == IntentionType::UnitTest
    }
    pub fn is_auto(&self) -> bool {
        self.intentionType == IntentionType::Auto
    }

    pub fn simplify(&self) -> Self {
        QueryRelatedRequestBean {
            sessionId: self.sessionId.clone(),
            projectUrl: self.projectUrl.clone(),
            fileUrl: self.fileUrl.clone(),
            language: self.language.clone(),
            intention: self.intention.clone(),
            intentionType: self.intentionType.clone(),
            fileContent: "...".to_string(),
            selectedContent: truncate_string(self.selectedContent.trim(), 64),
            startLine: self.startLine,
            endLine: self.endLine,
            startOffSet: self.startOffSet,
            endOffSet: self.endOffSet,
            testFile: self.testFile,
            modelName: self.modelName.clone(),
            testFramework: self.testFramework.clone(),
            userOrigQuery: self.userOrigQuery.clone(),
        }
    }
}

#[cfg(test)]
mod test {
    use std::default::Default;
    use crate::api::req_model::{IntentionType, QueryRelatedRequestBean};

    #[test]
    fn test() {
        let default_req_bean = QueryRelatedRequestBean::default();
        let abc = QueryRelatedRequestBean {
            sessionId: "abc".to_string(),
            projectUrl: "abc".to_string(),
            fileUrl: "abcFile".to_string(),
            language: "java".to_string(),
            intention: "ABC".to_string(),
            intentionType: IntentionType::Auto,
            fileContent: "FILE".to_string(),
            selectedContent: "SELECT".to_string(),
            ..default_req_bean
        };

        println!("{}", serde_json::to_string(&abc).unwrap());

        let json = r#"{
    "sessionId": "abc",
    "projectUrl": "abc",
    "fileUrl": "abcFile",
    "language": "java",
    "intention": "ABC",
    "fileContent": "FILE",
    "selectedContent": "SELECT",
    "startLine": 0,
    "endLine": 0,
    "startOffSet": 0,
    "endOffSet": 0
}"#;
        let parser: serde_json::error::Result<QueryRelatedRequestBean> = serde_json::from_str(json);
        let data = parser.unwrap();
        println!("{}", serde_json::to_string(&data).unwrap());
    }
}

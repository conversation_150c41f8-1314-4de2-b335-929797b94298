use serde_json;

// 复制相关的结构体定义来进行独立测试
#[derive(Debug, serde::Serialize, serde::Deserialize, Clone)]
pub struct RequestId(pub i64);

impl From<i64> for RequestId {
    fn from(id: i64) -> Self {
        RequestId(id)
    }
}

#[derive(Debug, serde::Serialize, serde::Deserialize, Clone)]
pub struct Request {
    pub id: RequestId,
    pub method: String,
    #[serde(default = "serde_json::Value::default")]
    #[serde(skip_serializing_if = "serde_json::Value::is_null")]
    pub params: serde_json::Value,
    #[serde(default)]
    pub project_url: String,
}

impl Request {
    pub fn new(id: RequestId, method: String, params: serde_json::Value) -> Request {
        Request { id, method, params, project_url: String::new() }
    }
    
    pub fn new_with_project_url(id: RequestId, method: String, params: serde_json::Value, project_url: String) -> Request {
        Request { id, method, params, project_url }
    }
}

fn main() {
    println!("测试 Request 结构体修改...");
    
    // 测试1: 使用原有的构造函数
    let request1 = Request::new(
        RequestId::from(1),
        "test_method".to_string(),
        serde_json::Value::Null
    );
    println!("Request 1: {:?}", request1);
    
    // 测试2: 使用新的构造函数
    let request2 = Request::new_with_project_url(
        RequestId::from(2),
        "test_method".to_string(),
        serde_json::Value::Null,
        "https://github.com/example/repo".to_string()
    );
    println!("Request 2: {:?}", request2);
    
    // 测试3: 序列化测试
    let json1 = serde_json::to_string(&request1).unwrap();
    println!("JSON 1: {}", json1);
    
    let json2 = serde_json::to_string(&request2).unwrap();
    println!("JSON 2: {}", json2);
    
    // 测试4: 反序列化测试 - 不包含project_url的JSON
    let json_without_project_url = r#"{"id":3,"method":"shutdown","params":null}"#;
    let deserialized1: Request = serde_json::from_str(json_without_project_url).unwrap();
    println!("反序列化1 (无project_url): {:?}", deserialized1);
    
    // 测试5: 反序列化测试 - 包含project_url的JSON
    let json_with_project_url = r#"{"id":4,"method":"init","params":null,"project_url":"https://github.com/test/repo"}"#;
    let deserialized2: Request = serde_json::from_str(json_with_project_url).unwrap();
    println!("反序列化2 (有project_url): {:?}", deserialized2);
    
    println!("所有测试通过！");
}

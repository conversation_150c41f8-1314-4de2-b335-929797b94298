[package]
name = "agent_client"
version = "0.2.0"
edition = "2021"


[dependencies]
log = "0.4.22"
tokio = { version = "1.45.1", features = ["full"] }
jsonrpsee = { version = "0.24.9", features = ["full"] }
anyhow = "1.0.86"
once_cell = "1.19.0"
regex = "1.11.1"
uuid = "1.13.1"
tower-http = { version = "0.5.0", features = ["cors", "compression-full"] }
hyper = "1.6.0"
tower = { version = "0.4.0", features = ["timeout"] }
openssl-sys = { version = "0.9.108", features = ["vendored"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
thiserror = "2.0.12"
fs2 = "0.4.3"

#agent_common_service = { git = "ssh://*******************/code-generator-group/agent_common_service.git", tag = "v0.2.0" }
#agent_codefuse_service = { git = "ssh://*******************/code-generator-group/agent_codefuse_service.git", tag = "v0.3.0" }
#agent_gentests_service = { git = "ssh://*******************/MTIOS/agent_gentests_service.git", tag = "v0.3.3" }
#agent_db = { git = "ssh://*******************/code-generator-group/agent_db.git", tag = "v0.2.0" }

agent_common_service = { path = "../agent_common_service" }
agent_codefuse_service = { path = "../agent_codefuse_service" }
agent_db = { path = "../agent_db" }
agent_gentests_service = { path = "../agent_gentests_service" }
agent_android_service = { path = "../agent_android_service" }


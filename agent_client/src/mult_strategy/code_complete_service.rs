use std::collections::HashMap;
use std::path::Path;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};

use anyhow::{anyhow, Result};
use log::{debug, error, info};
use tokio::sync::RwLock;

use agent_codefuse_service::function::code_edit_strategy::CodeFuseCodeEditStrategy;
use agent_codefuse_service::function::codecomplete_rag_framework_strategy::{
    save_file_with_not_change, CodeFuseCodeCompleteRagStrategy, RELATED_CACHE, SIMILARITY_CACHE,
};
use agent_codefuse_service::function::codecomplete_strategy::CodeFuseCodeCompleteStrategy;
use agent_common_service::model::code_complete_model::{
    CodeCompletionRequestBean, CodeCompletionType, CompletionResultModel,
};
use agent_common_service::service::code_complete::CodeCompleteStrategy;
use agent_common_service::service::code_complete_rag_framework::CodeCompleteRagFrameworkStrategy;
use agent_common_service::service::code_edit::CodeEditStrategy;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::remote::rpc_model::BaseResponse;

//代码补全类型value
const RECORD_VALUE_CODE_COMPLETE_TYPE: &str = "LOCAL_AGENT";
const RECORD_KEY_COMPLETE_TYPE: &str = "COMPLETE_TYPE";

const RECORD_KEY_CODE_COMPLETE_TIME: &str = "CODE_COMPLETE_TIME";
const RECORD_KEY_SIMILARITY_FLAG: &str = "SIMILARITY_FLAG";
const RECORD_KEY_RELATED_FLAG: &str = "RELATED_FLAG";
const RECORD_KEY_AGENT_VERSION: &str = "AGENT_VERSION";

///中间增加一个strategy，为了后续扩展能力
pub async fn code_complete(
    mut code_complete_request: CodeCompletionRequestBean,
) -> Result<BaseResponse<CompletionResultModel>> {
    let rag_result = local_rag(&mut code_complete_request).await;
    let file_diffs = CodeFuseCodeEditStrategy::get_diff_trajectories_safe().await;
    code_complete_request.fileDiffs = Some(file_diffs);

    info!(
        "=== code_complete START: {:?} ===",
        code_complete_request.completionType
    );
    if code_complete_request.completionType == Option::from(CodeCompletionType::CODEEDIT) {
        return Ok(CodeFuseCodeEditStrategy::code_edit(code_complete_request.clone()).await);
    }

    if code_complete_request.completionType == Option::from(CodeCompletionType::NEXT_TAB) {
        return Ok(CodeFuseCodeEditStrategy::next_tab(code_complete_request.clone()).await);
    }

    match rag_result {
        Ok(consume_time) => {
            info!("local agent rag consume time: {}", consume_time);
        }
        Err(e) => {
            error!("local agent rag error: {:?}", e);
        }
    }
    Ok(CodeFuseCodeCompleteStrategy::code_complete(code_complete_request).await)
}

///rag逻辑抽出独立函数，防止出现异常影响整体流程
async fn local_rag(code_complete_request: &mut CodeCompletionRequestBean) -> Result<u128> {
    let start_millis = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis();
    let mut record_info = code_complete_request
        .recordInfo
        .take()
        .unwrap_or_else(HashMap::new);
    record_info.insert(
        RECORD_KEY_COMPLETE_TYPE.to_string(),
        RECORD_VALUE_CODE_COMPLETE_TYPE.to_string(),
    );
    let new_content = format!(
        "{}{}",
        &code_complete_request.prompt, &code_complete_request.suffix
    );
    save_file_with_not_change(&code_complete_request.fileUrl, &new_content, false).await;

    let file_suffix_opt = Path::new(&code_complete_request.fileUrl)
        .extension()
        .and_then(|ext| ext.to_str());
    if let Some(file_suffix) = file_suffix_opt {
        code_complete_request.fileNameSuffix = Some(file_suffix.to_string());
    }
    if code_complete_request.projectUrl.is_some() {
        let get_similarity_task =
            CodeFuseCodeCompleteRagStrategy::get_similarity_snippet(&code_complete_request);
        let get_related_task =
            CodeFuseCodeCompleteRagStrategy::get_related_snippet(&code_complete_request);

        let (similarity_data_result, related_data_result) =
            tokio::join!(get_similarity_task, get_related_task);
        match similarity_data_result {
            Ok(similarity_data_opt) => {
                if similarity_data_opt.is_some() {
                    code_complete_request.similarSnippets = similarity_data_opt;
                    record_info.insert(RECORD_KEY_SIMILARITY_FLAG.to_string(), "Y".to_string());
                }
            }
            Err(e) => {
                error!("get similarity data error:{}", e);
            }
        }

        match related_data_result {
            Ok(related_data_opt) => {
                if related_data_opt.is_some() {
                    code_complete_request.relatedSnippets = related_data_opt;
                    record_info.insert(RECORD_KEY_RELATED_FLAG.to_string(), "Y".to_string());
                } else {
                    debug!("no related data found")
                }
            }
            Err(e) => {
                error!("get related data error:{}", e)
            }
        }
    }

    let end_millis = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis();
    let rag_time = end_millis - start_millis;
    info!("rag time: {}", rag_time);
    record_info.insert(
        RECORD_KEY_CODE_COMPLETE_TIME.to_string(),
        rag_time.to_string(),
    );
    record_info.insert(
        RECORD_KEY_AGENT_VERSION.to_string(),
        AGENT_CONFIG.agent_version.to_string(),
    );
    code_complete_request.recordInfo = Some(record_info);
    return Ok(rag_time);
}

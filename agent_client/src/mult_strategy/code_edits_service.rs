use agent_codefuse_service::constants::LOCK_TIMEOUT_MS;
use agent_codefuse_service::function::cursor_tab_diff_historical::manager::CURSOR_DIFF_HISTORICAL_INSTANCE;
use agent_common_service::model::code_edit_model::{
    DiffHistoricalInitModelRequest, DiffHistoricalRecordRequest, DiffHistoricalRecordResponse,
};
use agent_db::remote::rpc_model::{build_success_response, BaseResponse};
use jsonrpsee::core::RpcResult;
use jsonrpsee::types::ErrorObject;
use log::{debug, error, info, warn};
use tokio::time::{timeout, Duration};

pub async fn code_edits_service(
    request: DiffHistoricalRecordRequest,
) -> RpcResult<BaseResponse<DiffHistoricalRecordResponse>> {
    let lock_result = timeout(
        Duration::from_millis(LOCK_TIMEOUT_MS),
        CURSOR_DIFF_HISTORICAL_INSTANCE.lock(),
    )
    .await;

    let mut manager = match lock_result {
        Ok(manager) => {
            debug!("Lock acquired successfully for code_edits_service");
            manager
        }
        Err(_) => {
            let error_msg = format!(
                "Lock acquisition timeout after {}ms in code_edits_service",
                LOCK_TIMEOUT_MS
            );
            error!("{}", error_msg);
            return Err(ErrorObject::owned(90, error_msg, None::<()>));
        }
    };

    // 尝试处理文档变化，如果返回未初始化错误，则先初始化再重试
    match manager.handle_document_change(&request).await {
        Ok(_) => {
            info!("Model already initialized, proceeding with diff calculation");
        }
        Err(e) if e.to_string().contains("Model not initialized") => {
            info!(
                "Model not initialized, initializing model for path: {}",
                request.uri
            );
            let init_request = DiffHistoricalInitModelRequest {
                text: request.text.clone(),
                version: request.version as usize,
                uri: request.uri.clone(),
                eol: request.eol.clone(),
            };
            manager.init_model(&init_request);
            info!("Model initialization completed for path: {}", request.uri);

            // 重新尝试处理文档变化
            if let Err(e) = manager.handle_document_change(&request).await {
                error!(
                    "Failed to handle document change after initialization: {}",
                    e
                );
                return Err(ErrorObject::owned(90, e.to_string(), None::<()>));
            }
        }
        Err(e) => {
            error!("Failed to handle document change: {}", e);
            return Err(ErrorObject::owned(90, e.to_string(), None::<()>));
        }
    }

    let diff_trajectories = match manager.get_diff_trajectories().await {
        Ok(trajectories) => {
            debug!(
                "Successfully retrieved {} diff trajectories",
                trajectories.len()
            );
            trajectories
        }
        Err(e) => {
            error!("Failed to get diff trajectories: {}", e);
            return Err(ErrorObject::owned(90, e.to_string(), None::<()>));
        }
    };

    Ok(build_success_response(DiffHistoricalRecordResponse {
        success: true,
        error_message: None,
        diff_trajectories,
    }))
}

pub async fn code_edits_init_model(
    request: DiffHistoricalInitModelRequest,
) -> RpcResult<BaseResponse<()>> {
    {
        // 使用带超时的锁获取
        let lock_result = timeout(
            Duration::from_millis(LOCK_TIMEOUT_MS),
            CURSOR_DIFF_HISTORICAL_INSTANCE.lock(),
        )
        .await;

        let mut manager = match lock_result {
            Ok(manager) => {
                debug!("Lock acquired successfully for code_edits_init_model");
                manager
            }
            Err(_) => {
                let error_msg = format!(
                    "Lock acquisition timeout after {}ms in code_edits_init_model",
                    LOCK_TIMEOUT_MS
                );
                error!("{}", error_msg);
                return Err(ErrorObject::owned(90, error_msg, None::<()>));
            }
        };

        manager.init_model(&request);
        debug!("Successfully initialized model for URI: {}", request.uri);
    }

    Ok(build_success_response(()))
}

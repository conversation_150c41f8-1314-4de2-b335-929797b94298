use std::time::{SystemTime, UNIX_EPOCH};

use anyhow::Result;
use log::{debug, error, info};

use agent_common_service::model::code_complete_model::{CodeCompletionRequestBean, CompletionResultModel};
use agent_common_service::model::code_scan_model::ScanConfig;
use agent_common_service::service::code_complete::CodeCompleteStrategy;
use agent_common_service::service::code_scan::CodeScanStrategy;

pub async fn code_scan<T: CodeScanStrategy>(scan_strategy: T, project_url: String) -> Result<()> {
    debug!("code_scan: {}",project_url);
    let start = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    let scan_complete_request = ScanConfig {
        url: project_url.clone(),
        branch: None,
        code_completion_request_bean: None,
    };
    let r = T::scan_project_from_url(scan_complete_request).await;
    if r.is_err() {
        error!("code_scan error: {:?}",r);
    }
    let end = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    info!("code_scan: {} cost: {}ms",project_url, end - start);
    Ok(())
}

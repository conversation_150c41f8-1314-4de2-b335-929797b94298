use std::cmp::Ordering;
use std::net::SocketAddr;
use std::path::Path;
use std::{fs, process};

use jsonrpsee::server::Server;
use log::{error, info};
use tokio::signal::ctrl_c;

use crate::server::services::code_complete_service::{CodeComplateServiceImpl, CodeComplateServiceServer};
use crate::server::services::code_edits_service::{CodeEditsServiceImpl, CodeEditsServiceServer};
use crate::server::services::common_service::{CommonServiceImpl, CommonServiceServer};
use crate::server::services::project_understand_service::{ProjectUnderStandServiceImpl, ProjectUnderStandServiceServer};
use crate::server::services::qa_service::{QAServiceImpl, QAServiceServer};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::kv_client::KV_CLIENT;
use agent_db::dal::vector_client::VECTOR_CLIENT;
use agent_db::tools::common_tools::expand_user_home;
use hyper::Method;
use jsonrpsee::RpcModule;
use tower::layer::util::Identity;
use tower_http::cors::{Any, CorsLayer};

const DATA_VERSION_KEY: &str = "AGENT_DATA_VERSION";

///创建服务路由,并启动ws服务端
pub async fn start_server() -> std::io::Result<()> {
    let url = format!("0.0.0.0:{}", AGENT_CONFIG.port);
    check_and_update_version_data().await;
    // 创建 CORS 配置
    let cors = CorsLayer::new()
        .allow_origin(Any)
        .allow_methods(Any)
        .allow_headers(Any);
    let middleware = tower::ServiceBuilder::new().layer(cors);

    let server = Server::builder().set_http_middleware(middleware).build(url.parse::<SocketAddr>().unwrap()).await.unwrap();

    let project_understand_service = ProjectUnderStandServiceImpl{}.into_rpc();
    let qa_service = QAServiceImpl{}.into_rpc();
    let common_service = CommonServiceImpl{}.into_rpc();
    let code_complete_service = CodeComplateServiceImpl{}.into_rpc();
    let code_edits_service = CodeEditsServiceImpl{}.into_rpc();


    // 创建一个新的 RpcModule，使用空上下文()
    let mut module = RpcModule::new(());

    // 合并服务的 RPC 方法
    module.merge(project_understand_service).unwrap();
    module.merge(qa_service).unwrap();
    module.merge(common_service).unwrap();
    module.merge(code_complete_service).unwrap();
    module.merge(code_edits_service).unwrap();
    
    let server_addr = server.local_addr().unwrap();
    let handle = server.start(module);
    let pid = process::id();
    info!("starting server...,port: {},pid: {}",server_addr.port(),pid);
    // change_port_pid(AGENT_CONFIG.port, pid);
    tokio::select! {
        _ = ctrl_c() => info!("receive Ctrl C,agent will exit"),
    }
    handle.stop().unwrap();
    info!("agent shut down ");
    Ok(())
}

// 启动 tcp 服务，使用 port 通信，也可以使用 pipe
pub async fn start_tcp_server() {
    use crate::server::ipc_services::server::Server;
    use crate::server::ipc_services::{notification_dispatch, request_dispatch};

    let url = format!("0.0.0.0:{}", AGENT_CONFIG.tcp_port);
    let server = Server::build(url.parse::<SocketAddr>().unwrap()).await.unwrap();

    let server_addr = server.local_addr().unwrap();
    let handle = server.start(request_dispatch, notification_dispatch);
    info!("starting tcp server..., port: {}",server_addr.port());
    tokio::select! {
        _ = ctrl_c() => info!("receive Ctrl C,agent will exit"),
    }
    handle.stop().unwrap();
}

///如果del_data_flag不是0, 那么检查数据版本, 如果版本不匹配,则先删除已有数据
async fn check_and_update_version_data() {
    delete_old_data();
    let current_version = AGENT_CONFIG.agent_version.clone();
    if AGENT_CONFIG.del_data_flag != 0 {
        let data_version = KV_CLIENT.get(&DATA_VERSION_KEY.to_string());
        match data_version {
            Ok(data_version_opt) => {
                if data_version_opt.is_some() {
                    let old_version = data_version_opt.unwrap();
                    match old_version.cmp(&current_version)  {
                        Ordering::Less => {
                            match old_version.cmp(&"0.8.0".to_string()) {
                                Ordering::Less => {
                                    info!("data version is not match, del old data");
                                    //删除tantivy索引
                                    let data_cache_url = expand_user_home(&AGENT_CONFIG.base_data_url).unwrap().join("chat");
                                    if data_cache_url.exists() {
                                        let del_result = fs::remove_dir_all(data_cache_url);
                                        if let Err(e) = del_result {
                                            error!("del dir error {:?}", e);
                                        }
                                    }
                                    //删除kv索引
                                    info!("data version is not match, del old data");
                                    let del_kv_result = KV_CLIENT.clean();
                                    if let Err(e) = del_kv_result {
                                        error!("del kv data error {:?}", e);
                                    }
                                    let vector_data_url = expand_user_home(&AGENT_CONFIG.base_data_url)
                                        .unwrap()
                                        .join("vectory");
                                    let del_result = fs::remove_dir_all(vector_data_url.clone());
                                    if let Err(e) = del_result{
                                        error!("del vectory data error {:?}",e)
                                    }
                                }
                                _ => {}
                            }
                          
                        }
                        _ => {}
                    }
                }
            }
            Err(error) => {
                error!("get data version error {}", error);
                KV_CLIENT.clean();
            }
        }
    }
    KV_CLIENT.insert(&DATA_VERSION_KEY.to_string(), &current_version);
}

///删除老版本的agent数据目录
fn delete_old_data() {
    let old_data_url = expand_user_home(&AGENT_CONFIG.base_data_url).unwrap().join("agent_data");
    if old_data_url.exists() {
        let result = fs::remove_dir_all(&old_data_url); // 删除老版本数据
        if let Err(e) = result {
            error!("del dir error {}", e);
        }
    }
}

///删除索引文件
fn delete_data<P: AsRef<Path>>(path: P) {
    if path.as_ref().is_dir() {
        for entry in fs::read_dir(path).unwrap() {
            let child_path = entry.unwrap().path();
            //此处只删除文件夹, 因为代码会读取到lmdb的.lock文件, 不能直接删文件
            if child_path.is_dir() {
                let result = fs::remove_dir_all(&child_path); // 删除子目录及其内容
                if let Err(e) = result {
                    error!("del dir error {}", e);
                }
            }
        }
    }
}
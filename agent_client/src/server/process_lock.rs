use std::fs::{File, OpenOptions};
use std::path::PathBuf;
use std::io::{self, Write};
use std::time::{SystemTime, UNIX_EPOCH};
use fs2::FileExt;
use log::{info, error, warn};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::tools::common_tools::expand_user_home;

/// 进程锁管理器
pub struct ProcessLock {
    lock_file: Option<File>,
    lock_path: PathBuf,
}

impl ProcessLock {
    /// 创建新的进程锁实例
    pub fn new() -> io::Result<Self> {
        let base_data_path = expand_user_home(&AGENT_CONFIG.base_data_url)
            .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidInput, "Failed to expand home path"))?;
        
        // 确保数据目录存在
        std::fs::create_dir_all(&base_data_path)?;
        
        let lock_path = base_data_path.join("agent.lock");
        
        Ok(ProcessLock {
            lock_file: None,
            lock_path,
        })
    }
    
    /// 尝试获取进程锁
    /// 如果成功获取锁，返回Ok(())
    /// 如果已有进程在运行，返回Err
    pub fn try_lock(&mut self) -> io::Result<()> {
        info!("Attempting to acquire process lock at: {:?}", self.lock_path);
        
        // 打开或创建锁文件
        let file = OpenOptions::new()
            .create(true)
            .write(true)
            .truncate(true)
            .open(&self.lock_path)?;
        
        // 尝试获取独占锁
        match file.try_lock_exclusive() {
            Ok(()) => {
                info!("Successfully acquired process lock");
                let current_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                // 写入当前进程信息
                let pid = std::process::id();
                let mut file_ref = &file;
                writeln!(file_ref, "PID: {}", pid)?;
                writeln!(file_ref, "Started: {}",current_time.to_string())? ;
                file_ref.flush()?;
                
                self.lock_file = Some(file);
                Ok(())
            }
            Err(e) => {
                error!("Failed to acquire process lock: {}", e);
                
                // 尝试读取现有锁文件信息
                if let Ok(content) = std::fs::read_to_string(&self.lock_path) {
                    warn!("Existing lock file content:\n{}", content);
                }
                
                Err(io::Error::new(
                    io::ErrorKind::ResourceBusy,
                    "Another agent process is already running. Only one agent process can run at a time."
                ))
            }
        }
    }
    
    /// 释放进程锁
    pub fn release(&mut self) {
        if let Some(file) = self.lock_file.take() {
            info!("Releasing process lock");
            
            // 解锁文件
            if let Err(e) = FileExt::unlock(&file) {
                error!("Failed to unlock file: {}", e);
            }
            
            // 删除锁文件
            if let Err(e) = std::fs::remove_file(&self.lock_path) {
                error!("Failed to remove lock file: {}", e);
            } else {
                info!("Lock file removed successfully");
            }
        }
    }
    
    /// 检查是否持有锁
    pub fn is_locked(&self) -> bool {
        self.lock_file.is_some()
    }
}

impl Drop for ProcessLock {
    fn drop(&mut self) {
        self.release();
    }
}

/// 全局进程锁实例
static mut PROCESS_LOCK: Option<ProcessLock> = None;
static INIT: std::sync::Once = std::sync::Once::new();

/// 初始化并获取进程锁
pub fn init_process_lock() -> io::Result<()> {
    unsafe {
        INIT.call_once(|| {
            match ProcessLock::new() {
                Ok(mut lock) => {
                    if let Err(e) = lock.try_lock() {
                        error!("Failed to initialize process lock: {}", e);
                        std::process::exit(1);
                    }
                    PROCESS_LOCK = Some(lock);
                }
                Err(e) => {
                    error!("Failed to create process lock: {}", e);
                    std::process::exit(1);
                }
            }
        });
    }
    Ok(())
}

/// 释放全局进程锁
pub fn release_process_lock() {
    unsafe {
        if let Some(ref mut lock) = PROCESS_LOCK {
            lock.release();
        }
    }
}

/// 检查进程锁是否已初始化并持有
pub fn is_process_locked() -> bool {
    unsafe {
        PROCESS_LOCK.as_ref().map_or(false, |lock| lock.is_locked())
    }
}

use crate::mult_strategy::code_edits_service::{code_edits_init_model, code_edits_service};
use agent_common_service::model::code_edit_model::{
    DiffHistoricalInitModelRequest, DiffHistoricalRecordRequest, DiffHistoricalRecordResponse,
};
use agent_db::remote::rpc_model::BaseResponse;
use jsonrpsee::core::{async_trait, RpcResult};
use jsonrpsee::proc_macros::rpc;
use log::{debug, error, info};
use std::time::{SystemTime, UNIX_EPOCH};

#[rpc(server)]
trait CodeEditsService {
    // code edits 编辑轨迹计算
    #[method(name = "code_edits_diff_historical_record")]
    async fn code_edits_diff_historical_record(
        &self,
        request: DiffHistoricalRecordRequest,
    ) -> RpcResult<BaseResponse<DiffHistoricalRecordResponse>>;
    // code edits 编辑轨迹计算初始化
    #[method(name = "code_edits_init_model")]
    async fn code_edits_init_model(
        &self,
        request: DiffHistoricalInitModelRequest,
    ) -> RpcResult<BaseResponse<()>>;
}

///对外服务实现类
pub struct CodeEditsServiceImpl {}

///对外服务的函数实现
#[async_trait]
impl CodeEditsServiceServer for CodeEditsServiceImpl {
    async fn code_edits_diff_historical_record(
        &self,
        request: DiffHistoricalRecordRequest,
    ) -> RpcResult<BaseResponse<DiffHistoricalRecordResponse>> {
        let start_millis = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();

        // 记录接口入参
        info!("=== code_edits_diff_historical_record START ===");

        let result = tokio::spawn(async move { code_edits_service(request).await }).await;
        match result {
            Ok(Ok(response)) => {
                let end_millis = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis();

                // 记录正常出参
                info!("=== code_edits_diff_historical_record END ===");
                Ok(response)
            }
            Ok(Err(e)) => {
                let end_millis = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis();

                let error_response = BaseResponse {
                    errorCode: 90,
                    errorMsg: Some(format!("agent error: {}", e)),
                    data: None,
                    totalCount: Some(0),
                };

                // 记录错误出参
                info!("接口错误出参: {:?}", error_response);
                error!(
                    "code_edits_diff_historical_record consume: {} ms, error: {}",
                    end_millis - start_millis,
                    e
                );
                Ok(error_response)
            }
            Err(e) => {
                let error_response = BaseResponse {
                    errorCode: 90,
                    errorMsg: Some(format!("agent error: {}", e)),
                    data: None,
                    totalCount: Some(0),
                };

                // 记录任务失败出参
                info!("接口任务失败出参: {:?}", error_response);
                info!("=== code_edits_diff_historical_record END (TASK FAILED) ===");

                error!("Task failed: {}", e);
                Ok(error_response)
            }
        }
    }
    async fn code_edits_init_model(
        &self,
        request: DiffHistoricalInitModelRequest,
    ) -> RpcResult<BaseResponse<()>> {
        let start_millis = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();

        // 记录接口入参
        info!("=== code_edits_init_model START ===");

        let result = code_edits_init_model(request).await;
        match result {
            Ok(response) => {
                let end_millis = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis();

                // 记录正常出参
                info!("=== code_edits_init_model END ===");
                debug!(
                    "code_edits_init_model consume: {} ms ,result: {:?}",
                    end_millis - start_millis,
                    response
                );
                Ok(response)
            }
            Err(e) => {
                let end_millis = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis();

                let error_response = BaseResponse {
                    errorCode: 90,
                    errorMsg: Some("agent error,please check agent log".to_string()),
                    data: None,
                    totalCount: Some(0),
                };

                error!(
                    "code_edits_init_model consume: {} ms, error: {}",
                    end_millis - start_millis,
                    e
                );
                Ok(error_response)
            }
        }
    }
}

use agent_db::remote::rpc_model::BaseResponse;
use agent_gentests_service::api::api::query_related_data_api;
use agent_gentests_service::api::req_model::QueryRelatedRequestBean;
use jsonrpsee::core::__reexports::serde_json;
use jsonrpsee::core::{async_trait, RpcResult};
use jsonrpsee::proc_macros::rpc;
use log::error;

#[rpc(server)]
trait QAService {
    #[method(name = "query_related_data")]
    async fn query_related_data(
        &self,
        query_related_request: QueryRelatedRequestBean,
    ) -> RpcResult<BaseResponse<String>>;

}

///对外服务实现类
pub struct QAServiceImpl {}

///对外服务的函数实现
#[async_trait]
impl QAServiceServer for QAServiceImpl {
    ///测试用例生成场景
    async fn query_related_data(
        &self,
        query_related_request: QueryRelatedRequestBean,
    ) -> RpcResult<BaseResponse<String>> {
        let result = query_related_data_api(query_related_request);
        match result {
            Ok(query_related_result) => {
                let data_opt = query_related_result.data;
                if let Some(data) = data_opt {
                    Ok(BaseResponse {
                        errorCode: 0,
                        errorMsg: None,
                        data: Some(serde_json::to_string(&data).unwrap()),
                        totalCount: Some(0),
                    })
                } else {
                    Ok(BaseResponse {
                        errorCode: 0,
                        errorMsg: None,
                        data: None,
                        totalCount: Some(0),
                    })
                }
            }
            Err(error) => {
                error!("query_related_data error,err={}", error);
                Ok(BaseResponse {
                    errorCode: 90,
                    errorMsg: Some("agent error,please check agent log".to_string()),
                    data: None,
                    totalCount: Some(0),
                })
            }
        }
    }
}
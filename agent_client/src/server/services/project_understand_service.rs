use agent_codefuse_service::deepsearchV2::searchrouter_data::DeepsearchChatResponse;
use agent_codefuse_service::deepsearchV2::searchrouter_util::execute_atomic_search;
use agent_codefuse_service::dialogue::data_struct::DataStatus;
use agent_codefuse_service::dialogue::repo_index_operator::RepoStatusEnum;
use agent_codefuse_service::function::chat_strategy::{ChatFlowContext, TaskNode<PERSON>num, CHAT_RAG_BUILD_TIME_KEY, CHAT_RAG_QUERY_INDEX_KEY, CHAT_RAG_REQ_KEY, CHAT_RAG_RESPONSE_KEY, CHAT_RAG_SYNC_BUILD_TIME_KEY, CHAT_RAG_TYPE_KEY, CHAT_RAG_UNDERSTAND_TIME_KEY, CHAT_TASK_MANAGER_V2, QUERY_BM25_TIME_KEY, QUERY_CHANGE_RESULT_KEY, QUERY_FILTER_TIME_KEY, QUERY_RERANK_TIME_KEY, QUERY_SEARCH_HANDLER_TIME_KEY, QUERY_SEARCH_TIME_KEY, QUERY_VECTOR_TIME_KEY, TOTAL_CHAT_TIME_KEY};
use agent_codefuse_service::service::ide_service::{symbol_search, usages_search};
use agent_codefuse_service::utils::path::{fill_content, DEFAULT_BRANCH, DEFAULT_PROJECT_URL};
use agent_codefuse_service::utils::strategy_utils::{
    get_chat_strategy_result, get_deepsearch_chat_strategy_result,
};
use agent_common_service::model::chat_model::{
    AtomicSearchRequestBean, ChatFlowStatusEnum, ChatRelatedRequestBean,
    ChatRelatedResponse, IntentEnum, SymbolAndStreamSearchResult,
};
use agent_common_service::model::code_complete_model::{
    CodeCompletionRequestBean, CompletionResultModel,
};
use agent_common_service::model::code_scan_model::ScanConfig;
use agent_common_service::model::incremental_update_model::IncrementalUpdateRequestBean;
use agent_common_service::tools::common_tools::{detect_language, log_chat_step};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;
use agent_db::remote::rpc_model::{build_error_response, build_success_response, BaseResponse};
use agent_gentests_service::api::req_model::{IntentionType, QueryRelatedRequestBean};
use agent_gentests_service::ut::agent_ut::parser_unit_test_for_ai_agent;
use jsonrpsee::core::__reexports::serde_json;
use jsonrpsee::core::{async_trait, RpcResult};
use jsonrpsee::proc_macros::rpc;
use jsonrpsee::RpcModule;
use log::{error, info};
use serde_json::Value;
use std::any::Any;
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};
use agent_codefuse_service::dialogue::misc_util::upload_panic_info;
use agent_db::dal::remote_client::{query_web_fetch, TraceInfo};
use agent_db::domain::ap_data::AtomicSearchTypeEnum;
use uuid::Uuid;

/// 仓库理解服务
/// 输入：自然语言
/// 输出: 相关片段
/// 类型：SOP检索/DeepSearch检索
#[rpc(server)]
trait ProjectUnderStandService {
    //对话时查询相关数据
    #[method(name = "query_chat_related_info")]
    async fn query_chat_related_info(
        &self,
        chat_related_request: ChatRelatedRequestBean,
    ) -> RpcResult<BaseResponse<ChatRelatedResponse>>;

    //对话时查询相关数据(agent模式）
    #[method(name = "query_chat_related_info_with_agent")]
    async fn query_chat_related_info_with_agent(
        &self,
        chat_related_request: ChatRelatedRequestBean,
    ) -> RpcResult<BaseResponse<ChatRelatedResponse>>;

    //对话时查询相关数据(agent模式,augment方案。BM25检索时用Term而非模糊查找）
    #[method(name = "query_chat_related_info_v2")]
    async fn query_chat_related_info_v2(
        &self,
        chat_related_request: ChatRelatedRequestBean,
    ) -> RpcResult<BaseResponse<ChatRelatedResponse>>;


    #[method(name = "deepsearch_chat_related_info")]
    async fn deepsearch_chat_related_info(
        &self,
        deepsearch_related_request: ChatRelatedRequestBean,
    ) -> RpcResult<BaseResponse<DeepsearchChatResponse<serde_json::Value>>>;

    //原子搜索
    #[method(name = "atomic_search")]
    async fn atomic_search(
        &self,
        atomic_search_request: AtomicSearchRequestBean,
    ) -> RpcResult<BaseResponse<Vec<Vec<ChatRelatedCodeModel>>>>;
}

///对外服务实现类
pub struct ProjectUnderStandServiceImpl {}

///对外服务的函数实现
#[async_trait]
impl ProjectUnderStandServiceServer for ProjectUnderStandServiceImpl {
    ///对话rag检索
    async fn query_chat_related_info(
        &self,
        chat_related_request: ChatRelatedRequestBean,
    ) -> RpcResult<BaseResponse<ChatRelatedResponse>> {
        let start_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();

        let fill_param_result = fill_param(chat_related_request);
        //如果chat_related_response有值，说明是新对话，这个接口场景下直接返回
        if let Some(chat_related_response) = fill_param_result.0 {
            return Ok(build_success_response(chat_related_response));
        }

        let result = get_chat_strategy_result(fill_param_result.1).await;
        match result {
            Ok(result) => {
                let end_time = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis();
                info!(
                    "query_chat_related_info consume: {},result: {:?}",
                    end_time - start_time,
                    result.data
                );
                Ok(result)
            }
            Err(e) => {
                error!("query_chat_related_info error,err={:?}", e);
                Ok(BaseResponse {
                    errorCode: 90,
                    errorMsg: Some("agent error,please check agent log".to_string()),
                    data: None,
                    totalCount: Some(0),
                })
            }
        }
    }

    ///基于agent模式的检索能力
    async fn query_chat_related_info_with_agent(
        &self,
        chat_related_request: ChatRelatedRequestBean,
    ) -> RpcResult<BaseResponse<ChatRelatedResponse>> {
        self.query_chat_related_info_v2(chat_related_request).await
    }

    async fn query_chat_related_info_v2(
        &self,
        chat_related_request: ChatRelatedRequestBean,
    ) -> RpcResult<BaseResponse<ChatRelatedResponse>> {
        let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let mut extra_data:HashMap<&str,String> = HashMap::new();
        extra_data.insert(CHAT_RAG_REQ_KEY,serde_json::to_string(&chat_related_request).unwrap_or("".to_string()));
        let new_request = fill_param(chat_related_request.clone()).1;
        if let Some(IntentEnum::TEST) = new_request.chatIntent {
            extra_data.insert(CHAT_RAG_TYPE_KEY,"TEST".to_string());
            let result =  query_data_with_test_scene(&new_request).await;
            match result {
                Ok(response) => {
                    extra_data.insert(CHAT_RAG_RESPONSE_KEY,serde_json::to_string(&response).unwrap_or("".to_string()));
                    let consume_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() - start_time;
                    extra_data.insert(TOTAL_CHAT_TIME_KEY,consume_time.to_string());
                    log_chat_step("query_chat_related_info_v2",&chat_related_request,extra_data);
                    return Ok(response);
                }
                Err(e) => {
                    error!("query_chat_related_info_v2 error: {:?}",e);
                    let consume_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() - start_time;
                    extra_data.insert(TOTAL_CHAT_TIME_KEY,consume_time.to_string());
                    log_chat_step("query_chat_related_info_v2",&chat_related_request,extra_data);
                    return Ok(build_error_response(90, "agent error,please check agent log".to_string()) );
                }
            }

        }
        let search_type_vec = chat_related_request.searchTypeList.clone().unwrap_or(vec![AtomicSearchTypeEnum::Codebase_Search]);
        if search_type_vec.contains(&AtomicSearchTypeEnum::WEB_FETCH) {
            extra_data.insert(CHAT_RAG_TYPE_KEY,"WEB_FETCH".to_string());
            let result = query_web_fetch(chat_related_request.query).await;
            let mut chat_related_response = ChatRelatedResponse {
                level1Instruction: chat_related_request.level1Instruction.clone(),
                level2Instruction: chat_related_request.level2Instruction.clone(),
                assitantParam: None,
                extraData: None,
                localRepoSelectedList: None,
                localRepoReferenceRagList: None,
                chatStatus: None,
                necessaryIndexPercent: None,
                questionUid: None,
                sessionId: None,
            };
            if let Some(docResult) = result{
                chat_related_response.localRepoReferenceRagList =  Some(vec![docResult]);
            }
            let consume_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() - start_time;
            let result = build_success_response(chat_related_response);
            extra_data.insert(TOTAL_CHAT_TIME_KEY,consume_time.to_string());
            extra_data.insert(CHAT_RAG_RESPONSE_KEY,serde_json::to_string(&result).unwrap_or("".to_string()));
            return Ok(result);
        }else {
            let result = CHAT_TASK_MANAGER_V2.process(&new_request).await;
            let mut chat_related_response = ChatRelatedResponse {
                level1Instruction: chat_related_request.level1Instruction.clone(),
                level2Instruction: chat_related_request.level2Instruction.clone(),
                assitantParam: None,
                extraData: None,
                localRepoSelectedList: None,
                localRepoReferenceRagList: None,
                chatStatus: None,
                necessaryIndexPercent: None,
                questionUid: None,
                sessionId: None,
            };
            chat_related_response.chatStatus = Some(result.0.clone());
            if result.0 == ChatFlowStatusEnum::ANSWER
                || result.0 == ChatFlowStatusEnum::ERROR
            {
                let context_opt = result.2;
                match context_opt {
                    None => {}
                    Some(data) => {
                        extra_data.insert(
                            CHAT_RAG_BUILD_TIME_KEY,
                            data.build_consume_time_total.to_string(),
                        );
                        extra_data.insert(
                            CHAT_RAG_SYNC_BUILD_TIME_KEY,
                            data.build_consume_time_sync.to_string(),
                        );
                        extra_data.insert(
                            CHAT_RAG_UNDERSTAND_TIME_KEY,
                            data.understand_consume_time.to_string(),
                        );
                        extra_data.insert(
                            CHAT_RAG_QUERY_INDEX_KEY,
                            data.query_index_time.to_string(),
                        );
                        extra_data.insert(
                            QUERY_SEARCH_TIME_KEY,
                            data.search_time.to_string(),
                        );
                        extra_data.insert(
                            QUERY_FILTER_TIME_KEY,
                            data.filter_time.to_string(),
                        );
                        extra_data.insert(
                            QUERY_RERANK_TIME_KEY,
                            data.rerank_time.to_string(),
                        );
                        extra_data.insert(
                            QUERY_BM25_TIME_KEY,
                            data.bm25_search_time.to_string(),
                        );
                        extra_data.insert(
                            QUERY_VECTOR_TIME_KEY,
                            data.vector_search_time.to_string(),
                        );
                        extra_data.insert(
                            QUERY_SEARCH_HANDLER_TIME_KEY,
                            data.handler_search_time.to_string(),
                        );
                        // if let Some(query_change_result) = data.query_change_detail {
                        //     extra_data.insert(
                        //         QUERY_CHANGE_RESULT_KEY,
                        //         serde_json::to_string(&query_change_result).unwrap(),
                        //     );
                        // }
                        chat_related_response.localRepoReferenceRagList = data.result;
                    }
                }
                if chat_related_request.referenceList.is_some() {
                    let reference_list = chat_related_request.referenceList.clone().unwrap();
                    if reference_list.len() > 0 {
                        let mut select_data: Vec<ChatRelatedCodeModel> = vec![];
                        for reference in reference_list {
                            if let Some(content) = reference.content {
                                let item = ChatRelatedCodeModel {
                                    relativePath: reference.url,
                                    snippet: content,
                                    startLine: reference.lineStart,
                                    endLine: reference.lineEnd,
                                    source: None,
                                    title: None,
                                };
                                select_data.push(item);
                            }
                        }
                        if select_data.len() > 0 {
                            chat_related_response.localRepoSelectedList = Some(select_data);
                        }
                    }
                }
            }
            let consume_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() - start_time;
            extra_data.insert(
                TOTAL_CHAT_TIME_KEY,
                consume_time.to_string(),
            );
            let response = build_success_response(chat_related_response);
            // extra_data.insert(CHAT_RAG_RESPONSE_KEY,serde_json::to_string(&response).unwrap_or("".to_string()));
            let trace_info = TraceInfo{
                r#type: "CHAT_SEARCH_DATA".to_string(),
                data: serde_json::to_string(&extra_data).unwrap(),
                ideVersion: "".to_string(),
                pluginVersion: "".to_string(),
                userToken: chat_related_request.userToken.clone().unwrap_or("".to_string()),
                deviceInfo: Default::default(),
                occurTime: "".to_string(),
            };
            upload_panic_info(trace_info);
            log_chat_step("query_chat_related_info_v2",&chat_related_request,extra_data);
            Ok(response)
        }
    }



    async fn deepsearch_chat_related_info(
        &self,
        deepsearch_related_request: ChatRelatedRequestBean,
    ) -> RpcResult<BaseResponse<DeepsearchChatResponse<serde_json::Value>>> {
        info!("deepsearch chat request: {:?}", deepsearch_related_request);
        let start_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        let result = get_deepsearch_chat_strategy_result(deepsearch_related_request).await;
        match result {
            Ok(result) => {
                let end_time = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis();
                info!(
                    "deepsearch_chat_related_info consume: {},result: {:?}",
                    end_time - start_time,
                    result.data
                );
                Ok(result)
            }
            Err(e) => {
                error!("deepsearch_chat_related_info error,err={:?}", e);
                Ok(BaseResponse {
                    errorCode: 90,
                    errorMsg: Some("agent error,please check agent log".to_string()),
                    data: None,
                    totalCount: Some(0),
                })
            }
        }
    }
    ///原子检索能力
    async fn atomic_search(
        &self,
        atomic_search_request: AtomicSearchRequestBean,
    ) -> RpcResult<BaseResponse<Vec<Vec<ChatRelatedCodeModel>>>> {
        let search_result = execute_atomic_search(atomic_search_request).await;
        Ok(build_success_response(search_result))
    }
}

//单测场景
async fn query_data_with_test_scene(
    new_request: &ChatRelatedRequestBean,
) -> RpcResult<BaseResponse<ChatRelatedResponse>> {
    let req = cover_test_req(new_request.clone());
    let response = parser_unit_test_for_ai_agent(&req).await;
    match response {
        Ok(base_response) => {
            let mut result = ChatRelatedResponse {
                level1Instruction: new_request.level1Instruction.clone(),
                level2Instruction: new_request.level2Instruction.clone(),
                assitantParam: None,
                extraData: None,
                localRepoSelectedList: None,
                localRepoReferenceRagList: None,
                chatStatus: None,
                necessaryIndexPercent: None,
                questionUid: new_request.questionUid.clone(),
                sessionId: new_request.sessionId.clone(),
            };
            //如果返回值是空
            if base_response.data.is_none() {
                return Ok(build_success_response(result));
            }
            let response_data = ChatRelatedCodeModel {
                relativePath: "".to_string(),
                snippet: base_response.data.unwrap(),
                startLine: 0,
                endLine: 0,
                source: None,
                title: None,
            };
            let response_vec = vec![response_data];
            result.localRepoReferenceRagList = Some(response_vec);
            Ok(build_success_response(result))
        }
        Err(e) => {
            error!("parser_unit_test_for_ai_agent error,err={:?}", e);
            Ok(BaseResponse {
                errorCode: 90,
                errorMsg: Some("agent error,please check agent log".to_string()),
                data: None,
                totalCount: Some(0),
            })
        }
    }
}

fn cover_test_req(chat_related_request: ChatRelatedRequestBean) -> QueryRelatedRequestBean {
    let mut file_url = chat_related_request
        .currentFileUrl
        .clone()
        .unwrap_or("".to_string());
    let project_url = chat_related_request
        .projectUrl
        .clone()
        .unwrap_or("".to_string());
    //兼容绝对路径， 相对路径， file_url拼成绝对路径
    if !file_url.starts_with(project_url.as_str()) {
        file_url = Path::new(&project_url)
            .join(file_url)
            .to_str()
            .unwrap_or("")
            .to_string();
    }
    let mut current_content = "".to_string();
    if file_url.len() > 0 {
        current_content = fs::read_to_string(file_url.clone()).unwrap();
    }
    let referenceList_opt = chat_related_request.referenceList;
    let mut select_content = "".to_string();
    let mut start_line = 0;
    let mut end_line = 0;
    if let Some(referenceList) = referenceList_opt {
        let select_code_opt = referenceList.get(0);
        if let Some(select_code) = select_code_opt {
            select_content = select_code.content.clone().unwrap_or("".to_string());
            start_line = select_code.lineStart;
            end_line = select_code.lineEnd;
        }
    }

    let file_path = chat_related_request
        .currentFileUrl
        .clone()
        .unwrap_or("".to_string());
    // 判断是否为测试文件，兼容Windows和Linux路径分隔符
    let test_file = file_path.replace("\\", "/").contains("/src/test/");

    let request = QueryRelatedRequestBean {
        sessionId: chat_related_request
            .sessionId
            .clone()
            .unwrap_or("".to_string()),
        projectUrl: chat_related_request
            .projectUrl
            .clone()
            .unwrap_or("".to_string()),
        fileUrl: chat_related_request
            .currentFileUrl
            .clone()
            .unwrap_or("".to_string()),
        language: detect_language(&file_url),
        intention: "".to_string(),
        intentionType: IntentionType::UnitTest,
        fileContent: current_content,
        selectedContent: select_content,
        startLine: start_line,
        endLine: end_line,
        startOffSet: 0,
        endOffSet: 0,
        testFile: test_file,
        modelName: "".to_string(),
        testFramework: "".to_string(),
        userOrigQuery: chat_related_request.query.clone(),
    };
    request
}

//最长等待时间
const MAX_LOOP_TIME: u128 = 20000;

///agent模式的检索调用链路
pub async fn execute_query_with_agent(
    sync_flag: bool,
    chat_related_request: ChatRelatedRequestBean,
) -> ChatRelatedResponse {
    let mut chat_related_response = ChatRelatedResponse {
        level1Instruction: chat_related_request.level1Instruction.clone(),
        level2Instruction: chat_related_request.level2Instruction.clone(),
        assitantParam: None,
        extraData: None,
        localRepoSelectedList: None,
        localRepoReferenceRagList: None,
        chatStatus: None,
        necessaryIndexPercent: None,
        questionUid: None,
        sessionId: None,
    };
    let current_time = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis();
    //如果是同步，说明是正常检索流程，那么吃掉轮训调用的环节，只给agent模式返回最终结果即可
    if sync_flag {
        while true {
            let last_time = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis();
            if last_time - current_time > MAX_LOOP_TIME {
                break;
            }
            let task_result: (ChatFlowStatusEnum, TaskNodeEnum, Option<ChatFlowContext>) =
                CHAT_TASK_MANAGER_V2.process(&chat_related_request).await;

            chat_related_response.chatStatus = Some(task_result.0.clone());
            if task_result.0 == ChatFlowStatusEnum::ANSWER
                || task_result.0 == ChatFlowStatusEnum::ERROR
            {
                let mut extra_data: HashMap<String, String> = HashMap::new();
                let context_opt = task_result.2;
                match context_opt {
                    None => {}
                    Some(data) => {
                        extra_data.insert(
                            CHAT_RAG_BUILD_TIME_KEY.to_string(),
                            data.build_consume_time_total.to_string(),
                        );
                        extra_data.insert(
                            CHAT_RAG_UNDERSTAND_TIME_KEY.to_string(),
                            data.understand_consume_time.to_string(),
                        );
                        extra_data.insert(
                            CHAT_RAG_QUERY_INDEX_KEY.to_string(),
                            data.query_index_time.to_string(),
                        );
                        if let Some(query_change_result) = data.query_change_detail {
                            extra_data.insert(
                                QUERY_CHANGE_RESULT_KEY.to_string(),
                                serde_json::to_string(&query_change_result).unwrap(),
                            );
                        }
                        chat_related_response.localRepoReferenceRagList = data.result;
                    }
                }
                if chat_related_request.referenceList.is_some() {
                    let reference_list = chat_related_request.referenceList.clone().unwrap();
                    if reference_list.len() > 0 {
                        let mut select_data: Vec<ChatRelatedCodeModel> = vec![];
                        for reference in reference_list {
                            if let Some(content) = reference.content {
                                let item = ChatRelatedCodeModel {
                                    relativePath: reference.url,
                                    snippet: content,
                                    startLine: reference.lineStart,
                                    endLine: reference.lineEnd,
                                    source: None,
                                    title: None,
                                };
                                select_data.push(item);
                            }
                        }
                        if select_data.len() > 0 {
                            chat_related_response.localRepoSelectedList = Some(select_data);
                        }
                    }
                }

                break;
            }
        }
    } else {
        chat_related_response.chatStatus = Some(ChatFlowStatusEnum::BUILD_INDEX);

        //如果异步，说明是构建索引流程，起新线程异步构建索引
        tokio::spawn(async move {
            while true {
                let task_result = CHAT_TASK_MANAGER_V2.process(&chat_related_request).await;
                if task_result.0 != ChatFlowStatusEnum::BUILD_INDEX {
                    break;
                }
            }
        });
    }
    chat_related_response
}

///填充和修补上游参数。防止将无效数据透传给下游
/// 注意：如果sessionId/questionId是空，那么不仅修改chat_related_request的id，同时要返回ChatRelatedResponse
/// 修改参数目的：针对query_chat_related_info_with_agent，上游只会调用一次。那么改了参数的id后会自动向后继续执行
/// 返回结果目的：针对query_chat_related_info函数，看到有返回值就直接返回了，上游会循环调用
pub fn fill_param(
    mut chat_related_request: ChatRelatedRequestBean,
) -> (Option<ChatRelatedResponse>, ChatRelatedRequestBean) {
    let reference_vec_opt = &chat_related_request.referenceList;
    if reference_vec_opt.is_some() && chat_related_request.projectUrl.is_some() {
        fill_content(&mut chat_related_request)
    }
    //step 2 如果sessionId是空，或者questionId是空，说明是新对话，生成uuid后直接返回，不要进入对话流程。否则前端页面会一直等着
    let session_id_opt = chat_related_request.sessionId.clone();
    let question_id_opt = chat_related_request.questionUid.clone();
    if session_id_opt.as_ref().map_or(true, |v| v.is_empty()) {
        let chat_related_response = ChatRelatedResponse {
            level1Instruction: chat_related_request.level1Instruction.clone(),
            level2Instruction: chat_related_request.level2Instruction.clone(),
            assitantParam: None,
            extraData: None,
            localRepoSelectedList: None,
            localRepoReferenceRagList: None,
            chatStatus: Some(ChatFlowStatusEnum::BUILD_INDEX),
            necessaryIndexPercent: Some(0),
            questionUid: Some(Uuid::new_v4().simple().to_string()),
            sessionId: Some(Uuid::new_v4().simple().to_string()),
        };
        chat_related_request.sessionId = chat_related_response.sessionId.clone();
        chat_related_request.questionUid = chat_related_response.questionUid.clone();
        return (Some(chat_related_response), chat_related_request);
    }
    if question_id_opt.as_ref().map_or(true, |v| v.is_empty()) {
        let chat_related_response = ChatRelatedResponse {
            level1Instruction: chat_related_request.level1Instruction.clone(),
            level2Instruction: chat_related_request.level2Instruction.clone(),
            assitantParam: None,
            extraData: None,
            localRepoSelectedList: None,
            localRepoReferenceRagList: None,
            chatStatus: Some(ChatFlowStatusEnum::BUILD_INDEX),
            necessaryIndexPercent: Some(0),
            questionUid: Some(Uuid::new_v4().simple().to_string()),
            sessionId: Some(session_id_opt.unwrap()),
        };
        chat_related_request.questionUid = chat_related_response.questionUid.clone();
        return (Some(chat_related_response), chat_related_request);
    }
    if chat_related_request.branch.is_none() {
        chat_related_request.branch = Some(DEFAULT_BRANCH.to_string());
    }
    if chat_related_request.projectUrl.is_none() {
        chat_related_request.projectUrl = Some(DEFAULT_PROJECT_URL.to_string());
    }
    (None, chat_related_request)
}

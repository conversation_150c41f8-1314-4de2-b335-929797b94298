use crate::mult_strategy::code_complete_service::code_complete;
use agent_common_service::model::code_complete_model::{CodeCompletionRequestBean, CompletionResultModel};
use agent_db::remote::rpc_model::BaseResponse;
use jsonrpsee::core::{async_trait, RpcResult};
use jsonrpsee::proc_macros::rpc;
use log::{debug, error};
use std::time::{SystemTime, UNIX_EPOCH};

#[rpc(server)]
trait CodeComplateService {
    #[method(name = "code_complete")]
    async fn code_complete(
        &self,
        code_complete_request: CodeCompletionRequestBean,
    ) -> RpcResult<BaseResponse<CompletionResultModel>>;
}

///对外服务实现类
pub struct CodeComplateServiceImpl {}

///对外服务的函数实现
#[async_trait]
impl CodeComplateServiceServer for CodeComplateServiceImpl {
    ///代码补全
    async fn code_complete(
        &self,
        code_complete_request: CodeCompletionRequestBean,
    ) -> RpcResult<BaseResponse<CompletionResultModel>> {
        let start_millis = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        let result = code_complete(code_complete_request).await;
        match result {
            Ok(code_complete_result) => {
                let end_millis = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis();
                debug!(
                    "code complete consume: {} ,result: {:?}",
                    end_millis - start_millis,
                    code_complete_result
                );
                Ok(code_complete_result)
            }
            Err(e) => {
                let end_millis = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis();
                error!(
                    "code complete consume: {},error: {}",
                    end_millis - start_millis,
                    e
                );
                let result = BaseResponse {
                    errorCode: 90,
                    errorMsg: Some("agent error,please check agent log".to_string()),
                    data: None,
                    totalCount: Some(0),
                };
                Ok(result)
            }
        }
    }
}
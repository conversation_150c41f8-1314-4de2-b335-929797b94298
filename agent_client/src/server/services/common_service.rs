use crate::server::services::project_understand_service::{execute_query_with_agent, fill_param};
use agent_android_service::android_strategy::android_ideclient_request_handle::handle_ideclient_request;
use agent_codefuse_service::dialogue::codefuse_index_repository::{
    CHUNK_CLIENT, FILE_CLIET, METHOD_CLIENT,
};
use agent_codefuse_service::dialogue::data_struct::DataStatus;
use agent_codefuse_service::dialogue::file_index_operator::incremental_update_file;
use agent_codefuse_service::dialogue::index_db_utils::delete_project;
use agent_codefuse_service::dialogue::misc_util::autonomous_sleep_no_bolck;
use agent_codefuse_service::dialogue::repo_index_operator::{
    get_repo_build_status, save_repo_delete_status, RepoStatusEnum, GIT_REPO_CONTAINER,
    GIT_REPO_STATUS,
};
use agent_codefuse_service::function::chat_strategy::{
    calculate_index_build_progress, query_count_in_kv, query_data_status, CHAT_TASK_MANAGER_V2,
};
use agent_codefuse_service::service::index_service::{IndexService, IndexServiceResponse};
use agent_codefuse_service::utils::path::DEFAULT_BRANCH;
use agent_common_service::model::chat_model::{
    ChatRelatedRequestBean, ChatRelatedResponse, IndexOperateRequestBean, IntentEnum,
};
use agent_common_service::model::code_scan_model::ScanConfig;
use agent_common_service::model::incremental_update_model::IncrementalUpdateRequestBean;
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::remote_client::QueryInProject;
use agent_db::dal::vector_client::{test_paginated_query, VectorClient, VECTOR_CLIENT};
use agent_db::remote::rpc_model::{
    build_success_response, build_success_response_no_result, BaseResponse,
};
use anyhow::anyhow;
use jsonrpsee::core::{async_trait, RpcResult};
use jsonrpsee::proc_macros::rpc;
use log::{debug, error, info, warn};
use std::cmp::max;
use std::collections::HashMap;
use std::path::Path;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};

#[rpc(server)]
trait CommonService {
    //仓库扫描
    #[method(name = "scan_project")]
    async fn scan_project(&self, scan_config: ScanConfig) -> RpcResult<BaseResponse<()>>;

    //健康检查
    #[method(name = "health_check")]
    async fn health_check(&self) -> RpcResult<BaseResponse<HashMap<String, String>>>;

    //索引增量更新
    #[method(name = "incremental_update")]
    async fn incremental_update(
        &self,
        incremental_update_request: IncrementalUpdateRequestBean,
    ) -> RpcResult<BaseResponse<()>>;

    #[method(name = "ideclient_agent_request")]
    async fn ideclient_agent_request(
        &self,
        request_json_str: String,
    ) -> RpcResult<BaseResponse<String>>;

    //查询索引状态
    #[method(name = "query_data_status")]
    async fn query_data_status(
        &self,
        request: IndexOperateRequestBean,
    ) -> RpcResult<BaseResponse<DataStatus>>;
    //创建索引
    #[method(name = "create_index")]
    async fn create_index(
        &self,
        chat_related_request: IndexOperateRequestBean,
    ) -> RpcResult<BaseResponse<DataStatus>>;
    //删除索引
    #[method(name = "delete_index")]
    async fn delete_index(
        &self,
        chat_related_request: IndexOperateRequestBean,
    ) -> RpcResult<BaseResponse<DataStatus>>;

    // 检索测试
    #[method(name = "query_index_test")]
    async fn query_index_test(
        &self,
        chat_related_request: ChatRelatedRequestBean,
    ) -> RpcResult<BaseResponse<()>>;
}

///对外服务实现类
pub struct CommonServiceImpl {}

async fn build_project(chat_related_request: ChatRelatedRequestBean) {
    //agent模式下只关注ChatRelatedRequestBean的参数
    let new_request = fill_param(chat_related_request).1;

    let _ = CHAT_TASK_MANAGER_V2.process(&new_request).await;

    let build_repo_status = CHAT_TASK_MANAGER_V2.get_build_repo_status().await;
    //如果是第一次创建索引,当前线程直接返回结果，但同时异步循环调用底层链路。跑完构建索引过程
    if build_repo_status == RepoStatusEnum::CREATE {
        execute_query_with_agent(false, new_request).await;
    }
}

///对外服务的函数实现
#[async_trait]
impl CommonServiceServer for CommonServiceImpl {
    //对接插件侧的代码扫描接口，由于可能引起刚启动时候的cpu卡顿，故暂时不用这个接口，后续从agent侧下掉
    async fn scan_project(&self, scan_config: ScanConfig) -> RpcResult<BaseResponse<()>> {
        // let project_url = scan_config.url;
        // let service: CodeFuseScanStrategy = CodeFuseScanStrategy {};
        // tokio::spawn(code_scan(service, project_url.clone()));
        // if AGENT_CONFIG.agent_type > 0{
        //     info!("build index when project open.param: {:?}",scan_config);
        //     let req = ChatRelatedRequestBean{
        //         projectUrl: Some(scan_config.url.clone()),
        //         query: "测试任务".to_string(),
        //         currentFileUrl: None,
        //         referenceList: None,
        //         recentFilesInfo: None,
        //         branch: scan_config.branch,
        //         projectGitUrl: None,
        //         level1Instruction: None,
        //         level2Instruction: None,
        //         chatHistory: None,
        //         userToken: None,
        //         productType: None,
        //         questionUid: None,
        //         sessionId: None,
        //         chatIntent: None,
        //         tcpConnId: None,
        //     };
        //     tokio::spawn(build_project(req));
        // }
        Ok(BaseResponse::default())
    }

    ///健康检查
    async fn health_check(&self) -> RpcResult<BaseResponse<HashMap<String, String>>> {
        debug!("health check request start");
        let mut result: HashMap<String, String> = HashMap::new();
        result.insert(
            "agent_version".to_string(),
            AGENT_CONFIG.agent_version.clone(),
        );
        Ok(build_success_response(result))
    }

    ///当前函数如果打开，会影响project级的补全索引构建，所以暂时关闭
    async fn incremental_update(
        &self,
        incremental_update_request: IncrementalUpdateRequestBean,
    ) -> RpcResult<BaseResponse<()>> {
        let file_url = incremental_update_request.fileUrl.clone();
        let project_url = incremental_update_request.projectUrl.clone();
        let file_content = incremental_update_request.fileContent.clone();

        tokio::spawn(async move {
            incremental_update_file(&file_url, &project_url, &file_content).await
        });
        Ok(BaseResponse::default())
    }

    ///返回file, method, chunk的基本数据情况，总数，no summary， no vector的数据
    async fn query_data_status(
        &self,
        request: IndexOperateRequestBean,
    ) -> RpcResult<BaseResponse<DataStatus>> {
        let start_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        info!("query_data_status request: {:?}", request);
        let project_url = request
            .projectUrl
            .clone()
            .unwrap_or("default_project_url".to_string());
        let branch = request.branch.clone();
        let mut data_status = query_data_status(request).await;
        //如果必要索引是0，那么尝试构建索引
        if data_status.build_nessary_index_progress == 0 {
            let req = ChatRelatedRequestBean {
                projectUrl: Some(project_url),
                query: "测试任务".to_string(),
                currentFileUrl: None,
                referenceList: None,
                recentFilesInfo: None,
                branch: branch,
                projectGitUrl: None,
                level1Instruction: None,
                level2Instruction: None,
                userToken: None,
                productType: None,
                questionUid: None,
                sessionId: None,
                chatIntent: None,
                tcpConnID: None,
                changedQuery: None,
                explanation: None,
                searchTypeList: None,
            };
            tokio::spawn(build_project(req));
        }
        let mut rst = BaseResponse::<DataStatus>::default();
        rst.data = Some(data_status);
        let end_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        info!(
            "query_data_status response: {:?},consume_time: {}",
            rst,
            end_time - start_time
        );
        Ok(rst)
    }

    async fn ideclient_agent_request(
        &self,
        request_json_str: String,
    ) -> RpcResult<BaseResponse<String>> {
        let response_str_opt = handle_ideclient_request(request_json_str).await;
        Ok(BaseResponse {
            errorCode: 0,
            errorMsg: None,
            data: response_str_opt,
            totalCount: Some(0),
        })
    }

    //保存索引
    async fn create_index(
        &self,
        chat_related_request: IndexOperateRequestBean,
    ) -> RpcResult<BaseResponse<DataStatus>> {
        info!("create_index request: {:?}", chat_related_request);
        let req_clone = chat_related_request.clone();

        let project_url = chat_related_request.projectUrl.unwrap_or("".to_string());
        let branch = chat_related_request
            .branch
            .unwrap_or(DEFAULT_BRANCH.to_string());

        let mut build_handle = tokio::spawn(async move {
            let response = IndexService::build_index(
                project_url,
                branch,
                AGENT_CONFIG.concurrency_timeout_secs,
            )
            .await;
            info!(
                "IndexService::build_index completed in background: {:?}",
                response
            );
            response
        });

        // 使用 tokio::select! 来实现等待10秒，但不取消后台任务
        tokio::select! {
            result = &mut build_handle => {
                match result {
                    Ok(response) => {
                        info!("IndexService::build_index completed within 10s: {:?}", response);
                    }
                    Err(e) => {
                        error!("IndexService::build_index task failed: {:?}", e);
                    }
                }
            }
            _ = tokio::time::sleep(Duration::from_secs(10)) => {
                info!("IndexService::build_index timeout after 10s, but continues running in background");
                // 超时后任务仍在后台执行，不会被取消
                // build_handle 会继续在后台运行直到完成

                // 10秒超时后，先执行 CHUNK_CLIENT.commit() 保存已有数据
                info!("10秒超时，先执行 CHUNK_CLIENT.commit() 保存已处理的数据");
                CHUNK_CLIENT.commit();
                info!("CHUNK_CLIENT.commit() 执行完成，已保存当前数据");
            }
        }
        let result = build_success_response(query_data_status(req_clone).await);
        info!("create_index response: {:?}", result);
        Ok(result)
    }

    async fn delete_index(
        &self,
        chat_related_request: IndexOperateRequestBean,
    ) -> RpcResult<BaseResponse<DataStatus>> {
        info!("delete_index request: {:?}", chat_related_request);
        let project_url = chat_related_request
            .projectUrl
            .clone()
            .unwrap_or(String::from(""));
        let branch = chat_related_request
            .branch
            .clone()
            .unwrap_or(DEFAULT_BRANCH.to_string());
        ///设置deleting状态为true
        save_repo_delete_status(&project_url, &branch, true).await;
        ///如果有正在building的任务， 需要等待此任务结束， 最大等待时间15秒
        let mut project_url_running = "".to_string();
        let mut current_branch_running = "".to_string();

        let git_reop_status = GIT_REPO_STATUS.get_all().await;
        for (repo_info, repo_status_cache) in git_reop_status {
            if repo_info.project_url == project_url {
                if repo_status_cache.building {
                    project_url_running = repo_info.project_url.clone();
                    current_branch_running = repo_info.current_branch.clone();
                    break;
                }
            }
        }
        info!("project_delete index_building status {}, project_url_running , current_branch_running : {}, {}", get_repo_build_status(&project_url_running, &current_branch_running).await, project_url_running, current_branch_running);
        let mut is_need_wait = true;
        let mut start = Instant::now();
        let mut max_duration = Duration::from_secs(15);

        if !project_url_running.is_empty() {
            //最多等待两个超时
            let max_count = 2;
            let mut current_time = 0;
            while is_need_wait {
                while get_repo_build_status(&project_url_running, &current_branch_running).await {
                    autonomous_sleep_no_bolck(300).await;
                    if start.elapsed() >= max_duration {
                        info!(
                            "exceeded max_duration {:?} when waiting building task end",
                            max_duration
                        );
                        current_time = current_time + 1;
                        if current_time >= max_count {
                            info!("reach max time to wait building task end");
                            is_need_wait = false;
                            break;
                        } else {
                            start = Instant::now();
                            max_duration = Duration::from_secs(15);
                        }
                    }
                }
                let loop_count = 5;
                for i in 0..loop_count {
                    if get_repo_build_status(&project_url_running, &current_branch_running).await {
                        info!("lanch index_build task again");
                        break;
                    } else {
                        if i == loop_count - 1 {
                            info!("持续 {} 次没有build任务， no wait now", loop_count);
                            is_need_wait = false;
                            break;
                        }
                    }
                    autonomous_sleep_no_bolck(300).await;
                }
            }
        }

        let projectUrl = chat_related_request
            .projectUrl
            .clone()
            .unwrap_or("".to_string());
        GIT_REPO_CONTAINER.delete(&projectUrl).await;
        delete_project(&projectUrl).await;
        let result = build_success_response(query_data_status(chat_related_request).await);
        ///设置deleting状态为false
        save_repo_delete_status(&project_url, &branch, false).await;
        info!("delete_index response: {:?}", result);
        Ok(result)
    }

    async fn query_index_test(
        &self,
        chat_related_request: ChatRelatedRequestBean,
    ) -> RpcResult<BaseResponse<()>> {
        // 指定要查询的LanceDB向量数据库目录
        let vectory_path = &chat_related_request.projectUrl.unwrap_or("".to_string());

        // 检查目录是否存在
        if !std::path::Path::new(vectory_path).exists() {
            println!("Error: LanceDB directory {} does not exist!", vectory_path);
            println!("Please ensure the LanceDB data directory exists before running this test.");
            return Ok(BaseResponse::default());
        }

        // 执行分页查询测试（纯读取操作）
        test_paginated_query(&VECTOR_CLIENT).await;
        return Ok(BaseResponse::default());
    }
}

use jsonrpsee::{core::TEN_MB_SIZE_BYTES, server::{stop_channel, ServerHandle, StopHandle}};
use std::{io, net::SocketAddr, time::Duration};
use agent_codefuse_service::service::tcp::connection::{ConnConfig, ConnState};
use agent_codefuse_service::service::tcp::dispatch::{NotificationDispatcher, RequestDispatcher};
use tokio::{net::{TcpListener, TcpStream, ToSocketAddrs}, sync::mpsc};
use crate::server::ipc_services::socket::socket_transport;

/// JSON RPC server.
pub struct Server {
    listener: TcpListener,
    server_cfg: ServerConfig,
}

impl Server {
    pub async fn build(addrs: impl ToSocketAddrs) -> io::Result<Self> {
        let listener = TcpListener::bind(addrs).await?;
        Ok(Self {
            listener,
            server_cfg: ServerConfig::default(),
        })
    }

    pub fn start(self, request_dispatch: fn(&mut RequestDispatcher), notification_dispatch: fn(&mut NotificationDispatcher)) -> ServerHandle {
        let (stop_handle, server_handle) = stop_channel();

        tokio::spawn(async move {
            let mut connection_counter: u32 = 0;

            let (drop_on_completion, mut process_connection_awaiter) = mpsc::channel::<()>(1);

            loop {
                let result  = tokio::select! {
                    result = self.listener.accept() => result,
                    _ = stop_handle.clone().shutdown() => {
                        log::trace!("stop received, not accepting new connections");
                        break;
                    },
                };
                match result {
                    Ok((socket, remote_addr)) => {
                        process_connection(ProcessConnection {
                            remote_addr,
                            stop_handle: stop_handle.clone(),
                            connection_counter,
                            socket,
                            request_dispatch,
                            notification_dispatch,
                            server_cfg: self.server_cfg.clone(),
                            drop_on_completion: drop_on_completion.clone(),
                        });
                        connection_counter = connection_counter.wrapping_add(1);
                    },
                    Err(e) => {
                        log::debug!("Error while awaiting a new connection: {:?}", e);
                    }
                }
            }

            drop(drop_on_completion);

            // 等待所有连接结束
            while process_connection_awaiter.recv().await.is_some() {}
        });

        server_handle
    }

    pub fn local_addr(&self) -> std::io::Result<SocketAddr> {
        self.listener.local_addr()
    }
}

// 和 jsonrpsee ServerConfig 部分配置一致
#[derive(Debug, Clone)]
pub struct ServerConfig {
    /// 请求体最大值
    pub max_request_body_size: u32,
    /// 响应体最大值
    pub max_response_body_size: u32,
    /// response 的 buffer 大小
    pub response_buffer_capacity: usize,
    /// client 最大并发请求数
    pub max_concurrent_requests: usize,
    /// 请求超时时间
    pub request_timeout: Duration,
    /// `TCP_NODELAY` settings.
    pub tcp_no_delay: bool,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            max_request_body_size: TEN_MB_SIZE_BYTES,
            max_response_body_size: TEN_MB_SIZE_BYTES,
            response_buffer_capacity: 1024,
            max_concurrent_requests: 256,
            request_timeout: Duration::from_secs(300),
            tcp_no_delay: true,
        }
    }
}

struct ProcessConnection {
    connection_counter: u32,
    server_cfg: ServerConfig,
    stop_handle: StopHandle,
    socket: TcpStream,
    drop_on_completion: mpsc::Sender<()>,
    remote_addr: SocketAddr,
    request_dispatch: fn(&mut RequestDispatcher),
    notification_dispatch: fn(&mut NotificationDispatcher)
}

fn process_connection(params: ProcessConnection) {
    let ProcessConnection {
        connection_counter,
        remote_addr,
        socket,
        drop_on_completion,
        request_dispatch,
        notification_dispatch,
        stop_handle,
        server_cfg,
    } = params;

    if let Err(e) = socket.set_nodelay(server_cfg.tcp_no_delay) {
		log::warn!("Could not set NODELAY on socket: {:?}", e);
		return;
	}

    let (sender, receiver, io_threads) = socket_transport(socket, stop_handle);

    tokio::spawn(async move {
        log::info!("🔗 TCP连接已建立 - 连接计数器: {}, 远程地址: {}", connection_counter, remote_addr);

        let conn_config = ConnConfig {
            response_buffer_capacity: server_cfg.response_buffer_capacity,
            max_concurrent_requests: server_cfg.max_concurrent_requests,
            request_timeout: server_cfg.request_timeout,
        };

        // 运行连接状态处理，不传递conn_id，因为还没有生成
        tokio::join!(
            ConnState::new(sender, receiver, conn_config).run(None, request_dispatch, notification_dispatch),
            io_threads.join()
        );

        log::info!("❌ TCP连接已断开 - 连接计数器: {}, 远程地址: {}", connection_counter, remote_addr);

        drop(drop_on_completion)
    });
}

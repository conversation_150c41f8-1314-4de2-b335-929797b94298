use agent_codefuse_service::service::tcp::dispatch::{NotificationDispatcher, RequestDispatcher};
use agent_codefuse_service::service::tcp::init_service::handle_init;
use crate::server::ipc_services::greet::{handle_exe_helloworld, handle_initialize};

// 临时测试使用
pub mod greet;

pub mod server;
pub mod socket;

// 注册 request 方法
pub fn request_dispatch(dispatcher: &mut RequestDispatcher) {
    dispatcher
        .on("init", handle_init)
        .on("exe_helloworld", handle_exe_helloworld);
}

// 注册 notification 方法
pub fn notification_dispatch(dispatcher: &mut NotificationDispatcher) {
    dispatcher
        .on("initialize", handle_initialize);
}

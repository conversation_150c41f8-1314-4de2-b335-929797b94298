use agent_db::remote::rpc_model::{build_success_response, BaseResponse};
use anyhow::Result;
use log::info;
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::atomic::{AtomicBool, AtomicUsize, Ordering};
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use agent_codefuse_service::service::ide_service::get_project_connection_manager;
use agent_codefuse_service::service::tcp::connection::ConnContext;
use agent_codefuse_service::service::tcp::init_service::{handle_init, InitParams};
use serde_json::Value;
use tokio::sync::RwLock;
use uuid::Uuid;


#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct GreetParams {
    pub content: String,
}

#[derive(Debug, Eq, PartialEq, <PERSON><PERSON>, Deserialize, Serialize)]
pub struct GreetResponse {
    pub answer: String,
}

pub const GET_NAME_METHOD: &'static str = "get_name";

#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct GetNameParams {
    pub question: String,
}

#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct GetNameParamsResponse {
    pub name: String,
}






pub async fn handle_greet(
    conn_ctx: ConnContext,
    params: GreetParams,
) -> anyhow::Result<Option<GreetResponse>> {
    let GetNameParamsResponse { name } = conn_ctx
        .request(
            GET_NAME_METHOD,
            GetNameParams {
                question: "what's you name?".into(),
            },
        )
        .await
        .unwrap();
    return Ok(Some(GreetResponse {
        answer: format!("hello {}, you send {}", name, params.content).to_owned(),
    }));
}

/// HelloWorld 请求参数结构
#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct HelloWorldParams {
    pub message: String,
}

/// HelloWorld 响应结构
#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct HelloWorldResponse {
    pub reply: String,
}

/// ExeHelloWorld 请求参数结构
#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct ExeHelloWorldParams {
    pub project_url: String,
    pub conn_id: String,
    pub message: String,
}

/// ExeHelloWorld 响应结构
#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct ExeHelloWorldResult {
    pub success: bool,
    pub client_reply: Option<String>,
    pub error_message: Option<String>,
}

#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct InitializeParams {
    pub count: isize,
}

#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct InitializedParams {
    pub count: isize,
}

static CALL_COUNT: AtomicUsize = AtomicUsize::new(0);
pub async fn handle_initialize(
    conn_ctx: ConnContext,
    params: InitializeParams,
) -> anyhow::Result<()> {
    dbg!(&params);

    // 原子递增并获取旧值
    let count = CALL_COUNT.fetch_add(1, Ordering::SeqCst);
    if count == 0 {
        // 第二次及以后才执行
        let init = InitParams {
            project_url: vec!["/Users/<USER>/workspace/code-agent-demo".to_string()],
            conn_id: None,
        };
        let r = handle_init(conn_ctx.clone(), init).await;
        println!("{:?}", r);
    }

    return Ok(());
}

/// 处理exe_helloworld请求 - 通过已建立的连接调用客户端的helloworld接口
pub async fn handle_exe_helloworld(
    conn_ctx: ConnContext,
    params: ExeHelloWorldParams,
) -> anyhow::Result<BaseResponse<ExeHelloWorldResult>> {
    info!(
        "🚀 处理exe_helloworld请求 - 项目: {}, 连接ID: {}, 消息: {}",
        params.project_url, params.conn_id, params.message
    );

    // 查找项目连接映射
    let project_manager = get_project_connection_manager();
    let target_conn_ctx_opt = project_manager
        .find_connection(&params.project_url, &params.conn_id)
        .await;

    if let Some(target_conn_ctx) = target_conn_ctx_opt {
        info!("✅ 找到目标连接，准备调用客户端helloworld接口");

        // 构建helloworld请求参数
        let hello_params = HelloWorldParams {
            message: params.message.clone(),
        };

        // 通过已建立的连接调用客户端的helloworld接口
        match target_conn_ctx
            .request::<HelloWorldParams, HelloWorldResponse>("helloworld", hello_params)
            .await
        {
            Ok(response) => {
                info!(
                    "✅ 成功调用客户端helloworld接口，客户端回复: {}",
                    response.reply
                );
                Ok(build_success_response(ExeHelloWorldResult {
                    success: true,
                    client_reply: Some(response.reply),
                    error_message: None,
                }))
            }
            Err(e) => {
                let error_msg = format!("调用客户端helloworld接口失败: {}", e);
                info!("⚠️ {}", error_msg);
                Ok(build_success_response(ExeHelloWorldResult {
                    success: false,
                    client_reply: None,
                    error_message: Some(error_msg),
                }))
            }
        }
    } else {
        let error_msg = format!(
            "找不到项目连接映射，项目: {}, 连接ID: {}",
            params.project_url, params.conn_id
        );
        info!("⚠️ {}", error_msg);
        Ok(build_success_response(ExeHelloWorldResult {
            success: false,
            client_reply: None,
            error_message: Some(error_msg),
        }))
    }
}

#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct FindMethodParams {
    pub project_url: String,
    pub conn_id: String,
    pub method_name: String,
}

#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct MethodInfo {
    pub method_name: String,
    pub class_name: Option<String>,
    pub file_path: Option<String>,
    pub start_line: Option<u32>,
    pub end_line: Option<u32>,
    pub signature: Option<String>,
    pub content: Option<String>,
}

#[derive(Debug, Eq, PartialEq, Clone, Deserialize, Serialize)]
pub struct FindMethodResponse {
    pub success: bool,
    pub message: String,
    pub methods: Vec<MethodInfo>,
}

use agent_codefuse_service::service::tcp::msg::Message;
use jsonrpsee::server::StopHandle;
use tokio::{io::{self, BufReader}, net::{tcp::{OwnedReadHalf, OwnedWriteHalf}, TcpStream}, sync::mpsc::{self, Receiver, Sender}, task::<PERSON><PERSON><PERSON><PERSON><PERSON>};


pub fn socket_transport(
    stream: TcpStream,
    stop_handle: StopHandle
) -> (Sender<Message>, Receiver<Message>, IoThreads) {
    let (read_half, write_half) = stream.into_split();
    let (reader_receiver, reader) = make_reader(read_half, stop_handle.clone());
    let (writer_sender, writer) = make_write(write_half, stop_handle);

    let io_threads = make_io_threads(reader, writer);
    (writer_sender, reader_receiver, io_threads)
}

fn make_reader(stream: OwnedReadHalf, stop_handle: StopHandle) -> (Receiver<Message>, JoinHandle<io::Result<()>>) {
    let (reader_sender, reader_receiver) = mpsc::channel::<Message>(100);
    let reader = tokio::spawn(async move {
        let stopped = stop_handle.shutdown();
        tokio::pin!(stopped);

        let mut buf_read = BufReader::new(stream);
        loop {
            let msg = tokio::select! {
                msg = Message::read(&mut buf_read) => msg,
                _ = &mut stopped => break,
            };
            match msg {
                Ok(Some(msg)) => {
                    if let Err(e) = reader_sender.send(msg).await {
                        log::debug!("reader send error, {}", e);
                        break;
                    };
                }
                Ok(None) => {
                    // message 为 None，说明 socket 连接已断开
                    break;
                }
                Err(err) => {
                    // message 读取错误，记录日志，不断开连接
                    log::error!("read message error: {}", err);
                }
            }
        }
        Ok(())
    });
    (reader_receiver, reader)
}

fn make_write(mut stream: OwnedWriteHalf, stop_handle: StopHandle) -> (Sender<Message>, JoinHandle<io::Result<()>>) {
    let (writer_sender, mut writer_receiver) = mpsc::channel::<Message>(100);
    
    let writer = tokio::spawn(async move {
        let stopped = stop_handle.shutdown();
        tokio::pin!(stopped);

        loop {
            let msg = tokio::select! {
                msg = writer_receiver.recv() => msg,
                _ = &mut stopped => break,
            };
            if let Some(msg) = msg {
                if let Err(e) = msg.write(&mut stream).await {
                    log::error!("writer message error: {}", e);
                }
            } else {
                break;
            }
        }
        Ok(())
    });
    (writer_sender, writer)
}

pub fn make_io_threads(
    reader: JoinHandle<io::Result<()>>,
    writer: JoinHandle<io::Result<()>>,
) -> IoThreads {
    IoThreads { reader, writer }
}

pub struct IoThreads {
    reader: JoinHandle<io::Result<()>>,
    writer: JoinHandle<io::Result<()>>,
}

impl IoThreads {
    pub async fn join(self) -> () {
        let _ = tokio::join!(self.reader, self.writer);
    }
}

use log::info;

use crate::server::agent_server::{start_server, start_tcp_server};
use crate::server::process_lock::{init_process_lock, release_process_lock};
use agent_codefuse_service::dialogue::misc_util::init_panic_hook;
use agent_db::config::agent_logger::init_logger;
use agent_db::config::runtime_config::AGENT_CONFIG;
use tokio::task;

pub mod server{
    pub mod agent_server;
    pub mod process_lock;
    pub mod services{
        pub mod project_understand_service;
        pub mod code_complete_service;
        pub mod qa_service;
        pub mod common_service;
        pub mod code_edits_service;
    }
    pub mod ipc_services;
}


pub mod mult_strategy{
    pub mod code_complete_service;


    pub mod code_scan;
    
    pub mod code_edits_service;
}


#[tokio::main]
async fn main() -> std::io::Result<()> {
    //初始化日志
    let _ = init_logger();

    // 初始化进程锁，确保只有一个进程在运行
    if let Err(e) = init_process_lock() {
        eprintln!("Failed to initialize process lock: {}", e);
        std::process::exit(1);
    }

    // 设置优雅关闭处理
    let shutdown_handler = tokio::spawn(async {
        tokio::signal::ctrl_c().await.expect("Failed to listen for ctrl+c");
        info!("Received Ctrl+C, shutting down gracefully...");
        release_process_lock();
    });

    // task::spawn(init_panic_hook());
    info!("agent config: {:?}",AGENT_CONFIG);

    // 启动长连接服务
    tokio::spawn(start_tcp_server());

    // 启动主服务器
    tokio::select! {
        result = start_server() => {
            if let Err(e) = result {
                log::error!("Server error: {}", e);
            }
        }
        _ = shutdown_handler => {
            info!("Shutdown signal received");
        }
    }

    // 确保在退出前释放锁
    release_process_lock();
    info!("Agent process exited");
    Ok(())
}

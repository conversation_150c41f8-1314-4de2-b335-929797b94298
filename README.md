## agent_codefuse_service

本仓库主要负责了 agent client 的代码解析、相似度匹配的具体实现。

### 更新依赖

由于agent_codefuse_service 依赖了 agent_db 和 agent_common_service, 而这两个依赖是使用 git 进行管理的，所以每次要手动更新这两个依赖包的版本。

执行以下命令即可更新。

```sh
cargo update -p agent_db
cargo update -p agent_common_service
```

### 文件夹结构

#### 单测结构

单测需要用到的文件可以放在 `fixtures` 目录下。

ts 单测要用到的仓库：

```sh
git clone https://github.com/opensumi/core.git fixtures/repo/core
```
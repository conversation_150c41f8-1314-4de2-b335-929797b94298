# agent

## 依赖管理

由于内部没有 `crates.io` 类似的服务，目前采用 git 作为依赖管理的手段。

如果你执行 `cargo build` 遇到了 ssh 相关的报错，请把以下内容贴到你的 `~/.ssh/config` 文件中:

```sh
Host code.alipay.com
  User git
  AddKeysToAgent yes
  UseKeychain yes
  IdentityFile ~/.ssh/id_rsa
  Hostname code.alipay.com
  PreferredAuthentications publickey
```

这里的 `IdentityFile` 需要修改为你自己配置的 AntCode 私钥路径。

如果要更新依赖代码，请执行 `cargo update`。

## build

```sh
cargo build
```

## test

```sh
./scripts/start
```
